<?php

namespace Sparefoot\MyFootService\Controller;

use DataDog\DogStatsd;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Psr\Http\Message\ResponseInterface;
use Sparefoot\MyFootService\Clients\BidOptimizerClient;
use Sparefoot\MyFootService\Clients\ClientApiClient;
use Sparefoot\MyFootService\Clients\SearchClient;
use Sparefoot\MyFootService\Flot\SearchPositionLine;
use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Models\SearchRankQuery;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\IntegratedFields;
use Sparefoot\MyFootService\Service\UrlUtil;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Utils\CsrfUtil;
use Sparefoot\ServiceBundle\Stats\StatsD;
use Sparefoot\ServiceBundle\Stats\StatsInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class FacilityController extends AbstractRestrictedController
{
    public const ADD_FACILITY_CSRF_TOKEN = 'add_facility_token';
    public const AMENITIES_CSRF_TOKEN = 'amenities_csrf_token';
    public const UPDATE_BID_CSRF_TOKEN = 'update_bid_csrf_token';
    public const RANK_BID_CSRF_TOKEN = 'rank_bid_csrf_token';
    public const ADD_CS4_FACILITY_TOKEN = 'add_cs4_token';
    public const PERRYVILLE_DUMMY_LOCATION = ********;

    private StatsInterface $statsClient;
    private Client $phidoClient;

    protected function _init(): ?RedirectResponse
    {
        $request = $this->getRequest();
        $actionName = $this->getControllerActionName($request);

        // redirect if its not a fac signup route, but they have no manageable facilities
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            switch ($actionName) {
                case 'add-first':
                case 'type':
                case 'selectionhandler':
                case 'addcentershift4':
                case 'synccentershift4': // sync button in signup
                case 'addsummary':
                case 'add': // edomico
                case 'addquickstore':
                case 'addselfstoragemanager':
                case 'syncselfstoragemanager': // sync button in signup
                case 'addsitelink':
                case 'addstoredge':
                case 'syncsitelink': // sync button in signup
                case 'syncstoredge': // sync button in signup
                case 'synceasystoragesolutions': // sync button in signup
                case 'addeasystoragesolutions':
                case 'add_facility':
                    break;
                default:
                    return $this->redirectToRoute('features_add_first');
            }
        }

        // bypass this for JS only routes and excel, append for all other routes
        switch ($actionName) {
            case 'add_facility':
            case 'update_facility':
            case 'addfacilityclosure':
            case 'removefacilityclosure':
            case 'synccentershift4':
            case 'syncissn':
            case 'syncselfstoragemanager':
            case 'syncsitelink':
            case 'syncstoredge':
            case 'synceasystoragesolutions':
            case 'export':
            case 'exportbidopps':
            case 'unitexport':
            case 'save_bid':
            case 'update_bid':
            case 'close_bid_modal':
            case 'check_site_verify':
            case 'resync':
            case 'download_bid_opportunities':
            case 'poll_bid_opps_report':
            case 'bid_opps_report':
                break;
            default:
                // In Symfony, we would typically handle this via Twig templates or response modification
                // This script injection will be handled in the template layer
                break;
        }

        $session = $this->getSession();

        if ($this->getRequest()->get('search_term')) {
            $session->set('searchTerm', $this->getRequest()->get('search_term'));
        }

        if ($this->getRequest()->get('clear_search_term')) {
            $session->set('searchTerm', null);
        }
        // Initialize StatsD client for metrics
        if (getenv('SF_ENV') !== 'local') {
            $this->statsClient = new StatsD(new DogStatsd());
        }
        
        return null;
    }

    /**
     * Redirect old routes to new format
     * Sample: /features/redirectoldroutes?fid=123&actionName=inventory
     * https://myfoot.sparefoot.com/features/redirectoldroutes
     * http://localhost:9019/features/redirectoldroutes.
     */
    #[Route('/features/redirectoldroutes', name: 'features_redirect_old_routes')]
    public function redirectoldroutesAction(Request $request): RedirectResponse
    {
        $fid = $request->get($request, 'fid');
        $actionName = $request->get($request, 'actionName');

        return $this->redirect('/facilities/'.$fid.'/'.$actionName);
    }

    /**
     * Set common view fields used across multiple facility actions
     * This method populates view data for FMS software features and banner settings.
     */
    protected function setCommonViewFields(): void
    {
        $account = $this->getLoggedUser()->getAccount();

        // Set FMS software capability flag
        $this->view->hasOnlineMoveInFmsSoftware = Account::accountHasFmsSupportingOnlineMoveins($account);

        // Set banner configuration flags
        $this->view->banner = [
            'showMoveInsBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_ONLINE_BANNER, []),
            'showCovidMsgBanner' => \Genesis_Service_Feature::isActive(Features::COVID_BANNER),
            'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($account),
        ];
        $this->view->covidBanner = $this->view->banner['showCovidMsgBanner'];
    }

    /**
     * Facility index action - redirects to appropriate facility page
     * Sample: /features or /features/index
     * https://myfoot.sparefoot.com/features
     * http://localhost:9019/features.
     */
    #[Route('/features', name: 'features_index')]
    #[Route('/features/index', name: 'features_index_explicit')]
    #[Route('/features/{action}', name: 'facility-features-action', defaults: ['action' => 'index'])]
    #[Route('/features/photos', name: 'features_photos', defaults: ['action' => 'index'])]
    #[Route('/features/units', name: 'features_units', defaults: ['action' => 'index'])]
    public function indexAction(): RedirectResponse
    {
        $session = $this->getSession();
        // Optional: Clear facility ID from session
        // $session->set('facilityId', null);

        // Set up an interstitial to display once per session
        if (\Genesis_Service_Feature::isActive('bidInterstitial')) {
            if (!$session->has('showBidInterstitial')) {
                $session->set('showBidInterstitial', true);
            } elseif ($session->get('showBidInterstitial')) {
                $session->set('showBidInterstitial', false);
            }
            $this->view->showBidInterstitial = $session->get('showBidInterstitial');
        }
        // Optional debug line: $this->view->showBidInterstitial = true;

        $facilities = $this->getLoggedUser()->getAccount()->getFacilities();

        if (count($facilities->toArray()) == 0) {
            return $this->redirectToRoute('features_add_first');
        } else {
            return $this->redirectToRoute('features_units');
        }
    }

    /**
     * Add first facility action
     * Sample: /features/add-first
     * https://myfoot.sparefoot.com/features/add-first
     * http://localhost:9019/features/add-first.
     */
    #[Route('/features/add-first', name: 'features_add_first')]
    public function addFirstAction(): RedirectResponse|Response
    {
        if ($this->getLoggedUser()->getAccount()->getNumFacilities()) {
            return $this->redirectToRoute('features_type');
        }

        // If no facilities exist, render the add first facility template
        return $this->render('facility/add-first.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Close bid modal action - AJAX endpoint
     * Sample: /features/closebidmodal (POST)
     * https://myfoot.sparefoot.com/features/closebidmodal
     * http://localhost:9019/features/closebidmodal.
     */
    #[Route('/features/closebidmodal', name: 'features_close_bid_modal')]
    public function closebidmodalAction(Request $request): JsonResponse
    {
        $actionId = $request->request->get('action_id');
        $currentPos = $request->request->get('current_pos');
        $suggestedPos = $request->request->get('suggested_pos');

        try {
            \Genesis_Service_ActionMeta::associateBid($actionId, $currentPos, $suggestedPos, 0);

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * List all facilities that logged user has access to.
     * https://myfoot.sparefoot.com/overview
     * http://localhost:9019/overview.
     */
    #[Route('/overview', name: 'facility_overview')]
    #[Route('/overview', name: 'account_overview')]
    #[Route('/overview', name: 'account-overview')]
    public function listAction(Request $request): Response
    {
        if (User::isFeatureActive(Features::OVERVIEW_SINGLE_PAGE)) {
            // START for single page
            $this->initViewForSinglePageApp();
            // END for single page
            $this->view->layoutTemplate = 'layout-singlepage';
            // For single page overview, render Ember.js application
            $this->view->scripts = [
                '../dist/ember/features/assets/vendor',
                '../dist/ember/features/assets/features',
            ];

            return $this->render('layout-singlepage.html.twig', [
                'view' => $this->view,
            ], new Response('', 200, ['Content-Type' => 'text/html']));
        } else {
            $session = $this->getSession();
            $userAccess = $this->getLoggedUser()->getUserAccess();

            // clear session data when they hit the list page
            $session->set('facIds', null);

            $searchTerm = $session->get('searchTerm');
            $this->view->searchTerm = $searchTerm;
            $this->view->loggedUser = $userAccess;

            $restriction = \Genesis_Db_Restriction::equal('published', 1);

            if ($searchTerm) {
                $restriction = \Genesis_Db_Restriction::and_(
                    $restriction,
                    \Genesis_Db_Restriction::or_(
                        \Genesis_Db_Restriction::like('title', '%'.$searchTerm.'%'),
                        \Genesis_Db_Restriction::like('companyCode', '%'.$searchTerm.'%')
                    )
                );
            } else {
                $page = 1;
                $limit = 20;

                $limitParam = preg_replace('[^0-9]', '', $request->get($request, 'limit'));
                if ($limitParam && $limitParam > 0) {
                    $limit = $limitParam;
                }
                $pageParam = preg_replace('[^0-9]', '', $request->get($request, 'page'));
                if ($pageParam > 0) {
                    $page = $pageParam;
                }

                // Get Number of Facilities (published)
                $facilityIds = $this->getLoggedUser()->getManageableFacilityIds(null, false);
                $totalItems = count($facilityIds);
                $offset = ($page - 1) * $limit;

                $restriction->setLimit(\Genesis_Db_Limit::limitOffset($limit, $offset));

                $this->view->page = $page;
                $this->view->limit = $limit;
                $this->view->totalItems = $totalItems;
            }
            $restriction->setOrder(\Genesis_Db_Order::asc('title'));

            $this->view->masterSwitch = null;

            $facilities = $this->_fetchFacilitiesData($restriction);
            usort($facilities, function ($a, $b) {
                return strcmp($a['title'], $b['title']);
            });
            $this->view->facilities = $facilities;
            $this->view->title = 'Facilities';
        }

        $this->view->accountId = $request->get($request, 'account_id') ?? null;

        $this->setCommonViewFields();

        return $this->render('facility/list.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/features/units
     * http://localhost:9019/features/units.
     */
    #[Route('/features/units', name: 'features_units')]
    public function inventoryAction(Request $request): RedirectResponse|Response
    {
        $session = $this->getSession();

        // go to the new page if FF is on
        $this->view->facility = $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));

        $integrationId = $facility->getCorporation()->getSourceId();

        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        if ($integrationId == \Genesis_Entity_Source::ID_MANUAL || $isFSS) {
            // In Symfony, we need to handle this redirect differently
            // For now, we'll redirect to a route that handles listings
            return $this->redirect('/features/listings?fid='.$facility->getId());
        }

        $this->view->integratedFields = $this->_getIntegratedFields();

        // Sitelink or Centershift 4 account then load grouped unit view
        if ($facility->canGroupUnits()) {
            // In Symfony, instead of forward(), we redirect to the grouped inventory route
            return $this->redirectToRoute('features_grouped_inventory');
        }

        $this->view->promoSync = (int) $facility->getCorporation()->getPullPromos();
        $this->view->sourceType = $integrationId;
        $this->view->inventory = $this->_fetchInventoryData();
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER); // added check

        if (!$this->view->inventory) {
            /* If no units are returned, check to see if there are truly no
             * units associated with that facility, or if the facility has units,
             * but they're publish = 0.
             */

            $units = \Genesis_Service_StorageSpace::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('facilityId', $session->get('facilityId')),
                    \Genesis_Db_Restriction::equal('publish', 0)
                )
            );
            if ($units->current()) {
                $this->view->unpublishedUnits = 1;
            } else {
                $this->view->unpublishedUnits = 0;
            }
        }

        $this->setCommonViewFields();

        $this->view->scripts = [
            'facility/global-functions',
            'facility/hide-facility-reason-modal',
            'inventory/units-shared',
            'facility/inventory',
        ];

        return $this->render('facility/inventory.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/features/groupedinventory
     * http://localhost:9019/features/groupedinventory.
     */
    #[Route('/features/groupedinventory', name: 'features_uid')]
    public function groupedinventoryAction(Request $request): Response
    {
        $session = $this->getSession();
        $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));
        $integrationId = $facility->getCorporation()->getSourceId();

        if (!isset($this->view->integratedFields)) {
            $this->view->integratedFields = $this->_getIntegratedFields();
        }

        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER);

        $this->view->facility = $facility;
        $this->view->sourceType = $integrationId;
        $this->view->promoSync = (int) $facility->getCorporation()->getPullPromos();
        $this->view->passedIds = null;

        $this->setCommonViewFields();

        // if there are specific unit ids then load individual for those
        $unitIds = $request->get('unitIds') ?? null;
        if ($unitIds) {
            $this->view->passedIds = $unitIds;
            $this->view->inventory = $this->_fetchInventoryData($unitIds);

            if (!$this->view->inventory) {
                $units = \Genesis_Service_StorageSpace::load(
                    \Genesis_Db_Restriction::and_(
                        \Genesis_Db_Restriction::equal('facilityId', $this->view->facility->getId()),
                        \Genesis_Db_Restriction::equal('publish', 0)
                    )
                );
                if ($units->current()) {
                    $this->view->unpublishedUnits = 1;
                } else {
                    $this->view->unpublishedUnits = 0;
                }
            }
        }

        // getGroupedUnits uses the Genesis_Dao_StorageSpace::selectGroupedByFacility() which uses the Facility's
        // Integration type to choose the final SQL query method. The Group is made here in the Facility level.
        $this->view->groupedUnits = $facility->getGroupedUnits(true);

        // map the groups and get some extra info
        /**
         * @var $gUnit Genesis_Entity_StorageSpace
         */
        foreach ($this->view->groupedUnits as $gUnit) {
            // we have stored unit id's and names in the id field, parse out.  Ex:
            $id = uniqid();

            if (
                $facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER
                && $gUnit->getGroupedNumAvailable() < 1
            ) {
                continue;
            }

            // instantiate to make sure all indices are there
            $gUnitDetails[$id]['source_id'] = $facility->getCorporation()->getSourceId();
            $gUnitDetails[$id]['driveUp'] = null;
            $gUnitDetails[$id]['vehicle'] = null;
            $gUnitDetails[$id]['door_w'] = null;
            $gUnitDetails[$id]['door_h'] = null;
            $gUnitDetails[$id]['climate'] = null;
            $gUnitDetails[$id]['humidity'] = null;
            $gUnitDetails[$id]['alarm'] = null;
            $gUnitDetails[$id]['power'] = null;
            $gUnitDetails[$id]['outdoorAccess'] = null;
            $gUnitDetails[$id]['doorType'] = null;
            $gUnitDetails[$id]['covered'] = null;

            $gUnitDetails[$id]['id'] = $gUnit->getId();
            $gUnitDetails[$id]['dimensions'] = $gUnit->stringDimensions(false);
            $gUnitDetails[$id]['export_dimensions'] = $gUnit->stringDimensions(false);
            $gUnitDetails[$id]['type'] = $gUnit->stringType();
            $gUnitDetails[$id]['type_num'] = $gUnit->getType();
            $gUnitDetails[$id]['amenities'] = $gUnit->stringAmenities();
            $gUnitDetails[$id]['floor'] = $gUnit->stringFloor();
            $gUnitDetails[$id]['rawFloor'] = $gUnit->getFloor();
            $gUnitDetails[$id]['sparefoot_price'] = '';
            if ($gUnit->getSparefootPrice() > 0) {
                $gUnitDetails[$id]['sparefoot_price'] = $gUnit->getSparefootPrice();
            }
            $gUnitDetails[$id]['list_price'] = $gUnit->getRegularPrice();
            $gUnitDetails[$id]['reservation_days'] = $gUnit->getReservationDays();
            $gUnitDetails[$id]['quantity'] = $gUnit->getQuantity();
            $gUnitDetails[$id]['approved'] = $gUnit->getApproved() ? true : false;
            $gUnitDetails[$id]['published'] = $gUnit->getPublish() ? true : false;

            // special stuff just for grouped units
            $gUnitDetails[$id]['groupIdsStr'] =
                is_array($gUnit->getGroupedUnitIds())
                ? implode(', ', $gUnit->getGroupedUnitIds())
                : '';

            if ($gUnitDetails[$id]['groupIdsStr']) {
                $groupUnits = \Genesis_Service_StorageSpace::load(
                    \Genesis_Db_Restriction::in('id', $gUnit->getGroupedUnitIds())
                    // ->setOrder(Genesis_Db_Order::asc('unitName'))
                )->toArray();

                // sort by unit name
                // this should be done above when loading the list from the db but
                // unit_name is currently a supplemental field and thus cannot be sorted by
                // TODO: add unit_name as a column to listing_avail_space_2
                usort($groupUnits, function (
                    \Genesis_Entity_StorageSpace $a,
                    \Genesis_Entity_StorageSpace $b,
                ) {
                    return strcmp($a->getUnitName(), $b->getUnitName());
                });

                $gUnitDetails[$id]['groupUnits'] = $groupUnits;
            }

            $gUnitDetails[$id]['unit_names'] =
                is_array($gUnit->getGroupedUnitNames())
                ? implode(', ', $gUnit->getGroupedUnitNames())
                : '';
            $gUnitDetails[$id]['classType'] =
                is_array($gUnit->getGroupedApiTypes())
                ? implode(', ', $gUnit->getGroupedApiTypes())
                : '';
            $gUnitDetails[$id]['numRentable'] = $gUnit->getGroupedNumAvailable();

            if ($gUnit->getActive() == 1) {
                $gUnitDetails[$id]['hidden'] = false; // active in frontends; won't actually appear unless publish=1
            } else {
                $gUnitDetails[$id]['hidden'] = true;
            }

            $gUnitDetails[$id]['deposit'] = $gUnit->getDeposit();
            $gUnitDetails[$id]['unit_w'] = $gUnit->getWidth();
            $gUnitDetails[$id]['unit_l'] = $gUnit->getLength();
            $gUnitDetails[$id]['unit_h'] = $gUnit->getHeight();

            $gUnitDetails[$id]['special'] = $gUnit->getSpecialString();
            $gUnitDetails[$id]['desc'] = $gUnit->getDescription();

            // Supp unit data
            $gUnitDetails[$id]['stacked'] = $gUnit->getSkybox();
            $gUnitDetails[$id]['premium'] = $gUnit->getPremiumUnit();
            $gUnitDetails[$id]['heated'] = $gUnit->getHeatedOnly();
            $gUnitDetails[$id]['aircooled'] = $gUnit->getAirCooledOnly();
            $gUnitDetails[$id]['ada'] = $gUnit->getAdaAccessible();
            $gUnitDetails[$id]['unitlights'] = $gUnit->getUnitLights();
            $gUnitDetails[$id]['twentyfourhouraccess'] = $gUnit->getTwentyFourHourAccess();

            $gUnitDetails[$id]['shelvesinunit'] = $gUnit->getShelvesInUnit();
            $gUnitDetails[$id]['basement'] = $gUnit->getBasement();
            $gUnitDetails[$id]['parkingwarehouse'] = $gUnit->getParkingWarehouse();
            $gUnitDetails[$id]['pullthru'] = $gUnit->getPullThrough();

            $gUnitDetails[$id]['sitelinkunit'] = 'N';
            if ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SITELINK) {
                $gUnitDetails[$id]['sitelinkunit'] = 'Y';
            }

            // TODO: enable this once we also update UI to match
            //            // special handling for integrations that give us a record for each individual unit, but only the rentable ones
            //            if ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
            //                // what we think is quantity is actually num rentable
            //                $gUnitDetails[$id]['numRentable'] = $gUnitDetails[$id]['quantity'];
            //                // and we dont know real quantity
            //                $gUnitDetails[$id]['quantity'] = null;
            //            }

            // this section does not apply to parking or outdoor
            if (
                $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                && $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_OUTDOOR
            ) {
                $gUnitDetails[$id]['door_w'] = $gUnit->getDoorWidth();
                $gUnitDetails[$id]['door_h'] = $gUnit->getDoorHeight();

                if ($gUnit->getClimateControlled() == 1) {
                    $gUnitDetails[$id]['climate'] = true;
                } else {
                    $gUnitDetails[$id]['climate'] = false;
                }
                if ($gUnit->getHumidityControlled() == 1) {
                    $gUnitDetails[$id]['humidity'] = true;
                } else {
                    $gUnitDetails[$id]['humidity'] = false;
                }
                if ($gUnit->getAlarm() == 1) {
                    $gUnitDetails[$id]['alarm'] = true;
                } else {
                    $gUnitDetails[$id]['alarm'] = false;
                }
                if ($gUnit->getPower() == 1) {
                    $gUnitDetails[$id]['power'] = true;
                } else {
                    $gUnitDetails[$id]['power'] = false;
                }
                if ($gUnit->getOutdoorAccess() == 1) {
                    $gUnitDetails[$id]['outdoorAccess'] = true;
                } else {
                    $gUnitDetails[$id]['outdoorAccess'] = false;
                }
                if ($gUnit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                    $gUnitDetails[$id]['doorType'] = 'roll_up';
                } elseif ($gUnit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_SWING) {
                    $gUnitDetails[$id]['doorType'] = 'swing';
                } else {
                    $gUnitDetails[$id]['doorType'] = 'none';
                }
            } elseif ($gUnit->getType() == \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE) {
                if ($gUnit->getPower() == 1) {
                    $gUnitDetails[$id]['power'] = true;
                } else {
                    $gUnitDetails[$id]['power'] = false;
                }
            }

            // this section does not apply to parking, wine or locker
            if (
                $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                && $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                && $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE
            ) {
                if ($gUnit->getDriveUp() == 1) {
                    $gUnitDetails[$id]['driveUp'] = true;
                } else {
                    $gUnitDetails[$id]['driveUp'] = false;
                }
                if ($gUnit->getVehicle() == 1) {
                    $gUnitDetails[$id]['vehicle'] = true;
                } else {
                    $gUnitDetails[$id]['vehicle'] = false;
                }
            }

            // covered does not apply to wine or locker
            if (
                $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                && $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE
                && $gUnit->getType() != \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT
            ) {
                $gUnitDetails[$id]['covered'] = $gUnit->getCovered();
            }
        }

        if (isset($gUnitDetails)) {
            $this->view->groupedUnits = $gUnitDetails;
        }

        $this->view->scripts = [
            'facility/global-functions',
            'facility/hide-facility-reason-modal',
            'inventory/units-shared',
            'facility/inventory',
        ];

        return $this->render('facility/groupedinventory.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/features/unitexport
     * http://localhost:9019/features/unitexport.
     */
    #[Route('/features/unitexport', name: 'features_unit_export')]
    public function unitexportAction(Request $request): Response
    {
        $facility = \Genesis_Service_Facility::loadById($request->get('fid'));
        if (!$facility) {
            return new Response('', 404);
        }

        $out = '';

        // add more info for sitelink
        if ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SITELINK) {
            $headers = ['Unit Name', 'SiteLink Type', 'Active/Hidden', 'Approved?', 'Size', 'Sparefoot Type', 'Amenities', 'Floor', 'SpareFoot Price', 'List Price', 'Unit Promo?'];
        } else {
            $headers = ['Active/Hidden', 'Approved?', 'Size', 'Type', 'Amenities', 'Floor', 'SpareFoot Price', 'List Price', 'Unit Promo?', 'Quantity'];
        }

        $out .= '"'.implode('","', $headers).'"'."\n";

        $facilityName = $facility->getTitle();
        $inventory = $this->_fetchInventoryData();

        if (!$inventory) {
            $filename = $this->getLoggedUser()->getAccount()->getName().' '.date('Y-m-d').' unitExport.csv';

            return new Response($out, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            ]);
        }

        // add more info for sitelink
        if ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SITELINK) {
            foreach ($inventory as $unit) {
                $unitArr = [
                    $unit['unitName'],
                    $unit['classType'],
                    ($unit['hidden'] == 1) ? 'Hidden' : 'Active',
                    $unit['approved'] ? 'Yes' : 'No',
                    $unit['export_dimensions'],
                    $unit['type'],
                    $unit['amenities'],
                    $unit['floor'],
                    $unit['sparefoot_price'],
                    $unit['list_price'],
                    $unit['special'] ? 'Yes' : 'No',
                ];
                $out .= '"'.implode('","', $unitArr).'"'."\n";
            }
        } else {
            foreach ($inventory as $unit) {
                $unitArr = [
                    ($unit['hidden'] == 1) ? 'Hidden' : 'Active',
                    $unit['approved'] ? 'Yes' : 'No',
                    $unit['export_dimensions'],
                    $unit['type'],
                    $unit['amenities'],
                    $unit['floor'],
                    $unit['sparefoot_price'],
                    $unit['list_price'],
                    $unit['special'] ? 'Yes' : 'No',
                    $unit['quantity'],
                ];
                $out .= '"'.implode('","', $unitArr).'"'."\n";
            }
        }

        $filename = $this->getLoggedUser()->getAccount()->getName().' '.date('Y-m-d').' unitExport.csv';

        return new Response($out, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);
    }

    private function getUnitsRestriction(
        $facilityIdColumnKey,
        \Genesis_Entity_Facility $facility,
        $unitIdColumnKey,
        $unitIds,
    ) {
        if ($unitIds) {
            $restriction = \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal($facilityIdColumnKey, $facility->getId()),
                \Genesis_Db_Restriction::in($unitIdColumnKey, explode(',', $unitIds))
            );
        } else {
            $restriction = \Genesis_Db_Restriction::equal($facilityIdColumnKey, $facility->getId());
        }

        return $restriction;
    }

    /**
     * Fetch and organize all of the data to populate the inventory screen.
     *
     * @param array $unitIds
     *
     * @return array
     *
     * @throws Exception
     */
    private function _fetchInventoryData($unitIds = null)
    {
        $facilityId = $this->getSession()->get('facilityId');

        /** @var \Genesis_Entity_Facility $facility */
        $facility = \Genesis_Service_Facility::loadById($facilityId);

        switch ($facility->getCorporation()->getSourceId()) {
            case \Genesis_Entity_Source::ID_SITELINK:
                $units = \Genesis_Service_SitelinkFullUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'unitId',
                        $unitIds
                    )
                );
                break;
            case \Genesis_Entity_Source::ID_CENTERSHIFT4:
            case \Genesis_Entity_Source::ID_CENTERSHIFT4_LEADS360:
                $units = \Genesis_Service_Centershift4Unit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
                $units = \Genesis_Service_SelfStorageManagerUnit::load(
                    $this->getUnitsRestriction(
                        'listingAvailId',
                        $facility,
                        'listingAvailSpaceId',
                        $unitIds
                    )
                );
                break;
            case \Genesis_Entity_Source::ID_DOORSWAP:
                $units = \Genesis_Service_DoorswapUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case \Genesis_Entity_Source::ID_STOREDGE:
                $units = \Genesis_Service_StoredgeUnit::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;
            case \Genesis_Entity_Source::ID_ESS:
                $units = \Genesis_Service_StorageSpace::load(
                    $this->getUnitsRestriction(
                        'facilityId',
                        $facility,
                        'id',
                        $unitIds
                    )
                );
                break;

            default:
                $units = $facility->getUnits();
        }

        if ($units) {
            foreach ($units as $unit) {
                $id = $unit->getId();

                // some instantiation for ones that don't always get set
                $unitDetails[$id]['driveUp'] = null;
                $unitDetails[$id]['vehicle'] = null;
                $unitDetails[$id]['door_w'] = null;
                $unitDetails[$id]['door_h'] = null;
                $unitDetails[$id]['climate'] = null;
                $unitDetails[$id]['humidity'] = null;
                $unitDetails[$id]['alarm'] = null;
                $unitDetails[$id]['power'] = null;
                $unitDetails[$id]['outdoorAccess'] = null;
                $unitDetails[$id]['doorType'] = null;
                $unitDetails[$id]['covered'] = null;

                $unitDetails[$id]['id'] = $unit->getId();
                $unitDetails[$id]['dimensions'] = $unit->stringDimensions(false);
                $unitDetails[$id]['export_dimensions'] = $unit->stringDimensions(false);
                $unitDetails[$id]['type'] = $unit->stringType();
                $unitDetails[$id]['type_num'] = $unit->getType();
                $unitDetails[$id]['amenities'] = $unit->stringAmenities();
                $unitDetails[$id]['floor'] = $unit->stringFloor();
                $unitDetails[$id]['rawFloor'] = $unit->getFloor();
                $unitDetails[$id]['sparefoot_price'] = '';
                if ($unit->getSparefootPrice() > 0) {
                    $unitDetails[$id]['sparefoot_price'] = $unit->getSparefootPrice();
                }
                $unitDetails[$id]['list_price'] = $unit->getRegularPrice();
                $unitDetails[$id]['quantity'] = $unit->getQuantity();
                $unitDetails[$id]['approved'] = $unit->getApproved() ? true : false;
                $unitDetails[$id]['published'] = $unit->getPublish() ? true : false;

                if ($unit->getActive() == 1) {
                    $unitDetails[$id]['hidden'] = false; // active=1 then it's shown on the site
                } else {
                    $unitDetails[$id]['hidden'] = true;
                }

                $unitDetails[$id]['deposit'] = $unit->getDeposit();
                $unitDetails[$id]['unit_w'] = $unit->getWidth();
                $unitDetails[$id]['unit_l'] = $unit->getLength();
                $unitDetails[$id]['unit_h'] = $unit->getHeight();

                $unitDetails[$id]['qty'] = $unit->getQuantity();
                $unitDetails[$id]['special'] = $unit->getSpecialString();
                $unitDetails[$id]['desc'] = $unit->getDescription();

                // Supp unit data
                $unitDetails[$id]['stacked'] = $unit->getSkybox();
                $unitDetails[$id]['premium'] = $unit->getPremiumUnit();
                $unitDetails[$id]['heated'] = $unit->getHeatedOnly();
                $unitDetails[$id]['aircooled'] = $unit->getAirCooledOnly();
                $unitDetails[$id]['ada'] = $unit->getAdaAccessible();
                $unitDetails[$id]['unitlights'] = $unit->getUnitLights();
                $unitDetails[$id]['twentyfourhouraccess'] = $unit->getTwentyFourHourAccess();

                $unitDetails[$id]['shelvesinunit'] = $unit->getShelvesInUnit();
                $unitDetails[$id]['basement'] = $unit->getBasement();
                $unitDetails[$id]['parkingwarehouse'] = $unit->getParkingWarehouse();
                $unitDetails[$id]['pullthru'] = $unit->getPullThrough();

                // this section does not apply to parking or outdoor
                if (
                    $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                    && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_OUTDOOR
                ) {
                    $unitDetails[$id]['door_w'] = $unit->getDoorWidth();
                    $unitDetails[$id]['door_h'] = $unit->getDoorHeight();

                    if ($unit->getClimateControlled() == 1) {
                        $unitDetails[$id]['climate'] = true;
                    } else {
                        $unitDetails[$id]['climate'] = false;
                    }
                    if ($unit->getHumidityControlled() == 1) {
                        $unitDetails[$id]['humidity'] = true;
                    } else {
                        $unitDetails[$id]['humidity'] = false;
                    }
                    if ($unit->getAlarm() == 1) {
                        $unitDetails[$id]['alarm'] = true;
                    } else {
                        $unitDetails[$id]['alarm'] = false;
                    }
                    if ($unit->getPower() == 1) {
                        $unitDetails[$id]['power'] = true;
                    } else {
                        $unitDetails[$id]['power'] = false;
                    }
                    if ($unit->getOutdoorAccess() == 1) {
                        $unitDetails[$id]['outdoorAccess'] = true;
                    } else {
                        $unitDetails[$id]['outdoorAccess'] = false;
                    }
                    if ($unit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                        $unitDetails[$id]['doorType'] = 'roll_up';
                    } elseif ($unit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_SWING) {
                        $unitDetails[$id]['doorType'] = 'swing';
                    } else {
                        $unitDetails[$id]['doorType'] = 'none';
                    }
                } elseif ($unit->getType() == \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE) {
                    if ($unit->getPower() == 1) {
                        $unitDetails[$id]['power'] = true;
                    } else {
                        $unitDetails[$id]['power'] = false;
                    }
                }

                // this section does not apply to parking, wine or locker
                if (
                    $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                    && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                    && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE
                ) {
                    if ($unit->getDriveUp() == 1) {
                        $unitDetails[$id]['driveUp'] = true;
                    } else {
                        $unitDetails[$id]['driveUp'] = false;
                    }

                    if ($unit->getVehicleStorageOnly()) {
                        $unitDetails[$id]['vehicle'] = 'only';
                    } elseif ($unit->getVehicle() == 1) {
                        $unitDetails[$id]['vehicle'] = true;
                    } else {
                        $unitDetails[$id]['vehicle'] = false;
                    }
                }

                // covered does not apply to wine or locker
                if (
                    $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                    && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE
                    && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT
                ) {
                    $unitDetails[$id]['covered'] = $unit->getCovered();
                }

                $unitDetails[$id]['unitName'] = $unit->getUnitName();

                if ($unit instanceof \Genesis_Entity_SitelinkFullUnit) {
                    $unitDetails[$id]['classType'] = $unit->getSitelinkType();
                }
                if ($unit instanceof \Genesis_Entity_Centershift4Unit) {
                    $unitDetails[$id]['classType'] = $unit->getClassDesc();
                }
                if ($unit instanceof \Genesis_Entity_SelfStorageManagerUnit) {
                    $unitDetails[$id]['classType'] = $unit->getUnitTypeCode();
                }
                if ($unit instanceof \Genesis_Entity_ExtraspaceUnit) {
                    $unitDetails[$id]['unitType'] = $unit->getUnitTypeDisplay();
                }
            }

            if (isset($unitDetails)) {
                return $unitDetails;
            }
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/export
     * http://localhost:9019/facility/export.
     */
    #[Route('/facility/export', name: 'facility_export')]
    public function exportAction(Request $request): Response
    {
        $restriction = \Genesis_Db_Restriction::equal('published', 1);
        $restriction->setOrder(\Genesis_Db_Order::asc('title'));
        $facilities = $this->_fetchFacilitiesData($restriction);

        $csvContent = '"Facility Name","Facility Company Code","Active or Hidden","Bid","Reservations","Average Cost Per Reservation","Total Cost","Source","SpareFoot URL"'."\n";

        foreach ($facilities as $facility) {
            if (!$facility['published']) {
                continue;
            }

            $line = [
                $facility['title'],
                $facility['code'],
                $facility['hidden'] ? 'Hidden' : 'Active',
                $facility['bid_string'],
                $facility['num_reservations'],
                '$'.number_format($facility['cost_per_reservation'], 2),
                '$'.number_format($facility['total_cost'], 2),
                $facility['source_name'],
                $facility['url'],
            ];

            $csvContent .= '"'.implode('","', $line).'"'."\n";
        }

        $filename = $this->getLoggedUser()->getAccount()->getName().'-'.date('Y-m-d').'.csv';

        return new Response($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);
    }

    private function _saveBid($facilityId, ?Request $request = null)
    {
        $facility = \Genesis_Service_Facility::loadById($facilityId);

        // Gotta check this now because it gets changed in the else if it's not
        $isBidFlat = $facility->isBidFlat();

        $bidAmount = $request ? $request->get('bid_amount', $facility->getMinBid()) : $facility->getMinBid();
        $this->view->bidAmount = $bidAmount;
        $this->view->facility = $facility;
        $this->view->chart = new SearchPositionLine('chart', $facility, $this->getBeginDate(), $this->getEndDate());

        try {
            $facility->setEffectiveBidAmount($bidAmount);
            \Genesis_Service_Facility::save($facility, $this->getLoggedUser());
            $this->view->bidAmount = $facility->getEffectiveBidAmount();

            if (!$isBidFlat) {
                // Note: forward() needs to be replaced with redirect in calling method
                return ['forward' => 'bid'];
            }
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }

        return ['success' => true];
    }

    /**
     * https://myfoot.sparefoot.com/facility/updatebid
     * http://localhost:9019/facility/updatebid.
     */
    #[Route('/facility/updatebid', name: 'facility_update_bid')]
    public function updatebidAction(Request $request): Response
    {
        if (!CsrfUtil::validateToken(self::RANK_BID_CSRF_TOKEN, $request->get('csrf_token'))) {
            return new Response(json_encode(['Error' => 'Request structure is invalid. Refresh the page and try again.']), 400, [
                'Content-Type' => 'application/json',
            ]);
        }

        $result = $this->_updateBid($request->request->all());

        return new Response($result, 200, [
            'Content-Type' => 'application/json',
        ]);
    }

    private function _updateBid($params = [], ?Request $request = null)
    {
        try {
            /* @var $facility \Genesis_Entity_Facility */
            $facility = \Genesis_Service_Facility::loadById($params['fid']);

            $bidAmount = $request ? $request->get('bid_amount', $facility->getMinBid()) : ($params['bid_amount'] ?? $facility->getMinBid());
            $facility->setEffectiveBidAmount($bidAmount);
            $facility->validateBid($this->getLoggedUser());

            $this->view->bidAmount = $facility->getEffectiveBidAmount();
            $this->view->facility = $facility;
            $this->view->chart = new SearchPositionLine('chart', $facility, $this->getBeginDate(), $this->getEndDate());

            $response = [
                'bidSet' => [],
                'unitAvgs' => $facility->getAvgUnitPrices(),
            ];

            $cityLocation = $facility->getLocation()->getCity().', '.$facility->getLocation()->getState();
            $queries = [
                new SearchRankQuery('city', 'city', $cityLocation),
                new SearchRankQuery('zip', 'zip', $facility->getLocation()->getZip()),
            ];

            if (isset($params['custom_zip']) && strlen($params['custom_zip'])) {
                $queries[] = new SearchRankQuery('customZip', 'zip', $params['custom_zip']);
            }

            if (isset($params['custom_city']) && strlen($params['custom_city'])) {
                $queries[] = new SearchRankQuery('customCity', 'city', $params['custom_city']);
            }

            $searchClient = new SearchClient('bid-rank');
            $ranks = $searchClient->getSearchRanks($facility, $bidAmount, $queries);

            $response['bidSet']['city'] = [
                'location' => $facility->getLocation()->getCity().', '.$facility->getLocation()->getState(),
                'rank' => $ranks['city'],
            ];

            $response['bidSet']['zip'] = [
                'location' => $facility->getLocation()->getZip(),
                'rank' => $ranks['zip'],
            ];

            if (isset($params['custom_zip']) && strlen($params['custom_zip'])) {
                $customZip = $request ? $request->get('custom_zip') : $params['custom_zip'];
                $response['bidSet']['customZip'] = [
                    'location' => $customZip,
                    'rank' => $ranks['customZip'],
                ];
            }

            if (isset($params['custom_city']) && strlen($params['custom_city'])) {
                $customCity = $request ? $request->get('custom_city') : $params['custom_city'];
                $response['bidSet']['customCity'] = [
                    'location' => $customCity,
                    'rank' => $ranks['customCity'],
                ];
            }

            return json_encode($response);
        } catch (\Exception $e) {
            return json_encode(['Error' => $e->getMessage()]);
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/bidopportunities
     * http://localhost:9019/facility/bidopportunities.
     */
    #[Route('/facility/bidopportunities', name: 'facility_bid_opportunities')]
    public function bidOpportunitiesAction(): Response
    {
        return $this->redirectToRoute('features_bid');
    }

    /**
     * https://myfoot.sparefoot.com/features/bid
     * http://localhost:9019/features/bid.
     */
    #[Route('/features/bid', name: 'features_bid')]
    public function bidAction(Request $request): Response
    {
        $fid = $request->get('fid');

        /*
        $isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER);
        if($isBidOptimizerActive){
            return $this->redirectToRoute('features_bid_optimizer');
        }
        */

        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(
            \Genesis_Db_Restriction::equal('published', 1)->setOrder(\Genesis_Db_Order::asc('title'))
        );

        if ($fid) {
            $facility = \Genesis_Service_Facility::loadById($fid);
        }

        if (!$facility && $this->getSession()->get('facilityId') && $this->getSession()->get('facilityId') !== 'all') {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        }

        if (!$facility) {
            $facility = $this->view->facilities[0];
        }

        if (!$facility) {
            return $this->redirectToRoute('dashboard');
        }

        $this->view->update_bid_csrf_token = CsrfUtil::getToken(self::UPDATE_BID_CSRF_TOKEN);
        $this->view->rank_bid_csrf_token = CsrfUtil::getToken(self::RANK_BID_CSRF_TOKEN);
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);
        $this->view->isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER); // added to get the bid flag

        if (!$this->getLoggedUser()->getUserAccess()->canAccessFacility($facility)) {
            throw new \Exception('Not allowed to edit that facility (#'.$facility->getId().')');
        }

        switch ($facility->getAccount()->getBidType()) {
            case \Genesis_Entity_Account::BID_TYPE_TIERED:
            case \Genesis_Entity_Account::BID_TYPE_FLAT:
            case \Genesis_Entity_Account::BID_TYPE_PERCENT:
                break;
            default:
                return $this->redirectToRoute('features', ['fid' => $facility->getId()]);
        }

        if (!$facility->getEffectiveBidAmount()) {
            return $this->redirectToRoute('facility_bid_setup');
        }

        if (!isset($facility) || !$facility) {
            foreach ($this->view->facilities as $selectedFacility) {
                $facility = $selectedFacility;
                break;
            }
        }

        if ($request->get('bid_amount')) {
            $bidAmount = $request->get('bid_amount', $facility->getMinBid());
            $this->view->bidAmount = $bidAmount;

            try {
                $facility->setEffectiveBidAmount($bidAmount);
                \Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                $this->view->alert = 'Changes saved. Please allow 15 minutes for the changes to take effect.';
                $this->view->alertClass = 'alert-success';
            } catch (\Exception $e) {
                $this->view->alert = '<strong>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        $this->view->facility = $facility;
        $this->view->bidType = $this->getLoggedUser()->getAccount()->getBidType();

        if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->isMyfootAdmin()) {
            $this->view->canChangeBid = true;
        } else {
            $this->view->canChangeBid = false;

            $acct = $this->getLoggedUser()->getAccount();
            $admins = $acct ? $acct->getAdmins()->toArray() : null;

            $adminEmails = [];
            foreach ($admins as $admin) {
                $adminEmails[] = "<a href=\"mailto:{$admin->getEmail()}\">{$admin->getEmail()}</a>";
            }

            $count = count($adminEmails);
            if ($count == 0) {
                $this->view->adminEmails = 'an admin';
            } elseif ($count == 1) {
                $this->view->adminEmails = $adminEmails[0];
            } elseif ($count == 2) {
                $this->view->adminEmails = $adminEmails[0].' or '.$adminEmails[1];
            } elseif ($count > 2) {
                $this->view->adminEmails = implode(', ', array_slice($adminEmails, 0, -1)).', or '.$adminEmails[$count - 1];
            }
        }

        if ($request->get('save_bid')) {
            $this->_saveBid($fid, $request);
        }

        $this->setCommonViewFields();

        $this->view->scripts = ['facility/bid'];
        $this->view->title = 'Bidding';

        $authToken = UserOauth::getToken();
        $this->view->sparefootSearchCityUrl = UrlUtil::getSparefootSearchUrl(['location' => $facility->getLocation()->getCity().', '.$facility->getLocation()->getState()]);
        $this->view->sparefootSearchZipUrl = UrlUtil::getSparefootSearchUrl(['location' => $facility->getLocation()->getZip()]);

        $clientApi = new ClientApiClient();
        $bidOppsResponses = $clientApi->getCityAndZipBidOpps($facility->getAccountId(), $facility->getId(), $authToken);
        $zipBidOppsResponse = $bidOppsResponses['zipBidOpps'];
        $cityBidOppsResponse = $bidOppsResponses['cityBidOpps'];

        if ($this->isValidClientApiResponse($zipBidOppsResponse)) {
            $this->view->facilityZipBidOpps = $zipBidOppsResponse;
        } else {
            if (strpos($zipBidOppsResponse['message'], 'Search service data unavailable') !== false) {
                $this->view->zipBidOppsErrorMessage = 'Facility not found in search results for this zip code.';
            } else {
                $this->view->zipBidOppsErrorMessage = 'Unable to get bid opportunities for this zip code. Please try again momentarily.';
            }
        }

        if ($this->isValidClientApiResponse($cityBidOppsResponse)) {
            $this->view->facilityCityBidOpps = $cityBidOppsResponse;
        } else {
            if (strpos($cityBidOppsResponse['message'], 'Search service data unavailable') !== false) {
                $this->view->cityBidOppsErrorMessage = 'Facility not found in search results for this city.';
            } else {
                $this->view->cityBidOppsErrorMessage = 'Unable to get bid opportunities for this city. Please try again momentarily.';
            }
        }

        $this->view->currentBid = number_format($facility->getEffectiveBidAmount(), 2);
        $this->view->minBid = $facility->getMinBid();
        $this->view->bidDelta = $this->view->currentBid - $this->view->minBid;

        return $this->render('facility/bid.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/features/demandoptimizer
     * http://localhost:9019/features/demandoptimizer.
     */
    #[Route('/features/demandoptimizer', name: 'features_demand_optimizer')]
    public function bidOptimizerAction(): Response
    {
        $isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER);
        if (!$isBidOptimizerActive) {
            return $this->redirectToRoute('features_bid');
        }

        $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        if (!$facility) {
            return $this->redirectToRoute('dashboard');
        }
        $this->view->scripts = [
            'statement/bid-optimizer',
        ];
        $this->view->facility = $facility;
        $this->view->bidSummaryData = $this->getBidOptimizerData();

        return $this->render('facility/bid-optimizer.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * To get the bidoptimizer data based on the account id.
     */
    public function getBidOptimizerData(): array
    {
        $user_id = $this->getLoggedUser()->getId();
        $bidApi = new BidOptimizerClient();
        $params = [];
        $endpoint = '/bids/summary/'.$user_id;
        try {
            if ($this->getLoggedUser()->getMyfootRole() === \Genesis_Entity_UserAccess::ROLE_GOD) {
                $account_id = $this->getLoggedUser()->getAccount()->getAccountId();
                $params = [
                    'account_id' => $account_id,
                ];
            }
            $res = $bidApi->get($endpoint, $params);
            if (isset($res['facilities']) && is_array($res['facilities'])) {
                return $res['facilities'];
            }
        } catch (\Exception $e) {
            $this->view->errorMessages[] = $e->getMessage();
            $logger = new Logger('bid-optimizer-data');
            $logger->pushHandler(new StreamHandler('php://stdout', Logger::ERROR));
            $logger->error('Failed to load facilities Data.');
        }

        return [];
    }

    /**
     * https://myfoot.sparefoot.com/features/bidoptimizer-bidsexport
     * http://localhost:9019/features/bidoptimizer-bidsexport.
     */
    #[Route('/features/bidoptimizer-bidsexport', name: 'features_bid_optimizer_bids_export')]
    public function bidOptimizerBidsExportAction(Request $request): Response
    {
        $searchType = filter_var($request->get('searchType'), FILTER_SANITIZE_SPECIAL_CHARS);

        $bidOptimizerClient = new BidOptimizerClient();
        $userId = $this->getLoggedUser()->getId();
        $params = ['searchType' => $searchType];
        if ($this->getLoggedUser()->getMyfootRole() === \Genesis_Entity_UserAccess::ROLE_GOD) {
            $params['accountId'] = $this->getLoggedUser()->getAccount()->getAccountId();
        }
        try {
            $response = $bidOptimizerClient->getBidsExport($userId, $params);

            return new Response(json_encode($response), 200, [
                'Content-Type' => 'application/json',
            ]);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
    }

    /**
     * https://myfoot.sparefoot.com/features/bidoptimizer-bidupdate
     * http://localhost:9019/features/bidoptimizer-bidupdate.
     */
    #[Route('/features/bidoptimizer-bidupdate', name: 'features_bid_optimizer_bid_update')]
    public function bidOptimizerBidUpdateAction(Request $request): Response
    {
        $bidOptClient = new BidOptimizerClient();
        $bids = [];

        $isJsonRequest = $request->headers->get('Content-Type') && strpos($request->headers->get('Content-Type'), 'application/json') !== false;
        if ($isJsonRequest) {
            $jsonData = $request->getContent();
            $bids = json_decode($jsonData, true);
        } else {
            $uploadedFile = $request->files->get('bids');
            if (!$uploadedFile || !$uploadedFile->isValid()) {
                throw new ApiException(ApiException::BAD_REQUEST, 'No valid file uploaded. Please upload a valid .csv file.');
            }

            $bidCsvPath = $uploadedFile->getPathname();
            $csvData = array_map('str_getcsv', file($bidCsvPath));

            $header = array_map('trim', $csvData[0]);
            unset($csvData[0]);

            $errored = 0;
            $errors = [];

            foreach ($csvData as $row) {
                $rowData = array_combine($header, $row);
                $facilityId = $this->sanitizeId($rowData['facility_id']);
                $maxBid = $this->sanitizeMaxBid($rowData['max_bid']);

                if (empty($facilityId)) {
                    ++$errored;
                    $errors[] = "Invalid facility_id: {$rowData['facility_id']} is not a valid.";
                }

                if (empty($maxBid)) {
                    ++$errored;
                    $errors[] = "Invalid max_bid: {$rowData['max_bid']} is not a valid value.";
                }

                if ($errored > 0) {
                    $response = [
                        'errored' => $errored,
                        'error_count' => $errored,
                        'errors' => $errors,
                    ];

                    return new Response(json_encode($response), 400, [
                        'Content-Type' => 'application/json',
                    ]);
                }

                $bids[] = [
                    'facility_id' => $facilityId,
                    'max_bid' => $maxBid,
                    'bid_strategy' => 'max_bid',
                ];
            }
        }

        $requestBody = [
            'user_id' => $this->getLoggedUser()->getId(),
            'bids' => $bids,
        ];

        try {
            $resp = $bidOptClient->postBidUpdate($requestBody);

            return new Response(json_encode($resp), 200, [
                'Content-Type' => 'application/json',
            ]);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
    }

    public static function sanitizeId(string $id): ?int
    {
        $sanitized = preg_replace('/[^0-9]/', '', $id);
        if (empty($sanitized) || !ctype_digit($sanitized)) {
            return null;
        }

        return (int) $sanitized;
    }

    public static function sanitizeMaxBid(string $input, $nearest = 0.05): ?float
    {
        $number = filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);

        if (empty($number) || !is_numeric($number)) {
            return null;
        }
        $number = (float) $number;
        $rounded = round($number / $nearest) * $nearest;

        return (float) number_format($rounded, 2, '.', '');
    }

    /**
     * https://myfoot.sparefoot.com/facility/bulkbid
     * http://localhost:9019/facility/bulkbid.
     */
    #[Route('/facility/bulkbid', name: 'facility_bulk_bid')]
    public function bulkbidAction(Request $request): Response
    {
        /*
        $isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER);
        if($isBidOptimizerActive){
            $this->redirect($this->view->url(['action'=>'bidoptimizer'], 'features'));
        }
        */

        $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        if (!$facility) {
            $this->redirect($this->view->url([], 'dashboard'));
        }
        $this->view->facility = $facility;
        $this->view->loggedUser = $this->getLoggedUser();
        $this->view->title = 'Bulk Bid Update';
        $this->view->isBidOptimizerActive = User::isFeatureActive(Features::BID_OPTIMIZER); // added bidoptimizer check

        if ($request->isMethod('POST')) {
            $uploadedFile = $request->files->get('bids');
            $bidCsvPath = $uploadedFile->getPathname();
            $logger = new Logger('bulk-bid-update');
            $logger->pushHandler(new StreamHandler('php://stdout', Logger::INFO));
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
            if (!$facility) {
                return $this->redirectToRoute('dashboard');
            }
            $accountId = $facility->getAccountId();
            $bidUpdater = new \Genesis_Service_BulkBidUpdate($logger, $this->getLoggedUser(), $accountId, $bidCsvPath);
            try {
                $result = $bidUpdater->run();
                if ($result === false) {
                    $this->view->errorMessages[] = "Unable to update bid modifiers. Troubleshooting available via our <a href='https://support.sparefoot.com/hc/en-us/articles/************-Bulk-Bidding' target=”_blank”> Help Center ></a>";
                } else {
                    $this->view->successMessages[] = 'Bulk bid modifiers have been updated. Please allow 15 minutes for the updates to take effect.';
                }
            } catch (\Exception $e) {
                $this->view->errorMessages[] = "Unable to update bid modifiers. Troubleshooting available via our <a href='https://support.sparefoot.com/hc/en-us/articles/************-Bulk-Bidding' target = ”_blank”> Help Center ></a>.";
                $this->view->errorMessages[] = 'Reason: '.$e->getMessage();
            }
        }
        $this->view->scripts = ['facility/bid'];

        return $this->render('facility/bulk-bid.html.twig', [
            'view' => $this->view,
        ]);
    }

    public function isValidClientApiResponse($response)
    {
        if ($response['exception']) {
            return false;
        }

        if (strpos($response['message'], 'Search service data unavailable') !== false) {
            return false;
        }

        return true;
    }

    /**
     * https://myfoot.sparefoot.com/facility/downloadbidopportunities
     * http://localhost:9019/facility/downloadbidopportunities.
     */
    #[Route('/facility/downloadbidopportunities', name: 'facility_download_bid_opportunities')]
    public function downloadbidopportunitiesAction(Request $request): Response
    {
        $accountId = $this->getLoggedUser()->getAccount()->getId();
        $oppType = $request->get('oppType');
        $authToken = UserOauth::getToken();
        if (!$accountId) {
            throw new \Exception('No account found for logged in user');
        }
        $clientApi = new ClientApiClient();
        $response = $clientApi->getBidsReportByAccount($accountId, $authToken, $oppType);

        return new Response(json_encode($response), 200, [
            'Content-Type' => 'application/json',
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/pollbidoppsreport
     * http://localhost:9019/facility/pollbidoppsreport.
     */
    #[Route('/facility/pollbidoppsreport', name: 'facility_poll_bid_opps_report')]
    public function pollbidoppsreportAction(Request $request): Response
    {
        $jobId = $request->get('id');
        $authToken = UserOauth::getToken();
        $clientApi = new ClientApiClient();
        $response = $clientApi->getJobById($jobId, $authToken);

        return new Response(json_encode($response), 200, [
            'Content-Type' => 'application/json',
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/bidoppsreport
     * http://localhost:9019/facility/bidoppsreport.
     */
    #[Route('/facility/bidoppsreport', name: 'facility_bid_opps_report')]
    public function bidoppsreportAction(Request $request): Response
    {
        $jobId = $request->get('id');
        $authToken = UserOauth::getToken();
        $clientApi = new ClientApiClient();

        $csvContent = $clientApi->getJobResultById($jobId, $authToken);

        return new Response($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=bid_opportunities_report.csv',
            'Cache-Control' => 'max-age=0',
        ]);
    }

    /**
     * Prepare the bid opps we will show to the end user
     * - remove > 500
     * - remove duplicates
     * - round up to next 0.05/5 increment steps.
     */
    private function _prepareBidOpportunities(array &$bidOpportunities)
    {
        $uniqueBidCosts = [];
        $currBid = $bidOpportunities['bid'];
        $currRank = $bidOpportunities['rank'];

        $maxBidAmount = \Genesis_Service_BidMgmt::getMaxBidAmountByType(
            $bidOpportunities[\Genesis_Service_BidMgmt::FIELD_BID_TYPE]
        );

        // Get the amount to bump the bid to next increment.
        $bidAdder = ($bidOpportunities[\Genesis_Service_BidMgmt::FIELD_BID_TYPE] === \Genesis_Entity_Account::BID_TYPE_PERCENT)
            ? \Genesis_Service_BidMgmt::BID_ROUND_PERCENT
            : \Genesis_Service_BidMgmt::BID_ROUND;

        $currBidRound = \Genesis_Service_BidMgmt::roundBidOppAmount($currBid, $bidOpportunities[\Genesis_Service_BidMgmt::FIELD_BID_TYPE]);

        foreach ($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION] as $rankPosition => $bidCost) {
            // Round up the bid to the next 0.05/5 increment, so they will get that position.
            $bidCostRound = \Genesis_Service_BidMgmt::roundBidOppAmount($bidCost, $bidOpportunities[\Genesis_Service_BidMgmt::FIELD_BID_TYPE]);

            // Go up a increment if needed.
            if (abs($bidCostRound - $bidCost) <= 0.00001) {
                $bidCostRound += $bidAdder;
            }

            // Dont show if Ranking is higher number.
            if ($rankPosition >= $currRank) {
                unset($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if bids are the same.
            elseif ($currBidRound == $bidCostRound) {
                unset($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if bid is empty, (should not happen)
            elseif (!$bidCostRound) {
                unset($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if over max bid.
            elseif ($bidCostRound > $maxBidAmount) {
                $bidOpportunities['max_bid_exceeded'] = \Genesis_Service_BidMgmt::MAX_BIDDABLE; // flag for seeing why
                unset($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            }
            // Dont show if we already have a bid of this value (no dupe bids)
            elseif (isset($uniqueBidCosts["$bidCostRound"])) {
                unset($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition]);
            } else {
                // Save new value if passed all checks.
                $bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$rankPosition] = $bidCostRound;

                // Save so we dont show duplicate bids.
                $uniqueBidCosts["$bidCostRound"] = true;
            }
        }

        if (count($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION]) === 0) {
            $bidOpportunities['no_opportunities'] = true; // flag for seeing why
        }
    }

    /**
     * Summarize three options to send the view, using these rules
     * if there are 3 or more in the opportunity array, we should have 3 returned
     * high rankers (1st, 2nd) and mid-packers with low scores will have only 1 or 2 options
     * first: best position
     * second: next position
     * third: the floor midpoint between first and currentPosition.
     */
    private function _summaryThreeOpportunities(array &$bidOpportunities)
    {
        $count = count($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION]);

        // If less than 4 results just display the three we have, as is.
        if ($count < 4) {
            return;
        }

        // For the third choice, get one in mid range for them.
        $keys = array_keys($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION]);
        $firstPosition = $keys[0];
        $secondPosition = $keys[1];
        $midPoint = (int) floor(count($bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION]) / 2) + 1;
        $midPosition = $keys[$midPoint];

        $bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION] = [
            $firstPosition => $bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$firstPosition],
            $secondPosition => $bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$secondPosition],
            $midPosition => $bidOpportunities[\Genesis_Service_BidMgmt::ARRAY_POSITION][$midPosition],
        ];
    }

    /**
     * https://myfoot.sparefoot.com/features/bid-custom
     * http://localhost:9019/features/bid-custom.
     */
    #[Route('/features/bid-custom', name: 'features_bid_custom')]
    public function bidCustomAction(Request $request): Response
    {
        $fid = $request->get('fid');

        return $this->redirectToRoute('features_bid', ['fid' => $fid]);
    }

    /**
     * https://myfoot.sparefoot.com/features/bidOptimizer-health
     * http://localhost:9019/features/bidOptimizer-health.
     */
    #[Route('/features/bidOptimizer-health', name: 'features_bid_optimizer_health')]
    public function bidOptimizerHealthAction(): Response
    {
        $bidOptClient = new BidOptimizerClient();
        $resp = $bidOptClient->getHealthStatus();

        return new Response(json_encode($resp), 200, [
            'Content-Type' => 'application/json',
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/bidsetup
     * http://localhost:9019/facility/bidsetup.
     */
    #[Route('/facility/bidsetup', name: 'facility_bid_setup')]
    public function bidsetupAction(Request $request): Response
    {
        $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        if (!$facility) {
            return $this->redirectToRoute('dashboard');
        }
        $this->view->facility = $facility;

        switch ($facility->getAccount()->getBidType()) {
            case \Genesis_Entity_Account::BID_TYPE_TIERED:
            case \Genesis_Entity_Account::BID_TYPE_FLAT:
                break;
            default:
                return $this->redirectToRoute('features', ['fid' => $facility->getId()]);
        }

        // if this facility has a bid amount setup already go there
        if ($facility->getEffectiveBidAmount()) {
            return $this->redirectToRoute('features_bid');
        }
        if ($request->get('save_bid')) {
            $this->_saveBid($facility->getId(), $request);
        }

        return $this->render('facility/bid-setup.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/savebid
     * http://localhost:9019/facility/savebid.
     */
    #[Route('/facility/savebid', name: 'facility_save_bid')]
    public function savebidAction(Request $request): Response
    {
        if ($request->isMethod('POST')) {
            if (!CsrfUtil::validateToken(self::UPDATE_BID_CSRF_TOKEN, $request->get('csrf_token'))) {
                throw new ApiException(ApiException::FORBIDDEN, 'Request structure is invalid. Refresh the page and try again.');
            }
            $result = $this->_saveBid($request->get('fid'), $request);

            return new Response(json_encode($result), 200, [
                'Content-Type' => 'application/json',
            ]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED, 'Method not implemented');
    }

    /**
     * https://myfoot.sparefoot.com/facility/details
     * http://localhost:9019/facility/details.
     */
    #[Route('/facility/details', name: 'facility_details')]
    public function detailsAction(Request $request): Response
    {
        $this->view->facility = $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        if (
            $facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_MANUAL
            || $isFSS
        ) {
            $this->redirect($this->view->url(['action' => 'units'], 'features').'?fid='.$facility->getId());
        }

        $integratedFields = $this->_getIntegratedFields();

        if (in_array('promotion', $integratedFields)) {
            unset($integratedFields['promotion']);
            $integratedFields[] = 'facility_promotions';
        }

        $this->view->integratedFields = $integratedFields;
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);

        unset($integratedFields);

        $officeHrs = \Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'office');
        if ($officeHrs) {
            $this->view->officeHours = $officeHrs;
        } else {
            $this->view->officeHours = new \Genesis_Entity_FacilityHours();
        }

        $accessHrs = \Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'access');
        if ($accessHrs) {
            $this->view->accessHours = $accessHrs;
        } else {
            $this->view->accessHours = new \Genesis_Entity_FacilityHours();
        }

        $this->view->facility = $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->units = $units = $facility->getActiveGroupedUnits(true); // this will get whichever is right.
        $this->view->loggedUser = $this->getLoggedUser();

        $admins = $this->getLoggedUser()->getAccount()->getAdmins()->toArray();
        $this->view->admins = $admins;

        // contacts other than admins
        // Note: getEmailAddresses() is inefficient, also used in bookings.  refactor? -EH 8/27/11
        $otherContacts = $facility->getEmailAddresses();

        // remove admins from list so we don't have to list twice
        foreach ($admins as $a) {
            unset($otherContacts[array_search($a->getEmail(), $otherContacts)]);
        }

        $this->view->contacts = $otherContacts;
        $this->view->corporation = $corporation = \Genesis_Service_Corporation::loadById($facility->getCorporationId());

        // get info on the current integration, in case we let them self report
        $this->view->source = \Genesis_Service_Source::loadById(
            $corporation->getSourceId()
        );
        // decide if the user gets to pick to self report a software
        switch ((int) $corporation->getSourceId()) {
            // get the self-reported software options
            case \Genesis_Entity_Source::ID_MANUAL:
                $this->view->sources = \Genesis_Service_Source::load(
                    \Genesis_Db_Restriction::equal('manualIntegration', 1)->setOrder(
                        \Genesis_Db_Order::asc('source')
                    )
                );

                break;
            default:
                $this->view->sources = false;
        }

        $this->view->erroredFields = [];
        $this->view->errors = [];

        if ($request->isMethod('POST')) {
            try {
                $integratedInputs = $this->_getIntegratedFields();
                $integratedParams = $integratedInputs;
                // validate zip
                if (!(strlen($request->get('facility_zip')) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $request->get('facility_zip'))) {
                    $this->view->erroredFields = ['facility_zip'];
                    throw new \Exception('Please enter a valid zip code.');
                }

                // create new location
                $location = \Genesis_Service_Location::loadByAddress(
                    $request->get('facility_address1'),
                    $request->get('facility_city'),
                    $request->get('facility_state'),
                    $request->get('facility_zip')
                );

                // does this location already exist
                if ($location) {
                    // yes, do nothing
                } else {
                    // call geocoder
                    $location = \Genesis_Service_Location::geoCodePhysicalAddress(
                        $request->get('facility_address1')
                            .' '.$request->get('facility_city')
                            .' '.$request->get('facility_state')
                            .' '.$request->get('facility_zip')
                    );
                    $location = \Genesis_Service_Location::save($location);
                }

                // if this facility integration allows editing reswindow
                if ($facility->canEditReservationWindow()) {
                    // if reservation window days is set
                    $facilityResWindowRuleChange = $unitResWindowRuleChange = false;

                    $setRules = [];
                    $resWindowDays = $request->get('res-win-days');

                    if ($resWindowDays[0] !== '' || $resWindowDays === '') { // if isset and is blank, rules are set to blank
                        $facilityResWindowRuleChange = true;
                        if ($facility->getSourceId() == \Genesis_Entity_Source::ID_MANUAL) { // manual
                            $setRules = (empty($resWindowDays)) ? [] : ['0' => $resWindowDays[0]];
                            $occupancyType = \Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT;
                        } else { // integrated
                            $resWindowPercents = $request->get('res-win-low');
                            $occupancyType = \Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT;
                            foreach ($resWindowDays as $i => $day) {
                                $percent = $resWindowPercents[$i];
                                if ($day) {
                                    $key = (string) $percent / 100;
                                    $setRules[''.$key] = $day;
                                }
                            }
                        }

                        \Genesis_Service_ReservationWindowRule::saveRules(
                            $facility->getAccountId(),
                            $facility->getId(),
                            null,
                            $occupancyType,
                            $setRules,
                            User::getLoggedUser()->getId()
                        );
                    }
                    // if the user opened the unit overrides options, allow, save and apply unit reservation window overrides
                    /**
                     * @var $unit Genesis_Entity_StorageSpace
                     */
                    foreach ($units as $unit) {
                        $unitResWindowRuleChange = true;
                        $rule = []; // empty rule, so this always exists on save
                        $newWindowDays = $request->get('unit-reservation-override-'.$unit->getId());
                        if ($newWindowDays || $newWindowDays === '') {
                            $rule = (empty($newWindowDays)) ? [] : [0 => $newWindowDays];
                        }

                        // save single unit rule
                        // the applyRulesByFacilityId will propagate to all units in a unit group
                        \Genesis_Service_ReservationWindowRule::saveRules(
                            $facility->getAccountId(),
                            $facility->getId(),
                            $unit->getId(),
                            \Genesis_Entity_ReservationWindowRule::OCCUPANCY_TYPE_PERCENT,
                            $rule,
                            User::getLoggedUser()->getId()
                        );
                    }
                    /*
                     * update facility rules if we updated either units or the facility
                     * and reload the units because they changed res windows
                     */
                    if ($facilityResWindowRuleChange || $unitResWindowRuleChange) {
                        \Genesis_Service_ReservationWindowRule::applyRulesByFacilityId($facility->getId());
                        $this->view->units = $facility->getActiveGroupedUnits(true);
                    }
                }
                // $this->_logFacilityAttributeChange('edit_facility_name', $facility->getTitle(), $request->get('facility_name'), $facility);
                $facility->setTitle($request->get('facility_name'));
                if (!in_array('facility_description', $integratedParams)) {
                    $facility->setDescription($request->get('facility_description'));
                }

                if (
                    $request->get('facility_promotions')
                    && !in_array('facility_promotions', $integratedParams)
                ) {
                    if (strlen($request->get('facility_promotions')) > 100) {
                        $this->view->erroredFields = ['facility_promotions'];
                        throw new \Exception('Special Offer must be less than 100 characters long.');
                    }

                    if (!$corporation->getPullPromos()) {
                        $facility->setSpecials($request->get('facility_promotions'));
                    }
                }

                // Check to see if description or promo has phone number or email in it
                if (
                    \Genesis_Util_Validator::containsPhoneNumber($request->get('facility_description')) or \Genesis_Util_Validator::containsEmailAddress($request->get('facility_description'))
                    and !$this->getLoggedUser()->isMyFootGod()
                ) {
                    array_push($this->view->erroredFields, 'facility_description');
                }
                if (
                    \Genesis_Util_Validator::containsPhoneNumber($request->get('facility_promotions')) or \Genesis_Util_Validator::containsEmailAddress($request->get('facility_promotions'))
                    and !$this->getLoggedUser()->isMyFootGod()
                ) {
                    array_push($this->view->erroredFields, 'facility_promotions');
                }

                if (strlen($request->get('facility_admin_fee')) > 0) {
                    $facility->setAdminFee($request->get('facility_admin_fee'));
                } else {
                    $facility->setAdminFee(null);
                }

                if (strlen($request->get('facility_phone')) > 0) {
                    if (!preg_match('/[0-9]{7,14}/', preg_replace('/[^0-9]/', '', $request->get('facility_phone')))) {
                        $this->view->erroredFields = ['facility_phone'];
                        throw new \Exception('Phone number is invalid');
                    }
                    $facility->setPhone(preg_replace('/[^0-9]/', '', $request->get('facility_phone')));
                } else {
                    $facility->setPhone(null);
                }

                if (strlen($request->get('facility_phone')) > 0) {
                    if (!preg_match('/[0-9]{7,14}/', preg_replace('/[^0-9]/', '', $request->get('facility_phone')))) {
                        $this->view->erroredFields = ['facility_phone'];
                        throw new \Exception('Phone number is invalid');
                    }
                    $facility->setPhone(preg_replace('/[^0-9]/', '', $request->get('facility_phone')));
                } else {
                    $facility->setPhone(null);
                }

                if (\Genesis_Service_Feature::isActive('tenant_connect_sms')) {
                    if (strlen($request->get('facility_tenant_connect_sms_number')) > 0) {
                        $smsNumber = preg_replace('/[^0-9]/', '', $request->get('facility_tenant_connect_sms_number'));
                        if (!preg_match('/[0-9]{7,14}/', $smsNumber)) {
                            $this->view->erroredFields = ['facility_tenant_connect_sms_number'];
                            throw new \Exception('SMS phone number is invalid');
                        }
                        $facility->setTenantConnectSMSNumber($smsNumber);
                    } else {
                        $facility->setTenantConnectSMSNumber(0);
                    }
                }

                if (strlen($request->get('facility_tenant_connect')) > 0) {
                    $facility->setTenantConnect($request->get('facility_tenant_connect'));
                }

                if (strlen($request->get('facility_code')) > 0) {
                    $facility->setCompanyCode($request->get('facility_code'));
                } else {
                    $facility->setCompanyCode(null);
                }

                // check valid structure of a url
                $facilityUrl = $request->get('facility_url');
                if (isset($facilityUrl)) {
                    if (!preg_match("/^[A-Za-z]+:\/\//", $facilityUrl)) {
                        $testUrl = 'http://'.$facilityUrl;
                    } else {
                        $testUrl = $facilityUrl;
                    }

                    if ($this->_validateUrl($testUrl)) {
                        $facility->setUrl($testUrl);
                    } elseif ($facilityUrl == '') {
                        $facility->setUrl(null);
                        $facility->setUrlVerified(0);
                    } else {
                        $this->view->erroredFields = ['facility_url'];
                        throw new \Exception('The URL supplied appears to be invalid.');
                    }
                }

                // check for another published facility in this location that is not this facility, for manual users only
                if ($corporation->getSourceId() == \Genesis_Entity_Source::ID_MANUAL) {
                    $exist_facility = \Genesis_Service_Facility::load(
                        \Genesis_Db_Restriction::and_(
                            \Genesis_Db_Restriction::equal('locationId', $location->getId()),
                            \Genesis_Db_Restriction::equal('published', 1),
                            \Genesis_Db_Restriction::not(\Genesis_Db_Restriction::equal('directoryFacility', 1))
                        )
                    )->uniqueResult();

                    if ($exist_facility && ($exist_facility->getId() != $facility->getId())) {
                        // instead of throwing an exception here, just email support a notification
                        // Only send the email if the facility's location has changed since the last update.

                        if ($facility->getLocationId() && $facility->getLocationId() != $location->getId()) {
                            $msg = new \Genesis_Entity_EmailMessage();
                            if ($exist_facility->getDirectoryFacility()) {
                                $msg->setSubject('MyFoot: New Facility with same location as a Directory Facility');
                            } else {
                                $msg->setSubject('MyFoot: Duplicate Facility Location Edited');
                            }

                            $existingFacAccount = $exist_facility->getCorporation()->getAccount();
                            $existName = '';
                            if ($existingFacAccount) {
                                $existName = $existingFacAccount->getName();
                            }

                            if ($request->get('facility_address_change') == 1) {
                                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                                // instead of throwing an exception here, just email support a notification
                                $msg->setSubject('MyFoot: Duplicate Facility Location Edited');
                                $msg_str = 'A user changed an address of a facility to one already in use by another facility.<br/><br/>'.
                                    'Updated Facility Details:<br/>'.
                                    'Account: '.$this->getLoggedUser()->getAccount()->getName().'<br/>'.
                                    'Name: '.$facility->getTitle().'<br/>'.
                                    'Entered Address: '.$request->get('facility_address1').' '.
                                    $request->get('facility_city').' '.
                                    $request->get('facility_state').' '.
                                    $request->get('facility_zip').'<br/><br/>'.
                                    'Existing Facility Details:<br/>'.
                                    'Account: '.$existName.'<br/>'.
                                    'Name: '.$exist_facility->getTitle().'<br/>'.
                                    'Address: '.$exist_facility->getLocation()->getAddress1().' '.
                                    $exist_facility->getLocation()->getCity().' '.
                                    $exist_facility->getLocation()->getState().' '.
                                    $exist_facility->getLocation()->getZip().'<br/><br/>'.
                                    'User Details:<br/>'.
                                    'Account: '.$user_account_name.'<br/>'.
                                    'Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().'<br/>'.
                                    'Email: '.$this->getLoggedUser()->getEmail().'<br/>';

                                $msg->setBody(\Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>';
                                \Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, [], $this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
                            }
                        }
                    }
                }

                $altAddress = [];
                if ($request->get('has_alt_address')) {
                    if ($request->get('alt_facility_address1')) {
                        $altAddress['address'] = $request->get('alt_facility_address1');
                    }
                    if ($request->get('alt_facility_city')) {
                        $altAddress['city'] = $request->get('alt_facility_city');
                    }
                    if ($request->get('alt_facility_state')) {
                        $altAddress['state'] = $request->get('alt_facility_state');
                    }
                    if ($request->get('alt_facility_zip')) {
                        $altAddress['zip'] = $request->get('alt_facility_zip');
                    }
                    $throwError = false;
                    $this->view->erroredFields = [];
                    if (!isset($altAddress['address'])) {
                        $this->view->erroredFields[] = 'alt_facility_address1';
                        $throwError = true;
                    }
                    if (!isset($altAddress['city'])) {
                        $this->view->erroredFields[] = 'alt_facility_city';
                        $throwError = true;
                    }
                    if (!isset($altAddress['state'])) {
                        $this->view->erroredFields[] = 'alt_facility_state';
                        $throwError = true;
                    }
                    if (!isset($altAddress['zip'])) {
                        $this->view->erroredFields[] = 'alt_facility_zip';
                        $throwError = true;
                    }
                    if ($throwError) {
                        throw new \Exception('Please specify an alternate address for receiving packages');
                    }
                    $facility->setAltAddress($altAddress);
                } elseif ($request->get('has_alt_address') === '0') {
                    $facility->setAltAddress(0);
                } else {
                    $this->view->erroredFields[] = 'has_alt_address';
                }

                if (strlen($request->get('onsite_office_at_facility')) > 0) {
                    $facility->setOnsiteOfficeAtFacility($request->get('onsite_office_at_facility'));
                } else {
                    array_push($this->view->erroredFields, 'onsite_office_at_facility');
                }

                // TODO: do we want to remove the street view POV here if the address changed?
                $facility->setLocationId($location->getId());

                // set the software (which is called Source, and stored in key_source)
                $software = $request->get('facility_software', null);
                if ((int) $software > 0) {
                    $facility->setSelfReportedSourceId((int) $request->get('facility_software'));
                } elseif ($software !== null) { // was actually in the form
                    $this->view->erroredFields[] = 'facility_software';
                }

                if (!strlen($request->get('facility_active')) > 0) {
                    array_push($this->view->erroredFields, 'facility_active');
                } else {
                    $facility->setActive($request->get('facility_active') ? 1 : 0);

                    if ($facility->getActive()) {
                        $facility->setAutomaticReactivationDate(null);
                    } elseif ($request->get('automatic_reactivation_date') == '') {
                        $facility->setAutomaticReactivationDate(null);
                    } else {
                        $automaticReactivationDate = strtotime($request->get('automatic_reactivation_date'));
                        if ($automaticReactivationDate <= time()) {
                            array_push($this->view->erroredFields, 'automatic_reactivation_date');
                        } elseif ($automaticReactivationDate >= strtotime('+1 year')) {
                            array_push($this->view->erroredFields, 'automatic_reactivation_date');
                        } else {
                            $facility->setAutomaticReactivationDate(date('Y-m-d', $automaticReactivationDate));
                        }
                    }
                }

                $facility = \Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                $this->view->facility = $facility;
                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';
            } catch (\Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        if ($facility->canEditReservationWindow()) {
            // grab facility rules
            $facilityResRules = [];
            foreach ($facility->getReservationWindowRules() as $rule) {
                $facilityResRules[] = [
                    'id' => $rule->getId(),
                    'occupancyType' => $rule->getOccupancyType(),
                    'occupancyValue' => $rule->getOccupancyValue(),
                    'reservationWindowDays' => $rule->getReservationWindowDays(),
                ];
            }

            $this->view->facilityResRules = $facilityResRules;
            $this->view->units = $facility->getActiveGroupedUnits(true);
        }

        $this->setCommonViewFields();

        $this->view->scripts = [
            'facility/global-functions',
            'sparefoot/plugins/res-win-widget/script',
            'facility/hide-facility-reason-modal',
            'facility/details',
        ];

        return $this->render('facility/details.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/amenities
     * http://localhost:9019/facility/amenities.
     */
    #[Route('/facility/amenities', name: 'facility_amenities')]
    public function amenitiesAction(Request $request): Response
    {
        $this->view->csrf_token = CsrfUtil::getToken(self::AMENITIES_CSRF_TOKEN);
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);

        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $integrationId = $facility->getCorporation()->getSourceId();

        $this->view->facility = $facility;
        $this->view->params = ['app' => 'myfoot', 'user' => $this->getLoggedUser()];

        if ($integrationId != \Genesis_Entity_Source::ID_MANUAL) {
            $integratedFields = $this->_getIntegratedFields();
            $this->view->depositIsDisabled = $depositIsDisabled = in_array('security_deposit_required', $integratedFields);
            unset($integratedFields);
        } else {
            $this->view->depositIsDisabled = $depositIsDisabled = false;
        }

        $this->view->erroredFields = [];

        if ($request->isMethod('POST')) {
            try {
                // csrf token
                if (!CsrfUtil::validateToken(self::AMENITIES_CSRF_TOKEN, $request->get('csrf_token'))) {
                    throw new \Exception('Request structure is invalid. Refresh the page and try again.');
                }

                // Moving options
                if (strlen($request->get('truck_rental')) > 0) {
                    $facility->setTruckRental($request->get('truck_rental'));
                } else {
                    array_push($this->view->erroredFields, 'truck_rental');
                }

                if (strlen($request->get('free_truck_rental')) > 0) {
                    $facility->setFreeTruckRental($request->get('free_truck_rental'));
                } else {
                    array_push($this->view->erroredFields, 'free_truck_rental');
                }

                if ($request->get('free_truck_rental') == 1) {
                    if (strlen($request->get('truck_distance_limit')) == 0) {
                        array_push($this->view->erroredFields, 'truck_distance_limit');
                    }

                    if ($request->get('truck_distance_limit') == 1) {
                        $facility->setTruckDistanceLimit($request->get('truck_max_mileage'));
                    } elseif (strlen($request->get('truck_distance_limit')) > 0) {
                        $facility->setTruckDistanceLimit(0);
                    }

                    if ($request->get('truck_distance_limit') == 1 && strlen($request->get('truck_max_mileage')) == 0) {
                        array_push($this->view->erroredFields, 'truck_distance_limit');
                        array_push($this->view->erroredFields, 'truck_max_mileage');
                    }

                    if (strlen($request->get('truck_insurance_required')) == 0) {
                        array_push($this->view->erroredFields, 'truck_insurance_required');
                    }

                    if ($request->get('truck_insurance_required') == 1) {
                        $facility->setTruckInsurance($request->get('truck_insurance_amount'));
                    } elseif (strlen($request->get('truck_insurance_required')) > 0) {
                        $facility->setTruckInsurance(0);
                    }

                    if ($request->get('truck_insurance_required') == 1 && strlen($request->get('truck_insurance_amount')) == 0) {
                        array_push($this->view->erroredFields, 'truck_insurance_required');
                        array_push($this->view->erroredFields, 'truck_insurance_amount');
                    }

                    if (strlen($request->get('truck_fuel_refill')) > 0) {
                        $facility->setTruckRefuelPolicy($request->get('truck_fuel_refill'));
                    } else {
                        array_push($this->view->erroredFields, 'truck_fuel_refill');
                    }
                }

                if (strlen($request->get('truck_access')) > 0) {
                    $facility->setTruckAccess($request->get('truck_access'));
                } else {
                    array_push($this->view->erroredFields, 'truck_access');
                }

                if ($request->get('truck_access') == 1) {
                    if (strlen($request->get('truck_access_size')) > 0) {
                        $facility->setTruckAccessSize($request->get('truck_access_size'));
                    } else {
                        array_push($this->view->erroredFields, 'truck_access');
                        array_push($this->view->erroredFields, 'truck_access_size');
                    }
                }

                if (\Genesis_Service_Feature::isActive('myfoot.allow_18wheeler_dropoff')) {
                    if (strlen($request->get('allow_18wheeler_dropoff')) > 0) {
                        $facility->setAllow18WheelerDropoff($request->get('allow_18wheeler_dropoff'));

                        // second question response depends on if first question was answered
                        if (strlen($request->get('has_18wheeler_alleys')) > 0) {
                            $facility->setHas18WheelerAlleys($request->get('has_18wheeler_alleys'));
                        } else {
                            array_push($this->view->erroredFields, 'has_18wheeler_alleys');
                        }
                    } else {
                        array_push($this->view->erroredFields, 'allow_18wheeler_dropoff');
                    }
                }

                if (strlen($request->get('handcarts')) > 0) {
                    $facility->setHandcarts($request->get('handcarts'));
                } else {
                    array_push($this->view->erroredFields, 'handcarts');
                }

                if (strlen($request->get('elevator')) > 0) {
                    $facility->setElevator($request->get('elevator'));
                } else {
                    array_push($this->view->erroredFields, 'elevator');
                }

                if (strlen($request->get('sell_moving_supplies')) > 0) {
                    $facility->setSellsMovingSupplies($request->get('sell_moving_supplies'));
                } else {
                    array_push($this->view->erroredFields, 'sell_moving_supplies');
                }

                // Security options
                if (strlen($request->get('surveillance')) > 0) {
                    $facility->setSurveillance($request->get('surveillance'));
                } else {
                    array_push($this->view->erroredFields, 'surveillance');
                }

                if (strlen($request->get('egate_access')) > 0) {
                    $facility->setEGateAccess($request->get('egate_access'));
                } else {
                    array_push($this->view->erroredFields, 'egate_access');
                }

                if (strlen($request->get('fenced_lighted')) > 0) {
                    $facility->setFencedLighted($request->get('fenced_lighted'));
                } else {
                    array_push($this->view->erroredFields, 'fenced_lighted');
                }

                if (strlen($request->get('resident_manager')) > 0) {
                    $facility->setResidentManager($request->get('resident_manager'));
                } else {
                    array_push($this->view->erroredFields, 'resident_manager');
                }

                if (strlen($request->get('kiosk')) > 0) {
                    $facility->setKiosk($request->get('kiosk'));
                } else {
                    array_push($this->view->erroredFields, 'kiosk');
                }

                if (!strlen($request->get('bilingual_manager_available')) > 0) {
                    array_push($this->view->erroredFields, 'bilingual_manager_available');
                } elseif ($request->get('bilingual_manager_available') == '0') {
                    $facility->setBilingualManager(0);
                } elseif (!strlen($request->get('bilingual_language')) > 0) {
                    array_push($this->view->erroredFields, 'bilingual_language');
                } else {
                    $facility->setBilingualManager($request->get('bilingual_language'));
                }

                if (strlen($request->get('accept_tenant_mail')) > 0) {
                    $facility->setAcceptTenantMail($request->get('accept_tenant_mail'));
                } else {
                    array_push($this->view->erroredFields, 'accept_tenant_mail');
                }

                // Billing
                if (strlen($request->get('email_invoicing')) > 0) {
                    $facility->setEmailInvoicingAvailable($request->get('email_invoicing'));
                } else {
                    array_push($this->view->erroredFields, 'email_invoicing');
                }

                if (strlen($request->get('auto_payments')) > 0) {
                    $facility->setAutoPayAvailable($request->get('auto_payments'));
                } else {
                    array_push($this->view->erroredFields, 'auto_payments');
                }

                if (strlen($request->get('charge_date')) > 0) {
                    $facility->setChargeDate($request->get('charge_date'));
                } else {
                    array_push($this->view->erroredFields, 'charge_date');
                }

                if (strlen($request->get('charge_date')) > 0) {
                    $facility->setChargeDate($request->get('charge_date'));
                } else {
                    array_push($this->view->erroredFields, 'charge_date');
                }

                $securityDepositRequired = $request->get('security_deposit_required');
                $securityDepositRefundable = $request->get('security_deposit_refundable');
                $securityDepositType = $request->get('security_deposit_type');
                $securityDepositPercent = $request->get('security_deposit_percent');
                $securityDepositDollar = $request->get('security_deposit_dollar');

                // save security deposit required
                if (strlen($securityDepositRequired) == 0) {
                    array_push($this->view->erroredFields, 'security_deposit_required');
                } elseif ($securityDepositRequired == 1 && !$depositIsDisabled) {
                    $facility->setSecurityDeposit($securityDepositRequired);

                    // save security deposit refundable
                    if (strlen($securityDepositRefundable) == 0) {
                        array_push($this->view->erroredFields, 'security_deposit_refundable');
                    } else {
                        $facility->setSecurityDepositRefundable($securityDepositRefundable);
                    }

                    // save security deposit type
                    if (strlen($securityDepositType) == 0) {
                        array_push($this->view->erroredFields, 'security_deposit_type');
                    } else {
                        $facility->setSecurityDepositType($securityDepositType);

                        // save security deposit type percent
                        if ($securityDepositType === 'percent') {
                            if (strlen($securityDepositPercent) == 0) {
                                array_push($this->view->erroredFields, 'security_deposit_percent');
                            } else {
                                $facility->setSecurityDepositPercent($securityDepositPercent);
                                $facility->setSecurityDepositDollar(0);
                            }
                        }

                        // save security deposit type dollar
                        if ($securityDepositType === 'dollar') {
                            if (strlen($securityDepositDollar) == 0) {
                                array_push($this->view->erroredFields, 'security_deposit_dollar');
                            } else {
                                $facility->setSecurityDepositDollar($securityDepositDollar);
                                $facility->setSecurityDepositPercent(0);
                            }
                        }
                    }
                } elseif ($securityDepositRequired == 0 && !$depositIsDisabled) {
                    $facility->setSecurityDeposit(0);
                }

                // Discounts
                if (strlen($request->get('military_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'military_discount_available');
                } elseif ($request->get('military_discount_available') == 1) {
                    if (is_numeric($request->get('military_discount_amount'))) {
                        $facility->setMilitaryDiscount($request->get('military_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'military_discount_amount');
                        throw new \Exception('Military discount must be a number percentage');
                    }
                    if (is_numeric($request->get('military_discount_amount'))) {
                        $facility->setMilitaryDiscount($request->get('military_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'military_discount_amount');
                        array_push($this->view->erroredFields, 'military_discount_available');
                    }

                    $militaryDiscountAppliesToReserves = $request->get('military_discount_reserves');
                    if (strlen($militaryDiscountAppliesToReserves) == 0) {
                        array_push($this->view->erroredFields, 'military_discount_reserves');
                    } elseif ($militaryDiscountAppliesToReserves == 1 || $militaryDiscountAppliesToReserves == 0) {
                        $facility->setMilitaryDiscountAppliesToReserves($militaryDiscountAppliesToReserves);
                    }

                    $militaryDiscountAppliesToVeterans = $request->get('military_discount_veterans');
                    if (strlen($militaryDiscountAppliesToVeterans) == 0) {
                        array_push($this->view->erroredFields, 'military_discount_veterans');
                    } elseif ($militaryDiscountAppliesToVeterans == 1 || $militaryDiscountAppliesToVeterans == 0) {
                        $facility->setMilitaryDiscountAppliesToVeterans($militaryDiscountAppliesToVeterans);
                    }
                } elseif (strlen($request->get('military_discount_available')) > 0) {
                    $facility->setMilitaryDiscount(0);
                }

                if ($request->get('military_discount_available') == 1 && strlen($request->get('military_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'military_discount_available');
                    array_push($this->view->erroredFields, 'military_discount_amount');
                }

                if (strlen($request->get('senior_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'senior_discount_available');
                } elseif ($request->get('senior_discount_available') == 1) {
                    if (is_numeric($request->get('senior_discount_amount'))) {
                        $facility->setSeniorDiscount($request->get('senior_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'senior_discount_available');
                        array_push($this->view->erroredFields, 'senior_discount_amount');
                    }
                } elseif (strlen($request->get('senior_discount_available')) > 0) {
                    $facility->setSeniorDiscount(0);
                }

                if ($request->get('senior_discount_available') == 1 && strlen($request->get('senior_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'senior_discount_available');
                    array_push($this->view->erroredFields, 'senior_discount_amount');
                }

                if (strlen($request->get('student_discount_available')) == 0) {
                    array_push($this->view->erroredFields, 'student_discount_available');
                } elseif ($request->get('student_discount_available') == 1) {
                    if (is_numeric($request->get('student_discount_amount'))) {
                        $facility->setStudentDiscount($request->get('student_discount_amount'));
                    } else {
                        array_push($this->view->erroredFields, 'student_discount_available');
                        array_push($this->view->erroredFields, 'student_discount_amount');
                    }
                } elseif (strlen($request->get('student_discount_available')) > 0) {
                    $facility->setStudentDiscount(0);
                }

                if ($request->get('student_discount_available') == 1 && strlen($request->get('student_discount_amount')) == 0) {
                    array_push($this->view->erroredFields, 'student_discount_available');
                    array_push($this->view->erroredFields, 'student_discount_amount');
                }

                // Insurance
                if (strlen($request->get('insurance_required')) > 0) {
                    $facility->setInsuranceRequired($request->get('insurance_required'));
                } else {
                    array_push($this->view->erroredFields, 'insurance_required');
                }

                if (strlen($request->get('insurance_available')) > 0) {
                    $facility->setInsuranceAvailable($request->get('insurance_available'));
                } else {
                    array_push($this->view->erroredFields, 'insurance_available');
                }

                if (User::isFeatureActive('myfoot.protection_plan_facility_amenities')) {
                    if (strlen($request->get('protection_plan_required')) > 0) {
                        $facility->setProtectionPlanRequired($request->get('protection_plan_required'));
                    } else {
                        array_push($this->view->erroredFields, 'protection_plan_required');
                    }

                    if (strlen($request->get('protection_plan_available')) > 0) {
                        $facility->setProtectionPlanAvailable($request->get('protection_plan_available'));
                    } else {
                        array_push($this->view->erroredFields, 'protection_plan_available');
                    }
                }

                if (strlen($request->get('homeowners_insurance_accepted')) > 0) {
                    $facility->setHomeownersInsuranceAccepted($request->get('homeowners_insurance_accepted'));
                } else {
                    array_push($this->view->erroredFields, 'homeowners_insurance_accepted');
                }

                // Other amenities
                if (strlen($request->get('band_practice_allowed')) > 0) {
                    $facility->setBandPracticeAllowed($request->get('band_practice_allowed'));
                } else {
                    array_push($this->view->erroredFields, 'band_practice_allowed');
                }

                // Remote paperwork
                if (strlen($request->get('remote_paperwork')) > 0) {
                    $facility->setRemotePaperwork($request->get('remote_paperwork'));
                } else {
                    array_push($this->view->erroredFields, 'remote_paperwork');
                }

                // wash
                if (strlen($request->get('wash_station')) > 0) {
                    $facility->setWashStationAvailable($request->get('wash_station'));
                } else {
                    array_push($this->view->erroredFields, 'wash_station');
                }

                // dump
                if (strlen($request->get('dump_station')) > 0) {
                    $facility->setDumpStationAvailable($request->get('dump_station'));
                } else {
                    array_push($this->view->erroredFields, 'dump_station');
                }

                // Payment Types
                if (strlen($request->get('payment_accept_cash')) > 0) {
                    $paymentAcceptCash = $request->get('payment_accept_cash');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_cash');
                }
                if (strlen($request->get('payment_accept_check')) > 0) {
                    $paymentAcceptCheck = $request->get('payment_accept_check');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_check');
                }
                if (strlen($request->get('payment_accept_credit')) > 0) {
                    $paymentAcceptCredit = $request->get('payment_accept_credit');
                } else {
                    array_push($this->view->erroredFields, 'payment_accept_credit');
                }

                $paymentAcceptedCards = $request->get('payment_accepted_cards');

                $facility->setPaymentOptions(
                    $paymentAcceptCash,
                    $paymentAcceptCheck,
                    $paymentAcceptCredit,
                    $paymentAcceptedCards
                );

                // Vehicle Storage

                if (strlen($request->get('vehicle_require_title')) > 0) {
                    $vehicleRequireTitle = $request->get('vehicle_require_title');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_title');
                }

                if (strlen($request->get('vehicle_require_registration')) > 0) {
                    $vehicleRequireRegistration = $request->get('vehicle_require_registration');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_registration');
                }
                if (strlen($request->get('vehicle_require_insurance')) > 0) {
                    $vehicleRequireInsurance = $request->get('vehicle_require_insurance');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_insurance');
                }
                if (strlen($request->get('vehicle_require_running')) > 0) {
                    $vehicleRequireRunning = $request->get('vehicle_require_running');
                } else {
                    array_push($this->view->erroredFields, 'vehicle_require_running');
                }

                if (strlen($request->get('maintenance_allowed')) > 0) {
                    $facility->setMaintenanceAllowed($request->get('maintenance_allowed'));
                } else {
                    array_push($this->view->erroredFields, 'maintenance_allowed');
                }

                $vehicleRenterTitled = $request->get('vehicle_renter_titled');

                $facility->setVehicleStorage(
                    $vehicleRequireTitle,
                    $vehicleRenterTitled,
                    $vehicleRequireRunning,
                    $vehicleRequireInsurance,
                    $vehicleRequireRegistration
                );

                if (strlen($request->get('minimum_stay')) > 0 && $request->get('minimum_stay') > 0) {
                    $facility->setMinimumStayRequired($request->get('minimum_stay'));
                }

                if (strlen($request->get('general_maintenance')) > 0) {
                    $generalMaintenance = $request->get('general_maintenance');
                } else {
                    array_push($this->view->erroredFields, 'general_maintenance');
                }

                if (strlen($request->get('propane')) > 0) {
                    $propane = $request->get('propane');
                } else {
                    array_push($this->view->erroredFields, 'propane');
                }

                if (strlen($request->get('diesel_and_gas')) > 0) {
                    $dieselAndGas = $request->get('diesel_and_gas');
                } else {
                    array_push($this->view->erroredFields, 'diesel_and_gas');
                }

                if (strlen($request->get('does_state_inspections')) > 0) {
                    $doesStateInspections = $request->get('does_state_inspections');
                } else {
                    array_push($this->view->erroredFields, 'does_state_inspections');
                }

                if (strlen($request->get('auto_cleaning_and_detailing')) > 0) {
                    $autoCleaningAndDetailing = $request->get('auto_cleaning_and_detailing');
                } else {
                    array_push($this->view->erroredFields, 'auto_cleaning_and_detailing');
                }

                if (strlen($request->get('air_pump')) > 0) {
                    $airPump = $request->get('air_pump');
                } else {
                    array_push($this->view->erroredFields, 'air_pump');
                }

                if (strlen($request->get('vacuum_station')) > 0) {
                    $vacuumStation = $request->get('vacuum_station');
                } else {
                    array_push($this->view->erroredFields, 'vacuum_station');
                }

                if (strlen($request->get('ice_machine')) > 0) {
                    $iceMachine = $request->get('ice_machine');
                } else {
                    array_push($this->view->erroredFields, 'ice_machine');
                }

                if (strlen($request->get('water_hose_spigot')) > 0) {
                    $waterHoseSpigot = $request->get('water_hose_spigot');
                } else {
                    array_push($this->view->erroredFields, 'water_hose_spigot');
                }

                $facility->setPremiumVehicleStorage(
                    $generalMaintenance,
                    $propane,
                    $dieselAndGas,
                    $doesStateInspections,
                    $autoCleaningAndDetailing,
                    $airPump,
                    $vacuumStation,
                    $iceMachine,
                    $waterHoseSpigot
                );

                if ($paymentAcceptCredit && count($paymentAcceptedCards) == 0) {
                    array_push($this->view->erroredFields, 'payment_accepted_cards');
                }

                \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                $this->view->facility = $facility;

                if (count($this->view->erroredFields) == 1) {
                    $this->view->alert = 'You missed a spot. Tell us a little more.';
                    $this->view->alertClass = 'alert-danger';
                } elseif (count($this->view->erroredFields) > 1) {
                    $this->view->alert = 'You missed a few spots. Tell us a little more.';
                    $this->view->alertClass = 'alert-danger';
                } else {
                    $this->view->alert = 'Changes saved.';
                    $this->view->alertClass = 'alert-success';
                }

                // Discount errors
                if (count($this->view->erroredFields) > 0) {
                    if (in_array('military_discount_amount', $this->view->erroredFields)) {
                        $this->view->alert = 'Military discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';
                    } elseif (in_array('student_discount_amount', $this->view->erroredFields)) {
                        $this->view->alert = 'Student discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';
                    } elseif (in_array('senior_discount_amount', $this->view->erroredFields)) {
                        $this->view->alert = 'Senior discount must be a numeric percent';
                        $this->view->alertClass = 'alert-danger';
                    }
                }
            } catch (\Exception $e) {
                $this->view->alert = $e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        // Get the payment types
        list(
            $this->view->paymentAcceptCash,
            $this->view->paymentAcceptCheck,
            $this->view->paymentAcceptCredit,
            $this->view->paymentAcceptedCards,
        ) = $facility->getPaymentOptions();

        // Populate View w/ Vehicle Options
        list(
            $this->view->vehicleRequireTitle,
            $this->view->vehicleRenterTitled,
            $this->view->vehicleRequireRunning,
            $this->view->vehicleRequireInsurance,
            $this->view->vehicleRequireRegistration,
        ) = $facility->getVehicleStorage();

        $this->setCommonViewFields();

        $this->view->scripts = ['facility/amenities'];

        return $this->render('facility/amenities.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/hours
     * http://localhost:9019/facility/hours.
     */
    #[Route('/facility/hours', name: 'facility_hours')]
    public function hoursAction(Request $request): Response
    {
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);

        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;
        $this->view->params = ['app' => 'myfoot', 'user' => $this->getLoggedUser()];
        $this->view->erroredFields = [];

        // create new blank hours records if needed, view tries to use
        $officeHours = \Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'office');
        if (!$officeHours) {
            $officeHours = new \Genesis_Entity_FacilityHours();
            $officeHours->setFacilityId($facility->getId());
            $officeHours->setType(\Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE);
            $officeHours->setManualEdit(0);
            \Genesis_Service_FacilityHours::save($officeHours);
        }
        $this->view->officeHours = $officeHours;

        $accessHours = \Genesis_Service_FacilityHours::loadById($this->getSession()->facilityId, 'access');
        if (!$accessHours) {
            $accessHours = new \Genesis_Entity_FacilityHours();
            $accessHours->setFacilityId($facility->getId());
            $accessHours->setType(\Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS);
            $accessHours->setManualEdit(0);
            \Genesis_Service_FacilityHours::save($accessHours);
        }
        $this->view->accessHours = $accessHours;

        $this->view->usHolidays = \Genesis_Util_Holidays::getHolidays();

        if ($request->isMethod('POST')) {
            try {
                $obseredHolidays = $request->get('observed');
                if (!is_array($obseredHolidays)) {
                    $obseredHolidays = [];
                }
                $facility->setObservedHolidays(array_keys($obseredHolidays));

                $appointmentOnlyOfficeHours = [
                    'mon' => $request->get('appointment_only_office_hours_mon', 0),
                    'tue' => $request->get('appointment_only_office_hours_tue', 0),
                    'wed' => $request->get('appointment_only_office_hours_wed', 0),
                    'thu' => $request->get('appointment_only_office_hours_thu', 0),
                    'fri' => $request->get('appointment_only_office_hours_fri', 0),
                    'sat' => $request->get('appointment_only_office_hours_sat', 0),
                    'sun' => $request->get('appointment_only_office_hours_sun', 0),
                ];
                $facility->setAppointmentOnlyOfficeHours($appointmentOnlyOfficeHours);

                // setup new office hours objects and save then,  save will update automaticaly if they already exist
                $officeHours = new \Genesis_Entity_FacilityHours();
                $officeHours->setFacilityId($facility->getId());
                $officeHours->setType(\Genesis_Entity_FacilityHours::HOURS_TYPE_OFFICE);
                $officeHours->setManualEdit(true);
                $officeHours->setSunStart($officeHours->formatTime($request->get('hrs_o_sun_s')));
                $officeHours->setSunEnd($officeHours->formatTime($request->get('hrs_o_sun_e')));
                $officeHours->setMonStart($officeHours->formatTime($request->get('hrs_o_mon_s')));
                $officeHours->setMonEnd($officeHours->formatTime($request->get('hrs_o_mon_e')));
                $officeHours->setTueStart($officeHours->formatTime($request->get('hrs_o_tue_s')));
                $officeHours->setTueEnd($officeHours->formatTime($request->get('hrs_o_tue_e')));
                $officeHours->setWedStart($officeHours->formatTime($request->get('hrs_o_wed_s')));
                $officeHours->setWedEnd($officeHours->formatTime($request->get('hrs_o_wed_e')));
                $officeHours->setThuStart($officeHours->formatTime($request->get('hrs_o_thu_s')));
                $officeHours->setThuEnd($officeHours->formatTime($request->get('hrs_o_thu_e')));
                $officeHours->setFriStart($officeHours->formatTime($request->get('hrs_o_fri_s')));
                $officeHours->setFriEnd($officeHours->formatTime($request->get('hrs_o_fri_e')));
                $officeHours->setSatStart($officeHours->formatTime($request->get('hrs_o_sat_s')));
                $officeHours->setSatEnd($officeHours->formatTime($request->get('hrs_o_sat_e')));
                $this->view->officeHours = $officeHours;
                $facility->setOfficeHours($officeHours);

                $accessHours = new \Genesis_Entity_FacilityHours();
                $accessHours->setFacilityId($facility->getId());
                $accessHours->setType(\Genesis_Entity_FacilityHours::HOURS_TYPE_ACCESS);
                $accessHours->setManualEdit(true);
                $accessHours->setSunStart($accessHours->formatTime($request->get('hrs_a_sun_s')));
                $accessHours->setSunEnd($accessHours->formatTime($request->get('hrs_a_sun_e')));
                $accessHours->setMonStart($accessHours->formatTime($request->get('hrs_a_mon_s')));
                $accessHours->setMonEnd($accessHours->formatTime($request->get('hrs_a_mon_e')));
                $accessHours->setTueStart($accessHours->formatTime($request->get('hrs_a_tue_s')));
                $accessHours->setTueEnd($accessHours->formatTime($request->get('hrs_a_tue_e')));
                $accessHours->setWedStart($accessHours->formatTime($request->get('hrs_a_wed_s')));
                $accessHours->setWedEnd($accessHours->formatTime($request->get('hrs_a_wed_e')));
                $accessHours->setThuStart($accessHours->formatTime($request->get('hrs_a_thu_s')));
                $accessHours->setThuEnd($accessHours->formatTime($request->get('hrs_a_thu_e')));
                $accessHours->setFriStart($accessHours->formatTime($request->get('hrs_a_fri_s')));
                $accessHours->setFriEnd($accessHours->formatTime($request->get('hrs_a_fri_e')));
                $accessHours->setSatStart($accessHours->formatTime($request->get('hrs_a_sat_s')));
                $accessHours->setSatEnd($accessHours->formatTime($request->get('hrs_a_sat_e')));
                $this->view->accessHours = $accessHours;
                $facility->setAccessHours($accessHours);

                if (strlen($request->get('twentyfour_hr_access')) > 0) {
                    $facility->setTwentyFourHourAccess($request->get('twentyfour_hr_access') ? 1 : 0);

                    if ($request->get('twentyfour_hr_access') == 1) {
                        // 24-hr access without restrictions
                        // overwrite hours with midnight-to-midnight
                        $accessHours = \Genesis_Service_FacilityHours::buildTwentyFourHours($facility->getId());
                        $this->view->accessHours = $accessHours;
                        $facility->setAccessHours($accessHours);
                        $facility->setTwentyFourHourAccessRestricted(0);
                        $facility->setTwentyFourHourAccessSupplemental([]);
                    }
                    if ($request->get('twentyfour_hr_access') == 2) {
                        // yes with restrictions
                        if ($request->get('twentyfour_hr_access_type')) {
                            $facility->setTwentyFourHourAccessRestricted(1);
                            $facility->setTwentyFourHourAccessSupplemental($request->get('twentyfour_hr_access_type'));
                        } else {
                            $facility->setTwentyFourHourAccessRestricted(0);
                            // if 24-hr access restricted is set, some restrictions MUST be specified
                            // array_push($this->view->erroredFields, 'twentyfour_hr_access_restricted');
                        }

                        if (in_array('for additional $%s per month', $request->get('twentyfour_hr_access_type'))) {
                            if ($request->get('twentyfour_hr_access_fee') > 0) {
                                $facility->setTwentyFourHourAccessFee($request->get('twentyfour_hr_access_fee'));
                            } else {
                                array_push($this->view->erroredFields, 'twentyfour_hr_access_fee');
                            }
                        }
                    } else {
                        // no 24-hr access
                        $facility->setTwentyFourHourAccessRestricted(0);
                        $facility->setTwentyFourHourAccessSupplemental([]);
                    }
                } else {
                    array_push($this->view->erroredFields, 'twentyfour_hr_access');
                }

                \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'alert-success';
            } catch (\Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'alert-danger';
            }
        }

        $closures = [];
        foreach ($facility->getUpcomingClosures() as $facClsObj) {
            $closures[] = [
                'closureId' => $facClsObj->getId(),
                'closedDateStart' => $facClsObj->getClosedDateStart(),
                'closedDateEnd' => $facClsObj->getClosedDateEnd(),
                'dateString' => $facClsObj->getDisplayDate(),
            ];
        }

        $this->view->observedHolidays = (array) $facility->getObservedHolidays();
        $this->view->upcomingClosures = $closures;

        $this->setCommonViewFields();

        $this->view->scripts = ['facility/hours'];

        return $this->render('facility/hours.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/addfacilityclosure
     * http://localhost:9019/facility/addfacilityclosure.
     */
    #[Route('/facility/addfacilityclosure', name: 'facility_add_facility_closure')]
    public function addfacilityclosureAction(Request $request): Response
    {
        $startDate = $request->get('startDate');
        $endDate = $request->get('endDate');
        $facilityId = $request->get('fid');

        try {
            $facClosure = \Genesis_Service_FacilityClosure::createFromFacilityIdStartDateAndEndDate($facilityId, $startDate, $endDate, $this->getLoggedUser());

            // TODO: add context
            $facility = \Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.add_custom_closure', $context);

            $responseData = [
                'closureId' => $facClosure->getId(),
                'closedDateStart' => $facClosure->getClosedDateStart(),
                'closedDateEnd' => $facClosure->getClosedDateEnd(),
                'dateString' => $facClosure->getDisplayDate(),
            ];

            return new Response(json_encode($responseData), 200, [
                'Content-Type' => 'application/json',
            ]);
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new Response('Error: '.$e->getMessage(), 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/updatefacilityclosure
     * http://localhost:9019/facility/updatefacilityclosure.
     */
    #[Route('/facility/updatefacilityclosure', name: 'facility_update_facility_closure')]
    public function updatefacilityclosureAction(Request $request): Response
    {
        $facClosure = \Genesis_Service_FacilityClosure::loadById($request->get('closureId'));
        $facClosure->setClosedDateStart($request->get('startDate'));
        if ($request->get('endDate') && $request->get('endDate') != $request->get('startDate')) {
            $facClosure->setClosedDateEnd($request->get('endDate'));
        }

        try {
            $facClosure = \Genesis_Service_FacilityClosure::save($facClosure);

            // TODO: add context
            $facility = \Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.edit_custom_closure', $context);

            $responseData = [
                'closureId' => $facClosure->getId(),
                'closedDateStart' => $facClosure->getClosedDateStart(),
                'closedDateEnd' => $facClosure->getClosedDateEnd(),
                'dateString' => $facClosure->getDisplayDate(),
            ];

            return new Response(json_encode($responseData), 200, [
                'Content-Type' => 'application/json',
            ]);
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new Response('Error: '.$e->getMessage(), 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/removefacilityclosure
     * http://localhost:9019/facility/removefacilityclosure.
     */
    #[Route('/facility/removefacilityclosure', name: 'facility_remove_facility_closure')]
    public function removefacilityclosureAction(Request $request): Response
    {
        $closureID = $request->get('closureId');

        $facClosure = \Genesis_Service_FacilityClosure::loadById($closureID);
        if ($facClosure) {
            $facClosure->setActive(0);
            \Genesis_Service_FacilityClosure::save($facClosure, $this->getLoggedUser());

            // TODO: add context
            $facility = \Genesis_Service_Facility::loadById($facClosure->getFacilityId());
            $user = $this->getLoggedUser();
            $context['account_id'] = $facility->getAccountId();
            $context['closure_start_date'] = $facClosure->getClosedDateStart();
            $context['closure_end_date'] = $facClosure->getClosedDateEnd();
            $context['closure_active'] = $facClosure->getActive();
            $this->statsClient->increment('myfoot.features.remove_custom_closure', $context);

            return new Response('OK', 200, [
                'Content-Type' => 'text/plain',
            ]);
        } else {
            $errorString = 'Error: closure id ('.$closureID.") not found.\n";
            $e = new \Exception($errorString);
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new Response($errorString, 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/updateinventory
     * http://localhost:9019/facility/updateinventory.
     */
    #[Route('/facility/updateinventory', name: 'facility_update_inventory')]
    public function updateinventoryAction(Request $request): Response
    {
        \Genesis_Util_Debug::startProfile();
        $output = '';
        try {
            $type = $request->get('type');

            // check what type of call this is
            if ($type == 'price') {
                $unitId = $request->get('unit_id');
                $sparefootPrice = $request->get('sf_price');
                $unit = \Genesis_Service_StorageSpace::loadById($unitId);

                // TODO: some server side checking
                // sparefoot price cannot be higher than monthly rent
                if ($sparefootPrice <= $unit->getRegularPrice()) {
                    // $this->_logUnitAttributeChange('edit_sf_price', $unit->getSparefootPrice(), $sparefootPrice, $unit);
                    $unit->setSparefootPrice($sparefootPrice);
                    $unit = \Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());
                } else {
                    throw new \Exception('Error: SpareFoot price must be <= regular price.');
                }
            } elseif ($type === 'show' || $type === 'hide') {
                $unitIds = $request->get('unit_ids'); // comma separated list
                $unitIds = explode(',', $unitIds);

                $facilities = [];
                $active = $type === 'show' ? 1 : 0;

                foreach ($unitIds as $unitId) {
                    $unit = \Genesis_Service_StorageSpace::loadById($unitId);
                    if (!$unit) {
                        continue;
                    }

                    if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->canAccessFacility($unit->getFacility())) {
                        $facilities[$unit->getFacilityId()] = $unit->getFacility();

                        $unit->setActive($active);

                        $unit = \Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());

                        // side effect
                        // make the facility active if its off, and we turned a unit on.
                        // ...not in the service since we don't want the behavior from api sync
                        if ($active && !$unit->getFacility()->getActive()) {
                            $facility = $unit->getFacility();
                            $facility->setActive(1);
                            \Genesis_Service_Facility::save($facility);
                            \Genesis_Util_ActionLogger::logAction(
                                'unhide_facility_reason',
                                null,
                                'Auto activate for available inventory',
                                $this->getLoggedUser()->getId(),
                                $facility->getId(),
                                $unit->getId()
                            );
                        }

                        $output .= $unit->getActive();
                    }
                }
            } else {
                throw new \Exception('unknown update action: '.$type);
            }
        } catch (\Exception $e) {
            $output .= 'Error: '.$e->getMessage()."\n";
        }
        // TODO: Kaleb added
        $output .= \Genesis_Util_Debug::endProfile();

        return new Response($output, 200, [
            'Content-Type' => 'text/plain',
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/updatefacility
     * http://localhost:9019/facility/updatefacility.
     */
    #[Route('/facility/updatefacility', name: 'facility_update_facility')]
    public function updatefacilityAction(Request $request): Response
    {
        $output = '';
        try {
            $type = $request->get('type');
            $facilityIds = explode(',', $request->get('facility_ids'));

            foreach ($facilityIds as $facilityId) {
                try {
                    $facility = \Genesis_Service_Facility::loadById($facilityId);
                } catch (\Exception $e) {
                    continue;
                }

                if ($this->getLoggedUser()->isMyFootGod() || $this->getLoggedUser()->canAccessFacility($facility)) {
                    if ($type === 'show') {
                        $facility->setActive(1);
                    } elseif ($type === 'hide') {
                        $facility->setActive(0);
                    }
                }
                $logReasons = [
                    'hide_facility_reason' => $request->get('hide_facility_reason'),
                    'hide_facility_when_can_reactivate' => $request->get('hide_facility_when_can_reactivate'),
                    'hide_facility_how_full' => $request->get('hide_facility_how_full'),
                    'hide_facility_unhappy_because' => $request->get('hide_facility_unhappy_because'),
                    'hide_facility_new_owner' => $request->get('hide_facility_new_owner'),
                ];
                foreach ($logReasons as $action_name => $reason) {
                    if (!$reason) {
                        continue;
                    }
                    $action_log = new \Genesis_Entity_ActionLog();
                    $action_log->setFacilityId($facilityId);
                    $action_log->setParam($action_name);
                    $action_log->setPreValue(null);
                    $action_log->setPostValue($reason);
                    $action_log->setUserId($this->getLoggedUser()->getId());
                    $action_log->setTimestamp(date('Y-m-d H:i:s'));
                    \Genesis_Service_ActionLog::save($action_log);
                }
                // check to see if the user wants to reactivate on a certain date
                if ($request->get('reactivation_select') === 'yes') {
                    $date = \DateTime::createFromFormat('m-d-Y', $request->get('reactivation_date'));
                    $reactivation_date = $date ? $date->format('Y-m-d') : null;
                    if ($reactivation_date) {
                        $facility->setAutomaticReactivationDate($reactivation_date);
                    }
                }

                $facility = \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

                // Create ticket if customer is unhappy
                $reasonUnhappy = $logReasons['hide_facility_unhappy_because'];
                if ($reasonUnhappy) {
                    Facility::createUnhappyFacilityTicket($facility, $reasonUnhappy, $this->getLoggedUser());
                }

                // or sold the facility
                $reasonNewOwner = $logReasons['hide_facility_new_owner'];
                if ($reasonNewOwner) {
                    Facility::createChangeOfOwnershipTicket($facility, $reasonNewOwner, $this->getLoggedUser());
                }
            }

            // The following line works for god users too, because listAction sets this dynamically for gods.
            $acct = $this->getLoggedUser()->getAccount();
            $nameEmail = $this->getLoggedUser()->getFirstName().
                ' '.$this->getLoggedUser()->getLastName().
                ' ('.$this->getLoggedUser()->getEmail().')';

            if ($acct->getNumBillableEntities() > 0 && $acct->getNumActiveFacilities() == 0) {
                $msg = new \Genesis_Entity_EmailMessage();

                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';
                $msg->setSubject($acct->getName().' has hidden all their facilities.');
                $msg_str =
                    'The MyFoot user '.$nameEmail.' has hidden all the facilities for the account '.
                    $acct->getInfoString().'. Please check to see whether or not this is still the case.';

                $msg->setBody(\Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>';
                \Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, [], $this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
            }

            return new Response('OK', 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response($e->getMessage(), 500, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * https://myfoot.sparefoot.com/facility/photos
     * http://localhost:9019/facility/photos.
     */
    #[Route('/facility/photos', name: 'facility_photos')]
    public function photosAction(Request $request): Response
    {
        // Ensure the user has access to this facility
        $facilityId = $request->get('fid');
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            $this->view->errorMessages[] = 'You do not have permission to access this facility.';
            throw new \Exception('Unauthorized access to facility.', 403);
        }

        if ($request->isMethod('POST')) {
            $maxsize = 5242880; // 5MB

            $fileCount = count($_FILES['image']['name']);

            for ($i = 0; $i < $fileCount; ++$i) {
                if ($_FILES['image']['size'][$i] < $maxsize) {
                    $extension = explode('.', $_FILES['image']['name'][$i]);
                    $extension = $extension[count($extension) - 1];

                    $facilityId = $request->get('fid');

                    $failure = 0;
                    $savedImage = null;

                    try {
                        if ($request->get('logo')) {
                            \Genesis_Service_FacilityImage::append($facilityId, $extension, $_FILES['image']['tmp_name'][$i], true);
                        } else {
                            $savedImage = \Genesis_Service_FacilityImage::append($facilityId, $extension, $_FILES['image']['tmp_name'][$i]);
                        }
                    } catch (\Exception $e) {
                        $this->view->errorMessages[] = 'This image cannot be uploaded. Please upload another image.';
                        $this->view->errorMessages[] = 'Reason: '.$e->getMessage();
                        $failure = 1;
                    }

                    if (!$failure) {
                        $this->view->successMessages[] = 'New image uploaded.';
                        // images uploaded by god-level users are auto-reviewed
                        $savedImage->setReviewed($this->getLoggedUser()->isMyFootGod() ? 1 : 0);
                        $savedImage->setUploadedByUserId($this->getLoggedUser()->getUserId());
                        $savedImage = \Genesis_Service_FacilityImage::save($savedImage);
                    }
                } else {
                    $this->view->errorMessages[] = 'Image too big! Uploads must be smaller than 5 MB.';
                }
            }
        }
        $this->view->facility = $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);

        if ($request->get('logo')) {
            $this->forward('setup', 'Sites', null, null);
        }

        $this->setCommonViewFields();

        $this->view->scripts = ['facility/photos'];

        return $this->render('facility/photos.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/deleteimage
     * http://localhost:9019/facility/deleteimage.
     */
    #[Route('/facility/deleteimage', name: 'facility_delete_image')]
    public function deleteimageAction(Request $request): Response
    {
        $facilityId = $request->get('fid');
        $pictureNumber = $request->get('number');

        // Ensure the user has access to this facility
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            $this->view->errorMessages[] = 'You do not have permission to access this facility.';
            throw new \Exception('Unauthorized access to facility.', 403);
        }

        \Genesis_Service_FacilityImage::delete($facilityId, $pictureNumber);

        // Redirect to the photos page for this facility
        $url = $this->generateUrl('facility_photos', [], UrlGeneratorInterface::ABSOLUTE_PATH).'?fid='.$facilityId;

        return new RedirectResponse($url);
    }

    /**
     * https://myfoot.sparefoot.com/facility/defaultimage
     * http://localhost:9019/facility/defaultimage.
     */
    #[Route('/facility/defaultimage', name: 'facility_default_image')]
    public function defaultimageAction(Request $request): Response
    {
        $facilityId = $request->get('fid');
        $pictureNumber = $request->get('number');

        // Ensure the user has access to this facility
        $user = $this->getLoggedUser();
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            $this->view->errorMessages[] = 'You do not have permission to access this facility.';
            throw new \Exception('Unauthorized access to facility.', 403);
        }

        \Genesis_Service_FacilityImage::setDefault($facilityId, $pictureNumber);

        // Redirect to the photos page for this facility
        $url = $this->generateUrl('facility_photos', [], UrlGeneratorInterface::ABSOLUTE_PATH).'?fid='.$facilityId;

        return new RedirectResponse($url);
    }

    /**
     * https://myfoot.sparefoot.com/facility/add
     * http://localhost:9019/facility/add.
     */
    #[Route('/facility/add', name: 'facility_add')]
    public function addAction(Request $request): Response
    {
        // clear active facilityID
        if ($request->get('new') == true) {
            $this->getSession()->set('facilityId', false);
        }

        // clear session data when they hit the page to add a manual
        $this->getSession()->facIds = null;

        $this->view->csrf_token = CsrfUtil::getToken(self::ADD_FACILITY_CSRF_TOKEN);
        $this->view->facName = '';
        $this->view->facCoCode = '';
        $this->view->facURL = '';
        $this->view->facAdminFee = '';
        $this->view->facAddress = '';
        $this->view->facPhone = '';
        $this->view->facCity = '';
        $this->view->facState = '';
        $this->view->facZip = '';
        $this->view->facPromo = '';
        $this->view->facDesc = '';
        $this->view->update = 0;

        // prepopulate if there is an fid passed in
        if ($this->getSession()->facilityId) {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $location = $facility->getLocation();
            $this->view->update = 1;
            $this->view->facName = $facility->getTitle();
            $this->view->facCoCode = $facility->getCompanyCode();
            $this->view->facURL = $facility->getUrl();
            $this->view->facAdminFee = $facility->getAdminFee();
            if ($location) {
                $this->view->facAddress = $location->getAddress1();
                $this->view->facCity = $location->getCity();
                $this->view->facState = $location->getState();
                $this->view->facZip = $location->getZip();
            }
            $this->view->facPhone = $facility->getPhone();
            $this->view->facPromo = $facility->getSpecials();
            $this->view->facDesc = $facility->getDescription();
        }

        // pass user list that will have access to this facility by default
        $account = $this->getLoggedUser()->getAccount();

        // admins and full users will initially have access to this facility

        $accessUAs = \Genesis_Service_UserAccess::load(
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('accountId', $account->getId()),
                \Genesis_Db_Restriction::or_(
                    \Genesis_Db_Restriction::equal('myfootRole', \Genesis_Entity_UserAccess::ROLE_ADMIN),
                    \Genesis_Db_Restriction::equal('myfootRole', \Genesis_Entity_UserAccess::ROLE_FULL)
                )
            )
        );

        $accessEmails = [];

        foreach ($accessUAs as $ua) {
            $accessEmails[] = $ua->getEmail();
        }

        $accessEmails = array_unique($accessEmails);
        $this->view->accessemails = implode(', ', $accessEmails);

        $reservationUAs = \Genesis_Service_UserAccess::load(
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('accountId', $account->getId()),
                \Genesis_Db_Restriction::equal('getsEmails', 1),
                \Genesis_Db_Restriction::or_(
                    \Genesis_Db_Restriction::equal('myfootRole', \Genesis_Entity_UserAccess::ROLE_ADMIN),
                    \Genesis_Db_Restriction::equal('myfootRole', \Genesis_Entity_UserAccess::ROLE_FULL)
                )
            )
        );

        $reservationEmails = [];

        foreach ($reservationUAs as $ua) {
            $reservationEmails[] = $ua->getEmail();
        }

        $reservationEmails = array_unique($reservationEmails);
        $this->view->reservationemails = implode(', ', $reservationEmails);

        $this->view->scripts = ['facility/add'];

        return $this->render('facility/add.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Called from add page to create a new facility
     * https://myfoot.sparefoot.com/facility/addfacility
     * http://localhost:9019/facility/addfacility.
     */
    #[Route('/facility/addfacility', name: 'facility_add_facility')]
    public function addfacilityAction(Request $request): Response
    {
        // \Genesis_Util_Debug::startProfile();
        $update = false;

        // see if this is an update
        if ($request->get('update')) {
            $update = true;
        }

        try {
            $emails = [];

            // check valid email addresses
            if (!$update && strlen($request->get('reservation_emails')) > 0) {
                // format check emails
                $emails = explode(',', $request->get('reservation_emails'));
                $emails = array_unique($emails);

                foreach ($emails as $email) {
                    if (!(strlen($email) > 0)) {
                        continue;
                    }

                    $email = trim($email);

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new \Exception($email.' is an invalid email address');
                    }
                }
            }

            // validate zip
            if (!(strlen($request->get('zip')) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $request->get('zip'))) {
                throw new \Exception('Please enter a valid zip code.');
            }

            // validate phone
            if (!(strlen($request->get('phone')) > 0) || !preg_match('/[0-9]{7,14}/', preg_replace('/[^0-9]/', '', $request->get('phone')))) {
                throw new \Exception('Please enter a valid phone number');
            }

            // if this account does not have a manual integration, then add one
            $corp = $this->getLoggedUser()->getAccount()->getManualIntegration();
            if (!$corp) {
                $corp = new \Genesis_Entity_ManualCorporation();
                $corp->setAccountId($this->getLoggedUser()->getAccountId());
                $corp->setCreated(date('Y-m-d H:i:s', time()));
                $corp->setSourceId(\Genesis_Entity_Source::ID_MANUAL);
                $corp->setCorpname($this->getLoggedUser()->getAccount()->getName());

                $corp = \Genesis_Service_Corporation::save($corp);
            }

            // check that a facility with this name and corp doesn't already exist
            $exist_facility = \Genesis_Service_Facility::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('title', stripslashes($request->get('name'))),
                    \Genesis_Db_Restriction::equal('corporationId', $corp->getId())
                )
            )->uniqueResult();
            if ($exist_facility) {
                throw new \Exception('Facility with this name already exists in your account.');
            }

            // create new location
            $location = \Genesis_Service_Location::loadByAddress(
                $request->get('address'),
                $request->get('city'),
                $request->get('state'),
                $request->get('zip')
            );

            // does this location already exist
            if (!$location) {
                // call geocoder
                $location = \Genesis_Service_Location::geoCodePhysicalAddress(
                    $request->get('address').' '.$request->get('city').' '.$request->get('state').' '.$request->get('zip')
                );
                $location = \Genesis_Service_Location::save($location);
            }

            // $locationId = $location->getId();

            // create new facility or update w/session id if update = true
            if ($update && $this->getSession()->facilityId) {
                $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            } else {
                $facility = new \Genesis_Entity_Facility();
            }
            // pull this out of session. it was set during
            if (isset($this->getSession()->selfReportedSourceId)) {
                $facility->setSelfReportedSourceId($this->getSession()->selfReportedSourceId);
            }
            $facility->setTitle(stripslashes($request->get('name')));
            $facility->setPhone(preg_replace('/[^0-9]/', '', $request->get('phone')));
            $facility->setLocationId($location->getId());
            $facility->setLocation($location);
            $facility->setActive(0);
            $facility->setPublished(1);
            $facility->setApproved(1);
            $facility->setSourceId(\Genesis_Entity_Source::ID_MANUAL);

            $facility->setNetworkSubscriptionFee($this->getLoggedUser()->getAccount()->getNewFacilitySubscriptionFee());

            // configure products to account defaults
            $cpa = $this->getLoggedUser()->getAccount()->getCpa();
            $facility->setTenantConnect($cpa); // CPA facility Tenant Connect enabled by default
            $facility->setCpa($cpa);

            // turn on insights feature for all new signups
            $facility->setInsights(1);

            // if they are adding facility here then it is to the manual integration.  Only 1 manual integration per account
            $facility->setCorporationId($this->getLoggedUser()->getAccount()->getManualIntegration()->getId());

            // check for another published facility in this location
            $exist_facility = \Genesis_Service_Facility::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('locationId', $location->getId()),
                    \Genesis_Db_Restriction::equal('published', 1)
                )
            )->uniqueResult();

            if (
                $exist_facility && $exist_facility->getLocationId()
                && $exist_facility->getId() != $facility->getId()
            ) {
                $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                // instead of throwing an exception here, just email support a notification
                $msg = new \Genesis_Entity_EmailMessage();
                $msg->setSubject('MyFoot: Duplicate Facility Location Created');
                $msg_str = 'A user created a facility at an address where another facility is already on file.<br/><br/>'.
                    'New Facility Details:<br/>'.
                    'Account: '.$this->getLoggedUser()->getAccount()->getName().'<br/>'.
                    'Name: '.$facility->getTitle().'<br/>'.
                    'Entered Address:'.$request->get('address').' '.
                    $request->get('city').' '.
                    $request->get('state').' '.
                    $request->get('zip').'<br/><br/>'.
                    'Existing Facility Details:<br/>'.
                    'Account: '.$this->getLoggedUser()->getAccount()->getName().'<br/>'.
                    'Name: '.$exist_facility->getTitle().'<br/>'.
                    'Address: '.$exist_facility->getLocation()->getAddress1().' '.
                    $exist_facility->getLocation()->getCity().' '.
                    $exist_facility->getLocation()->getState().' '.
                    $exist_facility->getLocation()->getZip().'<br/><br/>'.
                    'User Details:<br/>'.
                    'Account: '.$user_account_name.'<br/>'.
                    'Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().'<br/>'.
                    'Email: '.$this->getLoggedUser()->getEmail().'<br/>';

                $msg->setBody(\Genesis_Util_Formatter::formatSalesForceEmailContent($msg_str));
                $mailto = ($this->getLoggedUser()->isMyFootGod()) ? '' : '<EMAIL>';
                \Genesis_Service_Mailer::sendInternalMessage($mailto, $msg, [], $this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
            }

            $facility = \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

            // create new users for any emails put in (validated previously), on creation only, to edit users they'll have to do it in users tab
            if (!$update && count($emails) > 0) {
                foreach ($emails as $email) {
                    $email = trim($email);

                    $user = \Genesis_Service_User::load(\Genesis_Db_Restriction::equal('email', $email))->uniqueResult();

                    if (!$user) {
                        $user = new \Genesis_Entity_User();
                        $user->setEmail($email);
                        $user = \Genesis_Service_User::save($user);
                    }

                    $newUserAccess = \Genesis_Service_UserAccess::loadById($user->getId());

                    // setup user access if needed
                    if (!$newUserAccess) {
                        $newUserAccess = new \Genesis_Entity_UserAccess();
                        $newUserAccess->setUserId($user->getId());
                        $newUserAccess->setGetsEmails(1);
                        $newUserAccess->setAccountId($this->getLoggedUser()->getAccountId());
                    } else {
                        // skip existing user if not on this account
                        if ($newUserAccess->getAccountId() != $this->getLoggedUser()->getAccountId()) {
                            continue;
                            // throw new Exception('User ' . $email . ' already has a MySpareFoot signin for another account.  <NAME_EMAIL> for help.');
                        }

                        // confirm existing user gets emails
                        $newUserAccess->setGetsEmails(1);
                    }

                    $newUserAccess = \Genesis_Service_UserAccess::save($newUserAccess);

                    // give them access to this facility in fac restrictions if not already there
                    if (
                        !$newUserAccess->getAllFacilities()
                        && !\Genesis_Service_UserFacilityRestrictions::loadById($facility->getId(), $user->getId())
                    ) {
                        $facRes = new \Genesis_Entity_UserFacilityRestrictions();
                        $facRes->setFacilityId($facility->getId());
                        $facRes->setUserId($user->getId());
                        \Genesis_Service_UserFacilityRestrictions::save($facRes);
                    }
                }
            }

            // set up a manual facility
            $db_connection = \Genesis_Db_Connection::getInstance();
            $stmt = $db_connection->prepare("SELECT * FROM manual_facilities WHERE listing_avail_id ='".$facility->getId()."'");
            $stmt->execute();
            if ($stmt->fetch()) {
                // update contact
                $stmt = $db_connection->prepare("UPDATE manual_facilities SET email = '', manual_corp_id = '".$this->getLoggedUser()->getAccount()->getManualIntegration()->getId()."' WHERE listing_avail_id = ".$facility->getId());
                $stmt->execute();
            } else {
                // write new contact
                $stmt = $db_connection->prepare('INSERT INTO manual_facilities (listing_avail_id, manual_corp_id, email) VALUES ('.$facility->getId().','.$this->getLoggedUser()->getAccount()->getManualIntegration()->getId().",'')");
                $stmt->execute();
            }

            // send emails if new facilities exist
            try {
                $this->notifyOnNewFacilities($facility);
            } catch (\Exception $e) {
                return new Response('Error: '.$e->getMessage()."\n", 500, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            // die ("Error: ".Genesis_Util_Debug::endProfile());
            // send back facility id for re-direction url
            return new Response($facility->getId(), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 500, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Called from add page to create and units
     * https://myfoot.sparefoot.com/facility/unit
     * http://localhost:9019/facility/unit.
     */
    #[Route('/facility/unit', name: 'facility_unit')]
    public function unitAction(Request $request): Response
    {
        try {
            if (!$request->request->has('unit_id')) {
                throw new \Exception('You must bring a unit_id to edit');
            }

            // put unit ids into array in case there are a lot of them
            if ($request->get('unit_id')) {
                $unitIds = explode(',', $request->get('unit_id'));
            } else {
                // create new space based on type if no ids passed in (MANUALS do this)
                switch ($request->get('unit_type')) {
                    case \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                        $space = new \Genesis_Entity_StorageUnit();
                        break;
                    case \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                        $space = new \Genesis_Entity_ParkingSpace();
                        break;
                    case \Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                        $space = new \Genesis_Entity_Workspace();
                        break;
                    case \Genesis_Entity_StorageSpace::TYPE_WINE:
                        $space = new \Genesis_Entity_WineStorage();
                        break;
                    case \Genesis_Entity_StorageSpace::TYPE_LOCKER:
                        $space = new \Genesis_Entity_StorageLocker();
                        break;
                    case \Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                        $space = new \Genesis_Entity_StorageOutdoor();
                        break;
                    default:
                        throw new \Exception('Error selecting the unit type');
                        break;
                }

                // set active and published for newly added units
                $space->setActive(1);
                $space->setPublish(1);
                $space->setQuantity(1);

                $space->setFacilityId($this->getSession()->facilityId);

                if ($request->get('unit_type')) {
                    $space->setType($request->get('unit_type'));
                }

                // set price for new unit
                $space->setRegularPrice($request->get('price_regular'));

                $savedSpace = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                $al = new \Genesis_Util_ActionLogger();
                $al->logAction('add_unit', 0, 0, $this->getLoggedUser()->getId(), $this->getSession()->facilityId, $savedSpace->getId());

                // put id in unitids array
                $unitIds = [$savedSpace->getId()];
            }

            $integratedFields = $this->_getIntegratedFields();

            // now we have $unitIds array to update rest of params
            foreach ($unitIds as $unitId) {
                // determine if this is a new creation or an update
                $space = \Genesis_Service_StorageSpace::loadById($unitId);
                if (isset($space) && !empty($space)) {
                    $corporation = $space->getFacility()->getCorporation();
                    $spaceOriginal = clone $space;

                    // make sure existing supp data gets lazy loaded
                    $space->getSupplementalData();

                    // populate fields that all types of units have
                    if ($request->request->has('unit_width')) {
                        $space->setWidth($request->request->get('unit_width'));
                    }

                    if ($request->request->has('unit_length')) {
                        $space->setLength($request->get('unit_length'));
                    }

                    if ($request->request->has('floor')) {
                        $space->setFloor($request->get('floor')); // form passes in 1 when outdoor
                    }

                    if ($request->request->has('description')) {
                        $space->setDescription(
                            strlen($request->get('description')) ? $request->get('description') : null
                        );
                    }

                    // if we aren't syncing promos....
                    if ($request->request->has('promotion') && $corporation && !$corporation->getPullPromos()) {
                        if (strlen($request->get('promotion')) > 100) {
                            throw new \Exception('Promotion must be less than 100 characters.');
                        }

                        $space->setSpecialString(
                            strlen($request->get('promotion')) ? $request->get('promotion') : null
                        );
                    }

                    ($request->request->has('deposit')) ? $space->setDeposit($request->get('deposit') ? $request->get('deposit') : null) : '';

                    // only update/change regular price for manuals
                    if (
                        $request->request->has('price_regular') && $request->get('price_regular')
                        && $request->request->has('source_type')
                        && $request->get('source_type') == \Genesis_Entity_Source::ID_MANUAL
                    ) {
                        $space->setRegularPrice($request->get('price_regular'));
                    }

                    if ($request->request->has('price_sf')) {
                        if (is_numeric($request->get('price_sf'))) {
                            if ($request->get('price_sf') <= 0) {
                                throw new \Exception('SpareFoot price must be greater than 0.');
                            }

                            if ($request->get('price_sf') > $space->getRegularPrice()) {
                                throw new \Exception('SpareFoot price must be less than or equal to regular price.');
                            } else {
                                // $this->_logUnitAttributeChange('edit_sf_price', $space->getSparefootPrice(), $request->get('price_sf'), $space);
                                $space->setSparefootPrice($request->get('price_sf'));
                            }
                        } else {
                            $space->setSparefootPrice(null);
                        }
                    }

                    if (!in_array('stacked', $integratedFields)) {
                        $space->setSkybox($request->get('stacked') == 'on' ? 1 : 0);
                    }

                    if (!in_array('premium', $integratedFields)) {
                        $space->setPremiumUnit($request->get('premium') == 'on' ? 1 : 0);
                    }

                    if (!in_array('heated', $integratedFields)) {
                        $space->setHeatedOnly($request->get('heated') == 'on' ? 1 : 0);
                    }

                    if (!in_array('aircooled', $integratedFields)) {
                        $space->setAirCooledOnly($request->get('aircooled') == 'on' ? 1 : 0);
                    }

                    if (!in_array('ada', $integratedFields)) {
                        $space->setAdaAccessible($request->get('ada') == 'on' ? 1 : 0);
                    }

                    if (!in_array('unitlights', $integratedFields)) {
                        $space->setUnitLights($request->get('unitlights') == 'on' ? 1 : 0);
                    }

                    if (!in_array('twentyfourhouraccess', $integratedFields)) {
                        $space->setTwentyFourHourAccess($request->get('twentyfourhouraccess') == 'on' ? 1 : 0);
                    }

                    if (!in_array('shelvesinunit', $integratedFields)) {
                        $space->setShelvesInUnit($request->get('shelvesinunit') == 'on' ? 1 : 0);
                    }

                    if (!in_array('basement', $integratedFields)) {
                        $space->setBasement($request->get('basement') == 'on' ? 1 : 0);
                    }

                    if (!in_array('parkingwarehouse', $integratedFields)) {
                        $space->setParkingWarehouse($request->get('parkingwarehouse') == 'on' ? 1 : 0);
                    }

                    if (!in_array('pullthru', $integratedFields)) {
                        $space->setPullThrough($request->get('pullthru') == 'on' ? 1 : 0);
                    }

                    if ($request->request->has('unit_type') && $request->get('unit_type') != $space->getType()) {
                        $space->setType($request->get('unit_type'));
                        // We have to save the space here to "transform" it into the
                        // proper class (Genesis_Entity_ParkingSpace, etc.)
                        $space = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }

                    // populate fields based on type
                    switch ($request->get('unit_type')) {
                        case \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                            if (!in_array('covered', $integratedFields)) {
                                $space->setCovered($request->get('covered') == 'true' ? 1 : 0);
                            }

                            ($request->request->has('unit_height')) ? $space->setHeight(($request->get('unit_height')) ? $request->get('unit_height') : null) : '';

                            if (!in_array('power', $integratedFields)) {
                                $space->setPower($request->get('power') == 'on' ? 1 : 0);
                            }

                            // NOTE: vehicle/driveup set to 1 in class constructor
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                            if (!in_array('covered', $integratedFields)) {
                                $space->setCovered($request->get('covered') == 'true' ? 1 : 0);
                            }

                            if (!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($request->get('driveup') == 'true' ? 1 : 0);
                            }

                            ($request->request->has('unit_height')) ? $space->setHeight(($request->get('unit_height')) ? $request->get('unit_height') : null) : '';

                            if ($request->request->has('vehicle')) {
                                if ($request->get('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($request->get('vehicle') == 'true') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }
                            }

                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                        case \Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                            ($request->request->has('unit_height')) ? $space->setHeight(($request->get('unit_height')) ? $request->get('unit_height') : null) : '';
                            ($request->request->has('door_height')) ? $space->setDoorHeight(($request->get('door_height')) ? $request->get('door_height') : null) : '';
                            ($request->request->has('door_width')) ? $space->setDoorWidth(($request->get('door_width')) ? $request->get('door_width') : null) : '';

                            if (!in_array('covered', $integratedFields)) {
                                $space->setCovered($request->get('covered') == 'true' ? 1 : 0);
                            }

                            if (!in_array('climate', $integratedFields)) {
                                $space->setClimateControlled($request->get('climate') ? 1 : 0);
                            }

                            if (!in_array('alarm', $integratedFields)) {
                                $space->setAlarm($request->get('alarm') ? 1 : 0);
                            }

                            if (!in_array('humidity', $integratedFields)) {
                                $space->setHumidityControlled($request->get('humidity') ? 1 : 0);
                            }

                            if (!in_array('power', $integratedFields)) {
                                $space->setPower($request->get('power') == 'on' ? 1 : 0);
                            }

                            if (!in_array('outdoor_access', $integratedFields)) {
                                $space->setOutdoorAccess($request->get('outdoor_access') == 'true' ? 1 : 0);
                            }

                            if (!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($request->get('driveup') == 'true' ? 1 : 0);
                            }

                            ($request->request->has('door_type')) ? $space->setDoorType($request->get('door_type')) : '';

                            if ($request->request->has('vehicle')) {
                                if ($request->get('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($request->get('vehicle') == 'true') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }
                            }

                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_WINE:
                        case \Genesis_Entity_StorageSpace::TYPE_LOCKER:
                            ($request->request->has('unit_height')) ? $space->setHeight(($request->get('unit_height')) ? $request->get('unit_height') : null) : '';
                            ($request->request->has('door_height')) ? $space->setDoorHeight(($request->get('door_height')) ? $request->get('door_height') : null) : '';
                            ($request->request->has('door_width')) ? $space->setDoorWidth(($request->get('door_width')) ? $request->get('door_width') : null) : '';

                            if (!in_array('climate', $integratedFields)) {
                                $space->setClimateControlled($request->get('climate') ? 1 : 0);
                            }

                            if (!in_array('alarm', $integratedFields)) {
                                $space->setAlarm($request->get('alarm') ? 1 : 0);
                            }

                            if (!in_array('humidity', $integratedFields)) {
                                $space->setHumidityControlled($request->get('humidity') ? 1 : 0);
                            }

                            if (!in_array('power', $integratedFields)) {
                                $space->setPower($request->get('power') ? 1 : 0);
                            }

                            if (!in_array('outdoor_access', $integratedFields)) {
                                $space->setOutdoorAccess($request->get('outdoor_access') == 'true' ? 1 : 0);
                            }

                            if (!in_array('driveup', $integratedFields)) {
                                $space->setDriveUp($request->get('driveup') == 'true' ? 1 : 0);
                            }

                            ($request->request->has('door_type')) ? $space->setDoorType($request->get('door_type')) : '';

                            break;
                        default:
                            throw new \Exception('Could not match type of storage space');
                            break;
                    }

                    $this->_resetUnitIntegratedInputs($space, $spaceOriginal, $integratedFields);

                    \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                    // throw new Exception('upstairs access: '.$savedSpace->getUpstairsViaLift());
                }
            }

            return new Response('OK', 500, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n".'Trace: '.$e->getTraceAsString()."\n", 500, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    private function _logUnitAttributeChange($attributeName, $oldValue, $newValue, $space)
    {
        $al = new \Genesis_Util_ActionLogger();

        if ($oldValue === true || $oldValue == 'true' || $oldValue == 'on') {
            $oldValue = 1;
        }
        if ($newValue === true || $newValue == 'true' || $newValue == 'on') {
            $newValue = 1;
        }
        if ($oldValue === false || $oldValue == 'false' || $oldValue == 'off' || is_null($oldValue)) {
            $oldValue = 0;
        }
        if ($newValue === false || $newValue == 'false' || $newValue == 'off' || is_null($newValue)) {
            $newValue = 0;
        }

        if ($oldValue != $newValue) {
            $al->logAction($attributeName, $oldValue, $newValue, $this->getLoggedUser()->getId(), $space->getFacilityId(), $space->getId());
        }
    }

    private function _logFacilityAttributeChange($attributeName, $oldValue, $newValue, $facility)
    {
        $al = new \Genesis_Util_ActionLogger();

        if ($oldValue === true || $oldValue == 'true' || $oldValue == 'on') {
            $oldValue = 1;
        }
        if ($newValue === true || $newValue == 'true' || $newValue == 'on') {
            $newValue = 1;
        }
        if ($oldValue === false || $oldValue == 'false' || $oldValue == 'off' || is_null($oldValue)) {
            $oldValue = 0;
        }
        if ($newValue === false || $newValue == 'false' || $newValue == 'off' || is_null($newValue)) {
            $newValue = 0;
        }

        if ($oldValue != $newValue) {
            $al->logAction($attributeName, $oldValue, $newValue, $this->getLoggedUser()->getId(), $facility->getId());
        }
    }

    /**
     * This is used when user selects multiple units and hits "edit selected"
     * https://myfoot.sparefoot.com/facility/multiunit
     * http://localhost:9019/facility/multiunit.
     */
    #[Route('/facility/multiunit', name: 'facility_multiunit')]
    public function multiunitAction(Request $request): Response
    {
        try {
            // put unit ids into array in case there are a lot of them
            if ($request->get('unit_ids')) {
                $unitIds = explode(',', $request->get('unit_ids'));
            } else {
                throw new \Exception('Please select units to update.');
            }

            $integratedInputs = $this->_getIntegratedFields();

            // now we have $unitIds array to update rest of params
            foreach ($unitIds as $unitId) {
                // determine if this is a new creation or an update
                $space = \Genesis_Service_StorageSpace::loadById($unitId);

                if ($space) {
                    $spaceOriginal = clone $space;

                    // if type was set then save that 1st
                    if ($request->get('change_type')) {
                        $space->setType($request->get('unit_type'));
                        $space = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }

                    // populate fields based on type
                    switch ($space->stringType()) {
                        case 'Parking':
                            if ($request->get('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                $space->setCovered(($request->get('covered') == 'true') ? 1 : 0);
                            }

                            if ($request->get('change_dimensions') == 'on' && !in_array('unit_height', $integratedInputs)) {
                                $space->setHeight($request->get('unit_height'));
                            }

                            break;
                        case 'Land/open lot':
                            if ($request->get('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                $space->setCovered(($request->get('covered') == 'true') ? 1 : 0);
                            }

                            if ($request->get('change_dimensions') == 'on' && !in_array('unit_height', $integratedInputs)) {
                                $space->setHeight($request->get('unit_height'));
                            }

                            if ($request->get('change_access')) {
                                if ($request->get('vehicle') && $request->get('vehicle') !== 'false') {
                                    $space->setVehicle(1);
                                } elseif ($request->get('vehicle') == 'false') {
                                    $space->setVehicle(0);
                                }

                                if ($request->get('driveup') == 'true') {
                                    $space->setDriveUp(1);
                                } elseif ($request->get('driveup') == 'false') {
                                    $space->setDriveUp(0);
                                }
                            }

                            break;
                        case 'Unit':
                        case 'Workspace':
                            if ($request->get('change_dimensions')) {
                                if (!in_array('unit_height', $integratedInputs)) {
                                    $space->setHeight($request->get('unit_height'));
                                }

                                if (!in_array('door_height', $integratedInputs)) {
                                    $space->setDoorHeight($request->get('door_height'));
                                }

                                if (!in_array('door_width', $integratedInputs)) {
                                    $space->setDoorWidth($request->get('door_width'));
                                }
                            }

                            if ($request->get('change_location') == 'on' && !in_array('covered', $integratedInputs)) {
                                $space->setCovered(($request->get('covered') == 'true') ? 1 : 0);
                            }

                            if ($request->get('change_amenities')) {
                                if (!in_array('climate', $integratedInputs)) {
                                    $space->setClimateControlled(($request->get('climate') == 'on') ? 1 : 0);
                                }

                                if (!in_array('alarm', $integratedInputs)) {
                                    $space->setAlarm(($request->get('alarm') == 'on') ? 1 : 0);
                                }

                                if (!in_array('humidity', $integratedInputs)) {
                                    $space->setHumidityControlled(($request->get('humidity') == 'on') ? 1 : 0);
                                }

                                if (!in_array('power', $integratedInputs)) {
                                    $space->setPower(($request->get('power') == 'on') ? 1 : 0);
                                }
                            }

                            if ($request->get('change_access')) {
                                if (!in_array('vehicle', $integratedInputs)) {
                                    $space->setVehicle(($request->get('vehicle') == 'true') ? 1 : 0);
                                }

                                if (!in_array('driveup', $integratedInputs)) {
                                    $space->setDriveUp(($request->get('driveup') == 'true') ? 1 : 0);
                                }

                                if (!in_array('outdoor_access', $integratedInputs)) {
                                    $space->setOutdoorAccess(($request->get('outdoor_access') == 'true') ? 1 : 0);
                                }
                            }

                            if ($request->get('change_door') == 'on' && !in_array('door_type', $integratedInputs)) {
                                $space->setDoorType($request->get('door_type')); // ROLL_UP,SWING,NONE
                            }

                            break;
                        case 'Wine Storage':
                        case 'Locker':
                            if ($request->get('change_dimensions')) {
                                if (!in_array('unit_height', $integratedInputs)) {
                                    $space->setHeight($request->get('unit_height'));
                                }

                                if (!in_array('door_height', $integratedInputs)) {
                                    $space->setDoorHeight($request->get('door_height'));
                                }

                                if (!in_array('door_width', $integratedInputs)) {
                                    $space->setDoorWidth($request->get('door_width'));
                                }
                            }

                            $space->setCovered(1); // covered is assumed

                            if ($request->get('change_amenities')) {
                                if (!in_array('climate', $integratedInputs)) {
                                    $space->setClimateControlled(($request->get('climate') == 'on') ? 1 : 0);
                                }

                                if (!in_array('alarm', $integratedInputs)) {
                                    $space->setAlarm(($request->get('alarm') == 'on') ? 1 : 0);
                                }

                                if (!in_array('humidity', $integratedInputs)) {
                                    $space->setHumidityControlled(($request->get('humidity') == 'on') ? 1 : 0);
                                }

                                if (!in_array('power', $integratedInputs)) {
                                    $space->setPower(($request->get('power') == 'on') ? 1 : 0);
                                }
                            }

                            if ($request->get('change_access')) {
                                if (!in_array('driveup', $integratedInputs)) {
                                    $space->setDriveUp(($request->get('driveup') == 'true') ? 1 : 0);
                                }

                                if (!in_array('outdoor_access', $integratedInputs)) {
                                    $space->setOutdoorAccess(($request->get('outdoor_access') == 'true') ? 1 : 0);
                                }
                            }

                            $space->setVehicle(0);

                            if ($request->get('change_door') == 'on' && !in_array('door_type', $integratedInputs)) {
                                $space->setDoorType($request->get('door_type')); // ROLL_UP,SWING,NONE
                            }

                            break;
                        default:
                            throw new \Exception('Could not match type of storage space');
                            break;
                    }

                    // populate fields that all types of units have

                    if ($request->get('change_dimensions') == 'on') {
                        if (!in_array('driveup', $integratedInputs)) {
                            $space->setDriveUp(($request->get('driveup') == 'true') ? 1 : 0);
                        }

                        ($request->request->has('unit_width')) ? $space->setWidth($request->get('unit_width')) : '';

                        ($request->request->has('unit_length')) ? $space->setLength($request->get('unit_length')) : '';
                    }

                    ($request->request->has('quantity')) ? $space->setQuantity($request->get('quantity')) : '';

                    if (
                        $request->get('change_location') == 'on'
                        && !in_array('floor', $integratedInputs)
                    ) {
                        $space->setFloor($request->get('floor'));
                    }

                    if (
                        $request->get('change_description') == 'on'
                        && !in_array('description', $integratedInputs)
                    ) {
                        $space->setDescription($request->get('description'));
                    }

                    if ($request->get('change_promotion') == 'on') {
                        if (strlen($request->get('promotion')) > 100) {
                            throw new \Exception('Promotion must be less than 100 characters.');
                        } else {
                            $space->setSpecialString($request->get('promotion'));
                        }
                    }

                    if ($request->get('change_pricing') == 'on') {
                        // only update/change regular price for manuals
                        if ($request->get('source_type') == \Genesis_Entity_Source::ID_MANUAL) {
                            $space->setRegularPrice($request->get('price_regular'));
                        }

                        ($request->get('deposit')) ? $space->setDeposit($request->get('deposit')) : '';

                        if ($request->request->has('price_sf')) {
                            if ($request->get('price_sf') <= $space->getRegularPrice()) {
                                $space->setSparefootPrice($request->get('price_sf'));
                            }
                        }

                        if ($request->request->has('price_sf') && $request->get('price_sf') <= $space->getRegularPrice()) {
                            $space->setSparefootPrice($request->get('price_sf'));
                        } elseif ($request->get('price_sf')) {
                            throw new \Exception('Error: SpareFoot price must be <= regular price.');
                        }
                    }

                    $this->_resetUnitIntegratedInputs($space, $spaceOriginal, $integratedInputs);
                    $savedSpace = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                }
            }

            return new Response('', 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * @return array
     *
     * @throws \Exception
     */
    private function _getIntegratedFields()
    {
        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);

        $integrationId = $facility->getCorporation()->getSourceId();

        // If not integrated, do nothing
        if ($integrationId == \Genesis_Entity_Source::ID_MANUAL) {
            return [];
        }

        $integratedFields = IntegratedFields::getBaseIntegratedFields($integrationId);

        if ($integrationId == \Genesis_Entity_Source::ID_SITELINK) {
            if (User::isFeatureActive(Features::SITELINK_STRIKETROUGH_PRICE)) {
                $integratedFields[] = IntegratedFields::$mappedFields['sparefootPrice'];
            }

            if (!$facility->getCorporation()->getPullPromos()) {
                $integratedFields = array_diff($integratedFields, ['promotion']);
            }
        }

        // It makes sure that returns an indexed array
        return array_values($integratedFields);
    }

    /**
     * Sets request parameters to actual DB values.
     *
     * This only applies on Integrated facilities based on Quicktagger validations.
     *
     * This is made to avoid user to modify the integrated input fields.
     *
     * @param Genesis_Entity_StorageUnit $modelInstance
     *
     * @return bool
     */
    private function _resetUnitIntegratedInputs($modelInstance, $modelInstanceCopy, $integratedFields)
    {
        // set integrated fields to actual DB values
        foreach ($integratedFields as $field) {
            $columnName = IntegratedFields::getDbColumnName($field);
            $getterMethod = 'get'.ucfirst($columnName);
            $setterMethod = 'set'.ucfirst($columnName);

            if (!is_null($columnName) && method_exists($modelInstance, $setterMethod)) {
                $modelValue = $modelInstanceCopy->{"get{$columnName}"}();
                $modelInstance->{$setterMethod}($modelValue);
            }
        }

        return true;
    }

    /**
     * https://myfoot.sparefoot.com/facility/siteverify
     * http://localhost:9019/facility/siteverify.
     */
    #[Route('/facility/siteverify', name: 'facility_site_verify')]
    public function siteVerifyAction(Request $request): Response
    {
        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;
        $this->view->scripts = ['facility/site-verify'];

        return $this->render('facility/site-verify.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * https://myfoot.sparefoot.com/facility/checksiteverify
     * http://localhost:9019/facility/checksiteverify.
     */
    #[Route('/facility/checksiteverify', name: 'facility_check_site_verify')]
    public function checksiteverifyAction(Request $request): Response
    {
        $testUrl = $request->get('url');
        $facilityId = $request->get('id');

        $facility = \Genesis_Service_Facility::loadById($facilityId);

        // check valid structure of a url
        if (strlen($testUrl) > 0) {
            if (!preg_match("/^[A-Za-z]+:\/\//", $testUrl)) {
                $testUrl = 'http://'.$testUrl;
            }

            if ($this->_validateUrl($testUrl)) {
                $facility->setUrl($testUrl);

                // check that this url has the sparefoot link
                if ($facility->hasSparefootLink()) {
                    $facility->setUrlVerified(1);
                } else {
                    $facility->setUrlVerified(0);
                    echo 'Error: the SpareFoot link was not found on your site';
                }
            } else {
                echo 'Error: The given URL does not appear to be valid';
            }
        } else {
            $facility->setUrlVerified(0);
            echo 'Error: Please enter a web address';
        }

        // write whatever data we validated
        $facility = \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

        return new Response('', 200, [
            'Content-Type' => 'text/plain',
        ]);
    }

    protected function getSideBarContent()
    {
        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Order::ASC('title'));
        $this->view->selectedFacilityId = $this->getSession()->facilityId;
        $this->view->loggedUser = $this->getLoggedUser();

        return $this->getTwig()->render('facility/left-sidebar-content.html.twig', [
            'view' => $this->view,
        ]);
    }

    protected function getTab(): string
    {
        return self::TAB_FACILITY;
    }

    /**
     * Fetch and organize all of the data to populate the facilities screen.
     *
     * Data comes back in form:
     *
     * array(
     *      facility_id => array (
     *          entity => Genesis_Entity_Facility,
     *          num_impressions => int,
     *          avg_position => double
     *      )
     * )
     *
     * @return array
     */
    private function _fetchFacilitiesData(?\Genesis_Db_Sqlable $s = null)
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities($s);
        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate();

        $facilityDetails = [];

        foreach ($facilities as $facility) {
            /** @var $facility \Genesis_Entity_Facility */
            if ($facility->getSourceId() == \Genesis_Entity_Source::ID_MANUAL) {
                $sourceName = 'MySpareFoot';
                if ($facility->getSelfReportedSourceId()) {
                    $sourceName .= ' ('.$facility->getSelfReportedSource()->getSource().')';
                }
            } else {
                $sourceName = $facility->getSource()->getName();
            }

            $consumerContacts = \Genesis_Service_ConsumerContact::loadUniqueByFacilityIdAndDateRange($facility->getId(), $startDate, $endDate);

            $array = [
                'id' => $facility->getId(),
                'title' => $facility->getTitleWithCompanyCode(),
                'code' => $facility->getCompanyCode(),
                'url' => \Genesis_Util_Url::facilityUrl($facility),
                'source_name' => $sourceName,
                'source_id' => $facility->getSourceId(),
                'self_reported_source_id' => $facility->getSelfReportedSourceId(),
                'bid_string' => $facility->isBidFlat() ? $facility->getBidString() : 'N/A',
                'num_impressions' => 0,
                'avg_position' => '-',
                'num_clicks' => 0,
                'num_reservations' => 0,
                'cost_per_reservation' => 0,
                'total_cost' => 0,
                'reservation_rate' => 0,
                'hidden' => !$facility->getActive(),
                'published' => $facility->getPublished(),
                'consumer_contacts' => $consumerContacts->count(),
            ];

            $facilityDetails[$facility->getId()] = array_merge(
                $array,
                \Genesis_Service_Reporting::getAllReservationDataByFacility($facility->getId(), $startDate, $endDate)
            );
        }

        return $facilityDetails;
    }

    /**
     * https://myfoot.sparefoot.com/facility/map
     * http://localhost:9019/facility/map.
     */
    #[Route('/facility/map', name: 'facility_map')]
    public function mapAction(Request $request): Response
    {
        $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;

        $facilityPov = \Genesis_Service_StreetViewPov::loadById($facility->getId());

        // send pov if it exists
        if ($facilityPov) {
            $this->view->latitude = $facilityPov->getLatitude();
            $this->view->longitude = $facilityPov->getLongitude();
            $this->view->pov = $facilityPov;
        } elseif ($facility->getLocationId()) {
            $this->view->latitude = $facility->getLocation()->getLatitude();
            $this->view->longitude = $facility->getLocation()->getLongitude();
        } else {
            $this->view->latitude = 0;
            $this->view->longitude = 0;
        }

        if ($_POST) {
            try {
                $streetView = new \Genesis_Entity_StreetViewPov();

                $streetView->setFacilityId($request->get('fid'));
                $streetView->setLatitude($request->get('lat'));
                $streetView->setLongitude($request->get('lng'));
                $streetView->setYaw($request->get('yaw'));
                $streetView->setPitch($request->get('pitch'));
                $streetView->setZoom($request->get('zoom'));
                $streetView->setShowStreetView(1);

                \Genesis_Service_StreetViewPov::save($streetView);

                $facilityPov = \Genesis_Service_StreetViewPov::loadById($facility->getId());
                $this->view->latitude = $facilityPov->getLatitude();
                $this->view->longitude = $facilityPov->getLongitude();
                $this->view->pov = $facilityPov;

                $this->view->alert = 'Changes saved.';
                $this->view->alertClass = 'success';
            } catch (\Exception $e) {
                $this->view->alert = '<strong<>Error</strong>: '.$e->getMessage();
                $this->view->alertClass = 'error';
            }
        }

        $this->view->scripts = ['facility/map'];

        return $this->render('facility/map.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Display the integration types to add a new faclity with
     * https://myfoot.sparefoot.com/facility/type
     * http://localhost:9019/facility/type.
     */
    #[Route('/facility/type', name: 'facility_type')]
    public function typeAction(Request $request): Response
    {
        dump('4726 here');
        exit;
        // confirm session vars are clear
        // $this->getSession()->set('facilityId', null);
        $this->getSession()->facIds = null;

        $account = $this->getLoggedUser()->getAccount();

        if ($account->getWholesale()) {
            throw new \Exception('Facilities can only be added to a Wholesale account in PITA.');
        }

        // comma separated list of integration types
        $this->view->accountId = $account->getId();
        $this->view->integrations = explode(',', $account->getIntegrationsString()); // MAN, SL, CS, QS, DOM
        $this->view->corporations = $account->getCorporations();

        $this->view->scripts = ['facility/type'];

        $this->view->newIntegrationChoices = \Genesis_Service_Source::load(
            \Genesis_Db_Restriction::equal('manualIntegration', 1)->setOrder(
                \Genesis_Db_Order::asc('source')
            )
        );

        return $this->render('facility/type.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * resyncs the passed in corporation & puts new facility ids in session.
     *
     * The js is checking for int (count of new facilities) or "0" as the response for no new facilities.
     * Any echo's here will break that function.
     *
     * Only return int or 0 to the caller
     *
     * @throws \Exception
     *
     * https://myfoot.sparefoot.com/facility/resync
     * http://localhost:9019/facility/resync
     */
    #[Route('/facility/resync', name: 'facility_resync')]
    public function resyncAction(Request $request): Response
    {
        try {
            if (!$request->get('corpId')) {
                throw new \Exception('No corp ID passed');
            }
            // load corp details for re-sync
            $corp = \Genesis_Service_Corporation::loadById($request->get('corpId'));

            // this can throw too, don't swallow it
            $facilityResult = \Genesis_Service_Sync::sync($corp);

            // return new facility id's
            $facs = $facilityResult->getNewFacilities();
            if (!$facs) {
                exit(print '0');
            }

            $client = new \Genesis_Client_Omnom();
            $facIds = [];
            // sync units
            // @todo this shouldn't be here, put this in a queue
            foreach ($facs as $fac) {
                $facIds[] = $fac->getId();
                try {   // let omnom errors go
                    $client->enqueueUnitPull($fac);
                } catch (\Exception $e) {
                    error_log($e->getMessage());
                }
            }

            $this->getSession()->facIds = implode(',', $facIds);

            // send emails if new facilities exist
            // @todo this shouldn't be here, put this in a queue
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log($e->getMessage());
                }
            }

            // return number new facilities as an int
            return new Response(count($facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process the  type of integration todo and fwd to the correct view
     * https://myfoot.sparefoot.com/facility/selectionhandler
     * http://localhost:9019/facility/selectionhandler.
     */
    #[Route('/facility/selectionhandler', name: 'facility_selection_handler')]
    public function selectionhandlerAction(Request $request): Response
    {
        $entity = \Genesis_Service_Source::loadByShortName(
            $request->get('integrationType')
        );

        // we don't have a facility to save to yet, so put this in the session for later
        if ($entity) {
            $this->getSession()->selfReportedSourceId = $entity->getId();
        }
        switch ($request->get('integrationType')) {
            case \Genesis_Entity_Source::ID_SITELINK:
            case 'sl_new':
                $this->forward('addsitelink');
                break;
            case \Genesis_Entity_Source::ID_CENTERSHIFT4:
            case 'cs4_new':
                $this->forward('addcentershift4');
                break;
            case \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
            case 'ssm':
                $this->forward('addselfstoragemanager');
                break;

            case \Genesis_Entity_Source::ID_QUIKSTOR:
                // @todo add the next button for the domico people
                $this->forward('addquickstore');
                break;

            case \Genesis_Entity_Source::ID_STOREDGE:
                $this->forward('addstoredge');
                break;

            case \Genesis_Entity_Source::ID_ESS:
                $this->forward('addeasystoragesolutions');
                break;
            default:
                $this->forward('add', 'facility', 'default', ['new' => true]);
                break;
        }

        $usesIssn = $request->get('usesIssn');
        if ($usesIssn == 1) {
            $this->forward('addissn');
        }

        return new Response('', 200, [
            'Content-Type' => 'text/plain',
        ]);
    }

    /**
     * Process the  type of integration todo and fwd to the correct view
     * https://myfoot.sparefoot.com/facility/addsitelink
     * http://localhost:9019/facility/addsitelink.
     */
    #[Route('/facility/addsitelink', name: 'facility_add_site_link')]
    public function addsitelinkAction()
    {
        $this->view->scripts = ['facility/addsitelink'];

        return $this->render('facility/addsitelink.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the SiteLink integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/syncsitelink
     * http://localhost:9019/facility/syncsitelink.
     */
    #[Route('/facility/syncsitelink', name: 'facility_sync_site_link')]
    public function syncsitelinkAction(Request $request): Response
    {
        $corpCode = $request->get('corpcode');
        $username = $request->get('username');
        $password = $request->get('password');

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($corpCode) > 0) {
                throw new \Exception('Please enter a corporation code.');
            }
            if (!strlen($username) > 0) {
                throw new \Exception('Please enter a user name.');
            }
            if (!strlen($password) > 0) {
                throw new \Exception('Please enter a password.');
            }

            // check to see if this integration already exists
            $sitelink_corp = \Genesis_Service_Corporation::load(\Genesis_Db_Restriction::equal('corpCode', $corpCode))->uniqueResult();

            // if corp doesn't already exist, create it
            if ($sitelink_corp) {
                throw new \Exception('We already have this SiteLink corporation code ('.$corpCode.') in an account.  Contact <EMAIL> for help.');
            } else {
                $sitelink_corp = new \Genesis_Entity_SitelinkCorporation();
                $sitelink_corp->setAccountId($account->getId());
                $sitelink_corp->setCorpname($account->getName());
                $sitelink_corp->setCreated(date('Y-m-d H:i:s', time()));
                $sitelink_corp->setSitelinkCorpCode($corpCode);
                $sitelink_corp->setSitelinkPassword($password);
                $sitelink_corp->setSitelinkUsername($username);

                $sitelink_corp = \Genesis_Service_Corporation::save($sitelink_corp);
            }

            // this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = \Genesis_Service_Sync::sync($sitelink_corp);
            } catch (\Exception $e) {
                // delete the corp if something messed up on sync
                \Genesis_Service_Corporation::delete($sitelink_corp);

                return new Response('Error: '.$e->getMessage()."\n", 400, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            if ($facilityResult->getErrors()) {
                $errorMessages = [];
                /** @var \Exception $e */
                foreach ($facilityResult->getErrors() as $i => $e) {
                    $errorMessages[] = 'Error #'.($i + 1).': '.$e->getMessage();
                }
                throw new \Exception('There were '.sizeof($facilityResult->getErrors())." error(s) syncing facilities: \n".implode("\n", $errorMessages));
            }

            // return new facility id's
            $facs = $facilityResult->getNewFacilities();

            $client = new \Genesis_Client_Omnom();

            $facIds = [];

            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    // kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (\Exception $e) {
                        // let go
                    }
                }
            } else {
                throw new \Exception('We did not find any facilities in sitelink.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log('Error: '.$e->getMessage());
                }
            }

            return new Response(implode(',', $facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process the CenterShift4 integration setup
     * https://myfoot.sparefoot.com/facility/addcentershift4
     * http://localhost:9019/facility/addcentershift4.
     */
    #[Route('/facility/addcentershift4', name: 'facility_add_centershift4')]
    public function addcentershift4Action(): Response
    {
        $this->view->cs4_csrf_token = CsrfUtil::getToken(self::ADD_CS4_FACILITY_TOKEN);
        $this->view->scripts = ['facility/addcentershift4'];

        return $this->render('facility/addcentershift4.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the CenterShift4 integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/synccentershift4
     * http://localhost:9019/facility/synccentershift4.
     */
    #[Route('/facility/synccentershift4', name: 'facility_sync_centershift4')]
    public function synccentershift4Action(Request $request): Response
    {
        $username = $request->get('username');
        $password = $request->get('pin');
        $dataCenter = $request->get('datacenter');
        $channel = $request->get('channel');
        $signature = $request->get('signature');
        $syncInactive = ((int) $request->get('syncInactive') == 1 ? false : true);

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!CsrfUtil::validateToken(self::ADD_CS4_FACILITY_TOKEN, $request->get('csrf_token'))) {
                throw new \Exception('Request structure is invalid. Refresh the page and try again.');
            }
            if (!strlen($username) > 0) {
                throw new \Exception('Please enter a username.');
            }
            if (!strlen($password) > 0) {
                throw new \Exception('Please enter a PIN.');
            }
            if (!strlen($signature) > 0) {
                throw new \Exception('Please enter your name in the signature box.');
            }

            // check to see if user already exists
            $centershift_corp = \Genesis_Service_Corporation::load(\Genesis_Db_Restriction::and_(\Genesis_Db_Restriction::equal('csPassword', $password), \Genesis_Db_Restriction::equal('csUsername', $username)))->uniqueResult();

            // if corp doesn't already exist, create it
            if ($centershift_corp) {
                throw new \Exception('We already an account with your centershift credentials('.$username.','.$password.').  Contact <EMAIL> for help.');
            } else {
                $centershift_corp = new \Genesis_Entity_Centershift4Corporation();
                $centershift_corp->setAccountId($account->getId());
                $centershift_corp->setCorpname($account->getName());
                $centershift_corp->setCreated(date('Y-m-d H:i:s', time()));
                $centershift_corp->setCsUsername($username);
                $centershift_corp->setCsPassword($password);
                $centershift_corp->setChannel($channel);
                $centershift_corp->setEndpoint($dataCenter);
                $centershift_corp->setSignature($signature);

                $client = new \Genesis_Client_Centershift4();

                if ($centershift_corp->getEndpoint() == 'den') {
                    // WARNING: DEN_WSDL constant may not exist in current Genesis version
                    // $centershift_corp->setWsdl(\Genesis_Client_Centershift4::DEN_WSDL);
                    // $client->switchWsdl($centershift_corp->getWsdl());
                }

                $csOrgs = $client->GetOrgList($username, $password, $channel);
                if ($csOrgs[0]) {
                    $centershift_corp->setOrgId($csOrgs[0]);
                }

                $centershift_corp = \Genesis_Service_Corporation::save($centershift_corp);
            }

            // this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = \Genesis_Service_Sync::sync($centershift_corp, null, $syncInactive);
            } catch (\Exception $e) {
                // delete the corp if something messed up on sync
                \Genesis_Service_Corporation::delete($centershift_corp);

                return new Response('Error: '.$e->getMessage()."\n", 400, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            // now we have to save corp id on the facilities that were sync'd
            $facs = $facilityResult->getNewFacilities();

            $client = new \Genesis_Client_Omnom();

            $facIds = [];

            // only try to sync units if we got some facility results
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    // kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (\Exception $e) {
                        // let go
                    }
                }
            } else {
                throw new \Exception('We did not find any facilities in centershift.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log('Error: '.$e->getMessage());
                }
            }

            return new Response(implode(',', $facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process the SelfStorageManager integration setup
     * https://myfoot.sparefoot.com/facility/addselfstoragemanager
     * http://localhost:9019/facility/addselfstoragemanager.
     */
    #[Route('/facility/addselfstoragemanager', name: 'facility_add_self_storage_manager')]
    public function addselfstoragemanagerAction(): Response
    {
        $this->view->scripts = ['facility/addselfstoragemanager'];

        return $this->render('facility/addselfstoragemanager.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the SelfStorageManager integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/syncselfstoragemanager
     * http://localhost:9019/facility/syncselfstoragemanager.
     */
    #[Route('/facility/syncselfstoragemanager', name: 'facility_sync_self_storage_manager')]
    public function syncselfstoragemanagerAction(Request $request): Response
    {
        $wsdlAddress = $request->get('wsdl');
        $username = $request->get('username');
        $password = $request->get('password');

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($wsdlAddress) > 0) {
                throw new \Exception('Please enter a WSDL address.');
            }
            if (!strlen($username) > 0) {
                throw new \Exception('Please enter a user name.');
            }
            if (!strlen($password) > 0) {
                throw new \Exception('Please enter a password.');
            }

            // check to see if this integration already exists
            $ssm_corp = \Genesis_Service_Corporation::load(\Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('accountId', $account->getId()),
                \Genesis_Db_Restriction::equal('wsdl', $wsdlAddress),
                \Genesis_Db_Restriction::equal('ssmUsername', $username)
            ))->uniqueResult();

            // if corp doesn't already exist, create it
            if (!$ssm_corp) {
                $ssm_corp = new \Genesis_Entity_SelfStorageManagerCorporation();
                $ssm_corp->setAccountId($account->getId());
                $ssm_corp->setCorpname($account->getName());
                $ssm_corp->setCreated(date('Y-m-d H:i:s', time()));
                $ssm_corp->setWsdl($wsdlAddress);
                $ssm_corp->setSsmPassword($password);
                $ssm_corp->setSsmUsername($username);
                $ssm_corp = \Genesis_Service_Corporation::save($ssm_corp);
            } elseif ($ssm_corp->getSsmPassword() !== $password) {
                // update password if different
                $ssm_corp->setSsmPassword($password);
            }

            // this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = \Genesis_Service_Sync::sync($ssm_corp);
            } catch (\Exception $e) {
                // delete the corp if something messed up on sync
                \Genesis_Service_Corporation::delete($ssm_corp);

                return new Response('Error: '.$e->getMessage()."\n", 400, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            // return new facility id's
            $facs = $facilityResult->getNewFacilities();
            $client = new \Genesis_Client_Omnom();

            $facIds = [];
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    // kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (\Exception $e) {
                        // let go
                    }
                }
            } else {
                throw new \Exception('We did not find any facilities in SelfStorageManager.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log('Error: '.$e->getMessage());
                }
            }

            return new Response(implode(',', $facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process the QuickStore integration setup
     * https://myfoot.sparefoot.com/facility/addquickstore
     * http://localhost:9019/facility/addquickstore.
     */
    #[Route('/facility/addquickstore', name: 'facility_add_quickstore')]
    public function addquickstoreAction(): Response
    {
        // QuickStore integration setup - currently empty implementation
        return $this->render('facility/addquickstore.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the StorEDGE integration setup
     * https://myfoot.sparefoot.com/facility/addstoredge
     * http://localhost:9019/facility/addstoredge.
     */
    #[Route('/facility/addstoredge', name: 'facility_add_storedge')]
    public function addstoredgeAction(): Response
    {
        $this->view->scripts = ['facility/addstoredge'];

        return $this->render('facility/addstoredge.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the EasyStorageSolutions integration setup
     * https://myfoot.sparefoot.com/facility/addeasystoragesolutions
     * http://localhost:9019/facility/addeasystoragesolutions.
     */
    #[Route('/facility/addeasystoragesolutions', name: 'facility_add_easy_storage_solutions')]
    public function addeasystoragesolutionsAction(): Response
    {
        $this->view->scripts = ['facility/addeasystoragesolutions'];

        return $this->render('facility/addeasystoragesolutions.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the StorEDGE integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/syncstoredge
     * http://localhost:9019/facility/syncstoredge.
     */
    #[Route('/facility/syncstoredge', name: 'facility_sync_storedge')]
    public function syncstoredgeAction(Request $request): Response
    {
        $corpCode = $request->get('corpcode');
        $username = \Genesis_Client_Storedge::SPAREFOOT_API_KEY;
        $password = \Genesis_Client_Storedge::SPAREFOOT_API_SECRET;

        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($corpCode) > 0) {
                throw new \Exception('Please enter a corporation code.');
            }

            // check to see if this integration already exists
            $storedge_corp = \Genesis_Service_Corporation::load(\Genesis_Db_Restriction::equal('corpCode', $corpCode))->uniqueResult();

            // if corp doesn't already exist, create it
            if ($storedge_corp) {
                throw new \Exception('We already have this storEDGE corporation code ('.$corpCode.') in an account.  Contact <EMAIL> for help.');
            } else {
                $storedge_corp = new \Genesis_Entity_StoredgeCorporation();
                $storedge_corp->setAccountId($account->getId());
                $storedge_corp->setCorpname($account->getName());
                $storedge_corp->setCreated(date('Y-m-d H:i:s', time()));
                $storedge_corp->setCorpCode($corpCode);
                $storedge_corp->setPassword($password);
                $storedge_corp->setUsername($username);

                $storedge_corp = \Genesis_Service_Corporation::save($storedge_corp);
            }

            // this determines which type of sync do (centershift/sitelink...) to and syncs all facilities
            try {
                $facilityResult = \Genesis_Service_Sync::sync($storedge_corp);
            } catch (\Exception $e) {
                // delete the corp if something messed up on sync
                \Genesis_Service_Corporation::delete($storedge_corp);

                return new Response('Error: '.$e->getMessage()."\n", 400, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            if ($facilityResult->getErrors()) {
                $errorMessages = [];
                /** @var \Exception $e */
                foreach ($facilityResult->getErrors() as $i => $e) {
                    $errorMessages[] = 'Error #'.($i + 1).': '.$e->getMessage();
                }
                throw new \Exception('There were '.sizeof($facilityResult->getErrors())." error(s) syncing facilities: \n".implode("\n", $errorMessages));
            }

            // return new facility id's
            $facs = $facilityResult->getNewFacilities();

            $client = new \Genesis_Client_Omnom();

            $facIds = [];

            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();

                    // kick off the unit sync
                    try {
                        $response = $client->enqueueUnitPull($fac);
                    } catch (\Exception $e) {
                        // let go
                    }
                }
            } else {
                throw new \Exception('We did not find any facilities in storEDGE.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log('Error: '.$e->getMessage());
                }
            }

            return new Response(implode(',', $facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    private function getPhidoClient(): Client
    {
        if (!$this->phidoClient) {
            $this->phidoClient = new Client();
        }

        return $this->phidoClient;
    }

    /**
     * @throws Exception
     */
    private function isEssIdentifierValid(string $essIdentifier): bool
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO')."/ess/facility/$essIdentifier/lookup";

        $response = $client->get($urlString, [
            'headers' => ['x-sparefoot-app' => 'myfoot'],
        ]);

        return $response->getStatusCode() == 200;
    }

    /**
     * @throws Exception
     */
    public function updateFacilityFromESS(int $manualFacilityId, string $essIdentifier, int $corporationId, int $userId): ResponseInterface
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO').'/update/ess/facility';

        return $client->put($urlString, [
            'headers' => ['x-sparefoot-app' => 'myfoot'],
            'json' => [
                'manualFacilityId' => $manualFacilityId,
                'essUniqueIdentifier' => $essIdentifier,
                'corporationId' => $corporationId,
                'userId' => $userId,
            ],
        ]);
    }

    /**
     * @throws Exception
     */
    public function triggerUnitSync(int $facilityId): ResponseInterface
    {
        $client = $this->getPhidoClient();
        $urlString = getenv('URL_PHIDO')."/sync/units/$facilityId";

        return $client->get($urlString, [
            'headers' => ['x-sparefoot-app' => 'myfoot'],
        ]);
    }

    /**
     * Process the EasyStorageSolutions integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/synceasystoragesolutions
     * http://localhost:9019/facility/synceasystoragesolutions.
     */
    #[Route('/facility/synceasystoragesolutions', name: 'facility_sync_easy_storage_solutions')]
    public function synceasystoragesolutionsAction(Request $request): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        // Strip out any non alpha numeric chars
        $essIdentifier = preg_replace('/[^A-Za-z0-9]/', '', $request->get('essidentifier'));

        try {
            if (!isset($essIdentifier) || empty($essIdentifier)) {
                throw new \Exception('Easy Storage Solutions invalid identifier. Contact <EMAIL> for help.');
            }

            // Since facility doesn't exist, corp doesn't exist
            // Fetch data from Marketplace API, create facility and corp
            try {
                $essFacilityValid = $this->isEssIdentifierValid($essIdentifier);
                if (!$essFacilityValid) {
                    throw new \Exception('Easy Storage Solutions invalid identifier. Contact <EMAIL> for help.');
                }

                // Create corporation
                $newEssCorp = new \Genesis_Entity_EasyStorageSolutionsCorporation();
                $newEssCorp->setAccountId($account->getId());
                $newEssCorp->setCorpname($account->getName());
                $newEssCorp->setCreated(date('Y-m-d H:i:s', time()));
                $newEssCorp->setCorpCode($essIdentifier);
                $newEssCorp = \Genesis_Service_Corporation::save($newEssCorp);

                // Use 400 Brooks Rd, Perryville AK for now; Phido will update this to whatever location ESS sends
                $location = \Genesis_Service_Location::loadById(self::PERRYVILLE_DUMMY_LOCATION);

                // Create facility
                $newEssFacility = new \Genesis_Entity_Facility();
                $newEssFacility->setCorporationId($newEssCorp->getId());
                $newEssFacility->setSourceId(\Genesis_Entity_Source::ID_ESS);
                $newEssFacility->setExternalId($essIdentifier);
                $newEssFacility->setLocation($location);
                $newEssFacility->setLocationId($location->getId());

                // This is what makes the new ESS Facility show in the dropdown
                // How would we determine how these are set?
                $newEssFacility->setActive(0);
                $newEssFacility->setPublished(1);
                $newEssFacility->setApproved(1);

                // Save this facility early so we can have access to the facility_id for office hours
                $newEssFacility = \Genesis_Service_Facility::save($newEssFacility);

                $this->updateFacilityFromESS($newEssFacility->getId(), $essIdentifier, $newEssCorp->getId(), $this->getLoggedUser()->getId());
                $this->triggerUnitSync($newEssFacility->getId());

                return new Response(implode(',', [$newEssFacility->getId()]), 200, [
                    'Content-Type' => 'text/plain',
                ]);
            } catch (BadResponseException $e) {
                $response = json_decode($e->getResponse()->getBody()->getContents(), true);

                if (isset($response['message'])) {
                    throw new \Exception($response['message']);
                }

                throw new \Exception('Unexpected error encountered. <NAME_EMAIL> for help.');
            } catch (\Exception $e) {
                throw new \Exception('Unexpected error encountered. <NAME_EMAIL> for help.');
            }
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process the facility add summary
     * https://myfoot.sparefoot.com/facility/addsummary
     * http://localhost:9019/facility/addsummary.
     */
    #[Route('/facility/addsummary', name: 'facility_add_summary')]
    public function addsummaryAction(Request $request): Response
    {
        $facIdArry = [];

        // if the new facilities are from an integration sync
        if ($request->get('syncd_fac_ids')) {
            $facilityIds = $request->get('syncd_fac_ids');

            // put these ids in the session so we know what the new facilities are
            $this->getSession()->facIds = $facilityIds;

            $facIdArry = explode(',', $facilityIds);
            $this->view->facIds = $facIdArry;

            $facility = \Genesis_Service_Facility::loadById($facIdArry[0]); // ok since all facs are same integration
        } elseif ($this->getSession()->facIds) {
            $facilityIds = $this->getSession()->facIds;
            $facIdArry = explode(',', $facilityIds);
            $this->view->facIds = $facIdArry;

            $facility = \Genesis_Service_Facility::loadById($facIdArry[0]);
        } elseif ($this->getSession()->facilityId) {
            // if the new facility was from an manual add
            // only a manual add would have set the facility in the session
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $this->view->facility = $facility;
            $this->view->facIds = '';
        } else {
            throw new \Exception('Could not determine what integration new facilities came from!');
        }

        // pass facilities to setup to the view
        $this->view->facilities = \Genesis_Service_Facility::load(\Genesis_Db_Restriction::equal('id', $facility->getId()));

        if (count($facIdArry) > 0) {
            $this->view->facilities = \Genesis_Service_Facility::load(\Genesis_Db_Restriction::in('id', $facIdArry));
        }

        // tell the back button were to go
        switch ($facility->getSourceId()) {
            case \Genesis_Entity_Source::ID_MANUAL:
                $this->view->backView = 'add';
                break;
            case \Genesis_Entity_Source::ID_SITELINK:
            case \Genesis_Entity_Source::ID_CENTERSHIFT4:
            case \Genesis_Entity_Source::ID_STOREDGE:
            case \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER:
            case \Genesis_Entity_Source::ID_OPENTECH:
            case \Genesis_Entity_Source::ID_ESS:
            case \Genesis_Entity_Source::ID_CENTERSHIFT:
                $this->view->backView = 'type';
                break;
            default:
                throw new \Exception('Could not determine what integration new facilities came from!');
        }

        $account = $this->getLoggedUser()->getAccount();

        $billableEntities = $account->getBillableEntities()->toArray();

        $output = [];

        // get each payment details from NS
        foreach ($billableEntities as $be) {
            if ((int) $be->getPublished() == 0) {
                continue;
            }

            $output[$be->getId()]['id'] = $be->getId();

            // Comment out this entire switch statement if you're trying to make new Facilities locally.
            switch ($be->getTerms()) {
                case 'Credit Card':
                    // get the credit card to display last for of CC in display string
                    try {
                        $cc = $be->getCreditCard();
                    } catch (\Exception $e) {
                        // nothing
                    }

                    if ($cc instanceof \Genesis_Entity_Netsuite_CreditCard) {
                        $output[$be->getId()]['displayStr'] = 'CC - '.$cc->getCcNumber().', '.$be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = 'CC - No Card On File, '.$be->getNsName();
                    }
                    break;
                case 'ACH':
                    $output[$be->getId()]['displayStr'] = 'ACH - '.$be->getNsName();
                    break;
                case 'Net 30':
                    $output[$be->getId()]['displayStr'] = 'Net 30 - '.$be->getNsName();
                    break;
                case 'Net 10':
                    $output[$be->getId()]['displayStr'] = 'Net 10 - '.$be->getNsName();
                    break;
                default:
                    $output[$be->getId()]['displayStr'] = 'n/a';
                    break;
            }
        }

        $this->view->payment_methods = $output;

        $this->view->scripts = ['facility/addsummary'];

        return $this->render('facility/addsummary.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process adding products to facilities
     * https://myfoot.sparefoot.com/facility/addproducts
     * http://localhost:9019/facility/addproducts.
     */
    #[Route('/facility/addproducts', name: 'facility_add_products')]
    public function addproductsAction(Request $request): RedirectResponse
    {
        $this->getLoggedUser()->getAccount();

        if ($this->getSession()->facilityId) {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->facilityId);
            $facility->setCpa(true);
            $facility->setActive(true);
            $facility = \Genesis_Service_Facility::save($facility, $this->getLoggedUser());

            $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
            if (
                $facility->getSourceId() == \Genesis_Entity_Source::ID_MANUAL
                || $isFSS
            ) {
                $nextStep = $this->generateUrl('features_inventory').'?fid='.$this->getSession()->facilityId; // fid is already in session so no need to pass
            }
        } else {
            // pass along the facility id's from the integration
            if ($request->get('syncd_fac_ids')) {
                $facilityIds = $request->get('syncd_fac_ids');
                $facIdArry = explode(',', $facilityIds);
                $facilities = \Genesis_Service_Facility::load(\Genesis_Db_Restriction::in('id', $facIdArry))->toArray();
                foreach ($facilities as $facility) {
                    $facility->setCpa(true);
                    $facility->setActive(true);
                    \Genesis_Service_Facility::save($facility, $this->getLoggedUser());
                }
            } else {
                throw new \Exception('Could not get the facility list that were synced!');
            }
            $nextStep = '/user';
        }

        return $this->redirect($nextStep);
    }

    private function _validateUrl($url)
    {
        // Taken from http://php.net/manual/en/function.preg-match.php
        // PHP's filter_var() sounds nice, but it's broken

        $nastyUrlRegex = "((https?)\:\/\/)?"; // SCHEME
        $nastyUrlRegex .= "([a-zA-Z0-9+!*(),;?&=\$_.-]+(\:[a-zA-Z0-9+!*(),;?&=\$_.-]+)?@)?"; // User and Pass
        $nastyUrlRegex .= "([a-zA-Z0-9-.]*)\.([a-zA-Z]{2,6})"; // Host or IP
        $nastyUrlRegex .= "(\:[0-9]{2,5})?"; // Port
        $nastyUrlRegex .= "(\/([a-zA-Z0-9+\$_-]\.?)+)*\/?"; // Path
        $nastyUrlRegex .= "(\?[a-zA-Z+&\$_.-][a-zA-Z0-9;:@&%=+\/\$_.-]*)?"; // GET Query
        $nastyUrlRegex .= '(#[a-zA-Z_.-][a-zA-Z0-9+$_.-]*)?'; // Anchor

        return (preg_match("/^$nastyUrlRegex$/", $url)) ? true : false;
    }

    /**
     * Set billing start dates on facility
     * Happens when they add a new facility
     * https://myfoot.sparefoot.com/facility/setbillingstartdate
     * http://localhost:9019/facility/setbillingstartdate.
     */
    #[Route('/facility/setbillingstartdate', name: 'facility_set_billing_start_date')]
    public function setbillingstartdateAction(Request $request): Response
    {
        $product = $request->get('prod');
        $facId = $request->get('facId');

        $facility = \Genesis_Service_Facility::loadById($facId);

        switch ($product) {
            case 'cpa':
                if ($facility->getCpa()) {
                    $prodMeta = new \Genesis_Entity_FacilityProductsMeta();
                    $prodMeta->setFacilityId($facId);
                    $prodMeta->setBillingStartDate(date('Y-m-d'));
                    $prodMeta->setProduct('AD_NETWORK');
                    \Genesis_Service_FacilityProductsMeta::save($prodMeta, true, $this->getLoggedUser());
                }
                break;
            case 'geo':
                if ($facility->getHostedWebsite()) {
                    $prodMeta = new \Genesis_Entity_FacilityProductsMeta();
                    $prodMeta->setFacilityId($facId);
                    $prodMeta->setBillingStartDate(date('Y-m-d'));
                    $prodMeta->setProduct('HOSTED_WEBSITE');
                    \Genesis_Service_FacilityProductsMeta::save($prodMeta, true, $this->getLoggedUser());
                }
                break;
            default:
                break;
        }

        return new Response('', 200, [
            'Content-Type' => 'text/plain',
        ]);
    }

    /**
     * For a prefix of 'Address: ', would return a string like:
     * Address: address line 1
     *          address line 2
     *          city, state, zip
     *
     * @param Genesis_Entity_Location $location
     */
    private function formatAddress(string $prefix, \Genesis_Entity_Location $location): string
    {
        $prefixLength = strlen($prefix);
        $linePrefixPaddingAfterFirst = str_repeat(' ', $prefixLength);

        $addressLines = [];
        $usePrefixPadding = false;

        if ($location->getAddress1()) {
            $addressLines[] = $location->getAddress1();
            $usePrefixPadding = true;
        }
        if ($location->getAddress2()) {
            $line = '';
            if ($usePrefixPadding) {
                $line .= $linePrefixPaddingAfterFirst;
            }
            $line .= $location->getAddress2();
            $addressLines[] = $line;
            $usePrefixPadding = true;
        }

        $line = '';
        if ($usePrefixPadding) {
            $line .= $linePrefixPaddingAfterFirst;
        }
        $zip = $location->getZip();
        $line .= $location->getCity().', '.$location->getState().($zip ? ' '.$zip : '');
        $addressLines[] = $line;

        return implode(PHP_EOL, $addressLines);
    }

    private function formatFacilityForEmail(\Genesis_Entity_Facility $facility): string
    {
        /**
         * @var Genesis_Entity_Location
         */
        $location = $facility->getLocation();

        return 'Facility Name: '.$facility->getTitle().PHP_EOL.
            $this->formatAddress('Facility Address: ', $location).PHP_EOL.
            'Facility ID: '.$facility->getId().PHP_EOL.
            'Facility Bid: '.$facility->getBidValue().PHP_EOL;
    }

    public function formatFooterForNewFacilityEmail(\Genesis_Entity_UserAccess $user): string
    {
        return PHP_EOL.
            'Email: '.$user->getEmail().PHP_EOL.
            PHP_EOL.
            'mysparefoot_new-facility_7yikV2'.PHP_EOL;
    }

    // send email to sales team when new facilities are added to an existing account

    /**
     * @param $facilities array|Genesis_Entity_Facility
     *
     * @throws \Exception
     */
    private function notifyOnNewFacilities($facilities)
    {
        $user = $this->getLoggedUser();
        // short circuit: do not send email if user is god level access on myfoot
        if ($user->isMyFootGod()) {
            return;
        }

        $emailMessage = new \Genesis_Entity_EmailMessage();
        $emailMessage->setName('new_facility_sales_notification');
        $emailMessage->setMarketingMail(0);
        $body = '';

        switch ($facilities) {
            case is_array($facilities): // for SiteLink and Centershift
                $subject = '';
                $count = 0;
                foreach ($facilities as $facility) {
                    ++$count;
                    if (empty($subject)) {
                        $accountName = $facility->getAccount()->getName();
                        $subject = "$accountName - New Facilities Added";
                    }
                    $body .= $this->formatFacilityForEmail($facility);
                }
                if ($count > 0) {
                    $body .= 'Number of new facilities: '.$count.PHP_EOL;
                    $body .= $this->formatFooterForNewFacilityEmail($user);
                    $emailMessage->setSubject($subject);
                    $emailMessage->setBody($body);
                }
                break;
            case $facilities instanceof \Genesis_Entity_Facility: // for manuals
                $facility = $facilities; // reassign name
                $facilityName = $facility->getTitle();
                $emailMessage->setSubject("$facilityName - New Facility Notice");
                $body = $this->formatFacilityForEmail($facility).
                    $this->formatFooterForNewFacilityEmail($user);
                $emailMessage->setBody($body);
                break;
            default:
                throw new \Exception('Invalid facilities type given.');
                break;
        }

        $emailMessage->validateInput();
        \Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $emailMessage, [], 'Sales Team', '<EMAIL>');
        \Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $emailMessage, [], 'Sales Team', '<EMAIL>');
    }

    /**
     * Process the ISSN integration setup
     * https://myfoot.sparefoot.com/facility/addissn
     * http://localhost:9019/facility/addissn.
     */
    #[Route('/facility/addissn', name: 'facility_add_issn')]
    public function addissnAction(): Response
    {
        $this->view->scripts = ['facility/addissn'];

        return $this->render('facility/addissn.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Process the ISSN integration sync
     * NOTE: this is also called in the signup process, if you change it, test signup!
     * https://myfoot.sparefoot.com/facility/syncissn
     * http://localhost:9019/facility/syncissn.
     */
    #[Route('/facility/syncissn', name: 'facility_sync_issn')]
    public function syncissnAction(Request $request): Response
    {
        $opentechAccountId = $request->get('accountId');
        $account = $this->getLoggedUser()->getAccount();

        try {
            if (!strlen($opentechAccountId) > 0) {
                throw new \Exception('Please enter an Account Id.');
            }

            // check to see if user already exists

            $opentech_corp = \Genesis_Service_Corporation::load(\Genesis_Db_Restriction::equal('corpCode', $opentechAccountId))->uniqueResult();

            // if corp doesn't already exist, create it
            if ($opentech_corp) {
                throw new \Exception('We already an account with your opentech account id.  Contact <EMAIL> for help.');
            } else {
                $opentech_corp = new \Genesis_Entity_OpentechCorporation();
                $opentech_corp->setAccountId($account->getId());
                $opentech_corp->setCorpname($account->getName());
                $opentech_corp->setCreated(date('Y-m-d H:i:s', time()));
                $opentech_corp->setOpentechAccountId($opentechAccountId);

                $opentech_corp = \Genesis_Service_Corporation::save($opentech_corp);
            }

            // this determines which type of sync do (centershift/sitelink) to and syncs all facilities
            try {
                $facilityResult = \Genesis_Service_Sync::sync($opentech_corp);
            } catch (\Exception $e) {
                // delete the corp if something messed up on sync
                \Genesis_Service_Corporation::delete($opentech_corp);

                return new Response('Error: '.$e->getMessage()."\n", 400, [
                    'Content-Type' => 'text/plain',
                ]);
            }

            $facs = $facilityResult->getNewFacilities();
            $facIds = [];

            // only try to sync units if we got some facility results
            if ($facilityResult->getNumNewFacilities() > 0 && $facs) {
                foreach ($facs as $fac) {
                    $facIds[] = $fac->getId();
                }
            } else {
                throw new \Exception('We did not find any facilities in opentech.  <NAME_EMAIL> if you need help.');
            }

            // send emails if new facilities exist
            if ($facilityResult->getNumNewFacilities() > 0) {
                try {
                    $this->notifyOnNewFacilities($facs);
                } catch (\Exception $e) {
                    error_log('Error: '.$e->getMessage());
                }
            }

            return new Response(implode(',', $facIds), 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage()."\n", 400, [
                'Content-Type' => 'text/plain',
            ]);
        }
    }

    /**
     * Process facility listings
     * https://myfoot.sparefoot.com/listings
     * http://localhost:9019/listings.
     */
    #[Route('/listings', name: 'features_listings')]
    public function listingsAction(Request $request): Response|RedirectResponse
    {
        $this->view->action = 'listings';
        $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        $isFSS = $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET;
        $isManual = $facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_MANUAL;

        // Redirect NON-Manual and NON-FSS
        if (!$isManual && !$isFSS) {
            return $this->redirect($this->generateUrl('features_units').'?fid='.$facility->getId());
        }

        $this->setCommonViewFields();

        $this->view->facility = $facility;
        $this->view->customClosuresBlogPost = 'https://support.sparefoot.com/hc/en-us/articles/*********-Setting-Holiday-Hours-Custom-Closures-for-Your-Facility';
        $this->view->covidModal = User::isFeatureActive(Features::COVID_MODAL);
        $this->view->customClosures = User::isFeatureActive(Features::CUSTOM_CLOSURES);
        $this->view->accountId = $request->get('account_id') ?? null;

        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features',
        ];

        return $this->render('layout-singlepage.html.twig', [
            'view' => $this->view,
        ]);
    }
}
