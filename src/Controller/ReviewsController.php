<?php

namespace Sparefoot\MyFootService\Controller;

use LightnCandy\LightnCandy;
use Sparefoot\EmailsServiceClient\Client\EmailsClient;
use Sparefoot\EmailsServiceClient\Util\Campaign;
use Sparefoot\EmailsServiceClient\Util\Email;
use Sparefoot\EmailsServiceClient\Util\Sender;
use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\Review;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReviewsController extends AbstractRestrictedController
{
    protected function _init(): ?RedirectResponse
    {
        $restriction = \Genesis_Db_Restriction::equal('published', 1);
        $restriction->setOrder(\Genesis_Db_Order::asc('title'));
        $this->view->facilities = $facilities = $this->getLoggedUser()->getManagableFacilities($restriction);
        if (!count($facilities)) {
            return $this->redirectToRoute('features_add_first');
        }
        $this->view->numManageableFacilities = count($facilities->toArray());
        $this->view->facilities->rewind();
        $this->view->banner = [
            'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount()),
        ];
        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/reviews
     * http://localhost:9019/reviews
     */
    #[Route('/reviews', name: 'reviews_index')]
    public function indexAction(Request $request): Response
    {
        $session = User::getSession($request);
        $facilityId = $request->query->get('fid') ?: $session->get('facilityId');
        $user = $this->getLoggedUser();

        // Check if the user has access to the facility
        if (!$user->isMyFootGod() && !in_array($facilityId, $user->getManageableFacilityIds())) {
            throw new \Exception('Unauthorized access to facility.', 403);
        }
        $this->view->q = $request->query->get('q', '');
        if (User::isFeatureActive(Features::REVIEWS_SINGLE_PAGE)) {
            // START for single page
            $this->initViewForSinglePageApp();
            // END for single page
            // Preload the Facility to embed at page load
            $facilityId = $session->get('facilityId');
            $facilityEntity = \Genesis_Service_Facility::loadById($facilityId);
            $fields = [
                'title',
                'company_code',
                'active',
            ];
            $facility = Facility::toArrayWithFields($facilityEntity, $fields);
            $this->view->facility = $facility;

            $this->view->scripts = [
                '../dist/ember/features/assets/vendor',
                '../dist/ember/features/assets/features',
            ];
            $this->view->accountId = $request->query->get('account_id') ?: null;

            return $this->render('layout-singlepage.html.twig', [
                'view' => $this->view,
            ], new Response('', 200, ['Content-Type' => 'text/html']));
        } else {
            if ($request->query->get('fid')) {
                $this->view->facility = \Genesis_Service_Facility::loadById($request->query->get('fid'));

                $restriction = \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('listingAvailId', $this->view->facility->getId()),
                    \Genesis_Db_Restriction::isNull('parentId')
                );
            } else {
                $restriction = \Genesis_Db_Restriction::isNull('parentId');
            }

            if ($request->query->get('q')) {
                $this->view->reviews = \Genesis_Service_Review::loadBySearch($this->getLoggedUser(), $request->query->get('q'), $restriction);
                $this->view->q = $request->query->get('q');
            } else {
                $this->view->reviews = \Genesis_Service_Review::loadReviewsByRestrictedUserId($this->getLoggedUser(), $restriction);
                $this->view->q = '';
            }
            $this->view->title = 'Reviews';

            $this->view->scripts = ['reviews/index'];
        }

        $this->view->accountId = $request->query->get('account_id') ?: null;

        return $this->render('reviews/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/reviews/detail
     * http://localhost:9019/reviews/detail
     */
    #[Route('/reviews/detail', name: 'reviews_detail')]
    public function detailAction(Request $request): Response
    {
        $this->view->review = \Genesis_Service_Review::loadById($request->query->get('rid'));
        $this->view->scripts = ['reviews/detail'];

        return $this->render('reviews/detail.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/reviews/response
     * http://localhost:9019/reviews/response
     */
    #[Route('/reviews/response', name: 'reviews_response')]
    public function responseAction(Request $request): JsonResponse
    {
        $output = ['success' => 1];

        try {
            if ($request->request->get('response') && $request->request->get('parent_id')) {
                $parentReview = \Genesis_Service_Review::loadById($request->request->get('parent_id'));
                if (empty($parentReview)) {
                    \Genesis_Util_ErrorLogger::errorHandler(E_ERROR, 'Cannot load parent review with ID: '.$request->request->get('parent_id'), __FILE__, __LINE__);
                    throw new \Exception('Cannot load parent review with ID: '.$request->request->get('parent_id'));
                }

                $user = $this->getLoggedUser();

                // Check if the logged-in user has access to the facility
                if (!$user->isMyFootGod() && !in_array($parentReview->getFacilityId(), $user->getManageableFacilityIds())) {
                    throw new \Exception('Unauthorized access to facility.', 403);
                }

                $review = new \Genesis_Entity_Review();
                $review->setListingAvailId($parentReview->getFacilityId());
                $review->setTitle("Manager's Response");
                $review->setMessage($request->request->get('response'));
                $review->setNickname('Manager Response');
                $review->setRating(0);
                $review->setIpAddress(ip2long($_SERVER['REMOTE_ADDR']));
                $review->setSource('MyFootResponse');
                $review->setParentId($parentReview->getId());
                $review->setUserId($user->getId());
                $review->setStatus(\Genesis_Entity_Review::STATUS_PENDING);
                $review->setSiteId(0);
                if (isset($_COOKIE['visit'])) {
                    $review->setVisitId($_COOKIE['visit']);
                }

                $updatedReview = \Genesis_Service_Review::save($review, false, $user);
                $output['review'] = Review::toArray($updatedReview);
            }
        } catch (\Exception $e) {
            $output['success'] = 0;
            $output['msg'] = $e->getMessage();
        }

        return new JsonResponse($output);
    }

    /**
     * Request reviews from customers via email
     * Sample: https://myfoot.sparefoot.com/reviews/request.
     *
     * GET:  Display form to enter email addresses
     * POST: Process email addresses and send review request emails
     */
    #[Route('/reviews/request', name: 'reviews_request')]
    public function requestAction(Request $request): Response
    {
        $session = User::getSession($request);
        $facilityId = $request->get('fid') ?: $session->get('facilityId');
        $this->view->facility = \Genesis_Service_Facility::loadById($facilityId);

        $esClient = new EmailsClient();
        $this->view->alertMessage = '';
        $this->view->emailFailures = [];
        $this->view->emails = '';
        $this->view->alert = null;
        $this->view->alertClass = null;
        if ($request->isMethod('POST')) {
            try {
                $this->view->emails = $request->get('emails');

                $facId = $request->get('facility_id');
                $emails = $request->get('emails');

                $facility = \Genesis_Service_Facility::loadById($facId);

                if (!$facility) {
                    throw new \Exception('We could not find your facility in our system.');
                }

                if (!$emails) {
                    throw new \Exception('Please enter email addresses separated by commas.');
                }

                if (strlen($emails) > 2500) {
                    throw new \Exception('Maximum length of 2500 was exceeded; please enter fewer emails at a time.');
                }

                $emails = str_replace(';', ',', $emails);
                $emails = str_replace("\n", ',', $emails);
                $emails = preg_replace('/\s+/', ',', $emails);
                $emails = explode(',', $emails);
                $emails = array_unique($emails);

                $invalidEmails = [];

                foreach ($emails as $email) {
                    if (trim($email) == '') {
                        continue;
                    }
                    // throw exception for invalid email addresses
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $invalidEmails[] = $email;
                    }
                }

                if ($invalidEmails) {
                    $exceptionText = 'Invalid email address(es) detected: ';
                    foreach ($invalidEmails as $invalidEmail) {
                        $exceptionText .= $invalidEmail.', ';
                    }
                    throw new \Exception($exceptionText);
                }
                $emailFailures = [];
                // send review request email to each address
                foreach ($emails as $email) {
                    // continue if empty
                    if (trim($email) == '') {
                        continue;
                    }

                    // create a user for anyone not in the system
                    $user = \Genesis_Service_User::loadByEmail($email);
                    if (!$user) {
                        $newUser = new \Genesis_Entity_User();
                        $newUser->setEmail(trim(strtolower($email)));
                        $user = \Genesis_Service_User::save($newUser);
                    }

                    // If user is not associated with a moved-in reservation, skip
                    $transaction = \Genesis_Service_Transaction::loadByEmail($email,
                        \Genesis_Db_Restriction::and_(
                            \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                            \Genesis_Db_Restriction::equal('bookingState', \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                        )
                    )->uniqueResult();
                    if (!$transaction) {
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'you can only request reviews from tenants who have successfully moved in.',
                        ];
                        continue;
                    }

                    // If user has a booking and move in was in the last 30 days, skip
                    $transaction = \Genesis_Service_Transaction::loadByEmail($email,
                        \Genesis_Db_Restriction::and_(
                            \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                            \Genesis_Db_Restriction::greaterThan('moveIn', date('Y-m-d', strtotime('-30 days')))
                        )
                    )->uniqueResult();
                    if ($transaction) {
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'they haven\'t been in the facility for over 30 days',
                        ];
                        continue;
                    }

                    // If user already left a review at this facility, skip
                    $review = \Genesis_Service_Review::loadByFacilityId($facility->getId(),
                        \Genesis_Db_Restriction::equal('userId', $user->getId()))->uniqueResult();
                    if ($review) {
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'they\'ve already left a review',
                        ];
                        continue;
                    }

                    // TODO find out what this means
                    // If user is affiliated with an account, skip
                    $ua = \Genesis_Service_UserAccess::loadById($user->getId());
                    if ($ua && $ua->getAccountId()) {
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'they\'re a SpareFoot facility client like yourself',
                        ];
                        continue;
                    }

                    try {
                        // CRED-155 - Converted consumer/review request to consumer-review-request via EmailsService
                        $emailMessage = new Email($email, Sender::SPAREFOOT, Campaign::CONSUMER_REVIEWREQUEST);
                        $emailMessage->setMergeVars([
                            ['name' => 'facility', 'content' => \Genesis_Util_EmailMapper::facilityToArray($facility)],
                            ['name' => 'admin', 'content' => \Genesis_Util_EmailMapper::userToArray($this->getLoggedUser())],
                        ]);

                        $emailMessage->setParams([
                            'admin_id' => $this->getLoggedUser()->getUserId(),
                            'facility_id' => $facility->getId(),
                        ]);

                        $esClient->sendEmailSqs($emailMessage);
                    } catch (\Exception $e) {
                        // On the do not email list, continue
                        $emailFailures[] = [
                            'email' => $email,
                            'error' => 'they\'ve requested not to receive emails',
                        ];
                        continue;
                    }
                }

                $logger = new \Genesis_Util_ActionLogger();
                $logger->logAction('myfoot_reviews_send_request', null, null, $this->getLoggedUser()->getId(), $facId, null);
                $this->view->emailFailures = $emailFailures;
                $this->view->alertType = 'success';
                $this->view->alertMessage = '<strong>Sent!</strong> Keep those good reviews flowing in.';
            } catch (\Exception $e) {
                $this->view->alertType = 'danger';
                $this->view->alertMessage = '<strong>Oops!</strong> '.$e->getMessage();
            }
        }
        // CRED-155 & CRED-326 - Rendering the email Preview
        $this->view->emailTemplate = $this->getEmailPreview($esClient, $this->view->facility);

        $this->view->scripts = ['reviews/request'];

        return $this->render('reviews/request.html.twig', ['view' => $this->view]);
    }

    /**
     * This function will instantiate an EmailsServiceClient, retrieve the consumer-review-request template for sparefoot,
     * then render with the currently loggedIn userInfo and a faked transaction object that just has the current facility
     * defined.  It also returns only the innerHtml of the body of the html document.
     *
     * @param EmailsClient             $client
     * @param \Genesis_Entity_Facility $facility
     *
     * @return string The rendered HTML preview of the email
     */
    protected function getEmailPreview($client, $facility)
    {
        try {
            // CRED-155 & CRED-326
            $template = $client->getTemplate(Sender::SPAREFOOT, Campaign::CONSUMER_REVIEWREQUEST);
            // Parsing the innerHtml of the body tag
            $startBodyTag = strpos($template['markup'], '<body');
            $endBodyTag = strpos($template['markup'], '>', $startBodyTag) + 1;
            $end = strpos($template['markup'], '</body>');
            $template = substr($template['markup'], $endBodyTag, $end - $endBodyTag);
            // Preparing template
            $tmpl = LightnCandy::compile($template);
            /** @var callable $renderer */
            $renderer = LightnCandy::prepare($tmpl);

            return $renderer([
                'facility' => \Genesis_Util_EmailMapper::facilityToArray($facility),
                'admin' => \Genesis_Util_EmailMapper::userToArray($this->getLoggeduser()),
            ]);
        } catch (\Exception $e) {
            return 'Failed to retrieve email template';
        }
    }
}
