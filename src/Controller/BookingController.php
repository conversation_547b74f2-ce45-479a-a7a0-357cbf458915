<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\Booking;
use Sparefoot\MyFootService\Service\UserAuthByBooking;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class BookingController extends AbstractRestrictedController
{
    private $booking;
    private $user;

    public function _init(): ?RedirectResponse
    {
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features',
        ];

        // Inject the Auth Booking Token into the Page
        $secret = $this->getRequest()->get('s');
        $bookingConfirmationCode = $this->getRequest()->get('confirmation_code');
        $email = $this->getRequest()->get('email');
        $this->view->authBookingToken = UserAuthByBooking::serializeToken($secret, $bookingConfirmationCode, $email);

        $this->user = \Genesis_Service_User::loadByEmail($email);

        $confirmation_code = $this->getRequest()->get('confirmation_code');
        $this->booking = $booking = Booking::validateAndGetBooking($confirmation_code);
        $this->view->facility = $booking->getFacility();

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/move-in?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/move-in?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/move-in', name: 'booking_move_in')]
    public function moveInAction(): Response
    {
        return $this->render('booking/move-in.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/remind-me-later?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/remind-me-later?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/remind-me-later', name: 'booking_remind_me_later', methods: ['POST'])]
    public function remindMeLaterAction(): Response
    {
        $this->booking->setBookingDataAttr(\Genesis_Entity_BookingData::POST_MOVE_IN_EMAIL_REMINDER_DATE, date('Y-m-d', strtotime('+10 day')));
        \Genesis_Service_Transaction::updateBookingData($this->booking);

        // Action Log
        $this->booking->facilityConfirmFutureMovein($this->user);

        return $this->render('booking/remind-me-later.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/booking/deny?s=SECRET&confirmation_code=CODE&email=EMAIL
     * http://localhost:9019/booking/deny?s=SECRET&confirmation_code=CODE&email=EMAIL
     */
    #[Route('/booking/deny', name: 'booking_deny')]
    public function denyAction(): Response
    {
        return $this->render('booking/deny.html.twig', [
            'view' => $this->view,
        ]);
    }
}
