<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\SignupCode;
use Sparefoot\MyFootService\Security\CustomAuthenticator;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Utils\CsrfUtil;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Authentication\UserAuthenticatorInterface;

class SignupStartController extends AbstractPublicController
{
    public const SIGNUP_COMPANY_TOKEN = 'signup_company_token';
    public const SIGNUP_CSRF_TOKEN = 'signup_token';

    private $tokenStorage;
    private UserAuthenticatorInterface $userAuthenticator;
    private CustomAuthenticator $authenticator;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        UserAuthenticatorInterface $userAuthenticator,
        CustomAuthenticator $authenticator,
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->userAuthenticator = $userAuthenticator;
        $this->authenticator = $authenticator;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-start
     * http://localhost:9019/signup-start
     */
    #[Route('/signup-start', name: 'signup_start_index')]
    public function indexAction(): Response
    {
        return $this->redirectToRoute('signup_start_code');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-start/code
     * http://localhost:9019/signup-start/code
     */
    #[Route('/signup-start/code', name: 'signup_start_code')]
    public function codeAction(Request $request): Response
    {
        $this->view->action = 'code';
        $this->view->scripts = ['signup-start/code'];
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_CSRF_TOKEN);

        $this->view->signupCode = $request->getSession()->get('code') ? $request->getSession()->get('code')->getCode() : '';
        $this->view->userId = $request->getSession()->get('userId') ?? 0;

        return $this->render('signup-start/code.html.twig', ['view' => $this->view]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-start/company
     * http://localhost:9019/signup-start/company
     */
    #[Route('/signup-start/company', name: 'signup_start_company', methods: ['GET', 'POST'])]
    public function companyAction(Request $request): Response
    {
        $session = User::getSession($request);
        // IMPORTANT: For only this time, the token for the login process will be generated on another controller
        $this->view->action = 'company';
        $this->view->login_csrf_token = CsrfUtil::getToken('login_csrf_token');
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_COMPANY_TOKEN);
        $this->view->scripts = ['signup-start/company'];
        $this->view->backlink = '/signup-start/code/';
        $this->view->signupCode = $session->get('code') ? $session->get('code')->getCode() : '';
        $this->view->userId = $session->get('userId');

        // Prepopulate if account id (only happens when back button is used)
        $user = $session->get('userId') ? \Genesis_Service_UserAccess::loadById($session->get('userId')) : null;
        $this->view->first = $user ? $user->getFirstName() : $session->get('firstName');
        $this->view->last = $user ? $user->getLastName() : $session->get('lastName');
        $this->view->phone = $user ? $user->getPhone() : $session->get('phone');
        $this->view->email = $user ? $user->getEmail() : $session->get('email');
        $this->view->userId = $user ? $user->getId() : null;

        $account = false;
        if ($user && $user instanceof \Genesis_Entity_UserAccess) {
            $account = $user->getAccount();
        } else {
            if (!$session->get('code')) {
                // throw new \Exception('We cannot determine your sign up code. Please go back and enter a valid sign up code.');
            }
        }
        $this->view->companyName = $account ? $account->getName() : $session->get('companyName');

        $location = false;
        if ($account) {
            $location = $account->getLocation();
        }

        $this->view->address = $location ? $location->getAddress1() : $session->get('companyAddress');
        $this->view->city = $location ? $location->getCity() : $session->get('companyCity');
        $this->view->state = $location ? $location->getState() : $session->get('companyState');
        $this->view->zip = $location ? $location->getZip() : $session->get('companyZip');

        $this->view->action = 'company';

        return $this->render('signup-start/company.html.twig', [
            'view' => $this->view,
        ], new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-start/validate-code
     * http://localhost:9019/signup-start/validate-code
     */
    #[Route('/signup-start/validate-code', name: 'signup_start_validate_code', methods: ['POST'])]
    public function validateCodeAction(Request $request): JsonResponse
    {
        if (!CsrfUtil::validateToken(self::SIGNUP_CSRF_TOKEN, $request->get('csrf_token'))) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Request structure is invalid. Refresh the page and try again.',
            ]);
        }

        // Return true if the user already made an account. you can't change terms
        $session = $request->getSession();
        $user = $session->get('userId') ? \Genesis_Service_UserAccess::loadById($session->get('userId')) : null;
        if ($user && $user instanceof \Genesis_Entity_UserAccess && $user->getAccount() && $user->getAccount()->getBidType()) {
            return new JsonResponse(['success' => true]);
        }

        try {
            /*
            // Sample valid signup codes
$validCodes = [
    'C5P1',  // CPA (Percent) with 1.5 multiplier
    'R0X2',  // Residual at rate of 20%
    'C52P1', // CPA (Percent) with 1.25 multiplier
    'CN5P1', // CPA with insights disabled
    'R1X5',  // Residual at rate of 51%
];
*/
            $code = new SignupCode($request->get('signup_code'));
            $session->set('code', $code);

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage(),
                'message2' => $request->get('csrf_token'),
            ]);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-start/add-company
     * http://localhost:9019/signup-start/add-company
     */
    #[Route('/signup-start/add-company', name: 'signup_start_add_company', methods: ['POST'])]
    public function addCompanyAction(Request $request): JsonResponse
    {
        $session = User::getSession($request);

        try {
            $csrfToken = $request->request->get('csrf_token');
            $session->set('csrf_token', $csrfToken);

            if (!CsrfUtil::validateToken(self::SIGNUP_COMPANY_TOKEN, $csrfToken)) {
                throw new \Exception('Request structure is invalid. Refresh the page and try again.');
            }

            $companyName = $request->request->get('company_name');
            $companyAddress = $request->request->get('address');
            $companyCity = $request->request->get('city');
            $companyState = $request->request->get('state');
            $companyZip = $request->request->get('zip');
            $signupCode = $session->get('code');
            $password = trim($request->request->get('password'));
            $passwordConfirm = trim($request->request->get('password_confirm'));
            $firstName = trim($request->request->get('first_name'));
            $lastName = trim($request->request->get('last_name'));
            $phone = preg_replace('/[^0-9]/', '', $request->request->get('phone'));
            $email = trim($request->request->get('email'));

            // Store in session
            $session->set('companyName', $companyName);
            $session->set('companyAddress', $companyAddress);
            $session->set('companyCity', $companyCity);
            $session->set('companyState', $companyState);
            $session->set('companyZip', $companyZip);
            $session->set('firstName', $firstName);
            $session->set('lastName', $lastName);
            $session->set('phone', $phone);
            $session->set('email', $email);

            // If we have a user, and the password does not match... BOOM
            $user = \Genesis_Service_User::loadByEmail($email);
            if ($user && \Genesis_Service_User::loadByLogin($email, $password) === null) {
                throw new \Exception("The account for '{$email}' is associated with a different password.");
            }

            $userAccess = \Genesis_Service_UserAccess::loadByEmail($email);
            // Check to see if the email used is a god admin account, throw an exception if so
            if ($userAccess && $userAccess->getMyfootRole() === \Genesis_Entity_UserAccess::ROLE_GOD) {
                throw new \Exception('Admins cannot be signed up as facilities.');
            }

            // This will only exist if they have come back from terms page and are re-submitting
            $userId = $session->get('userId');

            if ($password !== $passwordConfirm) {
                throw new \Exception('Passwords are not the same.');
            }
            if (strlen($password) < 6) {
                throw new \Exception('Password must be longer than 6 characters.');
            }

            if (!(strlen($companyZip) > 0) || !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $companyZip)) {
                throw new \Exception('Please enter a valid zip code.');
            }

            if (
                !(strlen($phone) > 0) || preg_match('/[a-zA-Z\.]/', $phone)
                || !preg_match('/[0-9]{7,14}/', preg_replace('/[^0-9]/', '', $phone))
            ) {
                throw new \Exception($phone.' is an invalid phone number');
            }

            // Create user and user access
            $userAccess = $this->_signupUser([
                'email' => $email,
                'firstName' => $firstName,
                'lastName' => $lastName,
                'password' => $password,
                'phone' => $phone,
            ]);

            // Set the userid in session
            $session->set('userId', $userAccess->getId());

            \Genesis_Service_UserAccess::updateLastLoggedIn($userAccess);

            // Create a new account as long as they weren't here because of the back button
            if (!$userId || !$userAccess->getAccount()) {
                $account = new \Genesis_Entity_Account();
                $account->setCpa(1);
                $account->setGeopage(0);
                $account->setBillingVerified(0);
                $account->setTimeCreated(date('Y-m-d H:i:s', time()));
                $signupCode->configureAccount($account, $userAccess);
            } else {
                $account = $userAccess->getAccount();
            }

            $account->setName($companyName);
            // $locationId = 55;
            // // Create a new location
            // if (getenv('SF_ENV') == 'local') {
            //     $location = \Genesis_Service_Location::loadById($locationId);
            // } else {
            //     $location = \Genesis_Service_Location::loadByAddress($companyAddress, $companyCity, $companyState, $companyZip);
            //     if (!$location) {
            //         $location = \Genesis_Service_Location::geoCodePhysicalAddress($companyAddress.' '.$companyCity.' '.$companyState.' '.$companyZip);
            //     }
            // }

            // $location = \Genesis_Service_Location::save($location);
            // $locationId = $location->getId();
            // $account->setLocationId($locationId);
            try {
                // local http can not call Google api, and missing keys
                if (getenv('SF_ENV') != 'local') {
                    $location = \Genesis_Service_Location::loadByAddress($companyAddress, $companyCity, $companyState, $companyZip);
                    if (!$location) {
                        $location = \Genesis_Service_Location::geoCodePhysicalAddress($companyAddress.' '.$companyCity.' '.$companyState.' '.$companyZip);
                    }

                    $location = \Genesis_Service_Location::save($location);
                    $locationId = $location->getId();
                    $account->setLocationId($locationId);
                }
            } catch (\Exception $e) {
                // do nothing, we will try to create the location below
            }
            // create a new location

            $account = \Genesis_Service_Account::save($account);

            // Update account id on user access
            $userAccess->setAccountId($account->getId());
            $userAccess = \Genesis_Service_UserAccess::save($userAccess);
            $session->set('userId', $userAccess->getId());

            // Log signup
            $logger = new \Genesis_Util_ActionLogger();
            $logger->logChanges(new \Genesis_Entity_Account(), $account);
            $logger->logAction('account_signup_code', null, $signupCode->getCode(), $userAccess, null, $account->getId());

            // Create the security user
            $securityUser = new \Sparefoot\MyFootService\Security\User($userAccess);

            // Use Symfony's built-in authentication
            $this->userAuthenticator->authenticateUser(
                $securityUser,
                $this->authenticator,
                $request
            );

            // Initialize user account facility context
            // UserAccountFacilityContext::init($userAccess->getId());

            // // Handle remember-me functionality
            // if ($request->request->get('remember_me')) {
            //     UserRememberMe::getSetRememberMe(true, $email);
            // }

            // Remove the manual token creation code
            // dump($this->getUser()); // This should now work
            // exit;

            return new JsonResponse(['success' => 1]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => 0, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Create user and user access account.
     */
    private function _signupUser(array $params): \Genesis_Entity_UserAccess
    {
        // A user might already exist if this person's booked with SpareFoot before.
        // If this is the case, use the existing user; otherwise, make a new one.
        $user = \Genesis_Service_User::load(\Genesis_Db_Restriction::equal('email', $params['email']))->uniqueResult();
        if (!$user) {
            $user = new \Genesis_Entity_User();
        }

        $user->setUsername(preg_replace('/[^A-Za-z0-9]/', '', $params['email']));
        $user->setFirstName($params['firstName']);
        $user->setLastName($params['lastName']);
        $user->setRawPassword($params['password']);
        $user->setEmail($params['email']);
        $user->setPhone($params['phone']);
        $user = \Genesis_Service_User::save($user);

        // Only make a user access profile if they don't already have one
        $acctMgmtUser = $user->getUserAccess();
        if (!$acctMgmtUser) {
            $acctMgmtUser = new \Genesis_Entity_UserAccess();
            $acctMgmtUser->setUserId($user->getId());
            $acctMgmtUser->setMyfootRole(\Genesis_Entity_UserAccess::ROLE_ADMIN); // Person signing up should be admin
            $acctMgmtUser->setGetsStatements(1);
            $acctMgmtUser->setGetsEmails(1);
            $acctMgmtUser->setAllFacilities(1);

            $acctMgmtUser = \Genesis_Service_UserAccess::save($acctMgmtUser);
        } else {
            // TODO: what do we do if they already have a user access profile?
            // Could be they used the back button or were formerly employed at another storage company
        }

        return $acctMgmtUser;
    }
}
