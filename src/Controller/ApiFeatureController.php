<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiFeatureController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/features
     * http://localhost:9019/api/features
     */
    #[Route('/api/features{slash}', name: 'api_features_index', requirements: ['slash' => '/?'], defaults: ['slash' => ''], methods: ['GET', 'POST', 'PUT'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        /**
         * @var $feature \Genesis_Entity_Feature
         */
        $results = [];
        foreach (self::loadFeatures() as $feature) {
            // $results[$feature->getFeature()][$feature->getKey()] = [];
            $results[$feature->getFeature()][$feature->getKey()] = $feature->getValue();
            // $results[$feature->getFeature()][$feature->getKey()]['url'] = '/api/features/name/'.$feature->getFeature().'/'.$feature->getKey();
        }

        return $this->json(['data' => $results], 200, [], ['json_encode_options' => JSON_PRETTY_PRINT]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/features/name/FEATURE_NAME/KEY_NAME
     * http://localhost:9019/api/features/name/FEATURE_NAME/KEY_NAME
     */
    #[Route('/api/features/name/{one}/{two}/{three}{slash}', name: 'api_features_name', requirements: ['slash' => '/?'], defaults: ['slash' => '', 'three' => null], methods: ['GET', 'PUT'])]
    public function nameAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        if (!$request->get('one')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'name required');
        }
        $name = $request->get('one');
        if (!$request->get('two')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'key required');
        }
        $key = $request->get('two');
        /**
         * @var $feature \Genesis_Entity_Feature
         */
        $feature = \Genesis_Service_Feature::load(
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::like('feature', $name),
                \Genesis_Db_Restriction::like('key', $key)
            )
        )->current();
        if (!$feature) {
            return $this->json(['empty' => 1]);
        }
        $value = $request->get('three');
        if ($value !== null) {
            if (!$this->getLoggedUser()) {
                throw new ApiException(ApiException::UNAUTHORIZED, 'must be signed in');
            }
            if (!call_user_func([$this->getLoggedUser(), 'isMyFootGod'])) {
                throw new ApiException(ApiException::UNAUTHORIZED, 'only gods can modify');
            }
            /* @var \Genesis_Entity_Feature $feature */
            $feature->setValue($value);
            $feature = \Genesis_Service_Feature::save($feature);
        }

        return $this->json(['data' => self::serialize($feature)]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/features/id/123
     * http://localhost:9019/api/features/id/123
     */
    #[Route('/api/features/id/{one}/{two}{slash}', name: 'api_features_id', requirements: ['slash' => '/?'], defaults: ['slash' => '', 'two' => null], methods: ['GET', 'PUT'])]
    public function idAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $id = $request->get('one');
        if (!$id) {
            throw new ApiException(ApiException::BAD_REQUEST, 'id required');
        }

        /**
         * @var $feature \Genesis_Entity_Feature
         */
        $feature = \Genesis_Service_Feature::load(
            \Genesis_Db_Restriction::equal('id', $id)
        )->current();
        $value = $request->get('two');
        if ($value !== null) {
            if (!$this->getLoggedUser()) {
                throw new ApiException(ApiException::UNAUTHORIZED, 'must be signed in');
            }
            if (!call_user_func([$this->getLoggedUser(), 'isMyFootGod'])) {
                throw new ApiException(ApiException::UNAUTHORIZED, 'only gods can modify');
            }
            /* @var \Genesis_Entity_Feature $feature */
            $feature->setValue($value);
            $feature = \Genesis_Service_Feature::save($feature);
        }
        /**
         * @var $feature \Genesis_Entity_Feature
         */
        $response = self::serialize($feature);

        return $this->json(['data' => $response], 200, [], ['json_encode_options' => JSON_PRETTY_PRINT]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/features/diff/prod
     * http://localhost:9019/api/features/diff/prod
     */
    #[Route('/api/features/diff/{one}{slash}', name: 'api_features_diff', requirements: ['slash' => '/?'], defaults: ['slash' => ''])]
    public function diffAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        if (\Genesis_Config_Server::isProduction()) {
            throw new \Exception('diff not possible to prod');
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $server = $request->get('one');
        switch ($server) {
            case 'dev':
            case 'development':
            case 'moreyard':
            case 3:
                $url = 'https://my.sparefoot.moreyard.com/api/features';
                break;
            case 'stage':
            case 2:
            case 'staging':
            case 'extrameter':
                $url = 'https://my.sparefoot.extrameter.com/api/features';
                break;
            case 'production':
            case 'prod':
            case 1:
            default:
                $url = 'https://my.sparefoot.com/api/features';
        }
        curl_setopt($ch, CURLOPT_URL, $url);
        $serverResponse = curl_exec($ch);
        $serverSettings = json_decode($serverResponse, true);
        if (!$serverSettings) {
            throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, 'unable to get settings from '.$url);
        }
        $serverSettings = $serverSettings['data'];
        $diff = [];
        foreach (self::loadFeatures() as $feature) {
            /** @var $feature \Genesis_Entity_Feature */
            $name = $feature->getFeature();
            $key = $feature->getKey();
            $value = $feature->getValue();
            if (!array_key_exists($name, $serverSettings)) {
                $diff[$name][$key] = [
                    'remote' => '<unset>',
                    'local' => $feature->getValue(),
                ];
                continue;
            }
            if (!array_key_exists($key, $serverSettings[$name])) {
                $diff[$name][$key] = [
                    'remote' => '<unset>',
                    'local' => $feature->getValue(),
                ];
                continue;
            }
            if ($serverSettings[$name][$key] != $value) {
                $diff[$name][$key] = [
                    'remote' => $serverSettings[$name][$key],
                    'local' => $value,
                ];
            }
            unset($serverSettings[$name][$key]);
        }
        // set any remainders
        foreach ($serverSettings as $name => $set) {
            foreach ($set as $key => $value) {
                $diff[$name][$key] = [
                    'remote' => $value,
                    'local' => '<unset>',
                ];
            }
        }

        return $this->json(['data' => $diff, 'meta' => ['server' => $url]], 200, [], ['json_encode_options' => JSON_PRETTY_PRINT]);
    }

    private function serialize(\Genesis_Entity_Feature $feature)
    {
        return [$feature->getFeature() => [
            $feature->getKey() => [
                'value' => $feature->getValue(),
                'url' => '/api/features/name/'.$feature->getFeature().'/'.$feature->getKey(),
            ],
        ]];
    }

    private function loadFeatures()
    {
        return $features = \Genesis_Service_Feature::load(
            \Genesis_Db_Restriction::or_(
                \Genesis_Db_Restriction::like('feature', 'myfoot.%'),
                \Genesis_Db_Restriction::like('feature', 'genesis.%')
            )->setOrder(\Genesis_Db_Order::asc('feature')->asc('key'))
        );
    }
}
