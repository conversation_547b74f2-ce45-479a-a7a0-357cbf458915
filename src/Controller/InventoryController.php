<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class InventoryController extends AbstractRestrictedController
{
    protected function _init(): ?RedirectResponse
    {
        $session = User::getSession($this->getRequest());
        $facilityId = -1;
        $actionName = $this->getControllerActionName($this->getRequest());
        $needRedirect = (self::isBlacklisted($actionName) && false);

        if ($this->getRequest()->get('fid')) {
            $facilityId = $this->getRequest()->get('fid');
        } elseif ($session->get('facilityId')) {
            $facilityId = $session->get('facilityId');

            if ($needRedirect) {
                $session->set('needsRedirectTo', 'features_'.$actionName);
                $session->set('redirectFacilityId', $facilityId);

                return $this->redirectToRoute('features_'.$actionName, ['fid' => $facilityId]);
            }
        } else {
            //facility ID was not passed in via the query string, nor was it in the session...redirect to facility home
            if ($needRedirect) {
                $session->set('facilityId', null);
                // Note: forward() typically doesn't return a RedirectResponse, you may need to use redirectToRoute instead
                // $this->forward('list');
                return $this->redirectToRoute('features_list');
            }
        }

        $session->set('facilityId', ($facilityId == -1 ? null : $facilityId));

        if ($this->getRequest()->get('search_term')) {
            $session->set('searchTerm', $this->getRequest()->get('search_term'));
        }

        if ($this->getRequest()->get('clear_search_term')) {
            $session->set('searchTerm', null);
        }
        
        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory
     * http://localhost:9019/inventory
     */
    #[Route('/inventory', name: 'inventory_index')]
    public function indexAction(Request $request): Response
    {
        $session = User::getSession($request);
        $session->set('facilityId', null);

        return $this->redirectToRoute('inventory_units');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/custom-promotion
     * http://localhost:9019/inventory/custom-promotion
     */
    #[Route('/inventory/custom-promotion', name: 'inventory_custom_promotion', methods: ['POST'])]
    public function customPromotionAction(Request $request): JsonResponse
    {
        try {
            $session = User::getSession($request);
            $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));

            if (!$facility) {
                throw new \Exception('Cannot load facility for id: '.$session->get('facilityId'));
            }
            $this->view->facility = $facility;

            $special = $this->_buildCustomSpecialFromParams($request);

            // this returns an existing special if there's an identical one already saved
            $special = \Genesis_Service_Special::save($special, $this->getLoggedUser());

            // Instead of setparam, call applySpecialAction with special ID
            $request->request->set('special_id', $special->getId());

            return $this->applySpecialAction($request);
        } catch (\Exception $e) {
            $output = [
                'success' => 0,
                'message' => $e->getMessage(),
            ];

            return new JsonResponse($output);
        }
    }

    private function _buildCustomSpecialFromParams(Request $request): \Genesis_Entity_Special
    {
        $special = new \Genesis_Entity_Special();

        if ($request->request->get('promotion_type')) {
            switch ($request->request->get('promotion_type')) {
                case 'percent_off':
                    if ($request->request->get('promotion_percent') <= 0 || $request->request->get('promotion_percent') > 100) {
                        throw new \Exception('Percent must be between 1 and 100.');
                    }
                    $special->setType(\Genesis_Entity_Special::TYPE_PROMO_PERCENT);
                    $special->setPercentOff(number_format($request->request->get('promotion_percent') / 100, 2));
                    $special->setRequiresPrepaidMonths($request->request->get('promotion_restrictions_prepaid_months'));
                    $special->setRequiresMinimumLeaseMonths($request->request->get('promotion_restrictions_minimum_lease_length'));
                    break;
                case 'dollar_off':
                    if ($request->request->get('promotion_dollar') <= 0) {
                        throw new \Exception('Price must be greater than zero.');
                    }
                    $special->setType(\Genesis_Entity_Special::TYPE_PROMO_DOLLAR);
                    $special->setDollarOff(number_format($request->request->get('promotion_dollar'), 2));
                    $special->setRequiresPrepaidMonths($request->request->get('promotion_restrictions_prepaid_months'));
                    $special->setRequiresMinimumLeaseMonths($request->request->get('promotion_restrictions_minimum_lease_length'));
                    break;
                case 'dollar_override':
                    if ($request->request->get('promotion_override') <= 0) {
                        throw new \Exception('Price must be greater than zero.');
                    }
                    $special->setType(\Genesis_Entity_Special::TYPE_PROMO_OVERRIDE);
                    $special->setDollarOverride(number_format($request->request->get('promotion_override'), 2));
                    $special->setRequiresPrepaidMonths($request->request->get('promotion_restrictions_prepaid_months'));
                    $special->setRequiresMinimumLeaseMonths($request->request->get('promotion_restrictions_minimum_lease_length'));
                    break;
                default:
                    throw new \Exception('Please select a promotion type');
            }

            $promoMonths = $request->request->get('promotion_months');
            if (!$promoMonths) {
                throw new \Exception('To build a promotion, you must specify the months it applies to');
            }
            // Handle promotion months if available
            $promoMonths = $request->request->get('promo_months', []);
            if (is_array($promoMonths) && !empty($promoMonths)) {
                $special->setMonths(implode(',', $promoMonths));
            }
        }

        if ($request->request->get('discount_type')) {
            switch ($request->request->get('discount_type')) {
                case 'percent_off':
                    if ($request->request->get('discount_percent', 0) <= 0 || $request->request->get('discount_percent') > 100) {
                        throw new \Exception('Percent must be between 1 and 100.');
                    }
                    $special->setType(\Genesis_Entity_Special::TYPE_DISCOUNT_PERCENT);
                    $special->setPercentOff(number_format($request->request->get('discount_percent') / 100, 2));
                    break;
                case 'dollar_off':
                    if ($request->request->get('discount_dollar', 0) <= 0) {
                        throw new \Exception('Price must be greater than zero.');
                    }
                    $special->setType(\Genesis_Entity_Special::TYPE_DISCOUNT_DOLLAR);
                    $special->setDollarOff(number_format($request->request->get('discount_dollar'), 2));
                    break;
                    /*
                    case 'dollar_override':
                        if ($request->request->get('discount_override') <= 0) {
                            throw new \Exception("Price must be greater than zero.");
                        }
                        $special->setType(\Genesis_Entity_Special::TYPE_DISCOUNT_OVERRIDE);
                        $special->setDollarOverride(number_format($request->request->get('discount_override'),2));
                        break;
                    */
                default:
                    throw new \Exception('Unknown discount type');
            }
        }

        return $special;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/custom-discount
     * http://localhost:9019/inventory/custom-discount
     */
    #[Route('/inventory/custom-discount', name: 'inventory_custom_discount', methods: ['POST'])]
    public function customDiscountAction(Request $request): JsonResponse
    {
        try {
            $session = User::getSession($request);
            $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));

            if (!$facility) {
                throw new \Exception('Cannot load facility for id: '.$session->get('facilityId'));
            }
            $this->view->facility = $facility;

            $special = $this->_buildCustomSpecialFromParams($request);

            // this returns an existing special if there's an identical one already saved
            $special = \Genesis_Service_Special::save($special, $this->getLoggedUser());

            // Instead of setparam, call applySpecialAction with special ID
            $request->request->set('special_id', $special->getId());

            return $this->applySpecialAction($request);
        } catch (\Exception $e) {
            $output = [
                'success' => 0,
                'message' => $e->getMessage(),
            ];

            return new JsonResponse($output);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/units
     * http://localhost:9019/inventory/units
     */
    #[Route('/inventory/units', name: 'inventory_units')]
    public function unitsAction(Request $request): Response
    {
        $session = User::getSession($request);

        $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));
        // if sitelink or centershift 4 account then load grouped unit view
        /*if ($facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SITELINK ||
                $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_CENTERSHIFT4 ||
                $facility->getCorporation()->getSourceId() == Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
            $this->forward('groupedinventory');
        } else {*/
        // $facility = Genesis_Service_Facility::loadById($this->getSession()->facilityId);
        $this->view->facility = $facility;
        $this->view->sourceType = $facility->getCorporation()->getSourceId();
       

        $this->view->inventory = $this->_fetchInventoryData();

        if (!$this->view->inventory) {
            /* If no units are returned, check to see if there are truly no
             * units associated with that facility, or if the facility has units,
             * but they're publish = 0.
             */
            $restriction = \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('facilityId', $this->view->facility->getId()),
                \Genesis_Db_Restriction::equal('publish', 0)
            );
            $units = \Genesis_Service_StorageSpace::load($restriction);
            if ($units->current()) {
                $this->view->unpublishedUnits = 1;
            } else {
                $this->view->unpublishedUnits = 0;
            }
            /* } */
        }

        // get the default specials
        $this->view->defaultSpecials = \Genesis_Service_Special::loadDefaultSpecials();

        $this->_getCustomSpecials();

        // needed for scripts - in Symfony this would be handled in the template
        $scripts = ['facility/hide-facility-reason-modal', 'inventory/units-shared'];
        $scripts[] = 'inventory/units';
        $this->view->scripts = $scripts;
        // for twig
        $this->view->units = $this->view->inventory ? count($this->view->inventory) : 0;
        return $this->render('inventory/units.html.twig', [
            'view' => $this->view,
            'controller' => '/features/',
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/unit
     * http://localhost:9019/inventory/unit
     */
    #[Route('/inventory/unit', name: 'inventory_unit', methods: ['GET', 'POST'])]
    public function unitAction(Request $request): Response
    {
        $session = User::getSession($request);

        $unitIds = $request->query->get('unit_ids', []);
        if (is_array($unitIds) && count($unitIds) > 0) {
            $this->view->units = $this->_fetchInventoryData($request->query->get('unit_ids'));
            $this->view->facility = \Genesis_Service_Facility::loadById($this->view->units[0]['facility']);
        } else {
            $this->view->units = [];
            $this->view->facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));
        }

        $this->view->unitGroupCount = $request->query->get('unit_group_count');

        if ($request->isMethod('POST')) {
            try {
                // put unit ids into array in case there are a lot of them
                if ($request->request->get('unit_ids')) {
                    $unitIds = $request->request->get('unit_ids');
                } else {
                    // throw new \Exception('Please select units to update.');

                    // create new space based on type if no ids passed in (MANUALS do this)
                    switch ($request->request->get('unit_type')) {
                        case \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                            $space = new \Genesis_Entity_StorageUnit();
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                            $space = new \Genesis_Entity_ParkingSpace();
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                            $space = new \Genesis_Entity_Workspace();
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_WINE:
                            $space = new \Genesis_Entity_WineStorage();
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_LOCKER:
                            $space = new \Genesis_Entity_StorageLocker();
                            break;
                        case \Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                            $space = new \Genesis_Entity_StorageOutdoor();
                            break;
                        default:
                            throw new \Exception('Error selecting the unit type');
                            break;
                    }

                    // set active and published for newly added units: immediately visible on frontends
                    $space->setActive(1);
                    $space->setPublish(1);
                    $space->setQuantity(1);

                    $space->setFacilityId($session->get('facilityId'));

                    if ($request->request->get('unit_type')) {
                        $space->setType($request->request->get('unit_type'));
                    }

                    // set price for new unit
                    $space->setRegularPrice($request->request->get('standard_rate'));

                    $savedSpace = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());

                    $al = new \Genesis_Util_ActionLogger();
                    $al->logAction('add_unit', 0, 0, $this->getLoggedUser()->getId(), $session->get('facilityId'), $savedSpace->getId());

                    // put id in unitids array
                    $unitIds = [$savedSpace->getId()];
                }

                // now we have $unitIds array to update rest of params
                foreach ($unitIds as $unitId) {
                    // determine if this is a new creation or an update
                    $space = \Genesis_Service_StorageSpace::loadById($unitId);

                    if ($space) {
                        // make sure existing supp data gets lazy loaded
                        $space->getSupplementalData();

                        if ($request->request->get('unit_type') != $space->getType()) {
                            $space->setType($request->request->get('unit_type'));
                            // We have to save the space here to "transform" it into the
                            // proper class (Genesis_Entity_ParkingSpace, etc.)
                            $space = \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                        }

                        // populate fields that all types of units have
                        $space->setWidth($request->request->get('width'));
                        $space->setLength($request->request->get('length'));
                        $space->setRegularPrice($request->request->get('standard_rate'));
                        $space->setUnitLights($request->request->get('unit_lights') ? 1 : 0);
                        $space->setShelvesInUnit($request->request->get('shelves_in_unit') ? 1 : 0);
                        $space->setAirCooledOnly($request->request->get('air_cooled') ? 1 : 0);
                        $space->setHeatedOnly($request->request->get('heated') ? 1 : 0);
                        $space->setTwentyFourHourAccess($request->request->get('twenty_four_hour_access') ? 1 : 0);
                        $space->setSkybox($request->request->get('stacked') ? 1 : 0);
                        $space->setBasement($request->request->get('basement') ? 1 : 0);
                        $space->setParkingWarehouse($request->request->get('parking_warehouse') == 'on' ? 1 : 0);
                        $space->setPullThrough($request->request->get('pull_thru') ? 1 : 0);
                        $space->setFloor($request->request->get('floor')); // form passes in 1 when outdoor
                        $space->setPremiumUnit($request->request->get('premium') ? 1 : 0);
                        $space->setAdaAccessible($request->request->get('ada') ? 1 : 0);

                        // populate fields based on type
                        switch ($request->request->get('unit_type')) {
                            case \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE:
                                if ($request->request->get('covered') == 1) {
                                    $space->setCovered(1);
                                } else {
                                    $space->setCovered(0);
                                }

                                $space->setHeight($request->request->get('height'));

                                if ($request->request->get('power')) {
                                    $space->setPower(1);
                                } else {
                                    $space->setPower(0);
                                }

                                if ($request->request->get('lot_type')) {
                                    $space->setLotType($request->request->get('lot_type'));
                                } else {
                                    $space->SetLotType(null);
                                }

                                if ($request->request->get('door_type')) {
                                    $space->setDoorType($request->request->get('door_type')); // ROLL_UP,SWING,NONE
                                }

                                if ($request->request->get('climate_controlled')) {
                                    $space->setClimateControlled(1);
                                } else {
                                    $space->setClimateControlled(0);
                                }

                                if ($request->request->get('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {
                                    $space->setHumidityControlled(0);
                                }
                                // NOTE: vehicle/driveup set to 1 in class constructor

                                break;
                            case \Genesis_Entity_StorageSpace::TYPE_OUTDOOR:
                                if ($request->request->get('covered') == 1) {
                                    $space->setCovered(1);
                                } else {
                                    $space->setCovered(0);
                                }

                                if ($request->request->get('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {
                                    $space->setDriveUp(0);
                                }

                                if ($request->request->get('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($request->request->get('vehicle') == 'yes') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }

                                $space->setHeight($request->request->get('height'));

                                // NOTE: outdoor is set to true by default in constructor

                                break;
                            case \Genesis_Entity_StorageSpace::TYPE_STORAGE_UNIT:
                            case \Genesis_Entity_StorageSpace::TYPE_WORKSPACE:
                                if ($request->request->get('height')) {
                                    $space->setHeight($request->request->get('height'));
                                } else {
                                    $space->setHeight(null);
                                }

                                if ($request->request->get('power')) {
                                    $space->setPower(1);
                                } else {
                                    $space->setPower(0);
                                }

                                if ($request->request->get('alarm')) {
                                    $space->setAlarm(1);
                                } else {
                                    $space->setAlarm(0);
                                }

                                if ($request->request->get('climate_controlled')) {
                                    $space->setClimateControlled(1);
                                } else {
                                    $space->setClimateControlled(0);
                                }

                                if ($request->request->get('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {
                                    $space->setHumidityControlled(0);
                                }

                                if ($request->request->get('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {
                                    $space->setDriveUp(0);
                                }

                                if ($request->request->get('outdoor_access') == 1) {
                                    $space->setOutdoorAccess(1);
                                } else {
                                    $space->setOutdoorAccess(0);
                                }

                                if ($request->request->get('vehicle') == 'only') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(1);
                                } elseif ($request->request->get('vehicle') == 'yes') {
                                    $space->setVehicle(1);
                                    $space->setVehicleStorageOnly(0);
                                } else {
                                    $space->setVehicle(0);
                                    $space->setVehicleStorageOnly(0);
                                }

                                $space->setDoorType($request->request->get('door_type')); // ROLL_UP,SWING,NONE

                                if ($request->request->get('covered')) {
                                    $space->setCovered(1);
                                } else {
                                    $space->setCovered(0);
                                }

                                break;
                            case \Genesis_Entity_StorageSpace::TYPE_WINE:
                            case \Genesis_Entity_StorageSpace::TYPE_LOCKER:
                                if ($request->request->get('height')) {
                                    $space->setHeight($request->request->get('height'));
                                } else {
                                    $space->setHeight(null);
                                }

                                if ($request->request->get('power')) {
                                    $space->setPower(1);
                                } else {
                                    $space->setPower(0);
                                }

                                if ($request->request->get('alarm')) {
                                    $space->setAlarm(1);
                                } else {
                                    $space->setAlarm(0);
                                }

                                if ($request->request->get('climate_controlled')) {
                                    $space->setClimateControlled(1);
                                } else {
                                    $space->setClimateControlled(0);
                                }

                                if ($request->request->get('humidity_controlled')) {
                                    $space->setHumidityControlled(1);
                                } else {
                                    $space->setHumidityControlled(0);
                                }

                                if ($request->request->get('drive_up') == 1) {
                                    $space->setDriveUp(1);
                                } else {
                                    $space->setDriveUp(0);
                                }

                                if ($request->request->get('outdoor_access') == 1) {
                                    $space->setOutdoorAccess(1);
                                } else {
                                    $space->setOutdoorAccess(0);
                                }

                                $space->setDoorType($request->request->get('door_type')); // ROLL_UP,SWING,NONE

                                $space->setCovered(1); // covered is assumed

                                break;
                            default:
                                throw new \Exception('Could not match type of storage space');
                                break;
                        }

                        \Genesis_Service_StorageSpace::save($space, $this->getLoggedUser()->getId());
                    }
                }

                $output = [
                    'success' => 1,
                ];
            } catch (\Exception $e) {
                $output = [
                    'success' => 0,
                    'message' => $e->getMessage(),
                ];
            }

            return new JsonResponse($output);
        }

        return $this->render('inventory/unit.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/toggle-units
     * http://localhost:9019/inventory/toggle-units
     */
    #[Route('/inventory/toggle-units', name: 'inventory_toggle_units', methods: ['POST'])]
    public function toggleUnitsAction(Request $request): JsonResponse
    {
        $response = ['success' => null, 'modified' => 0, 'skipped' => 0, 'units' => []];
        try {
            $unitIds = $request->request->get('unit_ids');

            if (!is_array($unitIds)) {
                throw new \Exception('no unit ids found');
            }

            // active in frontends; won't actually appear unless publish=1
            if ($request->request->get('active') == 'true') {
                $active = 1;
            } elseif ($request->request->get('active') == 'false') {
                $active = 0;
            } else {
                throw new \Exception('active state not provided');
            }
            $response['active'] = $active;
            foreach ($unitIds as $unitId) {
                $unit = \Genesis_Service_StorageSpace::loadById($unitId);
                if (!$unit) {
                    continue;
                }
                if (!in_array($unit->getFacilityId(), $this->getLoggedUser()->getManageableFacilityIds())) {
                    throw new \Exception('Unable to access facility: '.$unit->getFacilityId());
                }
                if ($unit->getActive() == $active) {
                    ++$response['skipped'];
                } else {
                    $unit->setActive($active);
                    \Genesis_Service_StorageSpace::save($unit, $this->getLoggedUser()->getId());
                    ++$response['modified'];
                }
                ++$response['count'];
                $response['units'][] = $unit->getId();
            }
            $response['success'] = true;
        } catch (\Exception $e) {
            $response['success'] = false;
            $response['message'] = $e->getMessage();
        }

        return new JsonResponse($response);
    }

    private function _getCustomSpecials(): void
    {
        $this->view->customPromos = [];
        $this->view->customDiscounts = [];
        $acctSpecials = \Genesis_Service_Special::loadByAccountId($this->getLoggedUser()->getAccountId());
        foreach ($acctSpecials as $special) {
            if ($special->getDefault()) {
                continue;
            }
            if ($special->isPromo()) {
                $this->view->customPromos[] = [
                    'id' => $special->getId(),
                    'name' => $special->getString(),
                ];
            }
            if ($special->isDiscount()) {
                $this->view->customDiscounts[] = [
                    'id' => $special->getId(),
                    'name' => $special->getString(),
                ];
            }
        }
        // 
        $this->view->specials = [];
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/apply-special
     * http://localhost:9019/inventory/apply-special
     */
    #[Route('/inventory/apply-special', name: 'inventory_apply_special', methods: ['POST'])]
    public function applySpecialAction(Request $request): JsonResponse
    {
        try {
            $specialId = $request->request->get('special_id');
            if (!$specialId) {
                throw new \Exception('No special id specified.');
            }

            $unitRows = json_decode(stripslashes($request->request->get('unit_rows')));

            $updatedUnitRows = [];

            foreach ($unitRows as $unitRow) {
                /**
                 * @var $unitId int
                 */
                foreach ($unitRow->unitIds as $unitId) {
                    \Genesis_Service_UnitSpecial::applyUnitSpecial($unitId, $request->request->get('special_id'));
                }

                $updatedUnit = \Genesis_Service_StorageSpace::loadById($unitRow->unitIds[0]);
                $updatedUnitRows[] = [
                    'unitIndex' => $unitRow->unitIndex,
                    'regularPrice' => number_format($updatedUnit->getRegularPrice(), 2),
                    'effectivePrice' => number_format($updatedUnit->getEffectivePrice(), 2),
                    'specialString' => $updatedUnit->buildSpecialString(),
                    'discountString' => $updatedUnit->buildDiscountString(),
                ];
            }

            $this->_getCustomSpecials();

            $output = [
                'success' => 1,
                'unitRows' => $updatedUnitRows,
                'customPromos' => $this->view->customPromos,
                'customDiscounts' => $this->view->customDiscounts,
            ];
        } catch (\Exception $e) {
            $output = [
                'success' => 0,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ];
        }

        return new JsonResponse($output);
    }

    /**
     * Fetch and organize all of the data to populate the inventory screen.
     *
     * @var
     */
    private function _fetchInventoryData($unitIds = null): ?array
    {
        // if unit ids were passed in then only load those
        if ($unitIds) {
            $restriction = \Genesis_Db_Restriction::in('id', $unitIds);
            $units = \Genesis_Service_StorageSpace::load($restriction);
            $facility = $units->current()->getFacility();
        } else {
            // For session access, we need to get it from the current request context
            // This is a simplified approach - in a full migration, session would be passed as parameter
            $session = $this->container->get('session');
            $facility = \Genesis_Service_Facility::loadById($session->get('facilityId'));
            $units = $facility->getGroupedUnits(true);
        }

        // set flag for grouped version
        $showGrouped = 0;
        if ($facility->canGroupUnits()) {
            $showGrouped = 1;
        }

        $this->view->showGrouped = $showGrouped;

        if ($units) {
            $i = 0;

            foreach ($units as $unit) {
                if (!$unitIds && $showGrouped) {
                    $groupUnitIds = $unit->getGroupedUnitIds();
                } else {
                    $groupUnitIds = [$unit->getId()];
                }

                $unitDetails[$i]['unitIds'] = $groupUnitIds;
                $unitDetails[$i]['facility'] = $unit->getFacility()->getId();
                $unitDetails[$i]['type'] = $unit->stringType();
                $unitDetails[$i]['typeNum'] = $unit->getType();

                $unitDetails[$i]['width'] = $unit->getWidth();
                $unitDetails[$i]['length'] = $unit->getLength();
                $unitDetails[$i]['height'] = $unit->getHeight();
                $unitDetails[$i]['squareFootage'] = $unit->getSquareFootage();

                // some instantiation for ones that don't always get set
                $unitDetails[$i]['driveUp'] = null;
                $unitDetails[$i]['vehicle'] = null;
                $unitDetails[$i]['doorWidth'] = null;
                $unitDetails[$i]['doorHeight'] = null;
                $unitDetails[$i]['climateControlled'] = null;
                $unitDetails[$i]['humidityControlled'] = null;
                $unitDetails[$i]['alarm'] = null;
                $unitDetails[$i]['power'] = null;
                $unitDetails[$i]['outdoorAccess'] = null;
                $unitDetails[$i]['doorType'] = null;
                $unitDetails[$i]['covered'] = null;

                $unitDetails[$i]['dimensions'] = $unit->stringDimensions(false);
                $unitDetails[$i]['exportDimensions'] = $unit->stringDimensions(false);
                $unitDetails[$i]['amenities'] = $unit->stringAmenities();
                $unitDetails[$i]['floor'] = $unit->getFloor();
                $unitDetails[$i]['hasDiscountSpecial'] = $unit->hasDiscountSpecial();
                $unitDetails[$i]['sparefootPrice'] = '';
                if ($unit->getSparefootPrice() > 0) {
                    $unitDetails[$i]['sparefootPrice'] = $unit->getSparefootPrice();
                }
                $unitDetails[$i]['standardRate'] = $unit->getRegularPrice();
                $unitDetails[$i]['quantity'] = $unit->getQuantity();
                $unitDetails[$i]['approved'] = $unit->getApproved() ? true : false;
                $unitDetails[$i]['published'] = $unit->getPublish() ? true : false; // Rentable in the FMS; only set by FMS

                if ($unit->getActive() == 1) {
                    $unitDetails[$i]['active'] = true; // active in frontends; won't actually appear unless publish=1
                } else {
                    $unitDetails[$i]['active'] = false;
                }

                $unitDetails[$i]['deposit'] = $unit->getDeposit();
                $unitDetails[$i]['quantity'] = $unit->getQuantity();
                $unitDetails[$i]['description'] = $unit->getDescription();

                $unitDetails[$i]['specialString'] = $unit->buildSpecialString();
                $unitDetails[$i]['discountString'] = $unit->buildDiscountString();

                // Supp unit data
                $unitDetails[$i]['stacked'] = $unit->getSkybox();
                $unitDetails[$i]['premium'] = $unit->getPremiumUnit();
                $unitDetails[$i]['heated'] = $unit->getHeatedOnly();
                $unitDetails[$i]['airCooled'] = $unit->getAirCooledOnly();
                $unitDetails[$i]['ada'] = $unit->getAdaAccessible();
                $unitDetails[$i]['unitLights'] = $unit->getUnitLights();
                $unitDetails[$i]['twentyFourHourAccess'] = $unit->getTwentyFourHourAccess();

                $unitDetails[$i]['shelvesInUnit'] = $unit->getShelvesInUnit();
                $unitDetails[$i]['basement'] = $unit->getBasement();
                $unitDetails[$i]['parkingWarehouse'] = $unit->getParkingWarehouse();
                $unitDetails[$i]['pullThru'] = $unit->getPullThrough();

                $unitDetails[$i]['lotType'] = $unit->getSuppAttr(\Genesis_Entity_UnitData::LOT_TYPE);

                // this section does not apply to outdoor only
                if ($unit->getType() != \Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {
                    if ($unit->getClimateControlled() == 1) {
                        $unitDetails[$i]['climateControlled'] = true;
                    } else {
                        $unitDetails[$i]['climateControlled'] = false;
                    }
                    if ($unit->getHumidityControlled() == 1) {
                        $unitDetails[$i]['humidityControlled'] = true;
                    } else {
                        $unitDetails[$i]['humidityControlled'] = false;
                    }
                    if ($unit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_ROLL_UP) {
                        $unitDetails[$i]['doorType'] = 'roll_up';
                    } elseif ($unit->getDoorType() == \Genesis_Entity_StorageSpace::DOOR_SWING) {
                        $unitDetails[$i]['doorType'] = 'swing';
                    } else {
                        $unitDetails[$i]['doorType'] = 'none';
                    }
                    if ($unit->getAlarm() == 1) {
                        $unitDetails[$i]['alarm'] = true;
                    } else {
                        $unitDetails[$i]['alarm'] = false;
                    }
                    if ($unit->getPower() == 1) {
                        $unitDetails[$i]['power'] = true;
                    } else {
                        $unitDetails[$i]['power'] = false;
                    }
                }

                // this section does not apply to parking or outdoor
                if ($unit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                        && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_OUTDOOR) {
                    if ($unit->getOutdoorAccess() == 1) {
                        $unitDetails[$i]['outdoorAccess'] = true;
                    } else {
                        $unitDetails[$i]['outdoorAccess'] = false;
                    }
                }

                // this section does not apply to parking, wine or locker
                if ($unit->getType() != \Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE
                        && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                        && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE) {
                    if ($unit->getDriveUp() == 1) {
                        $unitDetails[$i]['driveUp'] = true;
                    } else {
                        $unitDetails[$i]['driveUp'] = false;
                    }

                    if ($unit->getVehicleStorageOnly()) {
                        $unitDetails[$i]['vehicle'] = 'only';
                    } elseif ($unit->getVehicle() == 1) {
                        $unitDetails[$i]['vehicle'] = 'yes';
                    } else {
                        $unitDetails[$i]['vehicle'] = false;
                    }
                }

                // covered does not apply to wine or locker
                if ($unit->getType() != \Genesis_Entity_StorageSpace::TYPE_LOCKER
                        && $unit->getType() != \Genesis_Entity_StorageSpace::TYPE_WINE) {
                    $unitDetails[$i]['covered'] = $unit->getCovered();
                }

                // do grouped version only stuff
                if ($showGrouped) {
                    $unitDetails[$i]['numRentable'] = $unit->getGroupedNumAvailable();
                }

                // if sitelink, then add the sitelink unit type from sitelink_full_units
                if ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SITELINK) {
                    $slFullUnit = \Genesis_Service_SitelinkFullUnit::loadById($unit->getId());
                    $unitDetails[$i]['classType'] = $slFullUnit->getSitelinkType();
                    $unitDetails[$i]['unitName'] = $slFullUnit->getSitelinkUnitName();
                } elseif ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_CENTERSHIFT4
                            || $facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_CENTERSHIFT4_LEADS360) {
                    $csUnit = \Genesis_Service_Centershift4Unit::loadById($unit->getId());
                    $unitDetails[$i]['classType'] = $csUnit->getClassDesc();
                    $unitDetails[$i]['unitName'] = $csUnit->getUnitNumber();
                } elseif ($facility->getCorporation()->getSourceId() == \Genesis_Entity_Source::ID_SELFSTORAGEMANAGER) {
                    $ssmUnit = \Genesis_Service_SelfStorageManagerUnit::loadById($unit->getId());
                    $unitDetails[$i]['classType'] = $ssmUnit->getUnitTypeId();
                    $unitDetails[$i]['unitName'] = $ssmUnit->getUnitNumber();
                }

                $unitDetails[$i]['specials'] = $unit->getSpecials();

                ++$i;
            }

            usort($unitDetails, [$this, 'sortSquareFootage']);

            return $unitDetails ?? [];
        }

        return [];
    }

    public static function isBlacklisted($actionName)
    {
        switch ($actionName) {
            case 'add':
            case 'type':
            case 'index':
            case 'addsummary':
            case 'deleteimage':
            case 'defaultimage':
            case 'update_facility':
            case 'selectionhandler':
                return true;
        }

        return false;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/delete-special
     * http://localhost:9019/inventory/delete-special
     */
    #[Route('/inventory/delete-special', name: 'inventory_delete_special', methods: ['POST'])]
    public function deleteSpecialAction(Request $request): JsonResponse
    {
        try {
            $unitRows = json_decode($request->request->get('unit_rows'));
            $rootType = $request->request->get('root_type');

            // for each group of units (or single unit if not applicable)
            foreach ($unitRows as $unitRow) {
                // for each unit
                foreach ($unitRow->unitIds as $unitId) {
                    // not every unit in the batch will have every type, from a mixed batch of units, but handle it nicely
                    if (!\Genesis_Service_UnitSpecial::hasRootType($unitId, $rootType)) {
                        // but still put it in the output, since the ajax all expects it and will freak out
                        $updatedUnitIds[$unitId] = $unitRow->unitIndex;
                        continue;
                    }
                    if (!\Genesis_Service_UnitSpecial::deleteByRootType($unitId, $rootType)) {
                        throw new \Exception('Could not delete unit specials');
                    }
                    $updatedUnitIds[$unitId] = $unitRow->unitIndex;
                }
            }

            $updatedUnitRows = [];
            foreach ($updatedUnitIds as $unitId => $unitIndex) {
                $unit = \Genesis_Service_StorageSpace::loadById($unitId);
                $updatedUnitRows[] = [
                    'unitIndex' => $unitIndex,
                    'regularPrice' => number_format($unit->getRegularPrice(), 2),
                    'effectivePrice' => number_format($unit->getEffectivePrice(), 2),
                    'specialString' => $unit->buildSpecialString(),
                    'discountString' => $unit->buildDiscountString(),
                ];
            }

            $output = [
                'success' => 1,
                'unitRows' => $updatedUnitRows,
            ];
        } catch (\Exception $e) {
            $output = [
                'success' => 0,
                'message' => $e->getMessage(),
            ];
        }

        return new JsonResponse($output);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/inventory/get-special-string-preview
     * http://localhost:9019/inventory/get-special-string-preview
     */
    #[Route('/inventory/get-special-string-preview', name: 'inventory_get_special_string_preview', methods: ['POST'])]
    public function getSpecialStringPreviewAction(Request $request): JsonResponse
    {
        try {
            $special = $this->_buildCustomSpecialFromParams($request);

            $output = [
                'success' => 1,
                'message' => $special->getString(),
            ];
        } catch (\Exception $e) {
            $output = [
                'success' => 0,
                'message' => $e->getMessage(),
            ];
        }

        return new JsonResponse($output);
    }

    private function sortSquareFootage($a, $b)
    {
        if ($a['squareFootage'] == $b['squareFootage']) {
            return 0;
        }

        return ($a['squareFootage'] < $b['squareFootage']) ? -1 : 1;
    }
}
