<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Clients\SearchClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class InsightsController extends AbstractRestrictedController
{
    /**
     * @var \SoapClient
     */
    private $soap;

    public function _init(): ?RedirectResponse
    {
        $domain = \Genesis_Config_Server::getEnvDomain();
        if ($domain === 'localhost:8888') { // meh
            $domain = 'localhost';
        }

        $this->soap = new \SoapClient(
            'http://pita.sparefoot.'.$domain.'/quicksoap?wsdl',
            ['cache_wsdl' => WSDL_CACHE_NONE]
        );
        $this->view->user = $this->getLoggedUser();
        $this->view->accountId = $this->getLoggedUser()->getAccountId();
        $this->view->allReports = $this->soap->getReports($this->getLoggedUser()->getAccount()->getSfAccountId());

        return null;
    }

    // get report parameters and display option to view as HTML table or download
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/custom
     * http://localhost:9019/insights/custom
     */
    #[Route('/insights/custom', name: 'insights_custom')]
    public function customAction(Request $request): Response
    {
        $reportClassName = $request->get('report');
        $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $this->view->inputs = $this->soap->getReportParameters($reportClassName, $account->getSfAccountId());
        $this->view->reportClassName = $reportClassName;
        $this->view->reportName = $this->soap->getReportName($reportClassName);

        if ($request->isMethod('GET')) {
            if ($request->get('csv') === 'Download') {
                $csvContent = $this->soap->renderReport($reportClassName, $request->query->all(), $account->getSfAccountId());

                return new Response(
                    $csvContent,
                    Response::HTTP_OK,
                    [
                        'Content-Type' => 'text/csv',
                        'Content-Disposition' => 'attachment; filename="report.csv"',
                    ]
                );
            } else {
                $this->view->renderedTable = $this->soap->renderReportTable($reportClassName, $request->query->all(), $account->getSfAccountId());
            }
        }

        $this->view->scripts = ['insights/custom'];

        return $this->render('insights/custom.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/customtable
     * http://localhost:9019/insights/customtable
     */
    #[Route('/insights/customtable', name: 'insights_customtable')]
    public function customtableAction(Request $request): Response
    {
        $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $tableContent = $this->soap->renderReportTable($request->get('report'), $request->query->all(), $account->getSfAccountId());

        return new Response($tableContent);
    }

    // download as CSV file
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/download
     * http://localhost:9019/insights/download
     */
    #[Route('/insights/download', name: 'insights_download')]
    public function downloadAction(Request $request): Response
    {
        $csvContent = $this->soap->renderReport($request->get('report'), $request->query->all());

        return new Response(
            $csvContent,
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="report.csv"',
            ]
        );
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights
     * http://localhost:9019/insights
     */
    #[Route('/insights', name: 'insights_index')]
    public function indexAction(Request $request): Response
    {
        $this->view->scripts = ['insights/index'];
        $this->view->title = 'Pricing Data';

        $facilities = $this->view->user->getManagableFacilities();
        $show_report = false;
        $reports_count = 0;
        $str_report_row = '';
        $facilityIds = [];
        $logFacilities = [];
        $hasInsightsFacNoneEnabled = $hasInsightsFacNoData = $hasInsightsFacNoBe = $stillProcessingReport = false;
        $dayOfMonth = date('j');

        $reportDate = $request->get('report_date');
        if (!empty($reportDate)) {
            $insights_date = $reportDate;
        } elseif ($dayOfMonth < \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $insights_date = date('Y-m-'.\Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY, strtotime('-1 month'));
        } else {
            $insights_date = date('Y-m-'.\Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY);
        }

        if ($dayOfMonth < \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY) {
            $this->view->nextReport = date('F Y');
            $this->view->nextReportDays = \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY - $dayOfMonth;
        } else {
            $this->view->nextReport = date('F Y', strtotime('+1 month'));
            $this->view->nextReportDays = date('t') - $dayOfMonth + \Genesis_Entity_Insights::INSIGHTS_GENERATION_DAY;
        }

        $output = ['report_date' => $insights_date];

        $str_csv = "Facility Name,Facility Company Code,Facility Address,City, State,Zip,Sample Distance,Facilities Sampled,CC 5x5,CC 5x10,CC 5x15,CC 10x10,CC 10x15,CC 10x20,CC 10x30,non-CC 5x5,non-CC 5x10,non-CC 5x15,non-CC 10x10,non-CC 10x15,non-CC 10x20,non-CC 10x30\n";
        foreach ($facilities as $facility) {
            /* @var $facility Genesis_Entity_Facility */
            // We add the facility ID even if we don't show the report for this facility so that
            // we can properly populate the date dropdown picker.
            $facilityIds[] = $facility->getId();

            if (!$facility->getInsights()) {
                continue;
            }
            $hasInsightsFacNoneEnabled = false;

            if ($facility->getLocationId() == \Genesis_Entity_Location::BLANK_LOCATION_ID) {
                continue;
            }
            $hasInsightsFacNoData = false;

            if (!$facility->getBillableEntityId()) {
                continue;
            }
            $hasInsightsFacNoBe = false;

            $reportType = \Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL;
            if ($request->get('report_type') && $request->get('report_type') == \Genesis_Entity_Insights::REPORT_TYPE_LOCAL) {
                $reportType = \Genesis_Entity_Insights::REPORT_TYPE_LOCAL;
            }

            $insight_data = $facility->getInsightsReport($insights_date, $reportType);

            if (!$insight_data) {
                // only show "still processing" message if passed all the other gates above and no facility...
                // insights reports are ready or DB issues causing report data to return empty
                $stillProcessingReport = true;
                continue;
            }

            $logFacilities[] = $facility->getId(); // add facility id to be logged

            $show_report = true;
            ++$reports_count;

            // $this->view->setScriptPath(APPLICATION_PATH . '/views/scripts/');

            $this->view->facilityTitle = $facility->getTitle();
            $this->view->facilityAddress = $facility->getLocation()->getAddress1().', '.
                $facility->getLocation()->getCity().', '.$facility->getLocation()->getState().' '.$facility->getLocation()->getZip();

            $this->view->companyCode = $facility->getCompanyCode();
            $this->view->listingAvailId = $insight_data->getListingAvailId();
            $this->view->id = $insight_data->getId();

            if ($insight_data->getReportType() == \Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL) {
                $this->view->searchRadius = $insight_data->getSearchRadius();
            } elseif ($insight_data->getReportType() == \Genesis_Entity_Insights::REPORT_TYPE_LOCAL) {
                $this->view->searchRadius = $insight_data->getMaxDistance();
            }

            $this->view->numSampledFacilities = $insight_data->getNumSampledFacilities();

            $this->view->reportMonth = date('F', strtotime('01-'.$insight_data->getReportMonth().'-'.$insight_data->getReportYear()));
            $this->view->reportYear = $insight_data->getReportYear();
            $this->view->cc25 = ($insight_data->getCc25() > 0) ? $insight_data->getCc25() : 'Not enough data';
            $this->view->noncc25 = ($insight_data->getNoncc25() > 0) ? $insight_data->getNoncc25() : 'Not enough data';
            $this->view->cc50 = ($insight_data->getCc50() > 0) ? $insight_data->getCc50() : 'Not enough data';
            $this->view->noncc50 = ($insight_data->getNoncc50() > 0) ? $insight_data->getNoncc50() : 'Not enough data';
            $this->view->cc75 = ($insight_data->getCc75() > 0) ? $insight_data->getCc75() : 'Not enough data';
            $this->view->noncc75 = ($insight_data->getNoncc75() > 0) ? $insight_data->getNoncc75() : 'Not enough data';
            $this->view->cc100 = ($insight_data->getCc100() > 0) ? $insight_data->getCc100() : 'Not enough data';
            $this->view->noncc100 = ($insight_data->getNoncc100() > 0) ? $insight_data->getNoncc100() : 'Not enough data';
            $this->view->cc150 = ($insight_data->getCc150() > 0) ? $insight_data->getCc150() : 'Not enough data';
            $this->view->noncc150 = ($insight_data->getNoncc150() > 0) ? $insight_data->getNoncc150() : 'Not enough data';
            $this->view->cc200 = ($insight_data->getCc200() > 0) ? $insight_data->getCc200() : 'Not enough data';
            $this->view->noncc200 = ($insight_data->getNoncc200() > 0) ? $insight_data->getNoncc200() : 'Not enough data';
            $this->view->cc500 = ($insight_data->getCc500() > 0) ? $insight_data->getCc500() : 'Not enough data';
            $this->view->noncc500 = ($insight_data->getNoncc500() > 0) ? $insight_data->getNoncc500() : 'Not enough data';

            $yourInsightsData = \Genesis_Service_Insights::buildYourInsightsData($facility);

            $this->view->your_cc25 = ($yourInsightsData['cc']['25'] > 0) ? trim((string) $yourInsightsData['cc']['25']) : 'N/A';
            $this->view->your_cc50 = ($yourInsightsData['cc']['50'] > 0) ? trim((string) $yourInsightsData['cc']['50']) : 'N/A';
            $this->view->your_cc75 = ($yourInsightsData['cc']['75'] > 0) ? trim((string) $yourInsightsData['cc']['75']) : 'N/A';
            $this->view->your_cc100 = ($yourInsightsData['cc']['100'] > 0) ? trim((string) $yourInsightsData['cc']['100']) : 'N/A';
            $this->view->your_cc150 = ($yourInsightsData['cc']['150'] > 0) ? trim((string) $yourInsightsData['cc']['150']) : 'N/A';
            $this->view->your_cc200 = ($yourInsightsData['cc']['200'] > 0) ? trim((string) $yourInsightsData['cc']['200']) : 'N/A';
            $this->view->your_cc500 = ($yourInsightsData['cc']['500'] > 0) ? trim((string) $yourInsightsData['cc']['500']) : 'N/A';
            $this->view->your_noncc25 = ($yourInsightsData['noncc']['25'] > 0) ? trim((string) $yourInsightsData['noncc']['25']) : 'N/A';
            $this->view->your_noncc50 = ($yourInsightsData['noncc']['50'] > 0) ? trim((string) $yourInsightsData['noncc']['50']) : 'N/A';
            $this->view->your_noncc75 = ($yourInsightsData['noncc']['75'] > 0) ? trim((string) $yourInsightsData['noncc']['75']) : 'N/A';
            $this->view->your_noncc100 = ($yourInsightsData['noncc']['100'] > 0) ? trim((string) $yourInsightsData['noncc']['100']) : 'N/A';
            $this->view->your_noncc150 = ($yourInsightsData['noncc']['150'] > 0) ? trim((string) $yourInsightsData['noncc']['150']) : 'N/A';
            $this->view->your_noncc200 = ($yourInsightsData['noncc']['200'] > 0) ? trim((string) $yourInsightsData['noncc']['200']) : 'N/A';
            $this->view->your_noncc500 = ($yourInsightsData['noncc']['500'] > 0) ? trim((string) $yourInsightsData['noncc']['500']) : 'N/A';
            $this->view->your_edit_link = $this->view->url(['action' => 'inventory'], 'features').'?fid='.$facility->getId();

            $str_csv .= $this->view->facilityTitle.','
                .$this->view->companyCode.','
                .$facility->getLocation()->getAddress1().','
                .$facility->getLocation()->getCity().','
                .$facility->getLocation()->getState().','
                .$facility->getLocation()->getZip().','
                .$this->view->searchRadius.','
                .$this->view->numSampledFacilities.','
                .$this->view->cc25.','
                .$this->view->cc50.','
                .$this->view->cc75.','
                .$this->view->cc100.','
                .$this->view->cc150.','
                .$this->view->cc200.','
                .$this->view->cc500.','
                .$this->view->noncc25.','
                .$this->view->noncc50.','
                .$this->view->noncc75.','
                .$this->view->noncc100.','
                .$this->view->noncc150.','
                .$this->view->noncc200.','
                .$this->view->noncc500."\n";

            $str_report_row .= str_replace("\n", '', $this->getTwig()->render('insights/insightsdata.html.twig', [
                'view' => $this->view,
            ]));
        }

        $availableReports = \Genesis_Service_Insights::getAvailableReports($facilityIds);

        $monthNames = [
            '1' => 'January',
            '2' => 'February',
            '3' => 'March',
            '4' => 'April',
            '5' => 'May',
            '6' => 'June',
            '7' => 'July',
            '8' => 'August',
            '9' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];
        $this->view->availableReports = [];
        foreach ($availableReports as $year => $months) {
            foreach ($months as $month) {
                $this->view->availableReports[$year.'-'.$month] = $monthNames[$month].' '.$year;
            }
        }

        $this->view->outputstr = $output;

        $this->view->showreport = $show_report;
        $this->view->reports_count = $reports_count;
        $logger = new \Genesis_Util_ActionLogger();

        if ($show_report) {
            if ($request->get('report_date')) {
                if (empty($str_report_row)) {
                    return new Response("<div class='error'>Data is only available for months you have purchased Insights. Data is not available for this month.</div>");
                } else {
                    if ($request->get('export')) {
                        $file_name = 'InsightsReport-'.$insights_date;

                        return new Response(
                            $str_csv,
                            Response::HTTP_OK,
                            [
                                'Content-Type' => 'text/csv',
                                'Content-Disposition' => 'attachment; filename="'.$file_name.'.csv"',
                                'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                                'Pragma' => 'public',
                                'Expires' => '0',
                            ]
                        );
                    } else {
                        foreach ($logFacilities as $facId) {
                            $logger->logAction('view_insights', null, ' ', $this->getLoggedUser()->getId(), $facId, null);
                        }

                        return new Response($str_report_row);
                    }
                }
            } else {
                $this->view->scripts = ['insights/insightsreport'];

                return $this->render('insights/insightsreport.html.twig', [
                    'view' => $this->view,
                ]);
            }
        } elseif ($stillProcessingReport) {
            $this->view->scripts = ['insights/reportgenerating'];

            return $this->render('insights/reportgenerating.html.twig', [
                'view' => $this->view,
            ]);
        } else {
            // see if all facilities have insights turned on but don't have address or billable entity
            $this->view->askInsightsEnabled = $hasInsightsFacNoneEnabled ? true : false;
            $this->view->askAddress = $hasInsightsFacNoData ? true : false;
            $this->view->askBilling = $hasInsightsFacNoBe ? true : false;
        }

        return $this->render('insights/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/generatenew
     * http://localhost:9019/insights/generatenew
     */
    #[Route('/insights/generatenew', name: 'insights_generatenew')]
    public function generatenewAction(Request $request): JsonResponse
    {
        $accountId = $request->get('account_id');
        $facilities = \Genesis_Service_Facility::loadByAccountId($accountId);
        $facilities->setRewindable(false);

        $success = 0;
        $searchClient = new SearchClient('Myfoot-Insights', false);
        foreach ($facilities as $facility) {
            try {
                $facility->generateInsightsReport(false, \Genesis_Entity_Insights::REPORT_TYPE_ORIGINAL, $searchClient);
                $facility->generateInsightsReport(false, \Genesis_Entity_Insights::REPORT_TYPE_LOCAL, $searchClient);
                $success = 1;
            } catch (\Exception) {
                continue;
            }
        }

        return new JsonResponse([
            'success' => $success,
        ]);
    }

    // list available custom reports
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/reports
     * http://localhost:9019/insights/reports
     */
    #[Route('/insights/reports', name: 'insights_reports')]
    public function reportsAction(): Response
    {
        return $this->render('insights/reports.html.twig', [
            'view' => $this->view,
        ]);
    }

    // render as HTML table
    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/table
     * http://localhost:9019/insights/table
     */
    #[Route('/insights/table', name: 'insights_table')]
    public function tableAction(Request $request): Response
    {
        $params = $request->query->all();
        $reportName = $params['report'];
        $renderedTable = $this->soap->renderReportTable($reportName, $params);

        return new Response($renderedTable);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/tellme
     * http://localhost:9019/insights/tellme
     */
    #[Route('/insights/tellme', name: 'insights_tellme')]
    public function tellmeAction(): Response
    {
        $msg = new \Genesis_Entity_EmailMessage();
        $msg->setSubject('Insights product information requested');

        $msg->setBody(
            'First Name: '.$this->view->user->getFirstName().'<br/>'.
                'Last Name: '.$this->view->user->getLastName().'<br/><br/>'.
                'Phone: '.$this->view->user->getPhone().'<br/>'.
                'Email: '.$this->view->user->getEmail().'<br/><br/>'.
                'User Id: '.$this->view->user->getId().'<br/>'.
                'Member Since: '.$this->view->user->getMemberSince().'<br/>'
        );

        \Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, []);

        return new Response('Email Sent');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/insights/terms
     * http://localhost:9019/insights/terms
     */
    #[Route('/insights/terms', name: 'insights_terms')]
    public function termsAction(): Response
    {
        $acctMgmtUser = \Genesis_Service_UserAccess::loadById($this->view->user->getId());
        $acctMgmtUser->setTermsVersion(\Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);
        \Genesis_Service_UserAccess::save($acctMgmtUser);

        $this->getLoggedUser()->setTermsVersion(\Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION);

        $this->view->scripts = ['insights/terms'];

        return $this->render('insights/terms.html.twig', [
            'view' => $this->view,
        ]);
    }
}
