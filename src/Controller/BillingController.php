<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\Statement;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class BillingController extends AbstractRestrictedController
{
    public function _init(): ?RedirectResponse
    {
        $user = $this->getLoggedUser();
        if (!$user->canUseBilling()) {
            throw new \Exception('This user is not allowed to use billing.');
        }
        $this->view->nocache = 1;

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing
     * http://localhost:9019/billing
     */
    #[Route('/billing', name: 'billing_index')]
    public function indexAction(): RedirectResponse
    {
        return $this->redirectToRoute('billing_home');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/home
     * http://localhost:9019/billing/home
     */
    #[Route('/billing/home', name: 'billing_home')]
    public function homeAction(): RedirectResponse
    {
        $user = $this->getLoggedUser();
        $statements = \Genesis_Service_Statement::loadByAccount($user->getAccount());

        foreach ($statements as $statement) {
            if ($statement->getStatementBatch()->getStatus() === \Genesis_Entity_StatementBatch::STATUS_OPEN) {
                return $this->redirectToRoute('billing_viewstatement', ['id' => $statement->getId()]);
            }
        }

        return $this->redirectToRoute('billing_statements');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/pdf?statement_id=123
     * http://localhost:9019/billing/pdf?statement_id=123
     */
    #[Route('/billing/pdf', name: 'billing_pdf')]
    public function pdfAction(Request $request): Response
    {
        if (!$request->get('statement_id')) {
            return $this->redirectToRoute('billing_index');
        }

        $statement = \Genesis_Service_Statement::loadById($request->get('statement_id'));

        $acct = $statement->getAccount();

        if ($acct->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel('Rent Collected:'), 'pdf');
        } else {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel(), 'pdf');
        }

        $pdf = $statement->getPdf($this->getLoggedUser());

        // log a pdf view
        $logger = new \Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_pdf', '', '', $this->getLoggedUser() ? $this->getLoggedUser()->getId() : null, '', $statement->getId());

        $pdfContent = $pdf->Output('file.pdf', 'S');

        return new Response($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/checkbooking?state=1&confirmation_code=ABC123
     * http://localhost:9019/billing/checkbooking?state=0&confirmation_code=ABC123
     */
    #[Route('/billing/checkbooking', name: 'billing_checkbooking', methods: ['POST'])]
    public function checkbookingAction(Request $request): Response
    {
        if ($request->request->get('state')) {
            \Genesis_Dao_Transaction::check($request->request->get('confirmation_code'));
        } else {
            \Genesis_Dao_Transaction::unCheck($request->request->get('confirmation_code'));
        }

        return new Response('', 200);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/csv?statement_id=123&user_id=456
     * http://localhost:9019/billing/csv?statement_id=123&user_id=456
     */
    #[Route('/billing/csv', name: 'billing_csv')]
    public function csvAction(Request $request): Response
    {
        if (!$request->get('statement_id')) {
            return $this->redirectToRoute('billing_index');
        }

        $statement = \Genesis_Service_Statement::loadById($request->get('statement_id'));
        $user = \Genesis_Service_UserAccess::loadById($request->get('user_id'));

        $acct = $statement->getAccount();

        if ($acct->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel('Rent Collected:'), 'csv');
        } else {
            $fileName = $this->_exportFileName($acct->getName(), $statement->getStatementBatch()->getLabel(), 'csv');
        }

        if ($user) {
            $csv = $statement->getCsv($user);
        } else {
            $csv = $statement->getCsv();
        }

        $logger = new \Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_csv', '', '', $this->getLoggedUser() ? $this->getLoggedUser()->getId() : null, '', $statement->getId());

        return new Response($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
        ]);
    }

    private function _exportFileName($acctName, $batchName, $extension)
    {
        $name = 'SpareFoot-'.str_replace(' ', '', $acctName).'-'.$batchName.'.'.$extension;
        $name = str_replace(' ', '_', $name);
        $name = preg_replace('/[^a-zA-Z0-9\.\-\_]/', '', $name);

        return $name;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/statements
     * http://localhost:9019/billing/statements
     */
    #[Route('/billing/statements', name: 'billing_statements')]
    public function statementsAction(): RedirectResponse
    {
        // never let anyone hit old billing controller anymore
        return $this->redirectToRoute('statement_index');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/viewstatement?id=123&facility=456
     * http://localhost:9019/billing/viewstatement?id=123&facility=456
     */
    #[Route('/billing/viewstatement', name: 'billing_viewstatement')]
    public function viewstatementAction(Request $request): Response
    {
        $account = $this->getLoggedUser()->getAccount();
        if ($account->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            return $this->residualstatementAction($request);
        }

        $this->view->selectedAction = 'viewstatement';

        // Set up an interstitial to display once per session.
        $session = $request->getSession();
        if (!$session->has('showInterstitial')) {
            $session->set('showInterstitial', true);
        } elseif ($session->get('showInterstitial')) {
            $session->set('showInterstitial', false);
        }

        $this->view->showInterstitial = $session->get('showInterstitial');

        if ($this->view->showInterstitial) {
            $restriction = \Genesis_Db_Restriction::empty_();
            $restriction->setLimit(\Genesis_Db_Limit::limit(5));
            $this->view->interstitialFacilities = $this->getLoggedUser()->getManagableFacilities($restriction);
        } else {
            $this->view->interstitialFacilities = [];
        }

        $this->view->statementId = $request->get('id');
        $this->view->statement = \Genesis_Service_Statement::loadById($this->view->statementId);
        if (!$this->view->statement) {
            throw new \Exception('You must provide a valid statement id to view this page.');
        }

        $this->view->batch = $this->view->statement->getStatementBatch();

        // this is null if they're on the page that lists all facilities
        $this->view->facilityId = $request->get('facility');

        $this->view->allTableList = [];
        $this->view->freeTableList = [];
        $this->view->autoTableList = [];
        $this->view->earlyTableList = [];
        $this->view->lateTableList = [];
        $this->view->possibleDups = [];

        $facilities = $this->getLoggedUser()->getManagableFacilities(null, true);
        $this->view->facilities = $facilities;
        $this->view->availableSisterFacilities = $this->getLoggedUser()->getAccount()->getFacilities();

        foreach ($facilities as $facility) {
            // if we're looking at only 1 facility; skip all until we get to the one we want...
            if (isset($this->view->facilityId) && $facility->getId() != $this->view->facilityId) {
                continue;
            }

            $freeTransList = [];
            $paidTransList = [];
            $autoTransactions = [];

            $this->view->facility = \Genesis_Service_Facility::loadById($facility->getId());

            // build the statement from billable instances for 36 on
            if ($this->view->statement->getStatementBatchId() > 35) {
                $transactions = \Genesis_Service_BillableInstance::loadBookingsByFacilityStatementId($facility->getId(), $this->view->statement->getId());
            } else {
                $transactions = \Genesis_Service_Transaction::loadByFacilityStatementId($facility->getId(), $this->view->statement->getId());
            }

            if ($transactions) {
                foreach ($transactions as $confirmationCode => $trans) {
                    if ($trans['free'] != 1) {
                        // skip transaction if INVALID booking
                        if ($trans['booking_state'] == \Genesis_Entity_Transaction::BOOKING_STATE_INVALID) {
                            continue;
                        }

                        $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode, $this->view->statementId);

                        if ($trans['auto_state'] != \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                            // check that its not delayed in the BI before putting it in list
                            if ($bi->getReason() != \Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                                $paidTransList[] = $trans;
                            }
                        } elseif ($trans['auto_state'] == \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                            $autoTransactions[$confirmationCode] = $trans;
                        }
                    } else {
                        $freeTransList[] = $trans;
                    }

                    // look for a duplicate
                    $dupRecord = \Genesis_Service_ReservationDuplicate::load(
                        \Genesis_Db_Restriction::equal('confirmationCode', $confirmationCode))->uniqueResult();

                    // if found get the dup transaction
                    if ($dupRecord) {
                        $possibleDup = \Genesis_Service_Transaction::loadById($dupRecord->getDupConfirmationCode());

                        // only tag dups in same facility
                        if ($possibleDup->getFacility()->getId() == $facility->getId()) {
                            $this->view->possibleDups[] = $confirmationCode;
                        }
                    }
                }

                if (count($paidTransList) > 0) {
                    $this->view->allTableList[$facility->getId()] = ['FACILITY' => $facility, 'TRANSACTIONS' => $paidTransList];
                }

                if (count($freeTransList) > 0) {
                    $this->view->freeTableList[$facility->getId()] = ['FACILITY' => $facility, 'TRANSACTIONS' => $freeTransList];
                }

                if (count($autoTransactions) > 0) {
                    $this->view->autoTableList[$facility->getId()] = ['FACILITY' => $facility, 'TRANSACTIONS' => $autoTransactions];
                }
            }

            // get this facility early/late move ins
            $earlyMoves = $this->_getFacilityEarlyMoveIns($facility, $this->view->statement);
            if ($earlyMoves) {
                $this->view->earlyTableList[$facility->getId()] = ['FACILITY' => $facility, 'TRANSACTIONS' => $earlyMoves];
            }

            $lateMoves = $this->_getFacilityLateMoveIns($facility, $this->view->statement);
            if ($lateMoves) {
                $this->view->lateTableList[$facility->getId()] = ['FACILITY' => $facility, 'TRANSACTIONS' => $lateMoves];
            }
        }

        $db_connection = \Genesis_Db_Connection::getInstance();
        $stmt = $db_connection->prepare("SELECT source_id FROM account_software WHERE account_id ='".$account->getId()."'");
        $stmt->execute();
        $results = $stmt->fetchAll();
        $this->view->arr_softwares = [];
        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $this->view->scripts = ['billing/viewstatement'];

        // Render the view template with the entire view object
        return $this->render('billing/viewstatement.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/residualstatement?id=123&facility=456
     * http://localhost:9019/billing/residualstatement?id=123&facility=456
     */
    #[Route('/billing/residualstatement', name: 'billing_residualstatement')]
    public function residualstatementAction(Request $request): Response
    {
        $this->view->statementId = $request->get('id');
        $this->view->statement = \Genesis_Service_Statement::loadById($this->view->statementId);
        if (!$this->view->statement) {
            throw new \Exception('You must provide a valid statement id to view this page.');
        }

        $this->view->account = $this->view->statement->getAccount();
        $this->view->batch = $this->view->statement->getStatementBatch();

        // this is null if they're on the page that lists all facilities
        $this->view->facilityId = $request->get('facility');

        // build tables for output
        $tenantList = [];
        $reservationList = [];
        $earlyList = [];
        $lateList = [];
        $possibleDups = [];

        $earlyDate = date('Y-m-d', strtotime($this->view->batch->getEndDate()));
        $lateStartDate = date('Y-m-d', strtotime('-10 day'.$this->view->batch->getStartDate()));
        $lateEndDate = date('Y-m-d', strtotime('-1 day'.$this->view->batch->getStartDate()));

        $facilities = $this->getLoggedUser()->getManagableFacilities(null, true);
        $this->view->facilities = $facilities;
        $this->view->availableSisterFacilities = $this->getLoggedUser()->getAccount()->getFacilities();

        foreach ($facilities as $facility) {
            // if we're looking at only 1 facility; skip all untill we get to the one we want...
            if (isset($this->view->facilityId) && $facility->getId() != $this->view->facilityId) {
                continue;
            }

            $this->view->facility = \Genesis_Service_Facility::loadById($facility->getId());

            $billableInstances = \Genesis_Service_BillableInstance::loadResidualByFacilityStatementId($facility->getId(), $this->view->statement->getId());

            // get regular tenant and reservation lists
            if ($billableInstances) {
                foreach ($billableInstances as $billableInstance) {
                    $trans = \Genesis_Service_Transaction::loadById($billableInstance->getConfirmationCode());
                    if (!$trans) {
                        throw new \Exception('Could not load transaction ['.$billableInstance->getConfirmationCode().'] for billable instance: '.$billableInstance->getBillableInstanceId());
                    }

                    // skip invalid bookings
                    if ($trans->getBookingState() == \Genesis_Entity_Transaction::BOOKING_STATE_INVALID) {
                        continue;
                    }

                    if ($trans->getBookingState() == \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED) {
                        $tenantList[$billableInstance->getBillableInstanceId()] = $trans;
                    } else {
                        // only put them on reservation list if reasons != delayed which means they'll be moved to the early move in list
                        if ($billableInstance->getReason() != \Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                            $reservationList[$billableInstance->getBillableInstanceId()] = $trans;
                        }
                    }

                    // look for a duplicate
                    $dupRecord = \Genesis_Service_ReservationDuplicate::load(
                        \Genesis_Db_Restriction::equal('confirmationCode', $trans->getConfirmationCode()))->uniqueResult();

                    // if found get the dup transaction
                    if ($dupRecord) {
                        $possibleDup = \Genesis_Service_Transaction::loadById($dupRecord->getDupConfirmationCode());

                        // only tag dups in same facility
                        if ($possibleDup->getFacility()->getId() == $facility->getId()) {
                            $possibleDups[] = $trans->getConfirmationCode();
                        }
                    }
                }
            }

            // get early bookings (all future pending)
            $earlyRestriction = \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                \Genesis_Db_Restriction::greaterThan('moveIn', $earlyDate),
                \Genesis_Db_Restriction::equal('bookingState', \Genesis_Entity_Transaction::BOOKING_STATE_PENDING),
                \Genesis_Db_Restriction::isNull('statementId'));
            $earlyRestriction->setOrder(\Genesis_Db_Order::asc('moveIn'));
            $earlies = \Genesis_Service_Transaction::load($earlyRestriction);
            foreach ($earlies as $e) {
                // skip if already on the billable instance somewhere and reason != delay (which means it was on the BI but MID changed)
                $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($e->getConfirmationCode(), $this->view->statement->getId());
                if ($bi && $bi->getReason() != \Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY) {
                    continue;
                }

                $earlyList[] = $e;
            }

            // get late move-ins
            $lateRestriction = \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                \Genesis_Db_Restriction::between('moveIn', $lateStartDate, $lateEndDate),
                \Genesis_Db_Restriction::equal('bookingState', \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED));
            $lateRestriction->setOrder(\Genesis_Db_Order::asc('moveIn'));
            $lates = \Genesis_Service_Transaction::load($lateRestriction);
            foreach ($lates as $l) {
                $lateList[] = $l;
            }
        }

        // sort $tenantList by last name
        uasort($tenantList, [$this, 'cmpLastName']);

        // sort $reservationList by move in date
        uasort($reservationList, [$this, 'cmpMoveInDate']);

        $this->view->tenantTableList = $tenantList;
        $this->view->reservationTableList = $reservationList;

        $this->view->earlyTableList = $earlyList;
        $this->view->lateTableList = $lateList;

        $this->view->possibleDups = $possibleDups;

        $this->view->scripts = ['billing/residualstatement'];

        // Render the view template with the entire view object
        return $this->render('billing/residualstatement.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/updateresidualmid
     * http://localhost:9019/billing/updateresidualmid
     */
    #[Route('/billing/updateresidualmid', name: 'billing_updateresidualmid', methods: ['POST'])]
    public function updateresidualmidAction(Request $request): Response
    {
        $overrideMoveUntoOpenStatementRestriction = false;

        try {
            if (!$request->get('into_date')) {
                throw new \Exception('You must select a move-in date.');
            }

            $trans = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));

            if (!$trans) {
                throw new \Exception('Could not find booking '.$request->get('confirmation_code'));
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            $trans->setMoveIn(date('Y-m-d', strtotime(str_replace('-', '/', $request->get('into_date')))));
            \Genesis_Service_Transaction::updateMoveInDate($trans, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); // this adds to bi's if needed
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage(), 500);
        }

        return new Response('', 200);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/updateresidualfacility
     * http://localhost:9019/billing/updateresidualfacility
     */
    #[Route('/billing/updateresidualfacility', name: 'billing_updateresidualfacility', methods: ['POST'])]
    public function updateresidualfacilityAction(Request $request): JsonResponse
    {
        try {
            $ret = [];
            $confirmationCode = $request->get('confirmation_code');
            $statementId = $request->get('statement_id');

            if (!Statement::isStatementBatchOpen($statementId)) {
                throw new \Exception('StatementClosed');
            }

            // load billable instance and booking
            $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );
            if (!$billableInstance) {
                throw new \Exception('Could not load billable instance for confirmation code: '.$confirmationCode);
            }
            $booking = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));
            if (!$booking) {
                throw new \Exception('Could not load transaction for confirmation code: '.$confirmationCode);
            }

            // Check to see if facility name or unit number was changed
            $unitNumber = $request->get('unit_number');
            $newFacilityId = $request->get('facility_id');

            if ($unitNumber && $unitNumber != '') {
                $booking->setUnitNumber($unitNumber);
                \Genesis_Service_Transaction::updateUnitNumber($booking, $this->getLoggedUser());
                $ret['unitNumber'] = $unitNumber;
            } else {
                // Saved without a unit number, delete current unit number
                $booking->setUnitNumber(null);
                \Genesis_Service_Transaction::updateUnitNumber($booking, $this->getLoggedUser());
            }

            if ($newFacilityId) {
                $account = \Genesis_Service_Account::loadById($billableInstance->getAccountId());
                if (!in_array($newFacilityId, $account->getFacilityIds())) {
                    throw new \Exception('Cannot move to non-sister facility: '.$newFacilityId);
                }

                $billableInstance->setFacilityId($newFacilityId);
                $booking->setFacilityId($newFacilityId);
                $facility = \Genesis_Service_Facility::loadById($newFacilityId);
                $ret['companyCode'] = $facility->getCompanyCode();
                $ret['title'] = $facility->getTitle();
                $ret['facilityId'] = $facility->getId();

                // update BI's last if passed all other saves
                if (!\Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                    throw new \Exception("Couldn't save billable instance: ".$confirmationCode);
                }

                \Genesis_Service_Transaction::updateFacilityId($booking);
            }

            return new JsonResponse($ret);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/updateresidualcontactname
     * http://localhost:9019/billing/updateresidualcontactname
     */
    #[Route('/billing/updateresidualcontactname', name: 'billing_updateresidualcontactname', methods: ['POST'])]
    public function updateresidualcontactnameAction(Request $request): JsonResponse
    {
        try {
            $booking = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));

            $firstName = $request->get('first_name');
            $lastName = $request->get('last_name');
            if ($firstName == '' || $lastName == '') {
                throw new \Exception('You must supply both a first and last name');
            }
            $booking->setFirstName($firstName);
            $booking->setLastName($lastName);
            $ret = [
                'firstName' => $firstName,
                'lastName' => $lastName,
            ];
            $booking = \Genesis_Service_Transaction::updateName($booking, $this->getLoggedUser(), true); // this adds to bi's if needed

            return new JsonResponse($ret);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/updateresidualmoveindate
     * http://localhost:9019/billing/updateresidualmoveindate
     */
    #[Route('/billing/updateresidualmoveindate', name: 'billing_updateresidualmoveindate', methods: ['POST'])]
    public function updateresidualmoveindateAction(Request $request): JsonResponse
    {
        $overrideMoveUntoOpenStatementRestriction = false;
        $ret = [];

        try {
            $booking = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));

            $statementId = $booking->getStatementId();
            if (!Statement::isStatementBatchOpen($statementId)) {
                throw new \Exception('StatementClosed');
            }

            if (!$request->get('into_date')) {
                throw new \Exception('You must select a move-in date.');
            }

            if (!$booking) {
                throw new \Exception('Could not find booking '.$request->get('confirmation_code'));
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            $booking->setMoveIn(date('Y-m-d', strtotime(str_replace('-', '/', $request->get('into_date')))));
            \Genesis_Service_Transaction::updateMoveInDate($booking, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); // this adds to bi's if needed

            return new JsonResponse($ret);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/updateresidualrent
     * http://localhost:9019/billing/updateresidualrent
     */
    #[Route('/billing/updateresidualrent', name: 'billing_updateresidualrent', methods: ['POST'])]
    public function updateresidualrentAction(Request $request): JsonResponse
    {
        try {
            $overrideMoveUntoOpenStatementRestriction = false;
            $bookingUpdates = [];

            $confirmationCode = $request->get('confirmation_code');
            $statementId = $request->get('id');
            $residualPercent = $request->get('residual_percent');
            $isTenant = $request->get('is_tenant');

            if (!Statement::isStatementBatchOpen($statementId)) {
                throw new \Exception('StatementClosed');
            }

            $booking = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));
            if (!$booking) {
                throw new \Exception('Could not load transaction for confirmation code: '.$confirmationCode);
            }

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            // early late will come and will have early_late = 1 parameter, need to add to bi's
            if ($request->get('early_late')) {
                if (!$request->get('into_date')) {
                    throw new \Exception('You must select a move-in date.');
                }
                $booking->setMoveIn(date('Y-m-d', strtotime(str_replace('-', '/', $request->get('into_date')))));
                \Genesis_Service_Transaction::updateMoveInDate($booking, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction); // this adds to bi's if needed
            }

            // load billable instance and booking
            $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );
            if (!$billableInstance) {
                throw new \Exception('Could not load billable instance for confirmation code: '.$confirmationCode.' statement Id: '.$statementId);
            }

            $isContinuingTenant = \Genesis_Service_BillableInstance::isContinuingTenant($billableInstance->getConfirmationCode());
            // Those that are not continuing tenants need their booking state updated
            if (!$isContinuingTenant) {
                // reset the booking state to CONFIRMED/PENDING and let the rest of the logic flow as normal
                $booking->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                $bookingUpdates[] = 'updateState';
            }

            $thisMonthCollected = str_replace('$', '', $request->get('rent_other'));
            $amountIsValid = false;
            if (is_numeric($thisMonthCollected)) {
                $amountIsValid = true;
            }
            if ($thisMonthCollected === '0') {
                $amountIsValid = true;
            }
            if ($thisMonthCollected === '0.00') {
                $amountIsValid = true;
            }
            if ($thisMonthCollected < 0) {
                throw new \Exception('Enter an amount for the rent collected that is greater than 0.');
            }
            if (!$amountIsValid) {
                throw new \Exception('Enter a valid amount for the rent collected.');
            }
            // carry over to next month if the flag was set and amount collected is greater than unit price
            if (!empty($isTenant) && $thisMonthCollected > $billableInstance->getUnitPrice()) {
                $booking->setDataNextMonthAmountCollected($thisMonthCollected);
            }

            $billableInstance->setAmountCollected($thisMonthCollected);
            $billableInstance->setSparefootCharge($thisMonthCollected * $residualPercent);
            $billableInstance->setReason(null);
            \Genesis_Service_Transaction::updateBookingData($booking);
            $booking->appendCallCenterNotes('Residual booking edited in MyFoot.');
            $bookingUpdates[] = 'updateCallCenterNotes';

            // do the rest of the updates
            foreach ($bookingUpdates as $updateFunction) {
                \Genesis_Service_Transaction::$updateFunction($booking);
            }

            // update BI's last if passed all other saves
            if (!\Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                throw new \Exception("Couldn't save billable instance: ".$confirmationCode);
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/denyresidualmovein
     * http://localhost:9019/billing/denyresidualmovein
     */
    #[Route('/billing/denyresidualmovein', name: 'billing_denyresidualmovein', methods: ['POST'])]
    public function denyresidualmoveinAction(Request $request): JsonResponse
    {
        try {
            $bookingUpdates = [];
            $confirmationCode = $request->get('confirmation_code');
            $statementId = $request->get('id');

            if (!Statement::isStatementBatchOpen($statementId)) {
                throw new \Exception('StatementClosed');
            }

            $booking = \Genesis_Service_Transaction::loadById($request->get('confirmation_code'));
            if (!$booking) {
                throw new \Exception('Could not load transaction for confirmation code: '.$confirmationCode);
            }
            // load billable instance and booking
            $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $confirmationCode,
                $statementId
            );

            $billableInstance->setAmountCollected(0);
            $billableInstance->setSparefootCharge(0);
            $booking->appendCallCenterNotes('Residual booking edited in MyFoot. $0');
            $bookingUpdates[] = 'updateCallCenterNotes';
            if ($booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                && $booking->getBookingState() === \Genesis_Entity_Transaction::BOOKING_STATE_PENDING) {
                if (\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS, ['account_id' => $billableInstance->getAccountId()])) {
                    $booking->setReviewStatus(\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW);
                    $bookingUpdates[] = 'updateReviewStatus';
                }
            }

            $disputeReason = $request->get('rent-zero-reason');
            if (empty($disputeReason)) {
                // If there is no dispute Reason, assume that it is cancelled. Needs your review, late move ins, and auto matched do not take dispute reasons
                $booking->setDisputeReason(\Genesis_Entity_BillableInstance::REASON_CANCELED);
                $bookingUpdates[] = 'updateDisputeReason';
                $booking->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
                $bookingUpdates[] = 'updateState';
            } else {
                // Do specific actions based on the dispute reason. This is used for the Tenants Section
                switch ($disputeReason) {
                    case \Genesis_Entity_BillableInstance::REASON_MOVED_OUT:
                        // assume first of last month as move-out date
                        $booking->setMoveOut(date('Y-m-d', strtotime('last day of -1 months')));
                        $bookingUpdates[] = 'updateMoveOut';
                        break;
                    case \Genesis_Entity_BillableInstance::REASON_DELIQUENCY:
                        // TODO: handle delinquent-specifics somewhere
                        // NOTE FROM ALAN: This was here prior, so adding it in. Not sure where delinquency logic lives atm
                        // $delinquentSpecifics = $request->get('delinquent-specifics');
                        break;
                    default:
                        // other cases don't require any action
                        break;
                }
            }

            // do the rest of the updates
            foreach ($bookingUpdates as $updateFunction) {
                \Genesis_Service_Transaction::$updateFunction($booking);
            }

            // Set a reason for dispute if there is one
            $billableInstance->setReason($request->get('rent-zero-reason'));

            // update BI's last if passed all other saves
            if (!\Genesis_Service_BillableInstance::save($billableInstance, $this->getLoggedUser())) {
                throw new \Exception("Couldn't save billable instance: ".$confirmationCode);
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Is this used anywhere? Looks janky -EH 10/11/2012
     * Sample:
     * https://myfoot.sparefoot.com/billing/changestate
     * http://localhost:9019/billing/changestate.
     */
    #[Route('/billing/changestate', name: 'billing_changestate', methods: ['POST'])]
    public function changestateAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('statement_ids')) {
                return new JsonResponse(['error' => 'Missing statement IDs'], 400);
            }

            $codes = explode(',', $request->get('statement_ids'));
            $transItr = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::in('confirmationCode', $codes));

            foreach ($transItr as $trans) {
                /* @var \Genesis_Entity_Transaction $trans */
                $trans->setBookingState($request->get('state'));
                \Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
                if ($request->get('state') != \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED) {
                    $trans->setDisputeReason('');
                    \Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
                }
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/changefacility
     * http://localhost:9019/billing/changefacility.
     */
    #[Route('/billing/changefacility', name: 'billing_changefacility', methods: ['POST'])]
    public function changefacilityAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            if (!$request->get('facility_id')) {
                throw new \Exception('Please select a facility to move this booking to.');
            }

            $code = $request->get('id');

            $trans = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();
            /* @var \Genesis_Entity_Transaction $trans */

            $trans->setFacilityId($request->get('facility_id'));

            // note: statement to account relationship is 1-1 and since users can only
            // move bookings to facilities within their account we don't have to check the statement id
            \Genesis_Service_Transaction::updateFacilityId($trans, $this->getLoggedUser());

            // update billable instance reason & facility ID
            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(\Genesis_Entity_BillableInstance::REASON_FACILITY_CHANGE);
            $bi->setFacilityId($request->get('facility_id'));
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/changecustomername
     * http://localhost:9019/billing/changecustomername.
     */
    #[Route('/billing/changecustomername', name: 'billing_changecustomername', methods: ['POST'])]
    public function changecustomernameAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            $code = $request->get('id');

            $trans = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();
            /** @var \Genesis_Entity_Transaction $trans */
            $statementId = $trans->getStatementId();
            if (!Statement::isStatementBatchOpen($statementId)) {
                throw new \Exception('StatementClosed');
            }

            if (!$request->get('first_name')) {
                throw new \Exception('Please enter a first name.');
            }
            if (!$request->get('last_name')) {
                throw new \Exception('Please enter a last name.');
            }

            $trans->setFirstName($request->get('first_name'));
            $trans->setLastName($request->get('last_name'));

            \Genesis_Service_Transaction::updateName($trans, $this->getLoggedUser());

            // update billable instance reason
            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(\Genesis_Entity_BillableInstance::REASON_NAME_CHANGE);
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/submitdispute
     * http://localhost:9019/billing/submitdispute.
     */
    #[Route('/billing/submitdispute', name: 'billing_submitdispute', methods: ['POST'])]
    public function submitdisputeAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            $code = $request->get('id');

            $trans = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();
            /* @var \Genesis_Entity_Transaction $trans */

            $trans->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
            $trans->setDisputeReason($request->get('reason'));
            \Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            \Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
            \Genesis_Dao_Transaction::unCheck($trans->getConfirmationCode());

            // update billable instance reason
            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(\Genesis_Entity_BillableInstance::REASON_CANCELED);
            $bi->setSparefootCharge(0);
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/submitautoconfirmeddispute
     * http://localhost:9019/billing/submitautoconfirmeddispute.
     */
    #[Route('/billing/submitautoconfirmeddispute', name: 'billing_submitautoconfirmeddispute', methods: ['POST'])]
    public function submitautoconfirmeddisputeAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            $trans = \Genesis_Service_Transaction::loadById($request->get('id'));

            if ($request->get('dispute')) {
                if (!$trans->getReviewStatus()) {
                    $trans->setReviewStatus(\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW);
                } elseif ($trans->getReviewStatus() == \Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                    throw new \Exception('This booking is currently under review.');
                } elseif ($trans->getReviewStatus() == \Genesis_Entity_Transaction::STATUS_REVIEWED) {
                    throw new \Exception('This booking has already been reviewed.');
                }
            } else {
                $trans->setReviewStatus(null);
            }

            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());

            if ($request->get('dispute')) {
                $disputeReason = trim($request->get('reason'));
                if (!$disputeReason) {
                    throw new \Exception('You must enter a dispute reason.');
                }

                $trans->setDisputeReason($disputeReason);
                $trans->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED);
                $bi->setReason(\Genesis_Entity_BillableInstance::REASON_CANCELED);
                $bi->setSparefootCharge(0);
            } else {
                $trans->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
                $bi->setReason(null);
                $bi->setSparefootCharge($trans->getBidAmount());
            }

            \Genesis_Service_Transaction::updateReviewStatus($trans, $this->getLoggedUser());
            \Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            \Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/removedispute
     * http://localhost:9019/billing/removedispute.
     */
    #[Route('/billing/removedispute', name: 'billing_removedispute', methods: ['POST'])]
    public function removedisputeAction(Request $request): JsonResponse
    {
        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            $code = $request->get('id');

            $trans = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::equal('confirmationCode', $code))->uniqueResult();

            // update billable instance reason
            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId(
                $trans->getConfirmationCode(),
                $trans->getStatementId()
            );

            if (is_bool($bi) && !$bi) {
                throw new \Exception('Billable instance could not be loaded');
            }

            $trans->setBookingState(\Genesis_Entity_Transaction::BOOKING_STATE_PENDING);
            $trans->setDisputeReason('');
            \Genesis_Service_Transaction::updateDisputeReason($trans, $this->getLoggedUser());
            \Genesis_Service_Transaction::updateState($trans, $this->getLoggedUser());

            $bi->setReason(null);
            $bi->setSparefootCharge($trans->getBidAmount());
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            if ($request->get('new_date')) {
                $this->submitmoveinchangeAction($request);
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/billing/confirmconsumercontact
     * http://localhost:9019/billing/confirmconsumercontact
     */
    #[Route('/billing/confirmconsumercontact', name: 'billing_confirmconsumercontact', methods: ['POST'])]
    public function confirmconsumercontactAction(Request $request): Response
    {
        try {
            if (!$request->get('id')) {
                return $this->redirectToRoute('billing_index');
            }

            $consumerContactId = $request->get('id');
            $consumerContact = \Genesis_Service_ConsumerContact::load(\Genesis_Db_Restriction::equal('id', $consumerContactId))->uniqueResult();
            if (!$consumerContact) {
                throw new \Exception('Could not load consumer contact by id '.$consumerContactId);
            }

            // if a booking has already been mapped to this conf code, then return it
            if ($consumerContact->getConfirmationCode()) {
                return new Response($consumerContact->getConfirmationCode(), 200);
            }

            $statementId = $request->get('statement_id');
            $statement = \Genesis_Service_Statement::loadById($statementId);
            if (!$statement
                // TODO: add check to make sure user has access to this statement
                // || $statement->getAccountId() != $user->getAccountId()
            ) {
                throw new \Exception('Unable to load statement by id '.$statementId);
            }

            // create booking
            $booking = \Genesis_Service_Transaction::createByConsumerContact($consumerContact);

            // create user (this also saves the user in the db)
            $identifier = 'cc-'.$consumerContact->getId().'@sparefoot.conscontact';
            $user = \Genesis_Service_Transaction::createUserForBookingCreatedFromConsumerContact(
                $booking, $identifier,
                $consumerContact->getEmail(), $consumerContact->getPhone()
            );
            $user = \Genesis_Service_User::save($user);
            $booking->setUserId($user->getId());

            // set move-in date to the first day of the statement period
            $booking->setMoveIn($statement->getStatementBatch()->getStartDate());

            // set unit as cheapest unit at the facility
            if (!$booking->getUnitId()) {
                $unit = \Genesis_Service_StorageSpace::loadCheapestByFacilityId($consumerContact->getListingAvailId());
                if (!$unit) {
                    throw new \Exception('Unable to load a unit for consumer contact '.$consumerContact->getListingAvailId());
                }
                $booking->setUnitId($unit->getId());
            }

            $booking->appendCallCenterNotes("Manually created offline booking from consumer contact ({$consumerContact->getId()}) via statement UI");

            // save booking to db
            $booking = \Genesis_Service_Transaction::create($booking);
            if (!$booking) {
                throw new \Exception('Could not create booking record');
            }

            // map this booking to the consumer contact so it won't be considered in the future
            $consumerContact->setConfirmationCode($booking->getConfirmationCode());
            \Genesis_Service_ConsumerContact::save($consumerContact);

            // update move-in date to handle moving unto correct batch
            \Genesis_Service_Transaction::updateMoveInDate($booking, $this->getLoggedUser(),
                $overrideQuoteExpirationRestriction = false,
                $overrideMaximumBookingDelayRestriction = false,
                $overrideMoveUntoOpenStatementRestriction = true);

            return new Response($booking->getConfirmationCode(), 200);
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage(), 500);
        }
    }

    /**
     * CPA statement only
     * Sample:
     * https://myfoot.sparefoot.com/billing/submitmoveinchange
     * http://localhost:9019/billing/submitmoveinchange.
     */
    #[Route('/billing/submitmoveinchange', name: 'billing_submitmoveinchange', methods: ['POST'])]
    public function submitmoveinchangeAction(Request $request): JsonResponse
    {
        $overrideMoveUntoOpenStatementRestriction = false;

        try {
            if (!$request->get('id')) {
                return new JsonResponse(['error' => 'Missing transaction ID'], 400);
            }

            $code = $request->get('id');
            $trans = \Genesis_Service_Transaction::loadById($code);

            // If the logged in user is a myfoot god, we need to set $overrideMoveUntoOpenStatementRestriction to true so
            // the correct conditions are met in Genesis_Service_Transaction::updateMoveInDate to correctly mark the MID.
            // This is needed because within Genesis_Service_Transaction::updateMoveInDate, when $loggedUser->getUserAccess()->getManageableFacilityIds()
            // is called it uses the incorrect account id. It uses the account id of the logged in god user instead of the account id of the account the god user is viewing.
            // Because of this, if you didn't pass in $overrideMoveUntoOpenStatementRestriction in as true, the facility Id
            // wont be in the list of $loggedUser->getUserAccess()->getManageableFacilityIds() and booking will be left as pending.
            // See https://sparefoot.atlassian.net/browse/EPO-389.
            if ($this->getLoggedUser()->isMyFootGod()) {
                $overrideMoveUntoOpenStatementRestriction = true;
            }

            $trans->setMoveIn(date('Y-m-d', strtotime($request->get('new_date'))));
            \Genesis_Service_Transaction::updateMoveInDate($trans, $this->getLoggedUser(), true, false, $overrideMoveUntoOpenStatementRestriction);
            // NOTE: billable instance reset is done in updateMoveInDate() service

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    protected function getSideBarContent(): string
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Order::ASC('title'));
        $loggedUser = $this->getLoggedUser();

        // Get current action name from the view object if available, otherwise use a default
        $selectedAction = $this->view->getActionName() ?? 'unknown';

        if ($selectedAction != 'viewstatement') {
            return $this->getTwig()->render('facility/left-sidebar-content.html.twig', [
                'view' => $this->view,
                'facilities' => $facilities,
                'loggedUser' => $loggedUser,
                'selectedAction' => $selectedAction,
            ]);
        }

        return '';
    }

    protected function getTab(): string
    {
        return self::TAB_BILLING;
    }

    public function mirAction(Request $request)
    {
        // these are used to build the back link
        $this->view->statementId = $request->get('sid', '');
        $this->view->facilityId = $request->get('fid', '');
    }

    // returns an array of possible early move in.  1st 15 days of current month
    private function _getFacilityEarlyMoveIns($facility, $statement)
    {
        $earlyMoveIns = [];

        $dtFirstDay = date('Y-m-d', strtotime('+1 day'.$statement->getStatementBatch()->getEndDate())); // date('Y-m-d', mktime(0, 0, 0, date("m") , 1, date("Y")));
        $dtLastDay = date('Y-m-d', strtotime('+15 day'.$statement->getStatementBatch()->getEndDate())); // date('Y-m-d', mktime(0, 0, 0, date("m") , 15, date("Y")));

        $includeStates = [\Genesis_Entity_Transaction::BOOKING_STATE_PENDING, \Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED];
        $earlyMoveIns = \Genesis_Service_Transaction::loadByFacilityMoveInDate($facility->getId(), $dtFirstDay, $dtLastDay, $includeStates, true);

        return $earlyMoveIns;
    }

    // returns an array of possible late move in.  last 10 days of previous month
    private function _getFacilityLateMoveIns($facility, $statement)
    {
        $lateMoveIns = [];

        $dtFirstDay = date('Y-m-d', strtotime('-10 day'.$statement->getStatementBatch()->getStartDate())); // date('Y-m-d', mktime(0, 0, 0, date("m") , -10, date("Y")));
        $dtLastDay = date('Y-m-d', strtotime('-1 day'.$statement->getStatementBatch()->getStartDate())); // date('Y-m-d', mktime(0, 0, 0, date("m") , 0, date("Y")));

        $includeStates = [\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED, \Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED];
        $lateMoveIns = \Genesis_Service_Transaction::loadByFacilityMoveInDate($facility->getId(), $dtFirstDay, $dtLastDay, $includeStates, true);

        return $lateMoveIns;
    }

    // return positive is $a is ahead of $b
    public function cmpLastName(\Genesis_Entity_Transaction $a, \Genesis_Entity_Transaction $b)
    {
        return strcmp(strtolower($a->getLastName()), strtolower($b->getLastName()));
    }

    // return positive is $a is ahead of $b
    public function cmpMoveInDate(\Genesis_Entity_Transaction $a, \Genesis_Entity_Transaction $b)
    {
        $d1 = strtotime($a->getMoveIn());
        $d2 = strtotime($b->getMoveIn());

        if ($d1 > $d2) {
            return 1;
        } else {
            return -1;
        }
    }
}
