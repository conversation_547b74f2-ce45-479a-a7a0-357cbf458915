<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserCookie;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Twig\Environment;
use Twig\Loader\FilesystemLoader;

/**
 * Abstract Common Controller.
 *
 * Base controller containing shared functionality for both public and restricted controllers
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractCommonController extends AbstractController
{
    /**
     * @var SessionInterface
     */
    protected $_session;
    protected $view;
    protected $request;
    protected $_loggedUser = false;

    /**
     * Initialize controller - override in child controllers.
     */
    protected function _init(): ?RedirectResponse
    {
        // Override in child controllers
        return null;
    }

    /**
     * Get the current tab for navigation - override in child controllers.
     */
    protected function getTab(): string
    {
        return '';
    }

    /**
     * Dispatch error messages.
     */
    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    /**
     * Dispatch success messages.
     */
    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation.
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);

        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Helper method to extract controller name from Symfony route.
     */
    protected function getControllerNameFromRoute(?string $route): string
    {
        if (!$route) {
            return '';
        }

        // Extract controller name from route pattern
        // This is a simplified approach - you may need to adjust based on your routing patterns
        $parts = explode('_', $route);

        return $parts[0] ?? '';
    }

    /**
     * Set the request object.
     */
    public function setRequest(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Get the request object.
     */
    public function getRequest(): Request
    {
        return $this->request;
    }

    protected function getControllerActionName(Request $request): string
    {
        $controller = $request->attributes->get('_controller');
        /*
            Example: $parts = ^ array:3 [▼
                    0 => "Sparefoot\PitaService\Controller\FacilityController"
                    1 => ""
                    2 => "indexAction"
            ]
            */
        $parts = explode(':', $controller);

        $actionName = end($parts);
        $actionName = str_replace('Action', '', $actionName);

        return $actionName;
    }

    protected function getTwig(): Environment
    {
        static $twigInstance;

        if (!$twigInstance) {
            $loader = new FilesystemLoader(PROJECT_TEMPLATE_DIR);
            $twigInstance = new Environment($loader);
        }

        return $twigInstance;
    }

    protected function initSiteTop(): void
    {
        $this->view->siteTop = new \stdClass();
        $user = $this->getUser();
        $this->view->siteTop->haveLoggedUser = $user;
        $this->view->siteTop->segmentScript = '';

        // Segment code only if key is set
        $segment_key = getenv('SEGMENT_KEY');
        if ($segment_key) {
            $loggedUser = $this->getLoggedUser();
            $segmentScript = '<script>
            !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on"];analytics.factory=function(t){return function(){var e=Array.prototype.slice.call(arguments);e.unshift(t);analytics.push(e);return analytics}};for(var t=0;t<analytics.methods.length;t++){var e=analytics.methods[t];analytics[e]=analytics.factory(e)}analytics.load=function(t,e){var n=document.createElement("script");n.type="text/javascript";n.async=!0;n.src=("https:"===document.location.protocol?"https://":"http://")+"cdn.segment.com/analytics.js/v1/"+t+"/analytics.min.js";var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(n,o);analytics._loadOptions=e};analytics.SNIPPET_VERSION="4.1.0";
                analytics.load("'.$segment_key.'");
            }}();';

            if ($loggedUser) {
                $facility = \Genesis_Service_Facility::loadById(UserCookie::get(UserCookie::ACTIVE_FACILITY_ID));
                $integrationTypeScript = '';
                if ($facility) {
                    $integrationTypeScript = ',
                integration_type: '.json_encode($facility->getCorporation()->getSource()->getSource());
                }

                $segmentScript .= '
            analytics.identify("'.$loggedUser->getId().'", {
                name: '.json_encode($loggedUser->getFirstName().' '.$loggedUser->getLastName()).',
                email: "'.$loggedUser->getEmail().'",
                sf_account_id: "'.$loggedUser->getAccount()->getSfAccountId().'",
                account_name: '.json_encode($loggedUser->getAccount()->getName()).',
                bid_type: "'.$loggedUser->getAccount()->getBidType().'"'.$integrationTypeScript.'
            });';
            }

            $segmentScript .= '
            analytics.page();
        </script>';

            $this->view->siteTop->segmentScript = $segmentScript;
        }
    }

    /**
     * @return \Genesis_Entity_UserAccess|false
     */
    protected function getLoggedUser()
    {
        /** @var SecurityUser */
        $user = $this->getUser();
        if (!$user) {
            return false;
        }
        $this->_loggedUser = $user->getGenesisUserAccess() ?? false;
        $this->view->loggedUser = $this->_loggedUser;

        return $this->_loggedUser;
        if (!$this->_loggedUser) {
            // The account id the user posed should be in session by now, if they are a god this will modify
            // user access and allow it
            try {
                $this->_loggedUser = User::getLoggedUser();
            } catch (\Exception $e) {
                // Logout user if there is no auth bearer token in cookie
                $message = 'Auth token is invalid or has expired.';
                if (!\Genesis_Config_Server::isProduction()) {
                    $message .= '<br/>Exception: '.$e->getMessage();
                }
                $this->getSession()->set('logoutMessage', $message);

                return $this->redirectToRoute('login_logout');
            }
        }

        return $this->view->loggedUser = $this->_loggedUser;
    }

    /**
     * @return SessionInterface
     */
    protected function getSession(): SessionInterface
    {
        if (!$this->_session) {
            $this->_session = $this->container->get('request_stack')->getCurrentRequest()->getSession();
            $this->view->session = $this->_session;
        }

        return $this->_session;
    }
}
