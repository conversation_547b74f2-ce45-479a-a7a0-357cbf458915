<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DocumentController extends AbstractRestrictedController
{
    /**
     * Terms Addendum action.
     *
     * Sample:
     * https://myfoot.sparefoot.com/document/terms-addendum
     * http://localhost:9019/document/terms-addendum
     */
    #[Route('/document/terms-addendum', name: 'document_terms_addendum')]
    public function termsAddendumAction(): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        // If account has no terms addendum, then send them back home
        if (!$account->getTermsAddendum()) {
            throw new \InvalidArgumentException('No terms addendum PDF available for account '.$account->getSfAccountId());
        }

        // Determine filename and absolute path to the terms addendum file
        $absFilePath = $this->getParameter('kernel.project_dir')."/public/pdf/terms-addenda/{$account->getSfAccountId()}.pdf";

        return $this->showPdf($absFilePath, $account->getSfAccountId(), 'terms addendum');
    }

    /**
     * Check if a terms version is valid for LTV (Long Term Value) programs.
     */
    public static function isValidLtvVersion(?string $termsVersion): bool
    {
        if (!$termsVersion) {
            return false;
        }

        $validLtvVersions = [
            '********',
            '********',
            '********',
            '********',
            '********',
        ];

        return in_array($termsVersion, $validLtvVersions);
    }

    /**
     * Terms action.
     *
     * Sample:
     * https://myfoot.sparefoot.com/document/terms
     * http://localhost:9019/document/terms
     */
    #[Route('/document/terms', name: 'document_terms')]
    public function termsAction(): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        if ($account->getId() != 16) { // not cubesmart
            $domain = \Genesis_Config_Server::getEnvDomain();
            $termsVersion = $account->getTermsVersion();
            if (!$termsVersion) {
                // This should never happen, but show a link at least.
                $url = "https://www.sparefoot.{$domain}/legal/client.html";
            } else {
                $termsProgram = 'client';
                if ($account->getBidType() == \Genesis_Entity_Account::BID_TYPE_RESIDUAL
                && self::isValidLtvVersion($termsVersion)) {
                    $termsProgram = 'ltv';
                }
                $url = "https://www.sparefoot.{$domain}/legal/{$termsProgram}/{$termsVersion}.html";
            }

            return $this->redirect($url);
        }

        // Determine filename and absolute path to the terms addendum file
        $absFilePath = $this->getParameter('kernel.project_dir')."/public/pdf/tos/{$account->getSfAccountId()}.pdf";

        return $this->showPdf($absFilePath, $account->getSfAccountId(), 'terms of service');
    }

    /**
     * Show PDF file as response.
     */
    private function showPdf(string $absFilePath, int $accountId, string $description): Response
    {
        // Ensure the file exists
        if (!file_exists($absFilePath)) {
            throw new \InvalidArgumentException("No '$description' PDF available for account $accountId");
        }

        // Read file contents
        $content = file_get_contents($absFilePath);
        if (!$content) {
            throw new \Exception("Empty '$description' PDF detected for account $accountId");
        }

        // Return PDF response
        return new Response(
            $content,
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Length' => filesize($absFilePath),
                'Content-Disposition' => 'inline; filename="'.basename($absFilePath).'"',
            ]
        );
    }
}
