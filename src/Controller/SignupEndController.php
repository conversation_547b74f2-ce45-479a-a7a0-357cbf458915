<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\Util;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Utils\CsrfUtil;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SignupEndController extends AbstractRestrictedController
{
    public const SIGNUP_END_CSRF_TOKEN = 'signup_end_csrf_token';

    public function _init(): ?RedirectResponse
    {
        $this->view->loggedUser = $this->getLoggedUser();

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/terms
     * http://localhost:9019/signup-end/terms
     */
    #[Route('/signup-end/terms', name: 'signup_end_terms')]
    public function termsAction(Request $request): Response
    {
        $user = $this->getLoggedUser();

        $account = $user->getAccount();
        $termsVersion = \Genesis_Service_Cpanw_Account::CLIENT_TERMS_VERSION;

        $this->view->action = 'terms';
        $this->view->loggedUser = $user;
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN);
        $this->view->scripts = ['signup-end/terms'];
        $this->view->backlink = '/signup-start/company/';
        $this->view->termsVersion = $termsVersion;
        $this->view->account = $account;
        $this->view->bidType = $account->getBidType();
        $this->view->agree1 = $account->getTermsVersion() ? 1 : $request->request->get('agree1', false);
        $this->view->agree2 = $account->getTermsVersion() ? 1 : $request->request->get('agree2', false);
        $this->view->agree3 = $account->getTermsVersion() ? 1 : $request->request->get('agree3', false);
        $this->view->agree4 = $account->getTermsVersion() ? 1 : $request->request->get('agree4', false);
        $this->view->error = '';
        if ($request->isMethod('POST')) {
            $agree1 = $request->request->get('agree1', false);
            $agree2 = $request->request->get('agree2', false);
            $agree3 = $request->request->get('agree3', false);
            $agree4 = $request->request->get('agree4', false);
            if (
                ($account->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT
                    || $agree1 === 'on'
                )
                && $agree2 === 'on'
                && $agree3 === 'on'
                && $agree4 === 'on'
            ) {
                if (!CsrfUtil::validateToken(self::SIGNUP_END_CSRF_TOKEN, $request->request->get('csrf_token'))) {
                    $this->view->error = 'There was an error during the submission. Please refresh and try again.';
                } else {
                    // Save terms info
                    $account->setTermsVersion($termsVersion);
                    $account->setTermsAgreedByUserId($user->getId());
                    $account->setTermsAgreedDate(date('Y-m-d H:i:s'));

                    \Genesis_Service_Account::save($account);

                    return $this->redirectToRoute('signup_end_billing');
                }
            }
        }

        // Make sure the user can go back to the other controller
        $session = User::getSession($request);
        $session->set('userId', $user->getId());

        return $this->render('signup-end/terms.html.twig', ['view' => $this->view], new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/billing
     * http://localhost:9019/signup-end/billing
     */
    #[Route('/signup-end/billing', name: 'signup_end_billing')]
    public function billingAction(Request $request): Response
    {
        $this->view->backlink = '/signup-end/terms/';
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN);

        $user = $this->getLoggedUser();

        $this->view->backlink = '/signup-end/terms/';
        $this->view->csrf_token = CsrfUtil::getToken(self::SIGNUP_END_CSRF_TOKEN);

        $user = $this->getLoggedUser();

        $this->view->user = $user;
        $account = \Genesis_Service_Account::loadById($user->getAccountId());
        $this->view->accountId = $account->getAccountId();

        $this->view->completed = 'false';

        $this->view->beId = 0;
        $this->view->emails = $user->getEmail();
        $this->view->paymentTypeNickname = '';
        $this->view->address = '';
        $this->view->city = '';
        $this->view->state = '';
        $this->view->zip = '';

        $this->view->creditCardNumber = '';
        $this->view->ccType = ''; // VISA, etc
        $this->view->ccNsId = '';
        $this->view->ccName = '';
        $this->view->ccExpM = '';
        $this->view->ccExpY = '';
        // $this->view->myFootLink = AccountMgmt_Models_Util::getMyFootLandingPage();
        $this->view->myFootLink = '';

        // pass field for prepop if needed
        $be = \Genesis_Service_BillableEntity::loadByAccount($account)->uniqueResult();

        // if there is already a billable entity for this account, then signal view to notify user they're done
        $this->view->completed = 'false';
        if ($be) {
            $this->view->completed = 'true';
        }

        $this->view->scripts = ['signup-end/billing'];
        // missing in template view
        $this->view->billableEntityId = $be ? $be->getId() : 0;
        $this->view->creditCardNumber = $this->getParam($request, 'credit_card_number');
        $this->view->ccType = $this->getParam($request, 'ccType'); // VISA, etc
        $this->view->ccNsId = $this->getParam($request, 'ccNsId');
        $this->view->creditCardName = $this->getParam($request, 'credit_card_name');
        $this->view->creditCardExpirationMonth = $this->getParam($request, 'credit_card_expiration_month');
        $this->view->creditCardExpirationYear = $this->getParam($request, 'credit_card_expiration_year');
        $this->view->action = 'billing';
        $this->view->error = $this->getParam($request, 'error');

        return $this->render('signup-end/billing.html.twig', ['view' => $this->view], new Response('', 200, [
            'X-Layout' => 'signup-layout',
        ]));
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/software
     * http://localhost:9019/signup-end/software
     */
    #[Route('/signup-end/software', name: 'signup_end_software')]
    public function softwareAction(): Response
    {
        $this->view->myFootLink = Util::getMyFootLandingPage();

        /* TODO: actually save which software integrations were reported here */
        /* plus hide/show the add facility form based on whether it will be a manual integration or not */

        $this->view->scripts = ['signup-end/software'];
        $this->view->backlink = '/signup-end/billing';

        return $this->render('signup-end/software.html.twig', ['view' => $this->view]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/record-software
     * http://localhost:9019/signup-end/record-software
     */
    #[Route('/signup-end/record-software', name: 'signup_end_record_software', methods: ['POST'])]
    public function recordSoftwareAction(Request $request): JsonResponse
    {
        try {
            $user = $this->getLoggedUser();
            $account = \Genesis_Service_Account::loadById($user->getAccountId());

            // Record their software types
            $integrationType = $request->get('integration_type');
            if (is_array($integrationType)) {
                foreach ($integrationType as $val) {
                    $software = new \Genesis_Entity_AccountSoftware();
                    $software->setAccountId($account->getId());
                    $software->setSourceId($val);
                    $software->setUserId($user->getId());
                    \Genesis_Service_AccountSoftware::save($software);
                }
            }

            $facilityName = $request->get('facility_name');
            if ($facilityName) {
                // Make the first facility
                $corp = \Genesis_Service_Corporation::loadByAcctIdAndSourceId(
                    $account->getAccountId(),
                    \Genesis_Entity_Source::ID_MANUAL
                );

                if (!$corp) {
                    $corp = new \Genesis_Entity_ManualCorporation();
                    $corp->setAccountId($account->getAccountId());
                    $corp->setCreated(date('Y-m-d H:i:s', time()));
                    $corp->setCorpname($account->getName());
                    $corp->setSourceId(\Genesis_Entity_Source::ID_MANUAL);
                    $corp = \Genesis_Service_Corporation::save($corp);
                }

                $facility = new \Genesis_Entity_Facility();
                $facility->setTitle($facilityName);
                $facility->setCorporationId($corp->getId());
                $facility->setSourceId($corp->getSourceId());
                $facility->setActive(0);
                $facility->setPublished(1);
                $facility->setApproved(1);
                $facility->setPhone($request->get('phone'));

                $address1 = $request->get('address1');
                $city = $request->get('city');
                $state = $request->get('state');
                $zip = $request->get('zip');

                $location = \Genesis_Service_Location::loadByAddress($address1, $city, $state, $zip);

                // Does this location already exist?
                if (!$location) {
                    // Call geocoder
                    $fullAddress = $request->get('address').' '.$city.' '.$state.' '.$zip;
                    $location = \Genesis_Service_Location::geoCodePhysicalAddress($fullAddress);
                    $location = \Genesis_Service_Location::save($location);
                }

                $facility->setLocationId($location->getId());
                \Genesis_Service_Facility::save($facility, $user);
            }

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/signup-end/same-co-address
     * http://localhost:9019/signup-end/same-co-address
     */
    #[Route('/signup-end/same-co-address', name: 'signup_end_same_co_address', methods: ['POST'])]
    public function sameCoAddressAction(): JsonResponse
    {
        try {
            $user = $this->getLoggedUser();
            $account = \Genesis_Service_Account::loadById($user->getAccountId());

            if ($account && $account->getLocation()) {
                $address = [
                    'name' => $account->getName(),
                    'addr' => $account->getLocation()->getAddress1(),
                    'city' => $account->getLocation()->getCity(),
                    'state' => $account->getLocation()->getState(),
                    'zip' => $account->getLocation()->getZip(),
                    'phone' => $user->getPhone(),
                ];

                return new JsonResponse($address);
            } else {
                return new JsonResponse(['success' => false, 'message' => 'No corporate address on file.']);
            }
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}
