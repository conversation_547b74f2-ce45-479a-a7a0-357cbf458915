<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Utils\CsrfUtil;
use Sparefoot\SalesForce\Client;
use Sparefoot\SalesForce\SalesForceClientException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PaymentController extends AbstractRestrictedController
{
    public const PAYMENT_CSRF_TOKEN = 'payment_csrf_token';

    /**
     * Payment index - Directs users to appropriate payment flow.
     *
     * GET /payment
     * Sample:
     * https://myfoot.sparefoot.com/payment
     * http://localhost:9019/payment
     */
    #[Route('/payment', name: 'payment_index')]
    #[Route('/payment', name: 'payment')]
    public function index(Request $request): Response
    {
        // direct back to signup process if not yet completed
        try {
            $user = $this->getLoggedUser()->getUserAccess();

            // SIGNUP PROCESS RE-ENTRY
            if ($user->getAccount() && $user->getAccount()->getNumBillableEntities() == 0) {
                return $this->redirectToRoute('payment_complete_signup');
            } else {
                // return $this->redirectToRoute('payment_summary');
                return $this->summaryAction($request);
            }
        } catch (\Exception $e) {
            return $this->redirectToRoute('payment_summary');
        }
    }

    /**
     * Complete signup - Shows completion page for payment setup.
     *
     * GET /payment/complete-signup
     * Sample:
     * https://myfoot.sparefoot.com/payment/complete-signup
     * http://localhost:9019/payment/complete-signup
     */
    #[Route('/payment/complete-signup', name: 'payment_complete_signup')]
    public function completeSignup(): Response
    {
        return $this->render('payment/complete_signup.html.twig', []);
    }

    /**
     * Payment summary - Displays payment methods and billing information.
     *
     * This page is displayed when the user hits the payments tab in myfoot.
     *
     * GET /payment/summary
     * Sample:
     * https://myfoot.sparefoot.com/payment/summary
     * http://localhost:9019/payment/summary
     *
     * @throws \Exception
     */
    #[Route('/payment/summary', name: 'payment_summary')]
    public function summaryAction(Request $request): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        // make sure user is allowed to modify
        if (!\Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
            throw new \Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
        }

        $facilities = \Genesis_Service_Facility::loadByAccountId($account->getId(),
            \Genesis_Db_Restriction::equal('published', 1)
        );

        $billableEntities = $account->getPublishedBillableEntities()->toArray();
        $output = [];

        // get each payment details
        /**
         * @var $be Genesis_Entity_BillableEntity
         */
        foreach ($billableEntities as $be) {
            $be->popNetsuiteFields();

            $output[$be->getId()]['id'] = $be->getId();
            $output[$be->getId()]['nsCustId'] = $be->getNsCustId();
            $output[$be->getId()]['nsParentId'] = $be->getNsParentId();
            $output[$be->getId()]['paymentTypeNickname'] = $be->getNsName();
            $output[$be->getId()]['paymentType'] = $be->getTerms();
            $output[$be->getId()]['emails'] = str_replace(' ', ', ', $be->getBillingEmails());

            $output[$be->getId()]['address'] = '';
            $output[$be->getId()]['city'] = '';
            $output[$be->getId()]['state'] = '';
            $output[$be->getId()]['zip'] = '';

            $address = $be->getAddress();

            if ($address instanceof \Genesis_Entity_Netsuite_Address) {
                $output[$be->getId()]['address'] = $address->getAddress1();
                $output[$be->getId()]['city'] = $address->getCity();
                $output[$be->getId()]['state'] = $address->getState();
                $output[$be->getId()]['zip'] = $address->getZip();
            }

            switch ($be->getTerms()) {
                case \Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD:
                    $cc = $be->getCreditCard();

                    if ($cc instanceof \Genesis_Entity_Netsuite_CreditCard) {
                        $output[$be->getId()]['creditCard']['number'] = $cc->getCcNumber();
                        $output[$be->getId()]['creditCard']['nsId'] = $cc->getNsInternalId();
                        $output[$be->getId()]['creditCard']['type'] = $cc->getCcType();
                        $output[$be->getId()]['creditCard']['name'] = $cc->getCcName();
                        $output[$be->getId()]['creditCard']['expirationMonth'] = $cc->getCcExpireMonth();
                        $output[$be->getId()]['creditCard']['expirationYear'] = $cc->getCcExpireYear();

                        $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD.' - '.$cc->getCcNumber().', '.$be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD.' - No Card On File, '.$be->getNsName();
                    }
                    break;
                case \Genesis_Entity_BillableEntity::TYPE_ACH:
                    $ach = $be->getACH();
                    if ($ach instanceof \Genesis_Entity_Netsuite_ACH) {
                        $output[$be->getId()]['ach']['name'] = $ach->getBankName();
                        $output[$be->getId()]['ach']['account'] = $ach->getAccountNum();
                        $output[$be->getId()]['ach']['routing'] = $ach->getRoutingNum();

                        $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_ACH.' - '.$be->getNsName();
                    } else {
                        $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_ACH.' - '.$be->getNsName();
                    }

                    break;
                case \Genesis_Entity_BillableEntity::TYPE_NET_30:
                    $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_NET_30.' - '.$be->getNsName();
                    break;
                case \Genesis_Entity_BillableEntity::TYPE_NET_10:
                    $output[$be->getId()]['displayStr'] = \Genesis_Entity_BillableEntity::TYPE_NET_10.' - '.$be->getNsName();
                    break;
                default:
                    $output[$be->getId()]['displayStr'] = 'n/a';
                    break;
            }
        }

        $viewData = [
            'facilities' => $facilities,
            'payment_methods' => $output,
            'scripts' => ['payment/summary'],
            'title' => 'Payment - Settings',
        ];
        // Assign template data to view
        foreach ($viewData as $key => $value) {
            $this->view->$key = $value;
        }

        return $this->render('payment/summary.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Add payment method - Redirects to edit with add parameters.
     *
     * GET /payment/add
     * Sample:
     * https://myfoot.sparefoot.com/payment/add
     * http://localhost:9019/payment/add
     */
    #[Route('/payment/add', name: 'payment_add')]
    public function add(Request $request): Response
    {
        return $this->redirectToRoute('payment_edit', $request->query->all());
    }

    /**
     * Edit payment method - Shows form to edit existing payment method.
     *
     * GET /payment/edit
     * Sample:
     * https://myfoot.sparefoot.com/payment/edit
     * http://localhost:9019/payment/edit
     */
    #[Route('/payment/edit', name: 'payment_edit')]
    public function edit(Request $request): Response
    {
        $accountId = $this->getLoggedUser()->getAccountId();
        $account = \Genesis_Service_Account::loadById($accountId);
        $csrf_token = CsrfUtil::getToken(self::PAYMENT_CSRF_TOKEN);

        // make sure user is allowed to modify
        if (!\Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
            throw new \Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
        }

        $return = $request->query->get('return');

        // see if we should return to the facility and process after saving
        $returnscreen = '/payment';
        if ($return) {
            $returnscreen = $this->generateUrl('features_addsummary');
        } else {
            $returnscreen = '/payment';
        }

        // TODO: make this pull the NetSuite data, not the summary page.  Summary page may have to sacrifice last for of CC or store locally
        $billableEntityId = $request->query->get('billable_entity_id');

        // if be exists, then we know we are editing
        if ($billableEntityId) {
            $be = \Genesis_Service_BillableEntity::loadById($billableEntityId);
            if (empty($be)) {
                throw new \Exception('Billable Entity not found. <NAME_EMAIL> if you need help.');
            }
        } else {
            return $this->redirectToRoute('payment_index');
        }

        // NOTE: we are doing this param passing between views to minimize netsuite api calls
        return $this->render('payment/edit.html.twig', [
            'accountId' => $accountId,
            'account' => $account,
            'csrf_token' => $csrf_token,
            'isNew' => 0,
            'return' => $return,
            'returnscreen' => $returnscreen,
            'billableEntityId' => $billableEntityId,
            'paymentType' => $request->query->get('payment_type'),
            'emails' => $request->query->get('emails'),
            'paymentTypeNickname' => $request->query->get('payment_type_nickname'),
            'address' => $request->query->get('address'),
            'city' => $request->query->get('city'),
            'state' => $request->query->get('state'),
            'zip' => $request->query->get('zip'),
            'creditCardNumber' => $request->query->get('credit_card_number'),
            'ccType' => $request->query->get('ccType'), // VISA, etc
            'ccNsId' => $request->query->get('ccNsId'),
            'creditCardName' => $request->query->get('credit_card_name'),
            'creditCardExpirationMonth' => $request->query->get('credit_card_expiration_month'),
            'creditCardExpirationYear' => $request->query->get('credit_card_expiration_year'),
            'achName' => $request->query->get('achName'),
            'achAccount' => $request->query->get('achAccount'),
            'achRouting' => $request->query->get('achRouting'),
            'scripts' => ['payment/edit'],
        ]);
    }

    /**
     * Temporary payment page - Shows temporary payment interface.
     *
     * GET /payment/temp
     * Sample:
     * https://myfoot.sparefoot.com/payment/temp
     * http://localhost:9019/payment/temp
     */
    #[Route('/payment/temp', name: 'payment_temp')]
    public function temp(): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        return $this->render('payment/temp.html.twig', [
            'account' => $account,
        ]);
    }

    /**
     * Same company address - Gets the address to put in the address field when user checks
     * "billing address is same as company address".
     *
     * POST /payment/same-co-address
     * Sample:
     * https://myfoot.sparefoot.com/payment/same-co-address
     * http://localhost:9019/payment/same-co-address
     *
     * @throws \Exception
     */
    #[Route('/payment/same-co-address', name: 'payment_same_co_address', methods: ['POST'])]
    public function sameCoAddress(): JsonResponse
    {
        try {
            $accountId = $this->getLoggedUser()->getAccountId();
            if ($accountId === null) {
                throw new \Exception('account_id is required');
            }
            $account = \Genesis_Service_Account::loadById($accountId);
            if ($account === null) {
                throw new \Exception('No such account.');
            }
            // make sure user is allowed to modify
            if (!\Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
                throw new \Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
            }
            if (!$account->getLocation()) {
                throw new \Exception('No corporate address on file.');
            }

            return new JsonResponse([
                'success' => true,
                'address' => $account->getLocation()->getAddress1(),
                'city' => $account->getLocation()->getCity(),
                'state' => $account->getLocation()->getState(),
                'zip' => $account->getLocation()->getZip(),
            ]);
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Change facility billable entity - Updates the billable entity a facility is assigned to.
     *
     * POST /payment/change-facility-billable-entity
     * Sample:
     * https://myfoot.sparefoot.com/payment/change-facility-billable-entity
     * http://localhost:9019/payment/change-facility-billable-entity
     *
     * @throws \Exception
     */
    #[Route('/payment/change-facility-billable-entity', name: 'payment_change_facility_billable_entity', methods: ['POST'])]
    public function changeFacilityBillableEntity(Request $request): Response
    {
        try {
            $facilityId = $request->request->get('facility_id');
            $billableEntityId = $request->request->get('billable_entity_id');
            $facility = \Genesis_Service_Facility::loadById($facilityId);

            // make sure user is allowed to modify
            if (!$this->getLoggedUser()->getUserAccess()->canAccessFacility($facility)) {
                throw new \Exception('User '.$this->getLoggedUser()->getId().' is not allowed to change facility '.$facility->getId());
            }

            $facility->setBillableEntityId($billableEntityId);
            \Genesis_Service_Facility::save($facility);

            return new Response('');
        } catch (\Exception $e) {
            \Genesis_Util_ErrorLogger::exceptionToHipChat($e);

            return new Response('Error: '.$e->getMessage()."\n");
        }
    }

    /**
     * Update customer - Updates/adds the NetSuite customer payment information.
     *
     * POST /payment/update-customer
     * Sample:
     * https://myfoot.sparefoot.com/payment/update-customer
     * http://localhost:9019/payment/update-customer
     */
    #[Route('/payment/update-customer', name: 'payment_update_customer', methods: ['POST'])]
    public function updateCustomer(Request $request): JsonResponse
    {
        $csrfToken = $request->request->get('csrf_token');

        if (CsrfUtil::validateToken(SignupEndController::SIGNUP_END_CSRF_TOKEN, $csrfToken) || CsrfUtil::validateToken(self::PAYMENT_CSRF_TOKEN, $csrfToken)) {
            try {
                $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
                if (!$account) {
                    throw new \Exception('No account with that ID.');
                }
                // make sure user is allowed to modify
                if (!\Genesis_Service_UserAccess::isAllowed($this->getLoggedUser()->getUserAccess(), $account)) {
                    throw new \Exception('User '.$this->getLoggedUser()->getId().' is not authorized for an action on the requested account '.$account->getId());
                }

                if ($request->request->get('billable_entity_id') > 0) {
                    $billableEntity = \Genesis_Service_BillableEntity::loadById($request->request->get('billable_entity_id'));
                    if (!$billableEntity) {
                        throw new \Exception('No billable entity with that ID.');
                    }
                } else {
                    $billableEntity = new \Genesis_Entity_BillableEntity();
                    $billableEntity->setAccountId($account->getId());
                }
                if ($billableEntity->getAccountId() !== $account->getAccountId()) {
                    throw new \Exception('not authorized to modify billable entity '.$billableEntity->getId().' for account '.$account->getId());
                }
                $billableEntity->setNsName($request->request->get('payment_type_nickname', $account->getName()));
                $billableEntity->setPaymentType($request->request->get('payment_type'));
                /**
                 * do not move. must happen here. we set the real fields, the rest are temporary on entity.
                 */
                $billableEntity = \Genesis_Service_BillableEntity::save($billableEntity);

                // validate email
                $emailFound = false;
                $rawEmailsTest = $request->request->get('emails');
                $emails = array_unique(explode(',', $rawEmailsTest));
                foreach ($emails as $email) {
                    $email = trim($email);
                    if (!strlen($email)) {
                        continue;
                    }

                    $sanitizedEmail = filter_var($email, FILTER_SANITIZE_EMAIL);
                    if ($email != $sanitizedEmail || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new \Exception($email.' is an invalid email address');
                    }

                    if (strlen($email)) {
                        $emailFound = true;
                    }
                }
                if (!$emailFound) {
                    throw new \Exception('A valid billing email address was not found');
                }
                // format check emails
                try {
                    $billableEntity->setBillingEmails($rawEmailsTest);
                } catch (\InvalidArgumentException $e) {
                    throw new \Exception('Please provide billing emails as a comma separated list.');
                }
                // address
                $address = new \Genesis_Entity_Netsuite_Address();
                $address->setAddress1($request->request->get('address'));
                $address->setCity($request->request->get('city'));
                $address->setState($request->request->get('state'));
                // validate zip if supplied
                if (strlen($request->request->get('zip')) > 0 && !preg_match("/^\d{5}$|^\d{5}-\d{4}$/", $request->request->get('zip'))) {
                    throw new \Exception('Please enter a valid zip code.');
                }
                $address->setZip($request->request->get('zip'));
                $billableEntity->setAddress($address);
                $billableEntity->setNsPhone($account->getPhone());
                $creditCard = null; // override into a credit card
                switch ($request->request->get('payment_type')) {
                    case \Genesis_Entity_BillableEntity::TYPE_CREDIT_CARD:
                        foreach ([
                            'credit_card_number' => 'Credit Card number required.',
                            'credit_card_name' => 'Credit Card name required.',
                            'credit_card_expiration_month' => 'Credit Card expiration month required.',
                            'credit_card_expiration_year' => 'Credit Card expiration year required.',
                            'ccType' => 'Credit Card type required.',
                        ] as $inputName => $errorMsg) {
                            if ($request->request->get($inputName) === null) {
                                throw new \Exception($errorMsg);
                            }
                        }
                        if ($request->request->get('ccType') === 'existing') { // didn't change, so keep the CC null on update
                            break;
                        }

                        $creditCard = new \Genesis_Entity_Netsuite_CreditCard();
                        $creditCard->setCcNumber($request->request->get('credit_card_number'));
                        $creditCard->setCcName($request->request->get('credit_card_name'));
                        $creditCard->setCcExpireMonth($request->request->get('credit_card_expiration_month'));
                        $creditCard->setCcExpireYear($request->request->get('credit_card_expiration_year'));
                        $creditCard->setCcType($request->request->get('ccType'));

                        break;
                    case \Genesis_Entity_BillableEntity::TYPE_ACH:
                        //                    foreach (array(
                        //                                 'bankName'                 => 'Bank Name is required.',
                        //                                 'bankAcctNum'              => 'Account Number is required.',
                        //                                 'bankRoutingNum'           => 'Routing Number is required.',
                        //                             ) as $inputName => $errorMsg) {
                        //                        if ($this->getParam($inputName) === null) {
                        //                            throw new Exception($errorMsg);
                        //                        }
                        //                    }
                        //                    $ach = new Genesis_Entity_Netsuite_ACH();
                        //                    $ach->setBankName($this->getParam('bankName'));
                        //                    $ach->setAccountNum($this->getParam('bankAcctNum'));
                        //                    $ach->setRoutingNum($this->getParam('bankRoutingNum'));

                        break;
                    case \Genesis_Entity_BillableEntity::TYPE_NET_10:
                    case \Genesis_Entity_BillableEntity::TYPE_NET_30:
                        break;
                    default:
                        throw new \Exception('Invalid payment type: '.$request->request->get('payment_type'));
                }

                // make the api call
                try {
                    $nsId = \Genesis_Service_BillableEntity::saveNetsuiteCustomer(
                        $billableEntity, $creditCard, $this->getLoggedUser()
                    );
                } catch (\Exception $e) {
                    throw new \Exception('Unable to save credit card entry. '.$e->getMessage());
                }

                $this->sendPaymentMethodUpdateTicket($billableEntity);

                if ($request->request->get('cc_change_listener') == 1) {
                    $msg = new \Genesis_Entity_EmailMessage();
                    $msg->setSubject($account->getName().' has changed billing information');

                    $cpa = $account->getCpa() ? 'yes' : 'no';

                    $payment_type_str = ($billableEntity->getPaymentType() === 'Credit Card') ? $billableEntity->getPaymentType().' (new last 4 digits: '.substr($request->request->get('credit_card_number'), -4, 4).')' : $billableEntity->getPaymentType();

                    $user_account_name = ($this->getLoggedUser()->getAccount()) ? $this->getLoggedUser()->getAccount()->getName() : '';

                    $msg->setBody('Account: '.$account->getName().chr(10).chr(13).
                        'SPAREFOOT ID: '.$account->getSfAccountId().''.chr(10).chr(13).
                        'PITA Created Date: '.$account->getTimeCreated().''.chr(10).chr(13).chr(10).chr(13).
                        'CPA Enabled?: '.$cpa.' '.chr(10).chr(13).
                        '# of Billable Entities: '.$account->getNumBillableEntities().' '.chr(10).chr(13).
                        'Customer/Billable Entity Name: '.$billableEntity->getNsName().' '.chr(10).chr(13).
                        'Payment Type: '.$payment_type_str.' '.chr(10).chr(13).
                        'Action: Updated '.chr(10).chr(13).chr(10).chr(13).
                        'User That Updated This: '.chr(10).chr(13).
                        'Account: '.$user_account_name.chr(10).chr(13).
                        'Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().' '.chr(10).chr(13).
                        'Email: '.$this->getLoggedUser()->getEmail().' '.chr(10).chr(13).
                        'NetsuiteId: '.$nsId.' ');

                    if (!$this->getLoggedUser()->isMyFootGod()) {
                        \Genesis_Service_Mailer::sendInternalMessage('<EMAIL>', $msg, [], $this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName(), $this->getLoggedUser()->getEmail());
                    }
                }

                return new JsonResponse(['success' => true]);
            } catch (\Exception $e) {
                return new JsonResponse(['success' => false, 'message' => $e->getMessage()]);
            }
        } else {
            return new JsonResponse(['success' => false, 'message' => 'There was an error during  the submission. Please refresh and try again.']);
        }
    }

    /**
     * @throws SalesForceClientException
     */
    private function sendPaymentMethodUpdateTicket(
        \Genesis_Entity_BillableEntity $billableEntity,
    ): void {
        if ($billableEntity) {
            $user = $this->getLoggedUser();

            // For list of keys: https://developer.salesforce.com/docs/api-explorer/sobject/Case
            $ticketOptions = [
                // Owner ID set in SalesForce Client
                'Type' => 'Question',
                'Origin' => 'Internal',
                'Status' => 'New',
                'Subject' => $billableEntity->getAccount()->getName().' - Billing Information Update',
                'Priority' => 'Low',

                'SuppliedName' => $user->getFirstName().' '.$user->getLastName(),
                'SuppliedEmail' => $user->getEmail(),
                'SuppliedPhone' => $user->getPhone(),

                'Description' => 'Payment Nickname: '.$billableEntity->getNsName().PHP_EOL
                    .'Payment Type: '.$billableEntity->getPaymentType().PHP_EOL
                    .'Billable Entity ID:'.$billableEntity->getId().PHP_EOL
                    .'Billable Entity Email:'.$billableEntity->getBillingEmails().PHP_EOL
                    .PHP_EOL
                    .'Email:'.$user->getEmail().PHP_EOL
                    .PHP_EOL
                    .'mysparefoot_payment-update_q6xJ34',
            ];

            // Not frequently used (hopefully), so create a client per update
            $salesForceClient = new Client();
            $salesForceClient->createTicket($ticketOptions);
        }
    }
}
