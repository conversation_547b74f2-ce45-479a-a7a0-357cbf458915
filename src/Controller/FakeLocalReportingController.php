<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Fake Local Reporting Controller.
 * 
 * Serves fake/test data for local reporting functionality.
 * Returns JSON data from test files in tests/FakeData directory.
 */
class FakeLocalReportingController extends AbstractCommonController
{
    /**
     * Overview endpoint - returns facility overview data
     */
    #[Route('/fakelocalreporting/overview', name: 'fake_local_reporting_overview', methods: ['GET'])]
    public function overviewAction(Request $request): JsonResponse
    {
        $dataPath = $this->getParameter('kernel.project_dir') . '/tests/FakeData/reportingOverview.json';
        
        if (!file_exists($dataPath)) {
            return new JsonResponse(['error' => 'Overview data file not found'], 404);
        }
        
        $jsonData = file_get_contents($dataPath);
        $data = json_decode($jsonData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new JsonResponse(['error' => 'Invalid JSON in overview data file'], 500);
        }
        
        return new JsonResponse($data);
    }
    
    /**
     * Inventory endpoint - returns facility inventory data
     */
    #[Route('/fakelocalreporting/inventory', name: 'fake_local_reporting_inventory', methods: ['GET'])]
    public function inventoryAction(Request $request): JsonResponse
    {
        $dataPath = $this->getParameter('kernel.project_dir') . '/tests/FakeData/reportingInventory.json';
        
        if (!file_exists($dataPath)) {
            return new JsonResponse(['error' => 'Inventory data file not found'], 404);
        }
        
        $jsonData = file_get_contents($dataPath);
        $data = json_decode($jsonData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new JsonResponse(['error' => 'Invalid JSON in inventory data file'], 500);
        }
        
        return new JsonResponse($data);
    }
    
    /**
     * Bookings endpoint - returns facility booking data
     */
    #[Route('/fakelocalreporting/bookings', name: 'fake_local_reporting_bookings', methods: ['GET'])]
    public function bookingsAction(Request $request): JsonResponse
    {
        $dataPath = $this->getParameter('kernel.project_dir') . '/tests/FakeData/reportingBooking.json';
        
        if (!file_exists($dataPath)) {
            return new JsonResponse(['error' => 'Bookings data file not found'], 404);
        }
        
        $jsonData = file_get_contents($dataPath);
        $data = json_decode($jsonData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new JsonResponse(['error' => 'Invalid JSON in bookings data file'], 500);
        }
        
        return new JsonResponse($data);
    }
}
