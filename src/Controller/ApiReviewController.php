<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Review;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiReviewController extends ApiBaseController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/review?review_id=123&facility_id=456
     * http://localhost:9019/api/review?review_id=123&facility_id=456
     */
    #[Route('/api/review{slash}', name: 'api_review_show', requirements: ['slash' => '/?'], defaults: ['slash' => ''])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $review_id = $request->get('review_id');
        $facility_id = $request->get('facility_id');
        if (!$review_id) {
            throw new ApiException(ApiException::NOT_ACCEPTABLE, 'Review id must be passed');
        }

        if ($request->isMethod('GET')) {
            $review = Review::validateAndGetReview($review_id, $facility_id);

            return $this->json(['data' => Review::toArray($review)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }
}
