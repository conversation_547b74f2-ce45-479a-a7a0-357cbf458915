<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReferralController extends AbstractRestrictedController
{
    /**
     * Referral index - Forwards to home referral page.
     *
     * Manages referral system for MyFoot accounts where existing customers can refer new businesses.
     *
     * GET /referral
     * Sample:
     * https://myfoot.sparefoot.com/referral
     * http://localhost:9019/referral
     */
    #[Route('/referral', name: 'referral_index')]
    public function index(Request $request): Response|JsonResponse
    {
        return $this->homeAction($request);
    }

    /**
     * Referral home - Shows referral form and processes referral submissions.
     *
     * Handles the referral process where existing customers can refer new businesses.
     * Sends notification emails to referrals and creates leads in SalesForce.
     *
     * GET /referral/home - Shows the referral form
     * POST /referral/home - Processes referral submission
     * Sample:
     * https://myfoot.sparefoot.com/referral/home
     * http://localhost:9019/referral/home
     */
    #[Route('/referral/home', name: 'referral_home', methods: ['GET', 'POST'])]
    public function homeAction(Request $request): Response|JsonResponse
    {
        if ($request->isMethod('POST')) {
            // get current logged in users account info
            $loggedInUser = $this->getLoggedUser();
            $account = $loggedInUser->getAccount();
            $accountId = $account->getSfAccountId();
            $facility = $loggedInUser->getManagableFacilities()->uniqueResult();

            // create xml string and email it to salesforce parser
            $firstName = $request->request->get('referral_firstname');
            $lastName = $request->request->get('referral_lastname');
            $email = $request->request->get('referral_email');
            $phone = $request->request->get('referral_phone');
            $businessName = $request->request->get('referral_businessname');

            // send email to referral
            try {
                $notificationParams = [
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    'referrerName' => $loggedInUser->getFirstName().' '.$loggedInUser->getLastName(),
                    'referrerFacility' => $facility->getTitle(),
                    'site_id' => \Genesis_Entity_Site::ID_SPAREFOOT,
                ];

                \Genesis_Service_Emailer::sendMessage($email, 'consumer/referral-notification', $notificationParams);
            } catch (\Exception $e) {
                \Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            }

            // send email to salesforce to parse...
            try {
                $leadPushParams = [
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    'email' => $email,
                    'phone' => $phone,
                    'businessName' => $businessName,
                    'accountId' => $accountId,
                    'userId' => $loggedInUser->getId(),
                ];

                // $salesForceLeadPushAddress = '<EMAIL>';
                $salesForceLeadPushAddress = '<EMAIL>';
                \Genesis_Service_Emailer::sendMessage($salesForceLeadPushAddress, 'internal/referral-salesforce-lead-push', $leadPushParams);
            } catch (\Exception $e) {
                \Genesis_Util_ErrorLogger::exceptionToHipChat($e);
            }

            return new JsonResponse([
                'alert' => 'Thanks. Your referral has been received.',
                'alertClass' => 'alert-success',
            ]);
        }

        $this->view->scripts = ['referral/home'];

        return $this->render('referral/home.html.twig', [
            'view' => $this->view,
        ]);
    }
}
