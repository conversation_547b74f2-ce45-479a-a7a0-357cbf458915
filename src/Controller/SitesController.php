<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SitesController extends AbstractRestrictedController
{
    protected function _init(): ?RedirectResponse
    {
        $request = $this->getRequest();
        $session = User::getSession($request);

        if ($request->query->get('fid')) {
            $facilityId = $request->query->get('fid');
        } else {
            $facilityId = $session->get('facilityId');
        }

        $session->set('facilityId', $facilityId == -1 ? null : $facilityId);

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites
     * http://localhost:9019/sites
     */
    #[Route('/sites', name: 'sites_index')]
    public function indexAction(Request $request): Response
    {
        return $this->overviewAction($request);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/overview
     * http://localhost:9019/sites/overview
     */
    #[Route('/sites/overview', name: 'sites_overview')]
    public function overviewAction(Request $request): Response
    {
        $session = User::getSession($request);

        $account = $this->getLoggedUser()->getAccount();
        $session->set('facilityId', 'all');

        $this->view->facilities = $this->_fetchFacilitiesData();
        $this->view->showUrls = $account->getNumBillableEntities() == 0 ? false : true;
        $this->view->scripts = ['sites/overview'];

        if ($request->query->get('export_to_excel')) {
            $response = new Response();
            $response->headers->set('Content-type', 'text/csv');
            $response->headers->set('Content-disposition', 'attachment; filename="report.csv"');

            $content = '"Facility","Visits","Reservations","Calls","URL"'."\n";

            foreach ($this->view->facilities as $facility) {
                $out = [
                    $facility['entity']->getTitle(),
                    $facility['num_visits'],
                    $facility['num_reservations'],
                    $facility['num_calls'],
                    $this->view->showUrls ? \Genesis_Util_Url::hostedsiteUrl($facility['entity']) : '',
                ];

                $out = str_replace('"', '""', $out);
                $content .= '"'.implode('","', $out).'"'."\n";
            }

            $response->setContent($content);

            return $response;
        }

        return $this->render('sites/overview.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/calls
     * http://localhost:9019/sites/calls
     */
    #[Route('/sites/calls', name: 'sites_calls')]
    public function callsAction(Request $request): Response
    {
        $session = User::getSession($request);

        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Restriction::equal('hostedWebsite', 1));
        $this->view->calls = $this->_fetchCallsData($request, $this->view->facilities);
        $this->view->facility_id = $session->get('facilityId');
        $this->view->scripts = ['sites/calls'];

        if ($request->query->get('export_to_excel')) {
            $response = new Response();
            $response->headers->set('Content-type', 'text/csv');
            $response->headers->set('Content-disposition', 'attachment; filename="report.csv"');

            $content = '"Time","Duration","Facility","Status","Caller ID","Recording URL"'."\n";

            foreach ($this->view->calls as $call) {
                $out = [
                    date('m/d/Y g:ia', strtotime($call['start_time'])),
                    floor($call['duration'] / 60).':'.str_pad($call['duration'] % 60, 2, '0', STR_PAD_LEFT),
                    $call['facility_title'],
                    $call['dial_status'],
                    $call['caller_name'],
                    $call['recording_url'],
                ];

                $out = str_replace('"', '""', $out);
                $content .= '"'.implode('","', $out).'"'."\n";
            }

            $response->setContent($content);

            return $response;
        }

        return $this->render('sites/calls.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/reservations
     * http://localhost:9019/sites/reservations
     */
    #[Route('/sites/reservations', name: 'sites_reservations')]
    public function reservationsAction(Request $request): Response
    {
        $session = User::getSession($request);

        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Restriction::equal('hostedWebsite', 1));
        $this->view->reservations = $this->_fetchReservationsData($request, $this->view->facilities);
        $this->view->facility_id = $session->get('facilityId');
        $this->view->scripts = ['sites/reservations'];

        if ($request->query->get('export_to_excel')) {
            $response = new Response();
            $response->headers->set('Content-type', 'text/csv');
            $response->headers->set('Content-disposition', 'attachment; filename="report.csv"');

            $content = '"Facility","Date Reserved","Last Name","First Name","Email","Phone","Unit","Monthly Rent","Move-In Date","Unit Size"'."\n";

            foreach ($this->view->reservations as $reservation) {
                $out = [
                    $reservation['title'],
                    date('m/d/Y', strtotime($reservation['timestamp'])),
                    $reservation['last_name'],
                    $reservation['first_name'],
                    $reservation['email'],
                    $reservation['phone'],
                    $reservation['unit_number'],
                    '$'.number_format($reservation['monthly_rent'], 2),
                    date('m/d/Y', strtotime($reservation['move_in'])),
                    $reservation['size_w'].' x '.$reservation['size_d'],
                ];

                $out = str_replace('"', '""', $out);
                $content .= '"'.implode('","', $out).'"'."\n";
            }

            $response->setContent($content);

            return $response;
        }

        return $this->render('sites/reservations.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/setup
     * http://localhost:9019/sites/setup
     */
    #[Route('/sites/setup', name: 'sites_setup')]
    public function setupAction(): Response
    {
        $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Restriction::equal('hostedWebsite', 1));
        $this->view->account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $this->view->scripts = ['sites/setup'];

        return $this->render('sites/setup.html.twig', [
            'view' => $this->view,
        ]);
    }

    protected function getTab(): string
    {
        return self::TAB_HOSTEDWEBSITE;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/email
     * http://localhost:9019/sites/email
     */
    #[Route('/sites/email', name: 'sites_email', methods: ['GET', 'POST'])]
    public function emailAction(Request $request): Response
    {
        $account = $this->getLoggedUser()->getAccount();

        if ($request->request->get('send')) {
            $body = '
'.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().' of '.$account->getName().' has requested more information about hosted websites.

Name: '.$this->getLoggedUser()->getFirstName().' '.$this->getLoggedUser()->getLastName().'
Email: '.$this->getLoggedUser()->getEmail().'
Phone: '.$this->getLoggedUser()->getPhone().'
Billing Type: '.$account->getPaymentType().'
Integrations: '.$account->getIntegrationsString().'
            ';

            mail('<EMAIL>', 'Hosted Websites Info Request', $body);
        }

        return $this->render('sites/email.html.twig', [
            'view' => $this->view,
        ]);
    }

    private function _fetchFacilitiesData()
    {
        $facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Restriction::equal('hostedWebsite', 1));
        /*$facilities = Genesis_Service_Facility::loadByAccountId(
                $this->getLoggedUser()->getAccountId(),
                Genesis_Db_Restriction::equal('hostedWebsite', 1));*/

        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate();

        $facilityIds = [];
        $facilityDetails = [];

        foreach ($facilities as $facility) {
            $facilityId = $facility->getId();
            $facilityIds[] = $facilityId;
            $facilityDetails[$facilityId]['entity'] = $facility;
            $facilityDetails[$facilityId]['num_calls'] = 0;
            $facilityDetails[$facilityId]['num_reservations'] = 0;
            $facilityDetails[$facilityId]['num_visits'] = 0;
        }

        $callData = \Genesis_Service_Reporting::getCallsByFacilities($facilityIds, $startDate, $endDate);
        foreach ($callData as $id => $dataArray) {
            $facilityDetails[$id]['num_calls'] = $dataArray['num_calls'];
        }

        $reservationData = \Genesis_Service_Reporting::getValidHostedWebsiteReservationDataByFacilities($facilityIds, $startDate, $endDate);
        foreach ($reservationData as $id => $dataArray) {
            $facilityDetails[$id]['num_reservations'] = $dataArray['num_reservations'];
        }

        $visitsData = \Genesis_Service_Reporting::getHostedWebsiteVisits($facilityIds, $startDate, $endDate);
        foreach ($visitsData as $id => $dataArray) {
            $facilityDetails[$id]['num_visits'] = $dataArray['num_visits'];
        }

        return $facilityDetails;
    }

    /**
     * Fetch and organize all of the data to populate the calls screen.
     *
     * @param $facilities = itr of facilities to get data for
     */
    private function _fetchCallsData(Request $request, $facilities = null)
    {
        $session = User::getSession($request);
        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate().' 23:59:59';

        $facIds = [];
        foreach ($facilities as $fac) {
            $facIds[$fac->getId()] = true;
        }

        $facilityId = $session->get('facilityId');
        $accountId = $this->getLoggedUser()->getAccountId();

        $callData = \Genesis_Service_PhoneCall::loadByAccountId(
            $accountId, $startDate, $endDate);

        $callDetails = [];

        foreach ($callData as $id => $dataArray) {
            // if we are looking at facility subset and id not in array continue
            if ($facilities && !isset($facIds[$dataArray['facility_id']])) {
                continue;
            }

            // if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $callDetails[$id]['facility_id'] = $dataArray['facility_id'];
            $callDetails[$id]['facility_title'] = $dataArray['facility_title'];
            $callDetails[$id]['to_phone_number'] = $dataArray['to_phone_number'];
            $callDetails[$id]['from_phone_number'] = $dataArray['from_phone_number'];
            $callDetails[$id]['start_time'] = $dataArray['start_time'];
            $callDetails[$id]['duration'] = $dataArray['duration'];
            $callDetails[$id]['dial_status'] = $dataArray['dial_status'];
            $callDetails[$id]['caller_name'] = $dataArray['caller_name'];
            $callDetails[$id]['recording_url'] = $dataArray['recording_url'];
        }

        return $callDetails;
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen.
     */
    private function _fetchReservationsData(Request $request, $facilities = null)
    {
        $session = User::getSession($request);
        $startDate = $this->getTrueBeginDate();
        $endDate = $this->getTrueEndDate().' 23:59:59';

        $facIds = [];
        foreach ($facilities as $fac) {
            $facIds[$fac->getId()] = true;
        }

        $facilityId = $session->get('facilityId');
        $accountId = $this->getLoggedUser()->getAccountId();

        $impData = \Genesis_Service_Transaction::loadHostedWebsiteByAccountId(
            $accountId, $startDate, $endDate);

        $reservationDetails = [];

        foreach ($impData as $id => $dataArray) {
            // if we are looking at facility subset and id not in array continue
            if ($facilities && !isset($facIds[$dataArray['facility_id']])) {
                continue;
            }

            // skip invalid booking state
            if ($dataArray['booking_state'] == 'INVALID') {
                continue;
            }

            // if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $reservationDetails[$id]['last_name'] = $dataArray['last_name'];
            $reservationDetails[$id]['first_name'] = $dataArray['first_name'];
            $reservationDetails[$id]['unit_number'] = $dataArray['unit_number'];
            $reservationDetails[$id]['monthly_rent'] = $dataArray['monthly_rent'];
            $reservationDetails[$id]['timestamp'] = $dataArray['timestamp'];
            $reservationDetails[$id]['move_in'] = $dataArray['move_in'];
            $reservationDetails[$id]['size_w'] = $dataArray['size_w'];
            $reservationDetails[$id]['size_d'] = $dataArray['size_d'];
            $reservationDetails[$id]['facility_id'] = $dataArray['facility_id'];
            $reservationDetails[$id]['title'] = $dataArray['title'];
            $reservationDetails[$id]['email'] = $dataArray['email'];
            $reservationDetails[$id]['phone'] = $dataArray['phone'];
            $reservationDetails[$id]['traffic_source'] = $dataArray['traffic_source'];
        }

        return $reservationDetails;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/getfacilitysettings
     * http://localhost:9019/sites/getfacilitysettings
     */
    #[Route('/sites/getfacilitysettings', name: 'sites_getfacilitysettings')]
    public function getfacilitysettingsAction(Request $request): JsonResponse
    {
        $facility = \Genesis_Service_Facility::loadById($request->query->get('fid'));

        $settings = [
            'payment' => $facility->getPaymentPortal(),
            'google' => $facility->getGaAccount(),
            'video' => $facility->getYouTubeVideo(),
            'logo' => $facility->getLogo(),
        ];

        return new JsonResponse($settings);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/sites/savesettings
     * http://localhost:9019/sites/savesettings
     */
    #[Route('/sites/savesettings', name: 'sites_savesettings', methods: ['POST'])]
    public function savesettingsAction(Request $request): JsonResponse
    {
        try {
            $facilityId = $request->request->get('facilityId');
            $youtubeLink = $request->request->get('video');
            $paymentPortalLink = $request->request->get('payment');
            $googleAccountNum = $request->request->get('google');

            // error checking
            if (strlen($youtubeLink) > 0 && !preg_match('/youtube/i', $youtubeLink)) {
                throw new \Exception('Video link is not a valid YouTube link.');
            }

            $facility = \Genesis_Service_Facility::loadById($facilityId);

            $facility->setYouTubeVideo($youtubeLink);
            $facility->setPaymentPortal($paymentPortalLink);
            $facility->setGaAccount($googleAccountNum);

            \Genesis_Service_Facility::save($facility);

            return new JsonResponse(['success' => true]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 400);
        }
    }
}
