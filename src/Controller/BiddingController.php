<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class BiddingController extends AbstractRestrictedController
{
    /**
     * Initialize controller.
     */
    protected function _init(): ?RedirectResponse
    {
        $this->view->facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
        if (!\Genesis_Service_Feature::isActive(
            'genesis.unit_level_bidding',
            ['facility_id' => $this->view->facility->getId(), 'account_id' => $this->view->facility->getAccountId()])) {
            return $this->redirectToRoute('features_index');
        }
        
        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/bidding
     * http://localhost:9019/bidding
     */
    #[Route('/bidding', name: 'bidding_index', methods: ['GET', 'POST'])]
    public function indexAction(Request $request): Response
    {
        $this->view->facility = \Genesis_Service_Facility::loadById($this->view->getFacilityId());
        if (!\Genesis_Service_Feature::isActive(
            'genesis.unit_level_bidding',
            ['facility_id' => $this->view->facility->getId(), 'account_id' => $this->view->facility->getAccountId()])) {
            return $this->redirectToRoute('features_index');
        }

        $this->view->units = $this->view->facility->getGroupedUnits();

        if ($request->request->get('submit')) {
            $bidAmounts = [];
            foreach ($this->view->units as $unit) {
                $bidAmount = $request->request->get('bid_amount_'.$unit->getId());
                if ($bidAmount) {
                    if (!is_numeric($bidAmount)) {
                        throw new \Exception('Invalid bid amount: '.$bidAmount);
                    }

                    if ($bidAmount < \Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT) {
                        throw new \Exception('Bid cannot be less than '.\Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT);
                    }

                    $unit->setUnitBidAmount($bidAmount);
                    \Genesis_Service_StorageSpace::save($unit);
                }

                $bidAmounts[] = $unit->getUnitBidAmount();
            }

            if (count($bidAmounts) > 0) {
                $averageBidAmount = array_sum($bidAmounts) / count($bidAmounts);
            } else {
                $averageBidAmount = 0;
            }

            echo 'Setting new facility average bid to: '.$averageBidAmount."<br/>\n";

            $this->view->facility->setBidFlat($averageBidAmount);
            \Genesis_Service_Facility::save($this->view->facility);
        }

        return $this->render('bidding/index.html.twig', [
            'view' => $this->view,
        ]);
    }
}
