<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Dashboard Controller.
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> <PERSON>
 * Migrated to Symfony by Augment Agent
 */
class DashboardController extends AbstractRestrictedController
{
    /**
     * Initialize controller - migrated from _init().
     */
    protected function _init(): ?RedirectResponse
    {
        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            // TODO: Implement proper Symfony URL generation for features/add-first
            // This corresponds to the original: $this->redirect($this->view->url(['action' => 'add-first'], 'features'));
            throw new \Exception('Redirect to features/add-first needed - implement proper URL generation');
        }
        try {
            $this->view->hasOnlineMoveInFmsSoftware = Account::accountHasFmsSupportingOnlineMoveins($this->getLoggedUser()->getAccount());
        } catch (\Exception $e) {
            $this->view->hasOnlineMoveInFmsSoftware = false;
        }

        $this->view->banner = [
            'showMoveInsBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_BANNER, []),
            'showMoveInOnlineBanner' => \Genesis_Service_Feature::isActive(Features::MOVE_IN_ONLINE_BANNER, []),
            'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount()),
        ];
        
        return null;
    }

    /**
     * Dashboard Index Action.
     */
    #[Route('/dashboard', name: 'dashboard_index')]
    #[Route('/dashboard', name: 'dashboard')]
    public function indexAction(Request $request): Response
    {
        $this->view->account = $this->getLoggedUser()->getAccount();
        $this->view->facility = false;
        $this->view->showWelcomeWidget = false;
        if ($this->getSession()->get('facilityId')) {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
            $this->view->facility = $facility;

            // Show welcome widget if facility is NOT reconciled
            $this->view->showWelcomeWidget = !Facility::hasReconciled($facility);
        }

        // Gets 4 most recent messages
        $this->view->messages = \Genesis_Service_AccountMgmtMessage::loadByNumber(4);

        // Set widget visibility flags based on feature flags
        $this->view->showSubmitRateWidget = User::isFeatureActive('myfoot.dashboard-widget.submitRate');
        $this->view->showMoveInRateWidget = User::isFeatureActive('myfoot.dashboard-widget.moveInRate');
        $this->view->showInventoryWidget = User::isFeatureActive('myfoot.dashboard-widget.inventory');
        $this->view->showCurrentBidWidget = User::isFeatureActive('myfoot.dashboard-widget.currentBidRanking');
        $this->view->showCustomerReviewsWidget = User::isFeatureActive('myfoot.dashboard-widget.customerReviews');
        // we can use setScripts([])
        $this->view->scripts = [
            'facility/global-functions',
            '../vendors/chartist/dist/chartist.min',
            '../sparefoot/plugins/current-bid-widget/script',
            '../sparefoot/plugins/customer-reviews-widget/script',
            '../sparefoot/plugins/inventory-widget/script',
            '../sparefoot/plugins/move-in-rate-chart/script',
            '../sparefoot/plugins/views-and-reservations-chart/script',
            'dashboard/index',
        ];

        $this->view->title = 'Dashboard';
        $this->view->isShowInactiveFacilityMessage = false;
        if (isset($this->view->facility) && !empty($this->view->facility)) {
            if (!$this->view->facility->getActive()) {
                $this->view->isShowInactiveFacilityMessage = true;
            }
        }

        return $this->render('dashboard/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Get the current tab for navigation.
     */
    protected function getTab(): string
    {
        return parent::TAB_HOME;
    }
}
