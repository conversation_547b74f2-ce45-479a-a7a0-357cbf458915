<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\Unit;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ApiUnitController extends ApiBaseController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/unit/123?facility_id=456
     * http://localhost:9019/api/unit/123?facility_id=456
     */
    #[Route('/api/unit/{unit_id}{slash}', name: 'api_unit_show', requirements: ['slash' => '/?', 'unit_id' => '\d+'], defaults: ['slash' => ''], methods: ['GET', 'POST', 'PUT'])]
    #[Route('/api/units', name: 'api_units_index', methods: ['GET'])]
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $unitId = $request->get('unit_id');
        $facilityId = $request->get('facility_id');
        $json_body = $request->getContent();

        if ($request->isMethod('GET')) {
            $hasMultipleUnitIds = (strpos($unitId, ',') !== false) || (strpos($unitId, '%2C') !== false);
            // this combo, throw an exception when commas and no fid
            if ($hasMultipleUnitIds && !$facilityId) {
                throw new ApiException(ApiException::BAD_REQUEST, 'bad request. missing facility id');
            }
            // ?facilityId route
            if ($facilityId) {
                try {
                    $facility = Facility::validateFacilityId($facilityId);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }
                try {
                    User::validateFacilityAccess($facility);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
                }
                $unitId = str_replace('%2C', ',', $unitId); // change unicode commas to normal

                // TODO - we should add pagination and filtering here
                //
                $response = [];

                if ($hasMultipleUnitIds) {
                    $units = \Genesis_Service_StorageSpace::load(
                        \Genesis_Db_Restriction::and_(
                            \Genesis_Db_Restriction::in('id', explode(',', $unitId)),
                            \Genesis_Db_Restriction::equal('facilityId', $facility->getId()),
                            \Genesis_Db_Restriction::equal('publish', 1)
                        )
                    );
                    if (!count($units)) { // if this record doesn't exist
                        throw new ApiException(ApiException::NOT_FOUND, 'no units found');
                    }
                } else {
                    // this grouped call is always right
                    $units = $facility->getGroupedUnits();
                }

                foreach ($units as $unit) {
                    /* @var \Genesis_Entity_StorageSpace $unit */
                    $response[] = Unit::toArray($unit);
                }

                return $this->json(['data' => $response]);
            }
            // single unit path
            if ($unitId) {
                try {
                    $unit = Unit::validateUnitId($unitId);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
                }
                try {
                    User::validateUnitAccess($unit);
                } catch (\Exception $e) {
                    throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
                }

                return $this->json(['data' => Unit::toArray($unit)]);
            }
        }

        /*
         * Endpoint: [GET] /api/units?facility_id=:facility_id
         *     Grabs all units belonging to a facility.
         * Parameters:
         *     facility_id - (required) facility id you want
         * Response:
         *     200 - (array) array of units
         *     400 - bad request. missing {parameter}
         *     500 - server error
         */
        /*
         * Endpoint: [GET] /api/units/:id,:id,:id,...?facility_id=:facility_id
         *     Grabs specific units with :id belonging to a facility.
         * Parameters:
         *     :id - unit id
         *     facility_id - (required) facility id you want
         * Response:
         *     200 - (object) one unit
         *           (array) array of units
         *     400 - bad request. missing {parameter}
         *     404 - not found
         *     500 - server error
         *
         *  4xx = your damn fault
         *  5xx = our damn fault
         */

        /*
         * Endpoint: [PUT] /api/units/:id
         *     Updates a specific unit.
         */
        if ($request->isMethod('PUT')) {
            $decoded_json = json_decode($json_body, true);
            unset($decoded_json['facility_id']);
            try {
                $unit = Unit::validateUnitId($unitId);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }
            try {
                $checkWriteAccess = true;
                User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            try {
                return $this->json(['data' => Unit::upsertFromJson($unit, json_encode($decoded_json))]);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
        }

        /*
         * Endpoint: [POST] /api/units/
         *     Creates a new unit.
         */
        if ($request->isMethod('POST')) {
            $decoded_json = json_decode($json_body);
            if (!$decoded_json) {
                throw new ApiException(ApiException::BAD_REQUEST, 'json decode failed');
            }
            try {
                Unit::validateInsert($decoded_json);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            $unit = Unit::getNewUnitFromType($decoded_json->unit_type);
            $unit->setFacilityId($decoded_json->facility_id);

            try {
                $checkWriteAccess = true;
                User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            $unit->setFacilityId($decoded_json->facility_id);

            return $this->json(['data' => Unit::upsertFromJson($unit, $json_body)]);
        }

        if ($request->isMethod('DELETE')) {
            $reason = null;
            $decoded_json = json_decode($json_body, true);
            if ($decoded_json && array_key_exists('delete_reason', $decoded_json)) {
                $reason = $decoded_json['delete_reason'];
            }

            // Check valid unit
            try {
                $unit = Unit::validateUnitId($unitId);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            // Check Access
            try {
                $checkWriteAccess = true;
                User::validateUnitAccess($unit, $checkWriteAccess);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
            }

            // Delete (Unpublish, etc) Unit
            try {
                Unit::delete($unit, $reason);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }

            return self::sendOKEmptyResponse();
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/unit/123/specials
     * http://localhost:9019/api/unit/123/specials
     */
    #[Route('/api/unit/{unit_id}/specials/{special_id}{slash}', name: 'api_unit_specials', requirements: ['slash' => '/?', 'unit_id' => '\d+', 'special_id' => '\d+'], defaults: ['slash' => '', 'special_id' => null], methods: ['GET', 'POST', 'DELETE'])]
    public function specialsAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $unitId = $request->get('unit_id'); // required
        $specialId = $request->get('special_id'); // maybe
        $json_body = $request->getContent(); // maybe

        try {
            $unit = Unit::validateUnitId($unitId);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        // Check Access to Read / Write
        try {
            $checkWriteAccess = $request->isMethod('GET') ? false : true;
            User::validateUnitAccess($unit, $checkWriteAccess);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }

        if ($request->isMethod('GET')) {
            return $this->json(['data' => Unit::toSpecialsArray($unit)]);
        }

        if ($request->isMethod('POST')) {
            try {
                Unit::createSpecialFromJson($unit, $json_body);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            return $this->json(['data' => Unit::toSpecialsArray($unit)]);
        }

        if ($request->isMethod('DELETE')) {
            try {
                $unit = Unit::validateUnitId($unitId);
                $special = Unit::validateSpecialId($specialId);
            } catch (\Exception $e) {
                throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
            }

            try {
                \Genesis_Service_UnitSpecial::deleteByRootType($unit->getId(), $special->getType());
            } catch (\Exception $e) {
                throw new ApiException(ApiException::INTERNAL_SERVER_ERROR, $e->getMessage());
            }
            // reload
            $unit = \Genesis_Service_StorageSpace::loadById($unitId);

            return $this->json(['data' => Unit::toSpecialsArray($unit)]);
        }

        throw new ApiException(ApiException::NOT_IMPLEMENTED);
    }
}
