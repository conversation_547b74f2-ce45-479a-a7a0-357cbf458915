<?php

namespace Sparefoot\MyFootService\Controller;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;

class PingController extends AbstractController
{
    private $logger;
    private $session;

    public function __construct(LoggerInterface $logger, SessionInterface $session)
    {
        $this->logger = $logger;
        $this->session = $session;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/ping
     * http://localhost:9019/ping
     */
    #[Route('/ping{slash}', name: 'ping_index', requirements: ['slash' => '/?'], defaults: ['slash' => ''])]
    public function indexAction(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $session = $request->getSession();

        $response = [
            'sessionId' => $session ? $session->getId() : null,
            'user' => $user ? $user->getUserIdentifier() : null,
            'statusInstall' => 'successful',
            'name' => 'MyFoot',
            'version' => getenv('VERSION'),
            'sf_env' => getenv('SF_ENV'),
            'hostname' => gethostname(),
            'response' => 'pong - myfoot',
        ];
        $this->logger->info('Ping request received', [
            'user' => $user,
            'timestamp' => (new \DateTime())->format('Y-m-d H:i:s'),
            'ip' => $request->getClientIp(),
        ]);

        return new JsonResponse($response);
    }
}
