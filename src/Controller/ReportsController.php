<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReportsController extends AbstractRestrictedController
{
    /**
     * @var \SoapClient
     */
    private $soap;

    /**
     * Reports index - Shows main reports dashboard with SOAP-based external reports.
     *
     * GET /reports
     * Sample:
     * https://myfoot.sparefoot.com/reports
     * http://localhost:9019/reports
     */
    #[Route('/reports', name: 'reports_index')]
    public function indexAction(): Response
    {
        $this->view->title = 'Reports';
        $domain = \Genesis_Config_Server::getEnvDomain();
        if ($domain === 'localhost:8888') { // meh
            $domain = 'localhost';
        }

        $wsdl = 'https://pita.sparefoot.'.$domain.'/quicksoap?wsdl';
        try {
            $this->soap = new \SoapClient($wsdl, ['cache_wsdl' => WSDL_CACHE_NONE, 'trace' => true]);
            $account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
            $this->view->otherReports = $this->soap->getReports($account->getSfAccountId());
        } catch (\SoapFault $e) {
            $errorDetails = [
                'ErrorType' => 'SoapFault',
                'Message' => $e->getMessage(),
                'Code' => $e->getCode(),
                'File' => $e->getFile(),
                'Line' => $e->getLine(),
                'Trace' => $e->getTraceAsString(),
            ];
            error_log(json_encode([
                'Timestamp' => date('Y-m-d H:i:s'),
                'ErrorDetails' => $errorDetails,
            ]));
        } catch (\Exception $e) {
            $errorDetails = [
                'ErrorType' => 'GeneralException',
                'Message' => $e->getMessage(),
                'Code' => $e->getCode(),
                'File' => $e->getFile(),
                'Line' => $e->getLine(),
                'Trace' => $e->getTraceAsString(),
            ];
            error_log(json_encode([
                'Timestamp' => date('Y-m-d H:i:s'),
                'ErrorDetails' => $errorDetails,
            ]));
        }

        return $this->render('reports/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Tenant Connect Report - Shows tenant connect call data with export functionality.
     *
     * GET /reports/tenant-connect - Shows the report
     * GET /reports/tenant-connect?export=1 - Exports CSV
     * Sample:
     * https://myfoot.sparefoot.com/reports/tenant-connect
     * http://localhost:9019/reports/tenant-connect
     */
    #[Route('/reports/tenant-connect', name: 'reports_tenant_connect')]
    public function tenantConnectAction(Request $request): Response
    {
        $this->view->startDate = $this->getTrueBeginDate();
        $this->view->endDate = $this->getTrueEndDate();

        $data = \Genesis_Service_Reporting::getTenantConnectCallsByFacility($this->view->startDate, $this->view->endDate, $this->getLoggedUser());

        if ($request->query->get('export')) {
            $out = '"Facility Name","Response Rate","Call Attempts","Facility Answered","Facility Responded to Call Prompts","Connected to Tenant"'."\n";

            foreach ($data as $facTCData) {
                $responseRate = $facTCData['totalCalls'] > 0
                    ? (round($facTCData['facilityRespondedCalls'] / $facTCData['totalCalls'], 2) * 100).'%'
                    : '0%';

                $clean = [
                    $facTCData['facilityTitle'],
                    $responseRate,
                    $facTCData['totalCalls'],
                    $facTCData['answeredCalls'],
                    $facTCData['facilityRespondedCalls'],
                    $facTCData['connectedCalls'],
                ];

                foreach ($clean as &$entry) {
                    $entry = str_replace('"', '""', $entry);
                }

                $out .= '"'.implode('","', $clean).'"'."\n";
            }

            return new Response($out, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="Tenant Connect Report - '.$this->getLoggedUser()->getAccount()->getName().'-'.date('Y-m-d').'.csv"',
                'Cache-Control' => 'no-store, no-cache',
            ]);
        }

        $this->view->callData = $data;
        $this->view->scripts = ['reports/tenant-connect'];

        return $this->render('reports/tenant_connect.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Tenant Connect Detail Report - Shows detailed tenant connect call data for a specific facility.
     *
     * GET /reports/tenant-connect-detail - Shows the detailed report
     * GET /reports/tenant-connect-detail?export=1 - Exports CSV
     * Sample:
     * https://myfoot.sparefoot.com/reports/tenant-connect-detail?fid=123
     * http://localhost:9019/reports/tenant-connect-detail?fid=123
     */
    #[Route('/reports/tenant-connect-detail', name: 'reports_tenant_connect_detail')]
    public function tenantConnectDetailAction(Request $request): Response
    {
        $this->view->startDate = $this->getTrueBeginDate();
        $this->view->endDate = $this->getTrueEndDate();

        $facility = \Genesis_Service_Facility::loadById($request->query->get('fid'));
        $data = \Genesis_Service_TenantConnectCall::loadByFacilityIdAndDate($facility->getId(), $this->view->startDate, $this->view->endDate);

        if ($request->query->get('export')) {
            $out = '"Phone Number","Date/Time","Customer","Email","Call Status","Listen to Call"'."\n";

            foreach ($data as $tcCall) {
                $audioLink = ($tcCall->getRecordingUrl() ? $tcCall->getRecordingUrl() : 'No audio');

                $clean = [
                    $tcCall->getBooking()->stringPhone(),
                    $tcCall->stringDate(),
                    trim(ucwords($tcCall->getBooking()->getFirstName())).' '.trim(ucwords($tcCall->getBooking()->getLastName())),
                    $tcCall->getBooking()->getUser()->getEmail(),
                    $tcCall->stringStatus(),
                    $audioLink,
                ];

                foreach ($clean as &$entry) {
                    $entry = str_replace('"', '""', $entry);
                }

                $out .= '"'.implode('","', $clean).'"'."\n";
            }

            return new Response($out, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="Tenant Connect Report - '.$facility->getTitle().'-'.date('Y-m-d').'.csv"',
                'Cache-Control' => 'no-store, no-cache',
            ]);
        }

        $this->view->callData = $data;
        $this->view->facility = $facility;
        $this->view->scripts = ['reports/tenant-connect-detail'];

        return $this->render('reports/tenant_connect_detail.html.twig', [
            'view' => $this->view,
        ]);
    }
}
