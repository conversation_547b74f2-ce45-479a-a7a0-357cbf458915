<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Service\Statement;
use Sparefoot\MyFootService\Utils\StatementDataUtil;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;

class StatementController extends AbstractRestrictedController
{
    private $mirfUtil;

    protected function _init(): ?RedirectResponse
    {
        $this->mirfUtil = new \Genesis_Util_NewMirfCalculation();

        if (!count($this->getLoggedUser()->getManagableFacilities())) {
            return $this->redirectToRoute('features_add_first');
        }
        if (!$this->getLoggedUser()->canUseBilling()) {
            return $this->redirect('/');
        }

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement
     * http://localhost/statement
     */
    #[Route('/statement', name: 'statement_index')]
    public function indexAction(Request $request): Response
    {
        // if a statement is open, forward to it
        $statements = \Genesis_Service_Statement::loadByAccount($this->getLoggedUser()->getAccount());
        foreach ($statements as $statement) {
            if ($statement->getStatementBatch()->getStatus() === \Genesis_Entity_StatementBatch::STATUS_OPEN) {
                return $this->redirect('/statement/view/id/'.$statement->getId());
            }
        }

        // otherwise, forward to list of statements
        return $this->redirect('/statement/list');
    }

    /**
     * Get MIRF eligible facility IDs from a collection of facilities.
     */
    private function getMirfFacilities(iterable $facilities): array
    {
        $mirfFacilities = [];
        foreach ($facilities as $facility) {
            if ($this->mirfUtil->isMIRFEligible($facility, $facility->getAccount())) {
                $mirfFacilities[] = $facility->getId();
            }
        }

        return $mirfFacilities;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/getmirf
     * http://localhost/statement/getmirf
     */
    #[Route('/statement/getmirf', name: 'statement_getmirf', methods: ['GET', 'POST'])]
    public function getMirfAction(Request $request): Response
    {
        $facilityId = $request->query->get('facility_id') ?? $request->request->get('facility_id');

        if (!$facilityId) {
            throw new \Exception('a facility ID is required');
        }

        $data = $this->mirfUtil->getMoveInRateFloors(
            null,
            [$facilityId],
            true
        );

        return new Response(
            json_encode($data, JSON_PRETTY_PRINT),
            200,
            ['Content-Type' => 'application/json']
        );
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/list
     * http://localhost/statement/list
     */
    #[Route('/statement/list', name: 'statement_list')]
    public function listAction(Request $request): Response
    {
        $userAccess = $this->getLoggedUser();

        // Prepare view data
        $this->view->facilities = $userAccess->getManagableFacilities(\Genesis_Db_Order::ASC('title'));
        $this->view->selectedFacilityId = $this->getSession()->get('facilityId') ?? null;
        $this->view->loggedUser = $userAccess;
        $this->view->reconcileStatementAction = \Genesis_Service_feature::isActive(
            Features::NEW_STATEMENTS_PAGE,
            ['account_id' => $userAccess->getAccount()->getAccountId()]
        ) ? 'dispute' : 'view';

        if ($userAccess->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD) {
            $this->view->userId = null;
        } else {
            $this->view->userId = $userAccess->getId();
        }

        // Fetches the first open statement batch
        $openStatementBatch = \Genesis_Service_StatementBatch::loadByStatus();
        $this->view->openStatements = [];

        if (!empty($openStatementBatch) && $openStatementBatch) {
            // it does fetch only one record
            $statement = \Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                $userAccess->getAccount()->getAccountId(),
                $openStatementBatch->getId()
            );

            if ($statement) {
                $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
                $this->view->openStatements[] = $clientStatement;
            }
        }

        // Set page title and scripts
        $this->view->title = 'Statements';
        $this->view->scripts = [
            '../new-ui/js/statement/statement',
            '../new-ui/js/statement/billingHistory',
        ];

        // Go to the view for the account type
        switch ($this->getLoggedUser()->getAccount()->getBidType()) {
            case \Genesis_Entity_Account::BID_TYPE_RESIDUAL:
                return $this->render('statement/list-residual.html.twig', ['view' => $this->view]);

            case \Genesis_Entity_Account::BID_TYPE_TIERED:
            default:
                // MIRF flags are used only here
                if (\Genesis_Service_Feature::isActive('billing.mirf_enable')) {
                    $this->view->mirfElegibleFacilityIds = $this->getMirfFacilities($this->view->facilities);
                    $this->view->isMIRFElegible = count($this->view->mirfElegibleFacilityIds) > 0;

                    if ($this->view->isMIRFElegible) {
                        $mirfData = $this->mirfUtil->getMoveInRateFloors(null, $this->view->mirfElegibleFacilityIds, true);

                        if (empty($mirfData['facilities'])) {
                            // No facilities were returned which means no bookings were made at mirf eligible facilities
                            // Disable isMIRFElegible to prevent statements summary page from showing MIRF total of $0
                            // MIRF total of $0 will still show for facilities with bookings > 50% MIR
                            $this->view->isMIRFElegible = false;
                        }

                        $this->view->totalMIRF = '$'.number_format(
                            $mirfData['total_mirf'],
                            2
                        );
                    } else {
                        $this->view->totalMIRF = '$0.00';
                    }
                    $this->view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
                } else {
                    $this->view->mirfElegibleFacilityIds = [];
                    $this->view->isMIRFElegible = false;
                }

                $this->view->account = $userAccess->getAccount();

                return $this->render('statement/list-cpa.html.twig', ['view' => $this->view]);
        }
    }

    /**
     * Get statement by ID with access control and account switching for gods.
     */
    private function getStatement(?Request $request = null): \Genesis_Entity_Statement
    {
        $id = null;
        if ($request) {
            $id = $request->query->get('id') ?? $request->request->get('id');
        } else {
            // Fallback for non-migrated methods that don't pass Request
            $id = $_GET['id'] ?? $_POST['id'] ?? null;
        }

        $statement = \Genesis_Service_Statement::loadById($id);

        if (!$statement) {
            $account = $this->getLoggedUser()->getAccount();
            $statement = \Genesis_Service_Statement::loadUnbilledByAccount($account);
        }

        if (!$statement) {
            throw new \Exception('You must provide a valid statement id to view this page.');
        }

        // if attempting to view a statement for a diff account as a god, switch user to that account
        if ($statement->getAccountId() == $this->getLoggedUser()->getAccountId()) {
            return $statement;
        }

        if (!$this->getLoggedUser()->isMyFootGod()) {
            throw new \Exception('Access denied for statements on other accounts');
        }

        // For god users accessing different accounts, we would normally redirect
        // but since this is a private method, we'll throw an exception to handle in calling method
        throw new \Exception('God user account switch needed for account_id: '.$statement->getAccountId());
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/dispute
     * http://localhost/statement/dispute
     */
    #[Route('/statement/dispute', name: 'statement_dispute', methods: ['GET', 'POST'])]
    public function disputeAction(Request $request): Response
    {
        $start = microtime(true);
        $statement = $this->getStatement($request);

        $facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Order::ASC('title'), true);
        $facilities_with_bookings = [];

        // Prepare view data
        $this->view->statementId = $statement->getId();
        $statementBatch = $statement->getStatementBatch();
        $this->view->statementStartDate = $statementBatch->getStartDate();
        $this->view->statementEndDate = $statementBatch->getEndDate();

        $account = $statement->getAccount();
        $this->view->account = $account;
        $this->view->sisterFacilityList = $statement->getAccount()->getFacilities()->toArray();

        $this->view->isLtv = ($statement->getStatementType() === \Genesis_Entity_BillableInstance::ITEM_BOOKING_RESIDUAL) ? true : false;
        $this->view->isCpa = ($statement->getStatementType() === \Genesis_Entity_BillableInstance::ITEM_BOOKING_CPA) ? true : false;

        // Hybrid Accounts
        if (($account->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
            && $account->getSupportExistingLTVReservations()
        ) {
            $this->view->isHybrid = true;
            $this->view->isLtv = false;
            $this->view->isCpa = false;
        } else {
            $this->view->isHybrid = false;
        }

        // Getting all the bookings belonging to the statement with the facilities the user has access to
        $facilityBookings = \Genesis_Service_Transaction::load(
            \Genesis_Db_Restriction::and_(
                \Genesis_Db_Restriction::equal('statementId', $statement->getId()),
                \Genesis_Db_Restriction::in(
                    'facilityId',
                    array_map(function ($item) {
                        return $item->getId();
                    }, $facilities->toArray())
                ),
                \Genesis_Db_Restriction::not(
                    \Genesis_Db_Restriction::equal('bookingState', 'INVALID')
                )
            )
        );

        $facilityIdsFromBookings = array_map(
            function (\Genesis_Entity_Transaction $item) {
                return $item->getFacilityId();
            },
            $facilityBookings->toArray()
        );
        $facilityIdsFromBookings = array_unique($facilityIdsFromBookings);

        // This will be used to populate the `regularBookings` array
        $allManual = true;

        $this->view->facilityId = $facilityId = $request->query->get('facility') ?? $request->request->get('facility');
        $facility = false;
        foreach ($facilities as $_facility) {
            if ($facilityId === $_facility->getId()) {
                $facility = $_facility;
            }

            if (in_array($_facility->getId(), $facilityIdsFromBookings)) {
                $facilities_with_bookings[] = $_facility;

                if ($_facility->getSourceId() != \Genesis_Entity_Source::ID_MANUAL && $allManual) {
                    $allManual = false;
                }
            }
        }

        if ($facilityId) {
            // If it was not found in the list of facilities with bookings
            if (!$facility) {
                $facility = \Genesis_Service_Facility::loadById($facilityId);
            }
        }

        $this->view->facility = $facility;
        $this->view->facilities = $facilities_with_bookings;
        $this->view->facilityCount = count($facilities_with_bookings);
        $this->view->loggedUser = $userAccess = $this->getLoggedUser();

        if (\Genesis_Service_Feature::isActive('billing.mirf_enable') === true) {
            $this->view->mirfElegibleFacilityIds = $this->getMirfFacilities($facilities_with_bookings);
            $this->view->isMIRFElegible = count($this->view->mirfElegibleFacilityIds) > 0;
            if ($this->view->isMIRFElegible) {
                $this->view->MIRFData = $this->mirfUtil->getMoveInRateFloors(
                    null,
                    $this->view->mirfElegibleFacilityIds,
                    true
                );
                $this->view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
            } else {
                $this->view->isMIRFElegible = false;
            }
        } else {
            $this->view->mirfElegibleFacilityIds = [];
            $this->view->isMIRFElegible = false;
        }

        // load array of source_id's on this account
        $sql = 'SELECT DISTINCT source_id FROM account_software WHERE account_id = :account_id ;';
        $params = ['account_id' => $statement->getAccountId()];
        $results = \Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $this->view->arr_softwares = [];

        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $confirmedStatementData = $this->_getConfirmedStatementData($statement->getId(), $userAccess);
        $this->view->confirmations = $confirmedStatementData->confirmations;
        $this->view->confirmationTime = $confirmedStatementData->confirmationTime;
        $this->view->allowChanges = $statementBatch->getStatus() == \Genesis_Entity_StatementBatch::STATUS_OPEN ? $statement->getStatementBatch()->getReconciliationEndDate() : false;

        $this->view->scripts = ['statement/statements'];

        $bookingExtraMapping = $this->_getExtraInfoBookingsMapping([], $facilityBookings->toArray());

        if ($this->view->isCpa || $this->view->isHybrid) {
            $noAutoStateBookings = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && !($booking->getFree() == 1)
                            && $booking->getAutoState() == null
                            && !$booking->getReviewRuling()
                        ;
                    }

                    return
                        ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && !($booking->getFree() == 1)
                        && $booking->getAutoState() == null
                        && !$booking->getReviewRuling()
                    ;
                }
            );

            $autoDisputedBookings = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && $booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                            && !($booking->getFree() == 1)
                        ;
                    }

                    return
                        ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && $booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                        && !($booking->getFree() == 1)
                    ;
                }
            );

            if ($allManual) {
                $this->view->regularBookings = array_merge($noAutoStateBookings, $autoDisputedBookings);
                $this->view->autoDisputedBookings = [];
            } else {
                $this->view->regularBookings = $noAutoStateBookings;
                $this->view->autoDisputedBookings = $autoDisputedBookings;
            }

            $this->view->regularBookings = StatementDataUtil::sortItems($this->view->regularBookings);
            $this->view->autoDisputedBookings = StatementDataUtil::sortItems($this->view->autoDisputedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->regularBookings
            );
            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->autoDisputedBookings
            );

            $autoConfirmedBookings = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $booking->getFacilityId() === $facilityId
                            && ($booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                            && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                        ;
                    }

                    return
                        ($booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                        && !($booking->getFree() == 1)
                        && !$booking->getReviewRuling()
                        && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                    ;
                }
            );

            $this->view->autoConfirmedBookings = StatementDataUtil::sortItems($autoConfirmedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->autoConfirmedBookings
            );

            // The reviewed bookings contain bookings from the current AND PREVIOUS statements
            $reviewedBookingsFromCurrentStatement = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $facilityId === $booking->getFacilityId()
                            && ($booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                            && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                            && !($booking->getFree() == 1)
                            && $booking->getReviewRuling()
                        ;
                    }

                    return
                        ($booking->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                        && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                        && !($booking->getFree() == 1)
                        && $booking->getReviewRuling()
                    ;
                }
            );

            $previousStatementBatch = \Genesis_Service_StatementBatch::loadPreviousStatementBatch();
            $previousStatement = \Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                $account->getId(),
                $previousStatementBatch->getId()
            );

            if ($previousStatement) {
                $previousStatementBookings = \Genesis_Service_Transaction::load(
                    \Genesis_Db_Restriction::and_(
                        \Genesis_Db_Restriction::equal('statementId', $previousStatement->getId()),
                        \Genesis_Db_Restriction::in(
                            'facilityId',
                            array_map(function ($item) {
                                return $item->getId();
                            }, $facilities->toArray())
                        ),
                        \Genesis_Db_Restriction::not(
                            \Genesis_Db_Restriction::equal('bookingState', 'INVALID')
                        ),
                        \Genesis_Db_Restriction::equal(
                            'bidType',
                            \Genesis_Entity_Account::BID_TYPE_PERCENT
                        ),
                        \Genesis_Db_Restriction::equal(
                            'bookingState',
                            \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                        ),
                        \Genesis_Db_Restriction::equal(
                            'autoState',
                            \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        ),
                        \Genesis_Db_Restriction::notEqual(
                            'free',
                            '1'
                        )
                    )
                );

                $reviewedBookingsFromPreviousStatement = array_filter(
                    $previousStatementBookings->toArray(),
                    function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                        if ($facilityId) {
                            return
                                $facilityId === $booking->getFacilityId()
                                && $booking->getReviewRuling()
                            ;
                        }

                        return $booking->getReviewRuling();
                    }
                );

                $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                    $bookingExtraMapping,
                    $previousStatementBookings->toArray()
                );
            } else {
                $reviewedBookingsFromPreviousStatement = [];
            }

            $this->view->reviewedBookings = array_merge(
                $reviewedBookingsFromCurrentStatement,
                $reviewedBookingsFromPreviousStatement
            );

            $this->view->reviewedBookings = StatementDataUtil::sortItems($this->view->reviewedBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->reviewedBookings
            );

            $statementBatchStartDate = $statement->getStatementBatch()->getStartDate();
            $dtFirstDay = date('Y-m-d', strtotime('-10 day'.$statementBatchStartDate));
            $dtLastDay = date('Y-m-d', strtotime('-1 day'.$statementBatchStartDate));
            $lateBookings = \Genesis_Service_Transaction::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::in(
                        'facilityId',
                        array_map(function ($item) {
                            return $item->getId();
                        }, $facilities->toArray())
                    ),
                    \Genesis_Db_Restriction::between('moveIn', $dtFirstDay, $dtLastDay),
                    \Genesis_Db_Restriction::in('bookingState', [
                        \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED,
                        \Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED,
                    ]),
                    \Genesis_Db_Restriction::isNull('autoState'),
                    \Genesis_Db_Restriction::equal(
                        'bidType',
                        \Genesis_Entity_Account::BID_TYPE_PERCENT
                    ),
                    \Genesis_Db_Restriction::in('bookingType', [
                        \Genesis_Entity_Transaction::BOOKING_TYPE_CPA,
                        \Genesis_Entity_Transaction::BOOKING_TYPE_CPA_FLAT,
                        \Genesis_Entity_Transaction::BOOKING_TYPE_CPA_TIERED,
                        \Genesis_Entity_Transaction::BOOKING_TYPE_STANDARD,
                        \Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE,
                    ]),
                    \Genesis_Db_Restriction::notEqual('free', 1)
                )
            );

            $this->view->lateBookings = StatementDataUtil::sortItems($lateBookings->toArray());

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $lateBookings->toArray(),
                false,
                true
            );

            $freeBookings = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) {
                    return
                        $booking->getFree() == 1
                        && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_PERCENT)
                    ;
                }
            );

            $this->view->freeBookings = StatementDataUtil::sortItems($freeBookings);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $freeBookings,
                true,
                false
            );

            $this->view->bookingExtraInfo = $bookingExtraMapping;

            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';

            return $this->render('statement/new-dispute.html.twig', ['view' => $this->view]);
        }

        if ($this->view->isLtv || $this->view->isHybrid) {
            $this->view->ltvBookings = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $booking) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $facilityId === $booking->getFacilityId()
                            && ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                        ;
                    } else {
                        return
                            ($booking->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && !($booking->getFree() == 1)
                            && !$booking->getReviewRuling()
                        ;
                    }
                }
            );

            $this->view->ltvBookings = StatementDataUtil::sortItems($this->view->ltvBookings);

            $facilityIds = array_map(function ($item) {
                return $item->getId();
            }, $facilities->toArray());

            $existingLtvItems = array_filter(
                $facilityBookings->toArray(),
                function (\Genesis_Entity_Transaction $item) use ($facilityId) {
                    if ($facilityId) {
                        return
                            $facilityId === $item->getFacilityId()
                            && ($item->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && $item->getBookingState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        ;
                    } else {
                        return
                            ($item->getBidType() === \Genesis_Entity_Account::BID_TYPE_RESIDUAL)
                            && $item->getBookingState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                        ;
                    }
                }
            );

            // NOTE: On LTV bookings we charge monthly our clients for every booking that does not move-out
            $additionalLtvItems = $this->_getAdditionalLtvItems(
                ($facilityId) ? [$facilityId] : $facilityIds,
                $this->view->statementId,
                $existingLtvItems
            );

            $this->view->existingLtvItems = array_merge($existingLtvItems, $additionalLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->existingLtvItems
            );

            $this->view->numExistingLtvItems = count($this->view->existingLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->ltvBookings
            );

            $statementEndDate = $statement->getStatementBatch()->getEndDate();
            $statementStartDate = $statement->getStatementBatch()->getStartDate();

            $this->view->newLateLtvItems = $this->_getNewLateLtvItems(
                ($facilityId) ? [$facilityId] : $facilityIds,
                $statement,
                $statementStartDate,
                $statementEndDate
            );

            $this->view->newLateLtvItems = StatementDataUtil::sortItems($this->view->newLateLtvItems);

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->newLateLtvItems
            );

            $this->view->nonAutoconfirmedLtvItems = array_filter($this->view->ltvBookings, function ($item) {
                return
                    ($item->getBookingState() !== \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                    && ($item->getAutoState() !== \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED)
                    && ($item->getAutoState() !== \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED)
                ;
            });

            $this->view->nonAutoconfirmedLtvItems = StatementDataUtil::sortItems($this->view->nonAutoconfirmedLtvItems);

            $this->view->autoConfirmedLtvItems = array_filter(
                $this->view->ltvBookings,
                function (\Genesis_Entity_Transaction $item) {
                    return
                        $item->getBookingState() !== \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED // Validation coming from `buildItemFromInstance`
                        && $item->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED
                    ;
                }
            );

            $this->view->autoDisputedLtvItems = array_filter(
                $this->view->ltvBookings,
                function (\Genesis_Entity_Transaction $item) {
                    return
                        $item->getBookingState() !== \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED // Validation coming from `buildItemFromInstance`
                        && $item->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED
                    ;
                }
            );

            $bookingExtraMapping = $this->_getExtraInfoBookingsMapping(
                $bookingExtraMapping,
                $this->view->nonAutoconfirmedLtvItems
            );

            $this->view->bookingExtraInfo = $bookingExtraMapping;

            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';

            return $this->render('statement/new-view-residual.html.twig', ['view' => $this->view]);
        }

        // Fallback - should not reach here under normal circumstances
        throw new \Exception('Invalid statement type or account configuration');
    }

    /**
     * Get extra information mapping for bookings including status, tenant info, customer info, and duplicates.
     *
     * @param array $bookingsMapping Existing bookings mapping to extend
     * @param array $bookings        Array of Genesis_Entity_Transaction objects
     * @param bool  $isFree          Whether these are free bookings
     * @param bool  $isLate          Whether these are late bookings
     *
     * @return array The extended bookings mapping
     */
    private function _getExtraInfoBookingsMapping(array $bookingsMapping, array $bookings, bool $isFree = false, bool $isLate = false): array
    {
        $bookingConfCodes = array_reduce(
            $bookings,
            function ($carry, \Genesis_Entity_Transaction $item) use ($bookingsMapping) {
                $confCode = $item->getConfirmationCode();
                // Skip from the query any preexistent record
                // This will reduce the time to process the query
                if (!isset($bookingsMapping[$confCode])) {
                    $carry[] = $confCode;
                }

                return $carry;
            },
            []
        );

        if (count($bookingConfCodes)) {
            $dupRecords = \Genesis_Service_ReservationDuplicate::load(
                \Genesis_Db_Restriction::in(
                    'confirmationCode',
                    $bookingConfCodes
                )
            )->toArray();
        } else {
            $dupRecords = [];
        }

        $dupRecordsByConfCode = [];

        foreach ($dupRecords as $dupRecord) {
            // store it as an array because a single booking can have multiple duplicate records
            $dupRecordsByConfCode[$dupRecord->getConfirmationCode()][] = \Genesis_Service_Transaction::loadById(
                $dupRecord->getDupConfirmationCode()
            );
        }

        /** @var \Genesis_Entity_Transaction $singleBooking */
        foreach ($bookings as $singleBooking) {
            $bookingConfCode = $singleBooking->getConfirmationCode();

            // if we already processed a confirmation code, skip it
            if (isset($bookingsMapping[$bookingConfCode])) {
                continue;
            }
            $hasDuplicates = false;

            $bookingsMapping[$bookingConfCode] = new \stdClass();
            $bookingsMapping[$bookingConfCode]->status = $this->_getBookingStatus($singleBooking, $isFree, $isLate);
            $bookingsMapping[$bookingConfCode]->tenantInfo = StatementDataUtil::buildTenantInfo($singleBooking);
            $bookingsMapping[$bookingConfCode]->customerInfo = StatementDataUtil::buildCustomerInfo($singleBooking);

            if (isset($dupRecordsByConfCode[$bookingConfCode])) {
                $facilityId = $singleBooking->getFacility()->getId();

                foreach ($dupRecordsByConfCode[$bookingConfCode] as $reservationDuplicate) {
                    if (!empty($reservationDuplicate) && $reservationDuplicate->getFacility()->getId() == $facilityId) {
                        $hasDuplicates = true;
                        break;
                    }
                }
            }

            $bookingsMapping[$bookingConfCode]->hasDuplicates = $hasDuplicates;
        }

        return $bookingsMapping;
    }

    private function _getBookingStatus(\Genesis_Entity_Transaction $booking, bool $isFree = false, bool $isLate = false)
    {
        if ($isFree) {
            return \Genesis_Entity_Statement_Item_Booking::STATUS_NO_FEE;
        }

        if ($booking->getReviewStatus()) {
            if ($booking->getReviewStatus() === \Genesis_Entity_Transaction::STATUS_UNDER_REVIEW) {
                return \Genesis_Entity_Statement_Item_Booking::STATUS_UNDER_REVIEW;
            } elseif ($booking->getReviewStatus() === \Genesis_Entity_Transaction::STATUS_REVIEWED) {
                // if booking has been reviewed, then return moved-in or not status as (Reviewed)
                if ($booking->getLatestBillableInstance()->getReason() !== \Genesis_Entity_BillableInstance::REASON_CANCELED) {
                    return \Genesis_Entity_Statement_Item_Booking::STATUS_MOVED_IN_REVIEWED;
                } else {
                    return \Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN_REVIEWED;
                }
            }
        }

        if ($isLate) {
            return \Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN;
        } elseif (!empty($booking->getLatestBillableInstance()) && $booking->getLatestBillableInstance()->getReason() !== \Genesis_Entity_BillableInstance::REASON_CANCELED) {
            return \Genesis_Entity_Statement_Item_Booking::STATUS_MOVED_IN;
        } else {
            return \Genesis_Entity_Statement_Item_Booking::STATUS_NEVER_MOVED_IN;
        }
    }

    /**
     * Legacy view action - migrated to Symfony patterns
     * Sample:
     * https://myfoot.sparefoot.com/statement/view?id=123
     * http://localhost/statement/view?id=123.
     */
    #[Route('/statement/view', name: 'statement_view')]
    public function viewAction(Request $request): Response
    {
        if (\Genesis_Service_feature::isActive(Features::NEW_STATEMENTS_PAGE, ['account_id' => $this->getLoggedUser()->getAccountId()])) {
            return $this->redirectToRoute('statement_dispute', ['id' => $request->query->get('id')]);
        }

        $statement = $this->getStatement($request);

        $facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Order::ASC('title'), true);
        $facilities_with_bookings = [];
        foreach ($facilities as $facility) {
            $bookings = \Genesis_Service_BillableInstance::loadBookingsByFacilityStatementId($facility->getId(), $statement->getId());
            if (count($bookings) > 0) {
                $facilities_with_bookings[] = $facility;
            }
        }

        // Prepare view data using the migrated pattern
        $this->view->facilities = $facilities_with_bookings;
        $this->view->facilityCount = count($this->view->facilities);
        $this->view->selectedFacilityId = $this->getSession()->get('facilityId') ?? null;
        $this->view->loggedUser = $userAccess = $this->getLoggedUser();

        if (\Genesis_Service_Feature::isActive('billing.mirf_enable')) {
            $this->view->mirfElegibleFacilityIds = $this->getMirfFacilities($this->view->facilities);
            $this->view->isMIRFElegible = count($this->view->mirfElegibleFacilityIds) > 0;
            if ($this->view->isMIRFElegible) {
                $this->view->MIRFData = $this->mirfUtil->getMoveInRateFloors(
                    null,
                    $this->view->mirfElegibleFacilityIds,
                    true
                );
                $this->view->mirfPercentage = $this->mirfUtil->getAppliedMirf() * 100;
            } else {
                $this->view->isMIRFElegible = false;
            }
        } else {
            $this->view->mirfElegibleFacilityIds = [];
            $this->view->isMIRFElegible = false;
        }

        // Set up an interstitial to display once per session.
        if (!isset($this->getSession()->showInterstitial)) {
            $this->getSession()->showInterstitial = true;
        } elseif ($this->getSession()->showInterstitial) {
            $this->getSession()->showInterstitial = false;
        }
        $this->view->showInterstitial = $this->getSession()->showInterstitial;
        if ($this->view->showInterstitial) {
            $restriction = \Genesis_Db_Restriction::empty_();
            $restriction->setLimit(\Genesis_Db_Limit::limit(5));
            $this->view->interstitialFacilities = $this->getLoggedUser()->getManagableFacilities($restriction);
        } else {
            $this->view->interstitialFacilities = [];
        }

        $this->view->facilityId = $facilityId = $request->query->get('facility');
        $facility = false;
        if ($facilityId) {
            $facility = \Genesis_Service_Facility::loadById($facilityId);
        }

        // load array of source_id's on this account
        $sql = 'SELECT source_id FROM account_software WHERE account_id = :account_id ;';
        $params = ['account_id' => $statement->getAccountId()];
        $results = \Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $this->view->arr_softwares = [];
        if ($results) {
            foreach ($results as $result) {
                $this->view->arr_softwares[$result['source_id']] = $result['source_id'];
            }
        }

        $this->view->clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
        if ($facility) {
            $this->view->clientStatement->filterByFacility($facility);
        }
        // for the done/confirm form
        $this->view->confirmedTime = $this->view->clientStatement->getConfirmedTime();
        $this->view->confirmations = $this->view->clientStatement->getStatementConfirmations();
        $this->view->allowChanges = $statement->getStatementBatch()->getStatus() == \Genesis_Entity_StatementBatch::STATUS_OPEN ? $statement->getStatementBatch()->getReconciliationEndDate() : false;
        $this->view->scripts = ['statement/statements'];

        if ($this->view->clientStatement->isCpaWithLtv()) {
            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';
            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';

            return $this->render('statement/view-cpa-ltv.html.twig', ['view' => $this->view]);
        } elseif ($this->view->clientStatement->isLtv()) {
            $this->view->scripts[] = 'statement/view-residual';
            $this->view->scripts[] = 'statement/view-residual-handlers';

            return $this->render('statement/view-residual.html.twig', ['view' => $this->view]);
        } else {
            $this->view->scripts[] = '../new-ui/js/statement/view-cpa';

            return $this->render('statement/view-cpa.html.twig', ['view' => $this->view]);
        }
    }

    private function _getAdditionalLtvItems(array $facilityIds, int $statementId, array $skippedBookings)
    {
        $facilityIdsString = implode(',', $facilityIds);
        $skippedConfirmationCodesString = array_reduce($skippedBookings, function ($carry, $item) {
            return ($carry) ? "$carry, '".$item->getConfirmationCode()."'" : "'".$item->getConfirmationCode()."'";
        });

        $sql = <<<SQL
        SELECT
            bi.confirmation_code
        FROM
            billable_instances bi
            INNER JOIN listing_rent_submission b on b.confirmation_code = bi.confirmation_code
        WHERE
            bi.statement_id = :statement_id
            AND b.listing_avail_id IN($facilityIdsString)
            AND b.booking_state = :booking_state
            AND b.bid_type = :bid_type
SQL;

        if ($skippedConfirmationCodesString) {
            $sql .= "\nAND b.confirmation_code NOT IN($skippedConfirmationCodesString)";
        }

        $params = [
            'statement_id' => $statementId,
            'booking_state' => \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED,
            'bid_type' => \Genesis_Entity_Account::BID_TYPE_RESIDUAL,
        ];
        $results = \Genesis_Db_Connection::getInstance()->findAll($sql, $params, \PDO::FETCH_COLUMN);

        $additionalLtvBookings = \Genesis_Service_Transaction::load(
            \Genesis_Db_Restriction::in(
                'confirmationCode',
                $results
            )
        )->toArray();

        return $additionalLtvBookings;
    }

    public function _getNewLateLtvItems($facilityIds, $statement, $statementOpenDate, $statementCloseDate)
    {
        if (!$statement->getStatementBatch()->getStatus() == \Genesis_Entity_StatementBatch::STATUS_OPEN) {
            return [];
        }

        $newLateLtvItems = [];
        $lateStartDate = date('Y-m-d', strtotime('-10 day'.$statementOpenDate));
        $lateEndDate = date('Y-m-d', strtotime('-1 day'.$statementCloseDate));

        // get late move-ins
        $lateRestriction = \Genesis_Db_Restriction::and_(
            \Genesis_Db_Restriction::in('facilityId', $facilityIds),
            \Genesis_Db_Restriction::between('moveIn', $lateStartDate, $lateEndDate),
            \Genesis_Db_Restriction::equal('bookingState', \Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED),
            \Genesis_Db_Restriction::equal('bookingType', \Genesis_Entity_Transaction::BOOKING_TYPE_RESIDUAL),
            \Genesis_Db_Restriction::not(\Genesis_Db_Restriction::equal('free', 1))
        );
        $lateRestriction->setOrder(\Genesis_Db_Order::asc('moveIn'));
        $lates = \Genesis_Service_Transaction::load($lateRestriction);

        foreach ($lates as $l) {
            $newLateLtvItems[] = $l;
        }

        return $newLateLtvItems;
    }

    private function _getConfirmedStatementData($statementId, $userAccess)
    {
        $returnObj = new \stdClass();
        $confirmationTime = false;

        // confirmationTime
        if (\Genesis_Service_StatementConfirmation::isStatementConfirmed($statementId, $userAccess->getId())) {
            $entity = \Genesis_Service_StatementConfirmation::loadByStatementAndUserId($statementId, $userAccess->getId());
            $confirmationTime = $entity->getConfirmationTime();
        }

        $confirmations = \Genesis_Service_StatementConfirmation::loadByStatementId($statementId);

        $returnObj->confirmationTime = $confirmationTime;
        $returnObj->confirmations = $confirmations;

        return $returnObj;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/viewcsv?id=123
     * http://localhost/statement/viewcsv?id=123
     */
    #[Route('/statement/viewcsv', name: 'statement_viewcsv')]
    public function viewcsvAction(Request $request): Response
    {
        $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($this->getLoggedUser(), $this->getStatement($request));

        // set this to false for debugging. sends output to screen instead of file
        $outputCsv = true;

        $filename = $clientStatement->exportFileName('csv');

        $logger = new \Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_csv', '', '', $this->getLoggedUser() ? $this->getLoggedUser()->getId() : null, '', $this->getStatement($request)->getId());

        ob_start();
        $outfile = fopen('php://output', 'w');
        $clientStatement->getCsv($outfile);
        $csvContent = ob_get_clean();

        if ($outputCsv) {
            return new Response(
                $csvContent,
                200,
                [
                    'Content-Type' => 'text/csv',
                    'Cache-Control' => 'no-store, no-cache',
                    'Content-Disposition' => 'attachment; filename="'.$filename.'"',
                ]
            );
        } else {
            return new Response('CSV output:<br/>'.$csvContent);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/viewpdf?id=123
     * http://localhost/statement/viewpdf?id=123
     */
    #[Route('/statement/viewpdf', name: 'statement_viewpdf')]
    public function viewpdfAction(Request $request): Response
    {
        $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($this->getLoggedUser(), $this->getStatement($request));

        // set this to false for debugging. sends output to screen instead of file
        $outputPdf = true;

        $filename = $clientStatement->exportFileName('pdf');

        // log a pdf view
        $logger = new \Genesis_Util_ActionLogger();
        $logger->logAction('viewed_statement_pdf', '', '', $this->getLoggedUser() ? $this->getLoggedUser()->getId() : null, '', $this->getStatement($request)->getId());

        ob_start();
        $outfile = fopen('php://output', 'w');
        $clientStatement->getPdf($outfile);
        $pdfContent = ob_get_clean();

        if ($outputPdf) {
            return new Response(
                $pdfContent,
                200,
                [
                    'Content-Type' => 'application/pdf',
                    'Cache-Control' => 'no-store, no-cache',
                    'Content-Disposition' => 'attachment; filename="'.$filename.'"',
                ]
            );
        } else {
            return new Response('PDF output:<br/>'.$pdfContent);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/changecustomername
     * http://localhost/statement/changecustomername
     * POST: confirmation_code, first_name, last_name
     */
    #[Route('/statement/changecustomername', name: 'statement_changecustomername', methods: ['POST'])]
    public function changecustomernameAction(Request $request): Response
    {
        try {
            $confirmationCode = $request->request->get('confirmation_code');
            if (!$confirmationCode) {
                return $this->redirectToRoute('statement_index');
            }

            $trans = \Genesis_Service_Transaction::load(\Genesis_Db_Restriction::equal('confirmationCode', $confirmationCode))->uniqueResult();
            if (!$trans) {
                throw new \Exception('Unable to load booking for confirmation code: '.$confirmationCode);
            }

            $firstName = $request->request->get('first_name');
            $lastName = $request->request->get('last_name');

            if (!$firstName) {
                throw new \Exception('Please enter a first name.');
            }
            if (!$lastName) {
                throw new \Exception('Please enter a last name.');
            }

            $trans->setFirstName($firstName);
            $trans->setLastName($lastName);

            \Genesis_Service_Transaction::updateName($trans, $this->getLoggedUser());

            // update billable instance reason
            $bi = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($trans->getConfirmationCode(), $trans->getStatementId());
            $bi->setReason(\Genesis_Entity_BillableInstance::REASON_NAME_CHANGE);
            \Genesis_Service_BillableInstance::save($bi, $this->getLoggedUser());

            $newItem = \Genesis_Entity_Statement_Item_Client::buildItemFromInstance($bi);

            return new Response($newItem->stringCustomerInfo());
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage(), 400);
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/getbooking?confirmation_code=ABC123&statement_id=456
     * http://localhost/statement/getbooking?confirmation_code=ABC123&statement_id=456
     */
    #[Route('/statement/getbooking', name: 'statement_getbooking', methods: ['GET', 'POST'])]
    public function getbookingAction(Request $request): Response
    {
        try {
            $confirmationCode = $request->query->get('confirmation_code') ?? $request->request->get('confirmation_code');
            $statementId = $request->query->get('statement_id') ?? $request->request->get('statement_id');

            $billableInstance = \Genesis_Service_BillableInstance::loadByConfirmationCodeStatementId($confirmationCode, $statementId);
            $bookingItem = \Genesis_Entity_Statement_Item_Client::buildItemFromInstance($billableInstance);

            $data = [
                'sparefoot_fee' => $bookingItem->stringSparefootFee(),
                'status' => $bookingItem->stringStatus(),
            ];

            $response = [
                'success' => 1,
                'msg' => $data,
            ];

            return new Response(
                json_encode($response),
                200,
                ['Content-Type' => 'application/json']
            );
        } catch (\Exception $e) {
            $msg = 'Error: '.$e->getMessage();

            $response = [
                'success' => 0,
                'msg' => $msg,
            ];

            return new Response(
                json_encode($response),
                400,
                ['Content-Type' => 'application/json']
            );
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/receipts
     * http://localhost/statement/receipts
     */
    #[Route('/statement/receipts', name: 'statement_receipts')]
    public function receiptsAction(Request $request): Response
    {
        $userAccess = $this->getLoggedUser();

        // Restrict access to admin and god users only
        if (!($userAccess->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_ADMIN
            || $userAccess->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD)) {
            return $this->redirectToRoute('statement_list');
        }

        // Prepare view data
        $this->view->scripts = ['statement/receipts'];
        $this->view->loggedUser = $userAccess;

        return $this->render('statement/receipts.html.twig', ['view' => $this->view]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/getAsyncBillingHistory?dateFrom=01/2024&dateTo=03/2024
     * http://localhost/statement/getAsyncBillingHistory?dateFrom=01/2024&dateTo=03/2024
     * /statement/getAsyncBillingHistory - camelCase URL
     * /statement/get-async-billing-history - kebab-case URL
     */
    #[Route('/statement/getAsyncBillingHistory', name: 'statement_get_async_billing_history', methods: ['GET', 'POST'])]
    #[Route('/statement/get-async-billing-history', name: 'statement_get_async_billing_history_kebab', methods: ['GET', 'POST'])]
    public function getAsyncBillingHistoryAction(Request $request): Response
    {
        $userAccess = $this->getLoggedUser();
        $dateFrom = $request->query->get('dateFrom') ?? $request->request->get('dateFrom');
        $dateTo = $request->query->get('dateTo') ?? $request->request->get('dateTo');
        $params = ['account_id' => $userAccess->getAccountId()];

        if (!empty($dateFrom) && !empty($dateTo)) {
            $dateFrom = str_replace('/', '/01/', $dateFrom);
            $dateTo = str_replace('/', '/28/', $dateTo);
            $params['start_date'] = (new \DateTime($dateFrom))->format('Y-m-d');
            $params['end_date'] = (new \DateTime($dateTo))->format('Y-m-d');
        } else {
            $params['start_date'] = date('Y-m-01', strtotime('-3 months')); // First day three months ago
            $params['end_date'] = date('Y-m-t'); // Last day of this month
        }

        $sql = <<<SQL
        SELECT DISTINCT
            s.statement_id,
            sb.statement_batch_id
        FROM
            statements AS s
            INNER JOIN statement_batches AS sb ON sb.statement_batch_id = s.statement_batch_id
        WHERE
            sb.start_date BETWEEN :start_date AND :end_date
            AND s.account_id = :account_id
            AND sb.statement_batch_id > 35
SQL;

        $results = \Genesis_Db_Connection::getInstance()->findAll($sql, $params);
        $data = [];

        if (!empty($results)) {
            $statementIds = array_reduce($results, function ($accumulator, $item) {
                $accumulator[] = $item['statement_id'];

                return $accumulator;
            });
            $statementBatchIds = array_reduce($results, function ($accumulator, $item) {
                $accumulator[] = $item['statement_batch_id'];

                return $accumulator;
            });

            $statements = \Genesis_Service_Statement::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::in('id', $statementIds),
                    \Genesis_Db_Restriction::in('statementBatchId', $statementBatchIds)
                )
            );

            foreach ($statements as $statement) {
                switch ($this->getLoggedUser()->getAccount()->getBidType()) {
                    case \Genesis_Entity_Account::BID_TYPE_RESIDUAL:
                        if ($statement->getStatementType() == \Genesis_Entity_BillableInstance::ITEM_BOOKING_RESIDUAL) {
                            $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);
                            $data[] = [
                                'dateRange' => date('F j', strtotime($clientStatement->getStatementStartDate())).'-'.date('d, Y', strtotime($clientStatement->getStatementEndDate())),
                                'tenantFees' => $clientStatement->stringTotalBookingCharge(),
                                'tenants' => $clientStatement->getNumLtvItemsGettingBill(),
                                'urls' => [
                                    'PDF' => $this->generateUrl('statement_viewpdf', ['id' => $clientStatement->getStatementId()]),
                                    'CSV' => $this->generateUrl('statement_viewcsv', ['id' => $clientStatement->getStatementId()]),
                                ],
                            ];
                        }
                        break;
                    case \Genesis_Entity_Account::BID_TYPE_TIERED: // AKA CPA
                    default:
                        $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement($userAccess, $statement);

                        if ($userAccess->getMyfootRole() == \Genesis_Entity_UserAccess::ROLE_GOD) {
                            $userId = null;
                        } else {
                            $userId = $userAccess->getId();
                        }
                        try {
                            $movedIn = $clientStatement->getNumMovedInCpaItems();
                            $didNotMoveIn = $clientStatement->getNumCpaBookingItems() - $movedIn;
                        } catch (\Exception $e) {
                            $movedIn = $didNotMoveIn = '-';
                        }

                        $urlParams = ['id' => $clientStatement->getStatementId()];
                        if ($userId) {
                            $urlParams['user_id'] = $userId;
                        }

                        $data[] = [
                            'sortDate' => strtotime($clientStatement->getStatementStartDate()),
                            'dateRange' => date('F j', strtotime($clientStatement->getStatementStartDate())).'-'.date('d, Y', strtotime($clientStatement->getStatementEndDate())),
                            'movedIn' => $movedIn,
                            'didNotMoveIn' => $didNotMoveIn,
                            'moveInRate' => $clientStatement->stringMoveInRate(),
                            'tenantFees' => $clientStatement->stringTotalCharge(),
                            'urls' => [
                                'PDF' => $this->generateUrl('statement_viewpdf', $urlParams),
                                'CSV' => $this->generateUrl('statement_viewcsv', $urlParams),
                            ],
                        ];
                        break;
                }
            }
        }

        return new Response(
            json_encode($data),
            200,
            ['Content-Type' => 'application/json']
        );
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/getAsyncReceipts?dateFrom=01/2024&dateTo=03/2024
     * http://localhost/statement/getAsyncReceipts?dateFrom=01/2024&dateTo=03/2024
     */
    #[Route('/statement/getAsyncReceipts', name: 'statement_get_async_receipts', methods: ['GET', 'POST'])]
    #[Route('/statement/get-async-receipts', name: 'statement_get_async_receipts_kebab', methods: ['GET', 'POST'])]
    public function getAsyncReceiptsAction(Request $request): Response
    {
        $dateFrom = $request->query->get('dateFrom') ?? $request->request->get('dateFrom');
        $dateTo = $request->query->get('dateTo') ?? $request->request->get('dateTo');

        if (!empty($dateFrom) && !empty($dateTo)) {
            $dates = [];
            $dates['dateFrom'] = str_replace('/', '/01/', $dateFrom);
            $dates['dateTo'] = str_replace('/', '/28/', $dateTo);
        } else {
            $dates = null;
        }

        // NetSuite Receipts
        $netsuite_receipts = $this->getNetsuiteReceiptInfo($dates);

        return new Response(
            json_encode($netsuite_receipts),
            200,
            ['Content-Type' => 'application/json']
        );
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/receiptdownload?entity_id=123&entity_name=Company&statement_period=January%202024
     * http://localhost/statement/receiptdownload?entity_id=123&entity_name=Company&statement_period=January%202024
     */
    #[Route('/statement/receiptdownload', name: 'statement_receiptdownload')]
    public function receiptdownloadAction(Request $request): Response
    {
        try {
            $entityName = urldecode($request->query->get('entity_name', ''));
            $statementPeriod = urldecode($request->query->get('statement_period', ''));
            $entityId = urldecode($request->query->get('entity_id', ''));

            if (!$entityId || !$entityName || !$statementPeriod) {
                throw new \Exception('Missing required parameters: entity_id, entity_name, and statement_period are required.');
            }

            // Clean the names for filename
            $cleanEntityName = str_replace(' ', '_', $entityName);
            $cleanStatementPeriod = str_replace(' ', '_', $statementPeriod);

            $netsuitePdfContentUrl = "https://forms.netsuite.com/app/site/hosting/scriptlet.nl?script=24&deploy=1&compid=3370562&ns-at=AAEJ7tMQHnAfkgOH4EqIuqLTt9EuyKkVfW6id9uk-gsPbP_7dfw&entityId=$entityId";

            $pdf = file_get_contents($netsuitePdfContentUrl);

            if ($pdf === false) {
                throw new \Exception('Failed to retrieve PDF from NetSuite.');
            }

            $filename = "Sparefoot_{$cleanStatementPeriod}_{$cleanEntityName}_receipt.pdf";

            return new Response(
                $pdf,
                200,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="'.$filename.'"',
                    'Cache-Control' => 'no-store, no-cache',
                ]
            );
        } catch (\Exception $e) {
            return new Response('Error: '.$e->getMessage(), 400);
        }
    }

    public function getReceipts($entity_ids, $entity_names, $dates = null)
    {
        $receipt_info = [];
        // Note there is a limit of 1000 records returned from Netsuite.
        if (is_array($dates) && !empty($dates)) {
            $timeFrom = (new \DateTime($dates['dateFrom']))->modify('+1 months')->format('m/d/Y');
            $timeTo = (new \DateTime($dates['dateTo']))->modify('+1 months')->format('m/d/Y');
        } else {
            $timeFrom = (new \DateTime('now'))->modify('-2 months')->format('m/d/Y');
            $timeTo = (new \DateTime('now'))->format('m/d/Y');
        }

        // Get the transactions from netsuite
        $search = new \Genesis_Service_Netsuite_NsRecordSearch('transaction'); // will print receipt if paid, else invoice
        $search->setFilter('mainline', null, 'is', 'T');
        $search->setFilter('entity', null, 'anyof', $entity_ids);

        $search->setFilter('trandate', null, 'within', $timeFrom, $timeTo);
        $search->setFilter('type', null, 'anyof', ['CashSale', 'CustInvc']);
        $search->setColumn('amount');
        // sort on transaction date so we always get the most recent.
        $search->setColumn('trandate', null, null, true);
        $search->setColumn('amountremaining');
        $search->setColumn('amountunbilled');
        $search->setColumn('name');
        $search->setColumn('entity');

        $records = $search->lookup();

        if (isset($records) && !empty($records)) {
            for ($i = count($records) - 1; $i >= 0; --$i) {
                $rec = $records[$i];

                $info = [];
                foreach ($rec->columns as $col) {
                    if ($col->name == 'amount') {
                        $info['amount'] = $col->value;
                    } elseif ($col->name == 'amountremaining') {
                        $info['amountremaining'] = $col->value;
                    } elseif ($col->name == 'trandate') {
                        $tdate = $col->value;
                        $info['trandate'] = $tdate;
                        $tdate = strtotime($tdate.' -30 days');
                        $info['statement_period'] = date('F Y', $tdate);
                    } elseif ($col->name == 'entity') {
                        $info['entity_id'] = $col->value;
                        $info['name'] = $entity_names[$col->value];
                    }
                }
                if ($rec->type != 'journalentry') {
                    $info['link'] = '/statement/receiptdownload?entity_id='.$rec->id.'&entity_name='.urlencode($info['name']).'&statement_period='.urlencode($info['statement_period']);
                }
                $receipt_info[] = $info;
            }
        } else {
            error_log('MyFoot Receipts - Netsuite API Error: payload not on entity ids: '.implode(',', $entity_ids));
        }

        return $receipt_info;
    }

    private function getNetsuiteReceiptInfo($dates = null)
    {
        $user = $this->getLoggedUser();
        $account = $user->getAccount();
        // Look into function getBillableEntitiesId

        $billable_entities = $account->getBillableEntities();

        $receipt_info_total = [];
        $entity_ids = [];
        $entity_names = [];
        foreach ($billable_entities as $entity) {
            $entity_ids[] = $entity->getNsCustId();
            $entity_names[$entity->getNsCustId()] = $entity->getNsName();
        }

        $entity_count = count($entity_ids);
        if ($entity_count > 0) {
            // Dividing entities in different requests
            // because Netsuite API can only handle 281 entities in a single request
            $entities_per_request = 90;

            $netsuite_requests = ceil($entity_count / $entities_per_request);
            for ($request = 0; $request < $netsuite_requests; ++$request) {
                $current_request_entity_ids = array_slice($entity_ids, $request * $entities_per_request, $entities_per_request);
                $current_request_entity_names = array_slice($entity_names, $request * $entities_per_request, $entities_per_request, true);

                $receipt_info_total = array_merge($receipt_info_total, $this->getReceipts($current_request_entity_ids, $current_request_entity_names, $dates));
            }
        }

        return array_reverse($receipt_info_total);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/uploadMil
     * http://localhost/statement/uploadMil
     */
    #[Route('/statement/uploadMil', name: 'statement_upload_mil', methods: ['GET', 'POST'])]
    public function uploadMilAction(Request $request): Response
    {
        // Prepare view data
        $this->view->title = 'Reconcile with WebSelfStorage';
        $this->view->scripts = ['statement/upload-mil'];

        try {
            if (!\Genesis_Service_Feature::isActive(\Genesis_Entity_Feature::MYFOOT_SHOW_WSS_UPLOADER, ['account_id' => $this->getLoggedUser()->getAccount()->getId()])) {
                throw new \Exception('Access denied');
            }

            $this->view->facilities = $this->getLoggedUser()->getManagableFacilities(\Genesis_Db_Order::ASC('title'));

            $this->view->statementId = $request->query->get('id') ?? $request->request->get('id');
            $facilityId = $request->query->get('facility_id') ?? $request->request->get('facility_id');
            $moveInFile = $request->query->get('wss_file1') ?? $request->request->get('wss_file1');
            $customerEmailList = $request->query->get('wss_file2') ?? $request->request->get('wss_file2');
            $sourceId = $request->query->get('source_id') ?? $request->request->get('source_id');

            // Handle file uploads
            $uploadedFiles = $request->files->all();
            if (!empty($uploadedFiles)) {
                // make sure the integration type attempting to be uploaded is one of the ones this job can handle
                $validSources = [
                    \Genesis_Entity_Source::ID_WEB_SELF_STORAGE,
                ];
                if (!in_array($sourceId, $validSources)) {
                    throw new \Exception('Cannot upload move-in list for this integration type.');
                }

                // make sure the user has access to manage this facility
                $canManage = false;
                foreach ($this->view->facilities as $manageableFacility) {
                    if ($manageableFacility->getId() == $facilityId) {
                        $canManage = true;
                    }
                }
                if (!$canManage) {
                    throw new \Exception('You do not have access to reconcile for this facility.');
                }

                // make sure the statement is still open for reconciliation
                $statement = \Genesis_Service_Statement::loadById($this->view->statementId);
                $statementBatch = $statement->getStatementBatch();
                if ($statementBatch->getStatus() !== \Genesis_Entity_StatementBatch::STATUS_OPEN) {
                    throw new \Exception('The statement period you are attempting to reconcile is already closed. Please contact customer support.');
                }

                // Access uploaded files through Symfony's Request object
                $wssFile1 = $request->files->get('wss_file1');
                $wssFile2 = $request->files->get('wss_file2');

                // make sure files were uploaded without error
                if ($wssFile1 && $wssFile1->getError() > 0) {
                    throw new \Exception('Error: '.$wssFile1->getErrorMessage());
                } else {
                    $moveInFile = $wssFile1;
                }
                if ($wssFile2 && $wssFile2->getError() > 0) {
                    throw new \Exception('Error: '.$wssFile2->getErrorMessage());
                } else {
                    $customerEmailList = $wssFile2;
                }

                $matchRun = new \Genesis_Entity_Cdp_MatchRun();

                $matchRun->setFacilityId($facilityId);
                $matchRun->setSourceId($sourceId);
                $matchRun->setRunType(\Genesis_Entity_Cdp_MatchRun::RUN_TYPE_IMPORT_AND_MATCH);
                $matchRun->setTenantStartDate($statementBatch->getStartDate());
                $matchRun->setTenantEndDate($statementBatch->getEndDate());
                $matchRun->setBookingStartDate(date('Y-m-d', strtotime('-100 days', strtotime($statementBatch->getStartDate()))));
                $matchRun->setInputFilesObject([$moveInFile, $customerEmailList]);
                $matchRun->setUserId($this->getLoggedUser()->getId());
                $matchRun->setLogEchos(false);
                // $matchRun->setResetExistingMatches(true);
                $matchRun->setCanMoveBookingsUntoOpenStatement(true);

                $matchRun->execute();

                // handle any errors found during the import
                $this->view->cdpErrors = [];
                if (sizeof($matchRun->getErrors()) > 0) {
                    // handle errors
                    $this->view->cdpErrors = $matchRun->getErrors();
                }

                $this->view->matchRun = $matchRun;
            }
        } catch (\Exception $e) {
            $this->view->error = "Error: {$e->getMessage()}";
        }

        return $this->render('statement/upload-mil.html.twig', ['view' => $this->view]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/banners?statement_id=123&facility_id=456
     * http://localhost/statement/banners?statement_id=123&facility_id=456
     */
    #[Route('/statement/banners', name: 'statement_banners', methods: ['GET', 'POST'])]
    public function bannersAction(Request $request): Response
    {
        try {
            $statementId = $request->query->get('statement_id') ?? $request->request->get('statement_id');
            $facilityId = $request->query->get('facility_id') ?? $request->request->get('facility_id');

            if (!$statementId || !$facilityId) {
                throw new \Exception('statement_id and facility_id are required');
            }

            $banners = Statement::getBanners($statementId, $facilityId);

            return new Response(
                json_encode($banners),
                200,
                ['Content-Type' => 'application/json']
            );
        } catch (\Exception $e) {
            return new Response(
                json_encode(['error' => $e->getMessage()]),
                400,
                ['Content-Type' => 'application/json']
            );
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/statement/confirm
     * http://localhost/statement/confirm
     * POST: statement_id
     */
    #[Route('/statement/confirm', name: 'statement_confirm', methods: ['POST'])]
    public function confirmAction(Request $request): Response
    {
        try {
            $statementId = $request->request->get('statement_id');

            if (!$statementId) {
                throw new \Exception('statement_id is required');
            }

            $statement = \Genesis_Service_Statement::loadById($statementId);
            if (!$statement) {
                throw new \Exception('no statement found for statement_id '.$statementId);
            }

            $clientStatement = \Genesis_Entity_Statement_Client::buildClientStatement(
                $this->getLoggedUser(),
                $statement
            );
            if (!$clientStatement) {
                throw new \Exception('No statement found with access allowed for user');
            }

            $confirmation = $clientStatement->confirmStatement();

            return new Response(
                json_encode(array_merge(['success' => true], $confirmation->toArray())),
                200,
                ['Content-Type' => 'application/json']
            );
        } catch (\Exception $e) {
            return new Response(
                json_encode(['success' => false, 'error' => $e->getMessage()]),
                400,
                ['Content-Type' => 'application/json']
            );
        }
    }
}
