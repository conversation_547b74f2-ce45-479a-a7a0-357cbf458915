<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Flot\MtdBookingsBar;
use Sparefoot\MyFootService\Flot\MtdClicksBar;
use Sparefoot\MyFootService\Flot\MtdConversionsLine;
use Sparefoot\MyFootService\Flot\MtdVisitsBar;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class WidgetController extends AbstractRestrictedController
{
    public function _init(): ?RedirectResponse
    {
        $session = User::getSession($this->getRequest());

        if ($this->getRequest()->get('fid')) {
            $facilityId = $this->getRequest()->get('fid');
        } else {
            $facilityId = $session->get('facilityId');
        }

        $session->set('facilityId', $facilityId == -1 ? null : $facilityId);

        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget
     * http://localhost:9019/widget
     */
    #[Route('/widget', name: 'widget_index')]
    public function indexAction(Request $request): Response
    {
        return $this->overviewAction($request);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/overview
     * http://localhost:9019/widget/overview
     */
    #[Route('/widget/overview', name: 'widget_overview')]
    public function overviewAction(Request $request): Response
    {
        $session = User::getSession($request);
        $session->set('facilityId', 'all');
        $this->view->title = 'Booking Widget - Settings';

        return $this->render('widget/overview.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/reservations
     * http://localhost:9019/widget/reservations
     */
    #[Route('/widget/reservations', name: 'widget_reservations')]
    public function reservationsAction(Request $request): Response
    {
        $session = User::getSession($request);

        $this->view->reservations = $this->_fetchReservationsData($request);
        $this->view->facilities = \Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), \Genesis_Db_Restriction::equal('published', 1));
        $this->view->facility_id = $session->get('facilityId');
        $this->view->scripts = ['widget/reservations'];

        return $this->render('widget/reservations.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/setup
     * http://localhost:9019/widget/setup
     */
    #[Route('/widget/setup', name: 'widget_setup')]
    public function setupAction(): Response
    {
        $this->view->facilities = \Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), \Genesis_Db_Restriction::equal('published', 1));
        $this->view->account = \Genesis_Service_Account::loadById($this->getLoggedUser()->getAccountId());
        $this->view->scripts = ['widget/setup'];

        return $this->render('widget/setup.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/analytics
     * http://localhost:9019/widget/analytics
     */
    #[Route('/widget/analytics', name: 'widget_analytics')]
    public function analyticsAction(): Response
    {
        $accountId = $this->getLoggedUser()->getAccountId();

        $this->view->mtdVisitsFlot = new MtdVisitsBar('mtd_visits_flot', $accountId);
        $this->view->mtdClicksFlot = new MtdClicksBar('mtd_clicks_flot', $accountId);
        $this->view->mtdBookingsFlot = new MtdBookingsBar('mtd_bookings_flot', $accountId);
        $this->view->mtdConversionsFlot = new MtdConversionsLine('mtd_conversions_flot', $accountId);
        $this->view->scripts = ['widget/analytics'];

        return $this->render('widget/analytics.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/flotjson?id=mtd_visits_flot
     * http://localhost:9019/widget/flotjson?id=mtd_visits_flot
     */
    #[Route('/widget/flotjson', name: 'widget_flotjson')]
    public function flotjsonAction(Request $request): JsonResponse
    {
        $accountId = $this->getLoggedUser()->getAccountId();

        $flots = [
            'mtd_bookings_flot' => new MtdBookingsBar('mtd_bookings_flot', $accountId),
            'mtd_visits_flot' => new MtdVisitsBar('mtd_visits_flot', $accountId),
            'mtd_clicks_flot' => new MtdClicksBar('mtd_clicks_flot', $accountId),
            'mtd_conversions_flot' => new MtdConversionsLine('mtd_conversions_flot', $accountId),
        ];

        $id = $request->query->get('id');
        if (!isset($flots[$id])) {
            return new JsonResponse(['error' => 'Invalid flot ID'], 400);
        }

        return new JsonResponse($flots[$id]->getjson());
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/widget/downloadsetup
     * http://localhost:9019/widget/downloadsetup
     */
    #[Route('/widget/downloadsetup', name: 'widget_downloadsetup')]
    public function downloadsetupAction(): Response
    {
        $account = $this->getLoggedUser()->getAccount();
        $facilities = \Genesis_Service_Facility::loadByAccountId($this->getLoggedUser()->getAccountId(), \Genesis_Db_Restriction::equal('published', 1));
        $filename = $account->getName().'_SpareFoot_Instructions.csv';

        // replace spaces
        $filename = str_replace(' ', '', $filename);

        $content = 'SpareFoot analytics and booking widget instructions for '.$account->getName().',
,
Demo:,http://my.sparefoot.com/static/bookingWidgetDemo.php,
,
STEP 1: Place the following div tag on every page of your site in the body. We use this as a handle for the tracking information. It must come before any of the code in the other steps.,
"<div id=""__sf""></div> ",
,
STEP 2: Place the following code on your pages after the code from step 1. This includes our analytics library so we can tell you what your customers are doing!,
"<script type=""text/javascript"" src=""https://service.sparefoot.com/js/sfanalytics.js""></script><script type=""text/javascript"">var __sf = new SFAnalytics('.$account->getId().', document.getElementById(\'__sf\'));__sf.renderVisit();</script> ",
,
STEP 3: Place the following code on your page one time. These lines are required only once regardless of how many facility reservation buttons you put on a page.,
"<link href=""https://service.sparefoot.com/css/booking-widget.css"" rel=""stylesheet"" type=""text/css""/><script type=""text/javascript"" src=""https://service.sparefoot.com/js/booking-widget-package.js""></script> ",
,
"IMPORTANT: YOU MUST LEAVE THE LINK APPENDED TO THE BOOKING WIDGET CODE INTACT. REMOVAL OF THE LINK VIOLATES THE SPAREFOOT BOOKING WIDGET TERMS OF USE. While we don\'t charge anything to use the widget, the link is required (it helps us with our optimization).",
,
"STEP 4: Here is the booking widget button code for each facility.  Place it where you want the reserve button to appear. You can replace the text ""Reserve Unit"" with whatever you want the button to say. You can place multiple buttons on the same page or put then on seperate pages (provided that you complete the steps above for each page).",
';

        foreach ($facilities as $fac) {
            $content .= $fac->getTitle().',"<script type=""text/javascript"" src=""https://service.sparefoot.com/syndication/booking/proxy?fid='.$fac->getId().'""></script><a class=""client-hold-button"" style=""background-color: #088A09;"" id=""sparefootBooking_'.$fac->getId().'"">Reserve Unit</a> <p class=""sparefoot-hold-link""><a href=""'.$fac->getFacilityCityPageLink().'"">'.$fac->getSiteVerifyText().'</a> Powered by <a href=""http://www.sparefoot.com"">SpareFoot</a></p> "
';
        }

        $content .= ',
STEP 5: Publish the changes to your website so the code is live.,
';

        $response = new Response($content);
        $response->headers->set('Pragma', 'public');
        $response->headers->set('Expires', '0');
        $response->headers->set('Cache-Control', 'private');
        $response->headers->set('Content-type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename= "'.$filename.'"');
        $response->headers->set('Accept-Ranges', 'bytes');

        return $response;
    }

    protected function getTab(): string
    {
        return self::TAB_WIDGET;
    }

    /**
     * Fetch and organize all of the data to populate the reservations screen.
     */
    private function _fetchReservationsData(Request $request): array
    {
        $startDate = $this->getBeginDate();
        $endDate = $this->getEndDate();

        $reservationDetails = [];

        $facilityId = User::getSession($request)->get('facilityId');

        $accountId = $this->getLoggedUser()->getAccountId();

        $impData = \Genesis_Service_Transaction::loadWidgetByAccountId(
            $accountId, $startDate, $endDate);

        foreach ($impData as $id => $dataArray) {
            // skip invalid booking state
            if ($dataArray['booking_state'] == 'INVALID') {
                continue;
            }

            // if a facility id was specified, only get those bookings
            if ($facilityId && $facilityId != 'all' && ($facilityId != $dataArray['facility_id'])) {
                continue;
            }

            $reservationDetails[$id]['last_name'] = $dataArray['last_name'];
            $reservationDetails[$id]['first_name'] = $dataArray['first_name'];
            $reservationDetails[$id]['unit_number'] = $dataArray['unit_number'];
            $reservationDetails[$id]['monthly_rent'] = $dataArray['monthly_rent'];
            $reservationDetails[$id]['timestamp'] = $dataArray['timestamp'];
            $reservationDetails[$id]['move_in'] = $dataArray['move_in'];
            $reservationDetails[$id]['size_w'] = $dataArray['size_w'];
            $reservationDetails[$id]['size_d'] = $dataArray['size_d'];
            $reservationDetails[$id]['facility_id'] = $dataArray['facility_id'];
            $reservationDetails[$id]['title'] = $dataArray['title'];
            $reservationDetails[$id]['email'] = $dataArray['email'];
            $reservationDetails[$id]['phone'] = $dataArray['phone'];
            $reservationDetails[$id]['traffic_source'] = $dataArray['traffic_source'];
        }

        return $reservationDetails;
    }
}
