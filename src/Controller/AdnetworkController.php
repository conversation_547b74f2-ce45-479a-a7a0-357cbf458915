<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AdnetworkController extends AbstractRestrictedController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/adnetwork
     * http://localhost:9019/adnetwork
     */
    #[Route('/adnetwork', name: 'adnetwork_index')]
    public function indexAction(): Response
    {
        return $this->overviewAction();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/adnetwork/overview
     * http://localhost:9019/adnetwork/overview
     */
    #[Route('/adnetwork/overview', name: 'adnetwork_overview')]
    public function overviewAction(): Response
    {
        $account = $this->getLoggedUser()->getAccount();
        $this->view->account = $account;

        return $this->render('adnetwork/overview.html.twig', [
            'view' => $this->view,
        ]);
    }
}
