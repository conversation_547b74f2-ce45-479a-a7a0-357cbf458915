<?php

/**
 * ZendFrameworkClassAlias.php
 * to map old class names (Zend framework) to new namespaced classes. (Symfony framework).
 */

// Define all report class aliases mapping from modern namespaced classes to legacy Zend-style classes
$reportClassAliases = [
    // Modern namespaced class => Legacy Zend-style alias
    'Sparefoot\\PitaService\\Report\\Analytics\\Query' => 'Pita_Report_Analytics_Query',
    'Sparefoot\\MyFootService\\Service\\Account\\PendoData' => 'AccountMgmt_Models_PendoData',
    // Add more report class mappings here as needed
];

// Register all class aliases
foreach ($reportClassAliases as $modernClassName => $legacyAliasName) {
    // Only create the alias if the legacy alias doesn't exist and the modern class is available
    if (!class_exists($legacyAliasName) && (class_exists($modernClassName) || interface_exists($modernClassName) || trait_exists($modernClassName))) {
        class_alias($modernClassName, $legacyAliasName);
    }
}
