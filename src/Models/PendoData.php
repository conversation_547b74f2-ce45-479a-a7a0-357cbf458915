<?php

namespace Sparefoot\MyFootService\Models;

use Sparefoot\MyFootService\Service\Account;

class PendoData
{
    public function __construct(\Genesis_Entity_UserAccess $user, int $facilityId)
    {
        // data sources
        $this->facility = empty($this->facility) || !isset($this->facility) ? \Genesis_Service_Facility::loadById($facilityId) : $this->facility;
        $this->userData = empty($this->userData) || !isset($this->userData) ? \Genesis_Service_UserAccess::loadById($user->getUserId()) : $this->userData;
        $this->account = empty($this->account) || !isset($this->account) ? \Genesis_Service_Account::loadById($user->getAccountId()) : $this->account;
        $this->environment = empty($this->environment) || !isset($this->environment) ? getenv('SF_ENV') : $this->environment;

        // Visitor Metadata
        $this->visitorID = 'pal:user:mkt:'.$this->userData->getUserId()."/$this->environment";
        $this->email = $this->userData->getEmail();
        $this->fullName = $this->userData->getFirstName().' '.$this->userData->getLastName();
        $this->myFootRole = $user->getMyfootRole();
        $this->createdDate = $user->getAgentStartDate();
        $this->accountStatements = $user->getGetsStatements();
        $this->emails = $user->getGetsEmails();
        $this->canAccessAllFacilities = $user->canAccessAllFacilities();

        // Account Metadata
        $this->accountID = 'mal:account:mkt:'.$user->getAccountId()."/$this->environment";
        $this->accountName = $user->getAccount()->getName();
        $this->facilityID = 'pal:facility:mkt:'.$facilityId."/$this->environment";
        $this->facilityName = $this->facility->getTitle();
        $this->facilityOMIEnabledStatus = (bool) $this->facility->getSuppData('has online moveins');
        $this->topOMIEnabledStatus = empty($this->topOMIEnabledStatus) || !isset($this->topOMIEnabledStatus) ? (bool) $this->isTopOmiEnabled() : (bool) $this->topOMIEnabledStatus;
        $this->sourceName = $this->facility->getSource()->getName();
        $this->sourceId = $this->facility->getSourceId();
        $this->bidType = $this->account->getBidType();
        $this->city = $this->facility->getLocation()->getCity();
        $this->state = $this->facility->getLocation()->getCity();
        $this->paymentType = $this->account->getPaymentType();
        $this->monthlyAccountValue = '';
        $this->yearlyAccountValue = '';
        $this->publishedFacilityCount = count(Account::getAllFacilities($this->account->getId()));
        $this->activeFacilityCount = $this->account->getNumActiveFacilities();
        $this->hiddenFacilityCount = $this->account->getNumHiddenFacilities();
        $this->avgActiveBid = '';
        $this->storesAtOrAbove2Bid = '';
        $this->creationDate = $this->account->getTermsAgreedDate();
    }

    private $account;
    private $facility;
    private $userData;

    private $environment;
    // Visitor Metadata
    private $visitorID;
    private $email;
    private $fullName;
    private $myFootRole;
    private $createdDate;
    private $accountStatements;
    private $emails;
    private $canAccessAllFacilities;

    // Account Metadata
    private $accountID;
    private $accountName;
    private $facilityID;
    private $facilityName;
    private $facilityOMIEnabledStatus; // facility level
    private $topOMIEnabledStatus;
    private $sourceName;
    private $sourceId;
    private $bidType;
    private $city;
    private $state;
    private $paymentType;
    private $monthlyAccountValue;
    private $yearlyAccountValue;
    private $publishedFacilityCount;
    private $activeFacilityCount;
    private $hiddenFacilityCount;
    private $avgActiveBid;
    private $storesAtOrAbove2Bid;
    private $creationDate;

    public function buildPendoObject()
    {
        $pendoMetada = [
            'visitor' => [
                'id' => $this->visitorID,
                'email' => $this->email,
                'fullName' => $this->fullName,
                'myFootRole' => $this->myFootRole,
                'createdDate' => $this->createdDate,
                'accountStatements' => $this->accountStatements,
                'emails' => $this->emails,
                'canAccessAllFacilities' => $this->canAccessAllFacilities,
            ],
            'account' => [
                'id' => $this->accountID,
                'accountName' => $this->accountName,
                'facilityID' => $this->facilityID,
                'facilityName' => $this->facilityName,
                'facilityOMIEnabledStatus' => $this->facilityOMIEnabledStatus, // Facility leve,
                'topOMIEnabledStatus' => $this->topOMIEnabledStatus,
                'sourceName' => $this->sourceName, // Integration type
                'sourceId' => $this->sourceId,
                'bidType' => $this->bidType,
                'city' => $this->city,
                'state' => $this->state,
                'paymentType' => $this->paymentType,
                'monthlyAccountValue' => 'N/A',
                'yearlyAccountValue' => 'N/A',
                'publishedFacilityCount' => $this->publishedFacilityCount,
                'activeFacilityCount' => $this->activeFacilityCount,
                'hiddenFacilityCount' => $this->hiddenFacilityCount,
                'avgActiveBid' => 'N/A',
                'storesAtOrAbove2Bid' => 'N/A',
                'creationDate' => $this->creationDate,
            ],
        ];

        return json_encode($pendoMetada);
    }

    public function isTopOmiEnabled()
    {
        // $this->userData is \Genesis_Entity_UserAccess
        $facilities = $this->userData->getAllFacilities();

        if (is_int($facilities) && $facilities === 1) {
            return true;
        }

        if (count($facilities) <= 0) {
            return false;
        }

        foreach ($facilities as $facility) {
            if (in_array($facility->getSourceId(), [
                \Genesis_Entity_Source::ID_PUBLIC_STORAGE,
                \Genesis_Entity_Source::ID_SITELINK,
                \Genesis_Entity_Source::ID_STOREDGE,
            ])) {
                return true;
            } else {
                return false;
            }
        }
    }

    public function hiddenFacilityCount()
    {
        $hiddenFacilities = \Genesis_Service_Facility::loadByAccountId(
            $this->account->getId(),
            \Genesis_Db_Restriction::equal('live', 0)
        )->toArray();

        return count($hiddenFacilities);
    }
}
