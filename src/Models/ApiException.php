<?php

namespace Sparefoot\MyFootService\Models;

use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class ApiException extends \Exception
{
    protected $extended;
    protected $code;
    protected $message;

    public function __construct($code = self::BAD_REQUEST, $extended = '')
    {
        if (!is_numeric($code)) {
            $this->code = self::TEAPOT;
            $this->extended = 'string passed to api exception handler, please pass a HTTP code instead of: '.$code;
        } elseif ((int) $code < 400 || (int) $code > 599) {
            $this->code = self::TEAPOT; // done fucked up. cast this to something reasonable
            $this->extended = 'unsupported HTTP code passed to api exception handler. Please use a defined code instead of: '.$code;
        } else {
            $this->code = $code;
            $this->message = self::getErrorClassMessage();
            $this->extended = $extended;
        }
    }

    public const BAD_REQUEST = 400;
    public const UNAUTHORIZED = 401;
    public const FORBIDDEN = 403;
    public const NOT_FOUND = 404;
    public const METHOD_NOT_ALLOWED = 405;
    public const NOT_ACCEPTABLE = 406;
    public const TIMEOUT = 408;
    public const CONFLICT = 409;
    public const GONE = 410;
    public const PRECONDITION_FAILED = 412;
    public const TEAPOT = 418;

    public const INTERNAL_SERVER_ERROR = 500;
    public const NOT_IMPLEMENTED = 501;
    public const BAD_GATEWAY = 502;
    public const SERVICE_UNAVAILABLE = 503;
    public const GATEWAY_TIMEOUT = 504;

    public function getErrorClassMessage()
    {
        switch ((int) $this->getCode()) {
            case 400:
                return 'Bad Request';
            case 401:
                return 'Unauthorized';
            case 403:
                return 'Forbidden';
            case 404:
                return 'Not Found';
            case 405:
                return 'Method Not Allowed';
            case 406:
                return 'Not Acceptable';
            case 408:
                return 'Timeout';
            case 409:
                return 'Conflict';
            case 410:
                return 'Gone';
            case 412:
                return 'Precondition Failed';
            case 418:
                return 'Illegal HTTP Code';
            case 500:
                return 'Internal Server Error';
            case 501:
                return 'Not Implemented';
            case 502:
                return 'Bad Gateway';
            case 503:
                return 'Service Unavailable';
            case 504:
                return 'Gateway Timeout';
            default:
                return 'Unknown';
        }
    }

    final public function getExtended()
    {
        return $this->extended;
    }

    public static function apiExceptionHandler($e)
    {
        $body = [];
        $code = 400;

        if ($e instanceof ApiException) {
            $body['errors'][] = [
                'status' => $e->getCode(),
                'title' => $e->getMessage(),
                'detail' => $e->getExtended(),
            ];
            $code = $e->getCode();
        } else { // someone used Exception instead of ApiException, so no real http code
            $body['errors'][] = [
                'status' => 400,
                'title' => 'Internal Server Error',
                'detail' => $e->getMessage(),
            ];
            $body['errors'][] = [
                'status' => 400,
                'title' => 'Internal Server Error',
                'detail' => 'ApiException invoked using an Exception instead of ApiException at line '.$e->getLine().' of '.$e->getFile(),
            ];
        }

        // Log using Symfony/Monolog - create a direct logger instance
        // This approach ensures logging works even when container is not available
        $logger = new Logger('api_exception');
        $handler = new StreamHandler('php://stderr', Logger::ERROR);
        $formatter = new JsonFormatter();
        $handler->setFormatter($formatter);
        $logger->pushHandler($handler);

        $logger->error('API Exception occurred', [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'exception_class' => get_class($e),
        ]);

        header('Content-Type: application/json');
        http_response_code($code);
        exit(json_encode($body));
    }
}
