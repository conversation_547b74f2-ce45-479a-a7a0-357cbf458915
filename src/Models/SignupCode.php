<?php

namespace Sparefoot\MyFootService\Models;

class SignupCode
{
    /**
     * @var string
     */
    private $code;

    /**
     * @var string One of the pre-defined \Genesis_Entity_Account::BID_TYPE_* constants
     */
    private $bidType;

    /**
     * @var string
     */
    private $value;

    /**
     * SignupCode constructor.
     *
     * @param string $code The signup code to parse
     *
     * @throws \RuntimeException upon failure to parse code
     *
     * Examples of valid signup codes:
     * - C5P1  = CPA (Percent) with 1.5 multiplier
     * - R0X2  = Residual at rate of 20%
     * - C52P1 = CPA (Percent) with 1.25 multiplier
     * - CN5P1 = CPA with insights disabled
     * - R1X5  = Residual at rate of 51%
     */
    public function __construct($code)
    {
        // Attempt to parse code
        if (!$this->parse($code)) {
            throw new \RuntimeException('Please enter a valid signup code. To get one, call (512) 705-6208 and press 3 for Sales.');
        }
    }

    /**
     * Get the original signup code.
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Get the bid type for this signup code.
     *
     * @return string One of the \Genesis_Entity_Account::BID_TYPE_* constants
     */
    public function getBidType()
    {
        return $this->bidType;
    }

    /**
     * Get the parsed value from the signup code.
     *
     * @return string
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Configure an account based on the signup code.
     *
     * @param \Genesis_Entity_Account    $account    The account to configure
     * @param \Genesis_Entity_UserAccess $userAccess The user access to configure
     *
     * @return void
     */
    public function configureAccount(\Genesis_Entity_Account $account, \Genesis_Entity_UserAccess $userAccess)
    {
        $bidType = $this->getBidType();

        // Set account's bid type
        $account->setBidType($bidType);

        // Default to no subscription fee
        $account->setNewFacilitySubscriptionFee(0);

        if ($bidType === \Genesis_Entity_Account::BID_TYPE_RESIDUAL) {
            $userAccess->setProductInterest(\Genesis_Entity_UserAccess::PROD_RESIDUAL);
            $account->setLtvPercent($this->getValue() / 100);
            $account->setName($account->getName()); // @todo Why?!
        } else {
            $userAccess->setProductInterest(\Genesis_Entity_UserAccess::PROD_CPA);

            // Set insights on/off
            if (preg_match('/^CN.+/i', $this->code)) {
                $account->setDefaultInsights(0);
            } else {
                $account->setDefaultInsights(1);
                $account->setNewFacilitySubscriptionFee(\Genesis_Entity_Products::CPA_FACILITY_MONTHLY);
            }

            // Set minimum bid
            $account->setMinBid($this->getValue());
        }
    }

    /**
     * Parse and validate the signup code.
     *
     * @param string $code The raw signup code
     *
     * @return bool True if parsing was successful, false otherwise
     */
    private function parse($code)
    {
        // Sanitize code
        $code = trim(strtoupper($code));

        // Allow valid sign-up codes only. Examples:
        // C5P1  = CPA (Percent) with 1.5 multiplier
        // R0X2  = Residual at rate of 20%
        // C52P1 = CPA (Percent) with 1.25 multiplier
        // (3 digit rate amounts are also possible by adding to the first digit.
        // order of all digits is reversed when interpreted)
        $matches = [];
        preg_match('/^(R|C)(N)?([0-9]{1,2})(X|P)([0-9])$/', $code, $matches);

        if (!(strlen($code) > 0) || empty($matches) || $matches[0] !== $code) {
            return false;
        }

        // Set some defaults
        $bidType = null;
        $value = sprintf('%s%s', $matches[5], strrev($matches[3]));
        $bidTypeLetter = $matches[1];

        // Determine bid type (and override value if necessary) based on code
        switch ($bidTypeLetter) {
            case 'R':
                $bidType = \Genesis_Entity_Account::BID_TYPE_RESIDUAL;
                break;
            case 'C':
                $bidType = \Genesis_Entity_Account::BID_TYPE_PERCENT;
                // For CPA-percent, parse code as a decimal instead of integer
                $value = sprintf('%s.%s', $matches[5], strrev($matches[3]));
                break;
            default:
                throw new \Exception('Invalid sign-up code');
        }

        if (!($value > 0 || ($bidType === \Genesis_Entity_Account::BID_TYPE_VISITOR && $value >= 0))) {
            return false;
        }

        $this->bidType = $bidType;
        $this->value = $value;
        $this->code = $code;

        return true;
    }
}
