<?php

namespace Sparefoot\MyFootService\Service;

use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Response;

class UserCookie extends AbstractService
{
    public const REMEMBER_ME_EMAIL = 'remember_me_email';
    public const USER_REDIRECT = 'redirect_url';
    public const ACTIVE_ACCOUNT_ID = 'active_account_id';
    public const ACTIVE_FACILITY_ID = 'active_facility_id';
    public const AUTH_BEARER_TOKEN = 'auth_bearer_token';

    protected static array $hostOnly = [
        self::AUTH_BEARER_TOKEN => true,
    ];

    private static array $latches = [];

    public static function get(string $name, ?string $default = null): ?string
    {
        if (array_key_exists($name, self::$latches)) {
            return self::$latches[$name];
        }

        $request = RequestContextService::getRequest();
        if ($request && $request->cookies->has($name)) {
            $value = $request->cookies->get($name);

            // Basic sanitization
            return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $default;
        }

        // Fallback to $_COOKIE if no request is available
        $value = $_COOKIE[$name] ?? $default;

        return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $default;
    }

    public static function set(
        string $name,
        string $value,
        ?int $expire = null,
        ?string $path = null,
        ?string $domain = null,
    ): void {
        if (!$expire) {
            $expire = time() + 86400 * 30; // 30 days default
        }
        if (!$path) {
            $path = '/';
        }

        // Determine domain
        if (!$domain) {
            $domain = self::getDomainForCookie($name);
        }

        // Validate and sanitize value
        $sanitizedValue = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');

        if (self::hostOnly($name)) {
            setcookie($name, $sanitizedValue, $expire, $path);
        } else {
            setcookie($name, $sanitizedValue, $expire, $path, $domain);
        }

        self::$latches[$name] = $value;
    }

    public static function clear(string $name): void
    {
        $domain = self::getDomainForCookie($name);

        if (self::hostOnly($name)) {
            setcookie($name, '', 1, '/');
        } else {
            setcookie($name, '', 1, '/', $domain);
        }

        self::$latches[$name] = null;
        unset(self::$latches[$name]);
    }

    private static function hostOnly(string $cookieKey): bool
    {
        return isset(self::$hostOnly[$cookieKey]);
    }

    /**
     * Get the appropriate domain for cookie setting.
     */
    private static function getDomainForCookie(string $name): ?string
    {
        $request = RequestContextService::getRequest();

        if ($request) {
            $host = $request->getHost();

            // Remove 'my' prefix for domain cookies (legacy logic)
            return str_replace('my', '', $host);
        }

        // Fallback to $_SERVER if no request available
        return isset($_SERVER['SERVER_NAME'])
            ? str_replace('my', '', $_SERVER['SERVER_NAME'])
            : null;
    }

    /**
     * Create a Symfony Cookie object for response headers.
     */
    public static function createCookie(
        string $name,
        string $value,
        ?int $expire = null,
        ?string $path = null,
        ?string $domain = null,
        bool $secure = false,
        bool $httpOnly = true,
    ): Cookie {
        if (!$expire) {
            $expire = time() + 86400 * 30;
        }
        if (!$path) {
            $path = '/';
        }
        if (!$domain) {
            $domain = self::getDomainForCookie($name);
        }

        return new Cookie(
            $name,
            htmlspecialchars($value, ENT_QUOTES, 'UTF-8'),
            $expire,
            $path,
            self::hostOnly($name) ? null : $domain,
            $secure,
            $httpOnly
        );
    }
}
