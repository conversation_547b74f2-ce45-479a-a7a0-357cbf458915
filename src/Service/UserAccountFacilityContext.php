<?php

namespace Sparefoot\MyFootService\Service;

use Symfony\Component\HttpFoundation\Request;

class UserAccountFacilityContext extends AbstractService
{
    public const ACTIVE_ACCOUNT_ID = UserCookie::ACTIVE_ACCOUNT_ID;
    public const ACTIVE_FACILITY_ID = UserCookie::ACTIVE_FACILITY_ID;
    public const ACCOUNT_POST_VAR = 'account_id';
    public const FACILITY_POST_VAR = 'fid';
    public const CABINET_GOD_ACCOUNT_ID = 'myFootGodAccountId';

    private static $initMode;
    private static $userAccess;

    /**
     * getter.
     *
     * @return bool|mixed
     */
    private static function _activeAccountIdPost()
    {
        // $request = self::getRequest() ?: \Zend_Controller_Front::getInstance()->getRequest();
        $request = RequestContextService::getRequest();
        $accountId = $request->get(self::ACCOUNT_POST_VAR, false);
        if (!$accountId > 0) {
            return false;
        }

        return $accountId;
    }

    /**
     * getter.
     *
     * @return bool|mixed
     */
    private static function _activeFacilityIdPost()
    {
        $request = RequestContextService::getRequest();
        $facilityId = $request->get(self::FACILITY_POST_VAR, false);
        if (!$facilityId > 0) {
            return false;
        }

        return $facilityId;
    }

    /**
     * getter/setter.
     *
     * @param null $accountId
     *
     * @throws \Exception
     */
    private static function _activeAccountIdCookie($accountId = null)
    {
        if (!$accountId > 0) {
            return UserCookie::get(self::ACTIVE_ACCOUNT_ID, false);
        }

        // do not check cookie, just do
        UserCookie::set(self::ACTIVE_ACCOUNT_ID, ''.$accountId);
    }

    /**
     * getter/setter.
     *
     * @param null $facilityId
     *
     * @throws \Exception
     */
    private static function _activeFacilityIdCookie($facilityId = null)
    {
        if (!$facilityId > 0) {
            return UserCookie::get(self::ACTIVE_FACILITY_ID, false);
        }

        // do not check cookie, just do
        UserCookie::set(self::ACTIVE_FACILITY_ID, ''.$facilityId);
    }

    /**
     * get the first facility ID.
     */
    private static function _getDefaultFacilityId(\Genesis_Entity_UserAccess $userAccess, $accountId = null)
    {
        static $defaultFacilityId;
        if ($defaultFacilityId) {
            return $defaultFacilityId;
        }
        $restriction = \Genesis_Db_Restriction::equal('published', 1);

        if ($accountId && $userAccess->isMyFootGod()) {
            $facilities = \Genesis_Service_Facility::loadByAccountId($accountId, $restriction);
        } else {
            $facilities = $userAccess->getManagableFacilities($restriction);
        }

        if (!count($facilities)) {
            return false;
        }

        /**
         * @var $firstFacility \Genesis_Entity_Facility
         * @var $nextFacility \Genesis_Entity_Facility
         */
        $firstFacility = false;
        foreach ($facilities as $nextFacility) {
            if ($firstFacility === false || $firstFacility->getPublished() == 0) {
                $firstFacility = $nextFacility;
                continue;
            } elseif (strcasecmp($nextFacility->getTitle(), $firstFacility->getTitle()) > 0) {
                continue;
            }
            $firstFacility = $nextFacility;
        }

        return $defaultFacilityId = $firstFacility->getId();
    }

    private static function _getAccountIdForFacilityId($facilityId)
    {
        try {
            $facility = self::_facilityExists($facilityId);
            if (!$facility) {
                return false;
            }

            return $facility->getAccountId();
        } catch (\Exception $e) {
            return false;
        }
    }

    private static function _keysMatch($accountId, $facilityId)
    {
        if (!self::_accountExists($accountId)) {
            return false;
        }
        if (!self::_facilityExists($facilityId)) {
            return false;
        }

        return self::_getAccountIdForFacilityId($facilityId) === $accountId;
    }

    private static function _activeAccountIdSession($accountId = null)
    {
        if (!$accountId > 0) {
            return User::getSession()->get('accountId');
        }

        User::getSession()->set('accountId', $accountId);
    }

    private static function _activeFacilityIdSession($facilityId = null)
    {
        if (!$facilityId > 0) {
            return User::getSession()->get('facilityId');
        }

        User::getSession()->set('facilityId', $facilityId);
    }

    private static function _setAllServices(\Genesis_Entity_UserAccess $userAccess, $facilityId = null, $accountId = null)
    {
        if (!self::_accountExists($accountId)) {
            // fallback to default accountId (before accountId was modified)
            $accountId = $userAccess->getAccountId();
        }

        if ($userAccess->isMyFootGod()) {
            // cabinet
            self::_godAccountId($userAccess, $accountId);
            // useraccess
            $userAccess->setAccountId($accountId);

            // check for mismatch between acct and facility
            if (!self::_keysMatch($accountId, $facilityId)) {
                $facilityId = self::_getDefaultFacilityId($userAccess);
            }
        }
        // now set facility (everyone)
        // guardians
        if (!self::_facilityExists($facilityId)) {
            $facilityId = self::_getDefaultFacilityId($userAccess);
        }

        if (!in_array($facilityId, $userAccess->getManageableFacilityIds())) {
            $facilityId = self::_getDefaultFacilityId($userAccess);
        }

        // session
        self::_activeAccountIdSession($accountId);
        self::_activeFacilityIdSession($facilityId);
        self::_activeAccountIdCookie($userAccess->getAccountId());
        // cookie
        self::_activeAccountIdCookie($accountId);
        self::_activeFacilityIdCookie($facilityId);
        self::_debugExit($userAccess);

        return self::$userAccess = $userAccess;
    }

    /**
     * order is
     * - post
     * - cookie
     * - Cabinet (account only)
     * - NOT session.
     *
     * @return \Genesis_Entity_UserAccess
     *
     * @throws \Exception
     */
    public static function init($userId)
    {
        if (self::$initMode) {
            return self::$userAccess;
        }
        $userAccess = \Genesis_Service_UserAccess::loadById($userId);
        if (!$userAccess) { // bail when failed
            throw new \Exception('no user ID');
        }
        // get all possible
        $accountIdPost = self::_activeAccountIdPost();
        $facilityIdPost = self::_activeFacilityIdPost();
        $accountIdSession = self::_activeAccountIdSession();
        $facilityIdSession = self::_activeFacilityIdSession();
        $accountIdCookie = self::_activeAccountIdCookie();
        $facilityIdCookie = self::_activeFacilityIdCookie();
        $accountIdCabinet = self::_godAccountId($userAccess);

        // switch contexts to new account for account post
        if ($accountIdPost !== false && $accountIdPost != $accountIdSession) {
            self::$initMode = 'new accountId post';

            return self::_setAllServices($userAccess, null, $accountIdPost);
        }

        // switch context for new facility for facility post
        if ($facilityIdPost !== false && $facilityIdPost != $facilityIdSession) {
            self::$initMode = 'new facilityId post';

            return self::_setAllServices($userAccess, $facilityIdPost, self::_getAccountIdForFacilityId($facilityIdPost));
        }

        // double session set
        if (self::_keysMatch($accountIdSession, $facilityIdSession)) {
            self::$initMode = 'double session';

            return self::_setAllServices($userAccess, $facilityIdSession, $accountIdSession);
        }

        // cookies, try both first
        if (self::_keysMatch($accountIdCookie, $facilityIdCookie)) {
            self::$initMode = 'double cookies';

            return self::_setAllServices($userAccess, $facilityIdCookie, $accountIdCookie);
        }

        // cookie fallback to account
        if (false !== $accountIdCookie) {
            self::$initMode = 'account cookie fallback';

            return self::_setAllServices($userAccess, null, $accountIdCookie);
        }

        // cookie fallback to facility
        if (false !== $facilityIdCookie) {
            self::$initMode = 'facility cookie fallback';

            return self::_setAllServices($userAccess, $facilityIdCookie, self::_getAccountIdForFacilityId($facilityIdCookie));
        }

        // cabinet
        if (false !== $accountIdCabinet) {
            self::$initMode = 'cabinet account fallback';

            return self::_setAllServices($userAccess, null, $accountIdCabinet);
        }

        // ignore session

        // brand new user to get this far
        self::$initMode = 'useraccess account init';

        return self::_setAllServices($userAccess);
    }

    private static function _accountExists($accountId)
    {
        static $loadedAccount = [];
        if (!isset($loadedAccount[$accountId])) {
            $loadedAccount[$accountId] = \Genesis_Service_Account::loadById($accountId);
        }

        return $loadedAccount[$accountId];
    }

    private static function _facilityExists($facilityId)
    {
        static $loadedFacility = [];
        if (!isset($loadedFacility[$facilityId])) {
            $loadedFacility[$facilityId] = \Genesis_Service_Facility::load(
                \Genesis_Db_Restriction::and_(
                    \Genesis_Db_Restriction::equal('id', $facilityId),
                    \Genesis_Db_Restriction::equal('published', 1)
                )
            )->current();
        }

        return $loadedFacility[$facilityId];
    }

    /**
     * getter/setter depending on if account ID is passed.
     *
     * @param null $accountId
     *
     * @return bool|\Genesis_Entity_Cabinet|null
     */
    private static function _godAccountId(\Genesis_Entity_UserAccess $userAccess, $accountId = null)
    {
        if (!$userAccess || !$userAccess->isMyFootGod()) {
            return false;
        }
        $cabinet = \Genesis_Service_Cabinet::get();
        if ($accountId) {
            // Check for valid acct
            if (!self::_accountExists($accountId)) {
                return false;
            }

            if ($cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID) == $accountId) {
                return $accountId; // skip changing a nochange
            }

            $cabinet->setMeta(self::CABINET_GOD_ACCOUNT_ID, $accountId);
            \Genesis_Service_Cabinet::save($cabinet);
        }

        if (!$cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID)) {
            return false;
        }

        return $cabinet->getMeta(self::CABINET_GOD_ACCOUNT_ID);
    }

    private static function _debugExit(\Genesis_Entity_UserAccess $userAccess)
    {
        return;
        $session = User::getSession();
        error_log('['.self::$initMode.':'.$_SERVER['REQUEST_URI'].']');
        error_log('Fs '.$session->get('facilityId').' Fc '.self::_activeFacilityIdCookie().' Fp '.self::_activeFacilityIdPost());
        error_log('As '.$session->get('accountId').' Ac '.self::_activeAccountIdCookie().' Ap '.self::_activeAccountIdPost());

        // error_log($message);
        //            self::$initMode .' '.$facilityId. ' f=' . self::_getActiveFacilityIdPost() .' a='. self::_getActiveAccountIdPost().' '.$_SERVER["REQUEST_URI"]);
        // die();
    }

    public static function clearContext()
    {
        UserCookie::clear(self::ACTIVE_ACCOUNT_ID);
        UserCookie::clear(self::ACTIVE_FACILITY_ID);
        User::getSession()->set('facilityId', null);
        User::getSession()->set('accountId', null);
    }
}
