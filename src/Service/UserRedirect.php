<?php

namespace Sparefoot\MyFootService\Service;

use Symfony\Component\HttpFoundation\Request;

class UserRedirect
{
    public const REDIRECT = UserCookie::USER_REDIRECT;
    private static $_redirect = false;

    public static function needsRedirect(?Request $request = null)
    {
        if (!self::getRedirect()) {
            return false;
        }
        if (self::getRedirect() === '/') {
            self::clearRedirect();

            return false;
        }

        // Get current request URI using Symfony Request
        $currentUri = $request ? $request->getRequestUri() : '/';

        if ($currentUri === self::getRedirect()) {
            self::clearRedirect();

            return false;
        }

        return true;
    }

    public static function setRedirect($url)
    {
        if (self::$_redirect) {
            throw new \Exception('cannot set redirect url twice per http request');
        }

        self::$_redirect = $url;
        UserCookie::set(self::REDIRECT, $url, '0', '/');
    }

    public static function getRedirect()
    {
        if (self::$_redirect) {
            return self::$_redirect;
        }

        if (UserCookie::get(self::REDIRECT)) {
            return UserCookie::get(self::REDIRECT);
        }

        return false;
    }

    public static function clearRedirect()
    {
        UserCookie::clear(self::REDIRECT);
    }
}
