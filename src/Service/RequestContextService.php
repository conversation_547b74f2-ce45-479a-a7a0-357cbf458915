<?php

namespace Sparefoot\MyFootService\Service;

use Symfony\Component\HttpFoundation\Request;

class RequestContextService
{
    protected static ?Request $request = null;

    /**
     * Set the current request.
     */
    public static function setRequest(Request $request): void
    {
        self::$request = $request;
    }

    /**
     * Get the current request.
     */
    public static function getRequest(): ?Request
    {
        return self::$request;
    }
}
