<?php

namespace Sparefoot\MyFootService\Service;

class UserAuthByBooking extends AbstractService
{
    public const HEADER = 'X-AUTH-BOOKING';
    public const SALT = 'y6uh8`HFYp}+gwb%feXT=&T8o=r.-[bO5iPaH~N@';
    public const VALID_FOR_DAYS = 30;

    private static $_userId;

    public static function getUserId()
    {
        if (self::$_userId === null) {
            // Check if token is in the header?
            $payload = self::_getAuthByBookingHeader();
            $secret = null;
            if ($payload) {
                $secret = $payload->secret;
                $confirmationCode = $payload->booking;
                $email = $payload->email;
            }

            // Check if token is in the params?
            $paramsSet = self::_areParamsSet();
            if ($paramsSet) {
                $request = RequestContextService::getRequest();
                $secret = $request->get('s');
                $confirmationCode = $request->get('confirmation_code');
                $email = $request->get('email');
            }

            if ($secret && $confirmationCode && $email) {
                if (self::isSecretValid($secret, $confirmationCode, $email)) {
                    $user = \Genesis_Service_User::loadByEmail($email);
                    if ($user) {
                        self::$_userId = $user->getId();
                    }
                }
            }
        }

        return self::$_userId;
    }

    public static function serializeToken($secret, $bookingConfirmationCode, $email)
    {
        $pieces = [
            'secret' => $secret,
            'booking' => $bookingConfirmationCode,
            'email' => $email,
        ];

        return json_encode($pieces);
    }

    public static function logout()
    {
        self::$_userId = null;
    }

    private static function isSecretValid($secret, $confirmationCode, $email)
    {
        $result = false;

        // Today + 1 day for time drift
        $date = (new \DateTime())->modify('+1 day');

        for ($i = 0; $i <= self::VALID_FOR_DAYS + 1; ++$i) {
            $time = $date->format('Y-m-d');
            $hash = md5($confirmationCode.self::SALT.$time.$email);

            if ($hash == $secret) {
                $result = true;
                break;
            }

            $date->modify('-1 day');
        }

        return $result;
    }

    private static function _areParamsSet()
    {
        $result = false;

        $request = RequestContextService::getRequest();
        $secret = $request->get('s');
        $confirmationCode = $request->get('confirmation_code');
        $email = $request->get('email');

        if ($secret && $confirmationCode && $email) {
            $result = true;
        }

        return $result;
    }

    private static function _getAuthByBookingHeader()
    {
        $payload = RequestContextService::getRequest()->headers->get(self::HEADER);

        if ($payload) {
            return json_decode($payload);
        } else {
            return false;
        }
    }
}
