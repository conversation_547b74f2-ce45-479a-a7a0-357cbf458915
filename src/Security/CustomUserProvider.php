<?php

namespace Sparefoot\MyFootService\Security;

use Sparefoot\MyFootService\Service\RequestContextService;
use Sparefoot\MyFootService\Service\User as ServiceUser;
use Sparefoot\MyFootService\Service\UserAccountFacilityContext;
use Sparefoot\MyFootService\Service\UserOauth;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

class CustomUserProvider implements UserProviderInterface, PasswordUpgraderInterface
{
    private array $users = [];
    private RequestStack $requestStack;
    private ?LoggerInterface $logger;

    public function __construct(RequestStack $requestStack, ?LoggerInterface $logger = null)
    {
        $this->requestStack = $requestStack;
        $this->logger = $logger;
    }

    public function loadUserByIdentifier(string $identifier): UserInterface
    {
        $genesisUser = \Genesis_Service_UserAccess::loadByEmail($identifier);
        if (!$genesisUser) {
            throw new UserNotFoundException(sprintf('User "%s" not found.', $identifier));
        }

        // Set the static variable for legacy code compatibility
        ServiceUser::setUserAccess($genesisUser);

        // Validate OAuth token consistency if available
        $this->validateTokenConsistency($identifier);

        $user = new User($genesisUser);

        // Get the current request from the request stack
        $request = $this->requestStack->getCurrentRequest();
        if ($request !== null) {
            RequestContextService::setRequest($request);
            UserAccountFacilityContext::init($user->getId());
            
            // Attempt token renewal if needed
            $this->handleTokenRenewal($user, $request);
        }

        return $user;
    }

    public function loadUserByUsername(string $username): UserInterface
    {
        return $this->loadUserByIdentifier($username);
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Invalid user class "%s".', get_class($user)));
        }

        // Check if user's token is expired before refreshing
        if ($user->isTokenExpired()) {
            $this->logger?->info('User token expired during refresh, forcing re-authentication', [
                'user' => $user->getEmail()
            ]);
            throw new UserNotFoundException('User token expired, re-authentication required.');
        }

        return $this->loadUserByUsername($user->getUsername());
    }

    public function supportsClass(string $class): bool
    {
        return User::class === $class || is_subclass_of($class, User::class);
    }

    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
    }

    /**
     * Validate OAuth token consistency for the user
     */
    private function validateTokenConsistency(string $identifier): void
    {
        try {
            $request = $this->requestStack->getCurrentRequest();
            if (!$request || !$request->getSession()->has('oauth_token_id')) {
                return; // No session token to validate
            }

            $sessionTokenId = $request->getSession()->get('oauth_token_id');
            $sessionTokenExpires = $request->getSession()->get('oauth_token_expires');
            
            if (!UserOauth::validateTokenConsistency($sessionTokenId, $sessionTokenExpires)) {
                $this->logger?->warning('Token validation failed during user loading', [
                    'user' => $identifier,
                    'session_token_id' => $sessionTokenId
                ]);
                
                // Clear invalid session data
                $request->getSession()->remove('oauth_token_id');
                $request->getSession()->remove('oauth_token_expires');
            }
        } catch (\Exception $e) {
            $this->logger?->error('Error validating token consistency', [
                'user' => $identifier,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle token renewal if needed
     */
    private function handleTokenRenewal(User $user, Request $request): void
    {
        try {
            if ($user->needsTokenRenewal()) {
                $renewalSuccess = $user->renewToken();
                
                if ($renewalSuccess) {
                    // Update session with new token info
                    $token = UserOauth::getToken();
                    if ($token) {
                        $request->getSession()->set('oauth_token_id', $token->getIdentifier());
                        $request->getSession()->set('oauth_token_expires', $token->getExpires());
                    }
                    
                    $this->logger?->info('OAuth token renewed during user loading', [
                        'user' => $user->getEmail()
                    ]);
                } else {
                    $this->logger?->warning('OAuth token renewal failed during user loading', [
                        'user' => $user->getEmail()
                    ]);
                }
            }
        } catch (\Exception $e) {
            $this->logger?->error('Error handling token renewal', [
                'user' => $user->getEmail(),
                'error' => $e->getMessage()
            ]);
        }
    }
}
