<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    private $genesisUserAccess;
    private $email;
    private $roles = [];
    private $password;

    public function __construct(\Genesis_Entity_UserAccess $genesisUserAccess)
    {
        $this->genesisUserAccess = $genesisUserAccess;
        $this->email = $genesisUserAccess->getEmail();

        // Set roles based on the user's MyFoot role
        $myfootRole = $this->genesisUserAccess->getMyfootRole();
        // $roles = ['ROLE_USER']; // Default role for all authenticated users
        $roles = [];
        switch ($myfootRole) {
            case \Genesis_Entity_UserAccess::ROLE_GOD:
                $roles[] = 'ROLE_GOD';
                break;
            case \Genesis_Entity_UserAccess::ROLE_ADMIN:
                $roles[] = 'ROLE_ADMIN';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FULL:
                $roles[] = 'ROLE_FULL';
                break;
            case \Genesis_Entity_UserAccess::ROLE_LIMITED:
                $roles[] = 'ROLE_LIMITED';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FACILITYEDITOR:
                $roles[] = 'ROLE_FACILITYEDITOR';
                break;
            case \Genesis_Entity_UserAccess::ROLE_SEARCHANALYST:
                $roles[] = 'ROLE_SEARCHANALYST';
                break;
            case \Genesis_Entity_UserAccess::ROLE_GEOPAGE:
                $roles[] = 'ROLE_GEOPAGE';
                break;
            default:
                $roles[] = $myfootRole;
                break;
        }
        $this->roles = array_unique($roles);
        $this->password = $genesisUserAccess->getPassword();
    }

    public function getGenesisUserAccess(): \Genesis_Entity_UserAccess
    {
        return $this->genesisUserAccess;
    }

    public function setGenesisUserAccess(\Genesis_Entity_UserAccess $genesisUserAccess): self
    {
        $this->genesisUserAccess = $genesisUserAccess;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getRoles(): array
    {
        $roles = $this->roles;

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
    }

    public function getSalt(): ?string
    {
        return null; // Not needed when using bcrypt or argon2i
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the PITA role of the user.
     *
     * @return string the PITA role of the user
     */
    // This method is not part of the UserInterface but is useful for your application
    // to get the user's PITA role directly from the GenesisUserAccess entity.
    public function getPitaRole(): string
    {
        return $this->genesisUserAccess->getPitaRole();
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the user ID of the user.
     *
     * @return int the user ID of the user
     */
    public function getId(): int
    {
        return $this->genesisUserAccess->getUserId();
    }

    public function getAccount(): mixed
    {
        return $this->genesisUserAccess->getAccount();
    }

    public function getLastName(): mixed
    {
        return $this->genesisUserAccess->getLastName();
    }

    public function getFirstName(): mixed
    {
        return $this->genesisUserAccess->getFirstName();
    }

    public function getFullName(): mixed
    {
        return $this->genesisUserAccess->getFullName();
    }

    public function getUserId(): int
    {
        return $this->genesisUserAccess->getUserId();
    }

    /**
     * Check if the user's OAuth token is expired
     */
    public function isTokenExpired(): bool
    {
        try {
            $token = \Sparefoot\MyFootService\Service\UserOauth::getToken();
            return !$token || $token->getExpires() <= time();
        } catch (\Exception $e) {
            return true; // Assume expired on error
        }
    }

    /**
     * Check if the user's token needs renewal
     */
    public function needsTokenRenewal(): bool
    {
        try {
            return \Sparefoot\MyFootService\Service\UserOauth::needsRenew();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Attempt to renew the user's OAuth token
     */
    public function renewToken(): bool
    {
        try {
            \Sparefoot\MyFootService\Service\UserOauth::renew();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if this user was authenticated via Remember Me
     */
    public function isRememberMeUser(): bool
    {
        $request = \Sparefoot\MyFootService\Service\RequestContextService::getRequest();
        return $request && $request->attributes->get('_remember_me_token') !== null;
    }

    /**
     * Get the Remember Me expiration time
     */
    public function getRememberMeExpiration(): ?\DateTime
    {
        if (!$this->isRememberMeUser()) {
            return null;
        }

        // Calculate remember me expiration based on config (2 weeks)
        $lifetime = 1209600; // 2 weeks from security.yaml
        return new \DateTime('@' . (time() + $lifetime));
    }

    /**
     * Get OAuth token expiration as DateTime object
     */
    public function getTokenExpiration(): ?\DateTime
    {
        try {
            return \Sparefoot\MyFootService\Service\UserOauth::getTokenExpiration();
        } catch (\Exception $e) {
            return null;
        }
    }
}
