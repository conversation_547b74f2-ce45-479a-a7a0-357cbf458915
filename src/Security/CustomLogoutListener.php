<?php

namespace Sparefoot\MyFootService\Security;

use Psr\Log\LoggerInterface;
use Sparefoot\MyFootService\Service\RequestContextService;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Http\Event\LogoutEvent;

class CustomLogoutListener implements EventSubscriberInterface
{
    private $logger;
    private $session;

    public function __construct(LoggerInterface $logger, SessionInterface $session)
    {
        $this->logger = $logger;
        $this->session = $session;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            LogoutEvent::class => 'onLogout',
        ];
    }

    public function onLogout(LogoutEvent $event): void
    {
        $request = $event->getRequest();
        $user = $event->getToken() ? $event->getToken()->getUserIdentifier() : 'unknown';

        // Log BEFORE the potentially problematic operations
        $this->logger->info('User logged out', [
            'user' => $user,
            'timestamp' => (new \DateTime())->format('Y-m-d H:i:s'),
            'ip' => $request->getClientIp(),
        ]);

        try {
            RequestContextService::setRequest($request);
            User::logout();
            
            // Clear extended token lifetime flags
            $this->clearExtendedTokenFlags();
            
        } catch (\Exception $e) {
            $this->logger->error('Error during logout process', [
                'user' => $user,
                'error' => $e->getMessage(),
            ]);
        }

        // Clear session data related to tokens
        $this->session->remove('oauth_token_id');
        $this->session->remove('oauth_token_expires');

        // The response is already set by the logout configuration (target: login_index)
        // No need to set a response here unless you want to override it
    }
    
    private function clearExtendedTokenFlags(): void
    {
        try {
            $cabinet = \Genesis_Service_Cabinet::get();
            $cabinet->setMeta('extended_token_lifetime', false);
            \Genesis_Service_Cabinet::save($cabinet);
        } catch (\Exception $e) {
            $this->logger->warning('Failed to clear extended token flags', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
