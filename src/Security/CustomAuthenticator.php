<?php

namespace Sparefoot\MyFootService\Security;

use Sparefoot\MyFootService\Service\User as ServiceUser;
use Sparefoot\MyFootService\Service\UserAccountFacilityContext;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\UserRedirect;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;

class CustomAuthenticator extends AbstractAuthenticator
{
    private UrlGeneratorInterface $urlGenerator;
    private $csrfTokenManager;
    private $request;

    public function __construct(
        CsrfTokenManagerInterface $csrfTokenManager,
        UrlGeneratorInterface $urlGenerator,
    ) {
        $this->csrfTokenManager = $csrfTokenManager;
        $this->urlGenerator = $urlGenerator;
    }

    public function supports(Request $request): ?bool
    {
        return $request->attributes->get('_route') === 'login_check' && $request->isMethod('POST');
    }

    public function authenticate(Request $request): Passport
    {
        // if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'local') {
        //     // In local environment, use the fake local authentication

        //     return $this->authenticateFakeLocal($request);
        // }
        $this->request = $request;
        $csrfToken = $request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('authenticate', $csrfToken))) {
            throw new AuthenticationException('Invalid CSRF token.');
        }

        $email = $request->get('_username');
        $password = $request->get('_password');
        if (empty($email)) {
            throw new AuthenticationException('Email cannot be empty');
        }
        // $adapter = new \Genesis_Util_AuthAdapter($email, $password);

        // $genesisUser = $adapter->authenticate(\Genesis_Entity_UserAccess::APP_MYFOOT);

        // if (!$genesisUser) {
        //     throw new AuthenticationException('Invalid credentials');
        // }

        $this->processLoginAction();

        // $request->getSession()->set(Security::LAST_USERNAME, $genesisUser->getEmail());
        $request->getSession()->set(Security::LAST_USERNAME, $email);

        $badges = [];
        if ($request->request->get('_remember_me')) {
            $badges[] = new RememberMeBadge();
        }

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            $badges
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $user = $token->getUser();
        if (!$user instanceof User) {
            throw new \LogicException('The user must be an instance of Sparefoot\MyFootService\Security\User.');
        }

        // Handle post-login actions
        $this->handlePostLoginActions();
        
        // Handle Remember Me specific OAuth behavior
        if ($this->isRememberMeAuthentication($request)) {
            $this->extendOAuthTokenLifetime();
        }

        // Initialize the user account facility context after successful authentication
        // This is when the session is stable and won't be regenerated
        UserAccountFacilityContext::init($user->getId());
        
        // Bind token to session for security
        $this->bindTokenToSession($request);

        // $this->request->getSession()->set('facilityId', ServiceUser::getSession()->get('facilityId'));

        // Check if we need to redirect to a previously attempted page
        if (UserRedirect::needsRedirect($request)) {
            $redirectUrl = UserRedirect::getRedirect();
            UserRedirect::clearRedirect();

            return new RedirectResponse($redirectUrl);
        }

        // Default redirect to dashboard
        return new RedirectResponse($this->urlGenerator->generate('dashboard_index'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $request->getSession()->set(Security::AUTHENTICATION_ERROR, $exception);

        return new RedirectResponse($this->urlGenerator->generate('login_index'));
    }

    private function getParam($name, $default = null)
    {
        return $this->request->get($name, $default);
    }

    /**
     * Process user login
     * called from login form
     * This method is migrated from the old login controller.
     */
    private function processLoginAction()
    {
        $userAccess = false;
        try {
            \Genesis_Db_Connection::getInstance();
            UserOauth::authenticate($this->getParam('_username'), $this->getParam('_password'));
            $userAccess = UserOauth::getUserAccess();
            
            // Check if token needs renewal and handle it
            if ($userAccess && UserOauth::needsRenew()) {
                try {
                    UserOauth::renew();
                } catch (\Exception $e) {
                    // Log renewal failure but don't block login for fresh auth
                    \Genesis_Util_Logger::log('Token Renewal Warning', 
                        'Failed to renew token during login: ' . $e->getMessage(), 
                        __FILE__, __LINE__, \Genesis_Util_Logger::ERR_LEVEL_WARN);
                }
            }
        } catch (\Exception $e) {
            $userAccess = false;
            $error = 'Incorrect e-mail address or password. Please re-enter your credentials.';
            if (stripos($e->getMessage(), 'Invalid login') === false) {
                $error = $e->getMessage();
            }
            throw new AuthenticationException($error);
        }

        if (!$userAccess) {
            $ua = \Genesis_Service_UserAccess::loadByEmail($this->getParam('_username'));
            if ($ua && $ua->getAccount() && !$ua->getMyfootRole()) {
                $error = 'LEGACY Directions to reset your password have been emailed to you. If you do not receive an email, please contact our Support team at 855-427-8193 for further assistance.';
            } elseif ($this->getParam('_username') || $this->getParam('_password')) {
                $error = 'LEGACY Incorrect e-mail address or password. Please re-enter your credentials.';
            } else {
                $error = 'LEGACY Please enter your e-mail address and password.';
            }
            throw new AuthenticationException($error);
        }

        // we should init the session here
        $this->request->getSession()->set(Security::LAST_USERNAME, $this->getParam('_username'));
    }

    /**
     * Handle post-login actions after successful authentication.
     */
    private function handlePostLoginActions()
    {
        $userAccess = ServiceUser::getUserAccess();
        if (!$userAccess) {
            return;
        }

        \Genesis_Service_UserAccess::updateLastLoggedIn($userAccess);

        if ($userAccess->isMyFootGod()) {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }
    }
    
    private function isRememberMeAuthentication(Request $request): bool
    {
        return $request->request->get('_remember_me') === 'on';
    }
    
    private function extendOAuthTokenLifetime(): void
    {
        try {
            // Ensure OAuth tokens have extended lifetime for remember me users
            if (UserOauth::needsRenew()) {
                UserOauth::renew();
            }
            
            // Set a flag in Cabinet to indicate extended token lifetime
            $cabinet = \Genesis_Service_Cabinet::get();
            $cabinet->setMeta('extended_token_lifetime', true);
            \Genesis_Service_Cabinet::save($cabinet);
        } catch (\Exception $e) {
            // Log but don't fail authentication
            \Genesis_Util_Logger::log('Remember Me OAuth Extension Failed', 
                $e->getMessage(), __FILE__, __LINE__, \Genesis_Util_Logger::ERR_LEVEL_WARN);
        }
    }
    
    private function bindTokenToSession(Request $request): void
    {
        try {
            $oauthToken = UserOauth::getToken();
            if ($oauthToken) {
                $request->getSession()->set('oauth_token_id', $oauthToken->getIdentifier());
                $request->getSession()->set('oauth_token_expires', $oauthToken->getExpires());
            }
        } catch (\Exception $e) {
            // Log but don't fail authentication
            \Genesis_Util_Logger::log('Token Session Binding Failed', 
                $e->getMessage(), __FILE__, __LINE__, \Genesis_Util_Logger::ERR_LEVEL_WARN);
        }
    }
}
