<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    private $genesisUserAccess;
    private $email;
    private $roles = [];
    private $password;

    public function __construct(\Genesis_Entity_UserAccess $genesisUserAccess)
    {
        $this->genesisUserAccess = $genesisUserAccess;
        $this->email = $genesisUserAccess->getEmail();

        // Set roles based on the user's MyFoot role
        $myfootRole = $this->genesisUserAccess->getMyfootRole();
        // $roles = ['ROLE_USER']; // Default role for all authenticated users
        $roles = [];
        switch ($myfootRole) {
            case \Genesis_Entity_UserAccess::ROLE_GOD:
                $roles[] = 'ROLE_GOD';
                break;
            case \Genesis_Entity_UserAccess::ROLE_ADMIN:
                $roles[] = 'ROLE_ADMIN';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FULL:
                $roles[] = 'ROLE_FULL';
                break;
            case \Genesis_Entity_UserAccess::ROLE_LIMITED:
                $roles[] = 'ROLE_LIMITED';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FACILITYEDITOR:
                $roles[] = 'ROLE_FACILITYEDITOR';
                break;
            case \Genesis_Entity_UserAccess::ROLE_SEARCHANALYST:
                $roles[] = 'ROLE_SEARCHANALYST';
                break;
            case \Genesis_Entity_UserAccess::ROLE_GEOPAGE:
                $roles[] = 'ROLE_GEOPAGE';
                break;
            default:
                $roles[] = $myfootRole;
                break;
        }
        $this->roles = array_unique($roles);
        $this->password = $genesisUserAccess->getPassword();
    }

    public function getGenesisUserAccess(): \Genesis_Entity_UserAccess
    {
        return $this->genesisUserAccess;
    }

    public function setGenesisUserAccess(\Genesis_Entity_UserAccess $genesisUserAccess): self
    {
        $this->genesisUserAccess = $genesisUserAccess;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getRoles(): array
    {
        $roles = $this->roles;

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
    }

    public function getSalt(): ?string
    {
        return null; // Not needed when using bcrypt or argon2i
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the PITA role of the user.
     *
     * @return string the PITA role of the user
     */
    // This method is not part of the UserInterface but is useful for your application
    // to get the user's PITA role directly from the GenesisUserAccess entity.
    public function getPitaRole(): string
    {
        return $this->genesisUserAccess->getPitaRole();
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the user ID of the user.
     *
     * @return int the user ID of the user
     */
    public function getId(): int
    {
        return $this->genesisUserAccess->getUserId();
    }

    public function getAccount(): mixed
    {
        return $this->genesisUserAccess->getAccount();
    }

    public function getLastName(): mixed
    {
        return $this->genesisUserAccess->getLastName();
    }

    public function getFirstName(): mixed
    {
        return $this->genesisUserAccess->getFirstName();
    }

    public function getFullName(): mixed
    {
        return $this->genesisUserAccess->getFullName();
    }

    public function getUserId(): int\n    {\n        return $this->genesisUserAccess->getUserId();\n    }\n    \n    /**\n     * Check if the user's OAuth token is expired\n     */\n    public function isTokenExpired(): bool\n    {\n        try {\n            $token = \\Sparefoot\\MyFootService\\Service\\UserOauth::getToken();\n            return !$token || $token->getExpires() <= time();\n        } catch (\\Exception $e) {\n            return true; // Assume expired on error\n        }\n    }\n    \n    /**\n     * Check if the user's token needs renewal\n     */\n    public function needsTokenRenewal(): bool\n    {\n        try {\n            return \\Sparefoot\\MyFootService\\Service\\UserOauth::needsRenew();\n        } catch (\\Exception $e) {\n            return false;\n        }\n    }\n    \n    /**\n     * Attempt to renew the user's OAuth token\n     */\n    public function renewToken(): bool\n    {\n        try {\n            \\Sparefoot\\MyFootService\\Service\\UserOauth::renew();\n            return true;\n        } catch (\\Exception $e) {\n            return false;\n        }\n    }\n    \n    /**\n     * Check if this user was authenticated via Remember Me\n     */\n    public function isRememberMeUser(): bool\n    {\n        $request = \\Sparefoot\\MyFootService\\Service\\RequestContextService::getRequest();\n        return $request && $request->attributes->get('_remember_me_token') !== null;\n    }\n    \n    /**\n     * Get the Remember Me expiration time\n     */\n    public function getRememberMeExpiration(): ?\\DateTime\n    {\n        if (!$this->isRememberMeUser()) {\n            return null;\n        }\n        \n        // Calculate remember me expiration based on config (2 weeks)\n        $lifetime = 1209600; // 2 weeks from security.yaml\n        return new \\DateTime('@' . (time() + $lifetime));\n    }\n    \n    /**\n     * Get OAuth token expiration as DateTime object\n     */\n    public function getTokenExpiration(): ?\\DateTime\n    {\n        try {\n            return \\Sparefoot\\MyFootService\\Service\\UserOauth::getTokenExpiration();\n        } catch (\\Exception $e) {\n            return null;\n        }\n    }\n}
}
