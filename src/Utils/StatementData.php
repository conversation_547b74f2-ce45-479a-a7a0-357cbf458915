<?php

namespace Sparefoot\MyFootService\Utils;

class StatementDataUtil
{
    public const AGENT_EMAIL = '<EMAIL>';

    public static function buildTenantInfo(\Genesis_Entity_Transaction $booking)
    {
        return implode(
            "\n",
            array_merge(
                self::_getTenantNames($booking),
                self::_getTenantEmails($booking),
                self::_getTenantPhones($booking)
            )
        );
    }

    private static function _getTenantNames(\Genesis_Entity_Transaction $booking)
    {
        if (!$booking->getFacilityTenant()) {
            return [];
        }
        // Names
        $tenantInfoNames = array_unique([
            ucwords(trim($booking->getFacilityTenant()->getCustomerLastName())).', '.ucwords(trim($booking->getFacilityTenant()->getCustomerFirstName())),
            ucwords(trim($booking->getFacilityTenant()->getAltLastName())).', '.ucwords(trim($booking->getFacilityTenant()->getAltFirstName())),
        ]);
        if (($key = array_search(', ', $tenantInfoNames)) !== false) {
            unset($tenantInfoNames[$key]);
        }

        return $tenantInfoNames;
    }

    private static function _getTenantEmails($booking)
    {
        if (!$booking->getFacilityTenant()) {
            return [];
        }
        $tenantInfoEmails = array_unique([
            trim(strtolower($booking->getFacilityTenant()->getCustomerEmail())),
            trim(strtolower($booking->getFacilityTenant()->getAltEmail())),
        ]);
        if (($key = array_search('', $tenantInfoEmails)) !== false) {
            unset($tenantInfoEmails[$key]);
        }

        return $tenantInfoEmails;
    }

    private static function _getTenantPhones($booking)
    {
        if (!$booking->getFacilityTenant()) {
            return [];
        }
        $tenantInfoPhones = array_unique([
            \Genesis_Util_Formatter::phoneToString($booking->getFacilityTenant()->getCustomerPhone()),
            \Genesis_Util_Formatter::phoneToString($booking->getFacilityTenant()->getCustomerWorkPhone()),
            \Genesis_Util_Formatter::phoneToString($booking->getFacilityTenant()->getCustomerHomePhone()),
            \Genesis_Util_Formatter::phoneToString($booking->getFacilityTenant()->getCustomerCellPhone()),
            \Genesis_Util_Formatter::phoneToString($booking->getFacilityTenant()->getAltPhone()),
        ]);
        if (($key = array_search('', $tenantInfoPhones)) !== false) {
            unset($tenantInfoPhones[$key]);
        }

        return $tenantInfoPhones;
    }

    public static function buildCustomerInfo(\Genesis_Entity_Transaction $booking)
    {
        return implode(
            "\n",
            self::_getCustomerInfo($booking)
        );
    }

    private static function _customerEmail($booking)
    {
        $user = $booking->getUser();

        if ($user) {
            $customerEmail = $user->getEmail();
        } else {
            throw new \Exception('Could not load user for booking '.$booking->getConfirmationCode());
        }

        $email = strtolower(trim($customerEmail));

        if ($email === self::AGENT_EMAIL) {
            return 'No email address provided';
        } else {
            return $email;
        }
    }

    private static function _getCustomerInfo(\Genesis_Entity_Transaction $booking)
    {
        $customerInfo = [];
        $txnMeta = $booking->getBookingMeta(true);

        // Names
        $customerInfoNames = [];
        $customerInfoNames[] = trim(ucwords($booking->getLastName())).', '.trim(ucwords($booking->getFirstName()));
        foreach ($booking->getAlternateNamesMeta(true) as $names) {
            $firstName = '';
            if (isset($names['first']) && !empty($names['first'])) {
                $firstName = strval($names['first']->getValue());
            }

            $lastName = '';
            if (isset($names['last']) && !empty($names['last'])) {
                $lastName = strval($names['last']->getValue());
            }

            $trimmedName = trim($firstName);
            if (!empty($trimmedName)) {
                $customerInfoNames[] = trim(ucwords($lastName)).', '.trim(ucwords($firstName));
            }
        }

        foreach (array_unique($customerInfoNames) as $item) {
            array_push($customerInfo, $item);
        }

        // Emails
        $hasValidEmail = false;
        $customerInfoEmails = [];
        $customerInfoEmails[] = self::_customerEmail($booking);
        if (array_key_exists('alternate_email', $txnMeta)) {
            foreach ($txnMeta['alternate_email'] as $email) {
                $customerInfoEmails[] = $email->getValue();
            }
        }

        foreach (array_unique($customerInfoEmails) as $item) {
            // skip empty emails or where <NAME_EMAIL>
            if ($item === self::AGENT_EMAIL || $item === 'No email address provided' || empty($item)) {
                continue;
            }
            array_push($customerInfo, $item);
            $hasValidEmail = true;
        }

        if (!$hasValidEmail) {
            array_push($customerInfo, 'No email address provided');
        }

        // Phones
        $customerInfoPhones = [];
        $customerInfoPhones[] = $booking->stringPhone();
        if (array_key_exists('alternate_phone', $txnMeta)) {
            foreach ($txnMeta['alternate_phone'] as $phone) {
                $customerInfoPhones[] = \Genesis_Util_Formatter::phoneToString($phone->getValue());
            }
        }

        foreach (array_unique($customerInfoPhones) as $item) {
            array_push($customerInfo, $item);
        }

        return $customerInfo;
    }

    public static function sortItems($bookings)
    {
        // NOTE: This function was created based on the output generated by `Genesis_Util_Sort::sortObjectsByProperties` used
        // inside `Genesis_Entity_Statement_Client::sortItems`
        usort($bookings, function (\Genesis_Entity_Transaction $a, \Genesis_Entity_Transaction $b) {
            $aIsAutoConfirmed = $a->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED;
            $bIsAutoConfirmed = $b->getAutoState() === \Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED;

            $aFacilityName = $a->getFacility()->getTitleWithCompanyCode();
            $bFacilityName = $b->getFacility()->getTitleWithCompanyCode();

            $aCustomerFirstName = ucwords(trim($a->getFirstName()));
            $bCustomerFirstName = ucwords(trim($b->getFirstName()));

            $aCustomerLastName = ucwords(trim($a->getLastName()));
            $bCustomerLastName = ucwords(trim($b->getLastName()));

            return
                strcasecmp($aIsAutoConfirmed, $bIsAutoConfirmed) != 0 ?
                strcasecmp($aIsAutoConfirmed, $bIsAutoConfirmed) : (
                    strcasecmp($aFacilityName, $bFacilityName) != 0 ?
                    strcasecmp($aFacilityName, $bFacilityName) : (
                        strcasecmp($aCustomerLastName, $bCustomerLastName) != 0 ?
                        strcasecmp($aCustomerLastName, $bCustomerLastName) : (
                            strcasecmp($aCustomerFirstName, $bCustomerFirstName) != 0 ?
                            strcasecmp($aCustomerFirstName, $bCustomerFirstName) : (0)
                        )
                    )
                )
            ;
        });

        return $bookings;
    }
}
