<?php

namespace Sparefoot\MyFootService\EventListener;

use Psr\Log\LoggerInterface;
use Sparefoot\MyFootService\Controller\AbstractView\GenericView;
use Sparefoot\MyFootService\Service\GenesisService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\InsufficientAuthenticationException;
use Twig\Environment;

class ExceptionListener
{
    private $twig;
    private $urlGenerator;
    private $kernel;
    private $logger;
    private $tokenStorage;
    private $genesisService;

    public function __construct(
        Environment $twig,
        UrlGeneratorInterface $urlGenerator,
        HttpKernelInterface $kernel,
        LoggerInterface $logger,
        TokenStorageInterface $tokenStorage,
        GenesisService $genesisService,
    ) {
        $this->twig = $twig;
        $this->urlGenerator = $urlGenerator;
        $this->kernel = $kernel;
        $this->logger = $logger;
        $this->tokenStorage = $tokenStorage;
        $this->genesisService = $genesisService;
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();
        $request = $event->getRequest();

        // Handle authentication and authorization exceptions
        if ($exception->getPrevious() instanceof AccessDeniedException
            || $exception->getPrevious() instanceof AuthenticationException
            || $exception->getPrevious() instanceof InsufficientAuthenticationException
        ) {
            // Get the current token (user) from token storage
            $token = $this->tokenStorage->getToken();
            $user = $token ? $token->getUser() : null;

            // If user is not authenticated at all, redirect to login
            if ($user === null || $user === 'anon.') {
                $loginUrl = $this->urlGenerator->generate('login_index');
                $response = new RedirectResponse($loginUrl);
                $event->setResponse($response);

                return;
            }

            // If user is authenticated but has insufficient authentication (partial authentication),
            // continue showing the error instead of redirecting
            if ($exception instanceof InsufficientAuthenticationException) {
                // Let the error be shown - user is logged in but doesn't have full authentication
                // Fall through to show error page
            } else {
                // For other access denied cases where user is authenticated,
                // also show the error page instead of redirecting
                // Fall through to show error page
            }
        }

        $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
        $view = new GenericView();
        $view->exception = $exception;
        $view->message = $exception->getMessage();
        $view->code = $exception->getCode();
        $view->exceptionClassName = get_class($exception);
        $view->envClass = getenv('SF_ENV');
        $response = new Response(
            $this->twig->render('error/error.html.twig', [
                'exception' => $exception,
                'statusCode' => $statusCode,
                'message' => $exception->getMessage() ?: 'An unknown error occurred',
                'view' => $view,
            ]),
            $statusCode
        );
        $event->setResponse($response);
    }
}
