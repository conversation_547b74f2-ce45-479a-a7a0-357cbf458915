<?php

namespace Sparefoot\MyFootService\EventListener;

use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\RequestContextService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Psr\Log\LoggerInterface;

/**
 * Token validation listener that runs on each request to ensure OAuth token consistency
 */
class TokenValidationListener implements EventSubscriberInterface
{
    private UrlGeneratorInterface $urlGenerator;
    private LoggerInterface $logger;

    public function __construct(UrlGeneratorInterface $urlGenerator, LoggerInterface $logger)
    {
        $this->urlGenerator = $urlGenerator;
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['onKernelRequest', 10],
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $session = $request->getSession();

        // Skip validation for public routes
        $route = $request->attributes->get('_route');
        if ($this->isPublicRoute($route)) {
            return;
        }

        // Set request context for legacy code
        RequestContextService::setRequest($request);

        // Check if user is authenticated and token is bound to session
        if ($session->has('oauth_token_id')) {
            try {
                $currentToken = UserOauth::getToken();
                $sessionTokenId = $session->get('oauth_token_id');
                $sessionTokenExpires = $session->get('oauth_token_expires');

                // Validate token consistency
                if (!UserOauth::validateTokenConsistency($sessionTokenId, $sessionTokenExpires)) {
                    $this->logger->warning('Token validation failed - forcing logout', [
                        'session_token_id' => $sessionTokenId,
                        'current_token_valid' => $currentToken !== false,
                        'ip' => $request->getClientIp(),
                    ]);

                    // Token mismatch or expired - force logout
                    $session->invalidate();
                    $event->setResponse(new RedirectResponse($this->urlGenerator->generate('login_index')));
                    return;
                }

                // Check if token needs renewal and attempt it
                if (UserOauth::needsRenew()) {
                    try {
                        UserOauth::renew();
                        
                        // Update session with new token info
                        $renewedToken = UserOauth::getToken();
                        if ($renewedToken) {
                            $session->set('oauth_token_id', $renewedToken->getIdentifier());
                            $session->set('oauth_token_expires', $renewedToken->getExpires());
                        }
                        
                        $this->logger->info('OAuth token renewed successfully');
                    } catch (\Exception $e) {
                        $this->logger->warning('OAuth token renewal failed', [
                            'error' => $e->getMessage(),
                            'user_session' => $session->getId(),
                        ]);
                        // Don't force logout on renewal failure - token might still be valid
                    }
                }
            } catch (\Exception $e) {
                $this->logger->error('Token validation error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
    }

    private function isPublicRoute(?string $route): bool
    {
        $publicRoutes = [
            'login_index',
            'login_check',
            'login_logout',
            'signup-start',
            'ping',
        ];

        return in_array($route, $publicRoutes) || str_starts_with($route ?? '', 'public_');
    }
}