<?php

namespace Sparefoot\MyFootService\Flot;

class SearchPositionLine extends FlotAbstract
{
    /**
     * @var \Genesis_Entity_Facility
     */
    private $_facility;
    private $_startDate;
    private $_endDate;

    public function __construct($id, \Genesis_Entity_Facility $facility, $startDate, $endDate)
    {
        parent::__construct($id);
        $this->_facility = $facility;
        $this->_startDate = $startDate;
        $this->_endDate = $endDate;
    }

    public function render()
    {
        $view = $this->getViewData();
        $view->data = $this->_getDailyAveragePosition();

        return $this->renderView('flot/search_position_line.html.twig', $view);
    }

    private function _getDailyAveragePosition()
    {
        $data = \Genesis_Service_Reporting::getDailyImpressionsByFacility($this->_facility->getId(), $this->_startDate, $this->_endDate);

        $retData = [];

        if (is_array($data)) {
            foreach ($data as $date => $row) {
                if ($row['avg_position'] > 0) {
                    $retData[$date] = $row['avg_position'];
                }
            }
        }

        return $retData;
    }
}
