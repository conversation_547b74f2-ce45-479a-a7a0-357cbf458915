<?php

namespace Sparefoot\MyFootService\Flot;

class MtdConversionsLine extends FlotAbstract
{
    private $accountId;

    public function __construct($id, $accountId)
    {
        parent::__construct($id);
        $this->accountId = $accountId;
    }

    public function render()
    {
        return $this->renderView('flot/mtd_conversions_line.html.twig');
    }

    public function getjson()
    {
        return $this->_getData();
    }

    private function _getData()
    {
        $sql = <<<SQL

SELECT date,
num_bookings / num_visits AS conversion_rate,
rolling_bookings / rolling_visits AS rolling_conversion_rate,
rolling_week_bookings / rolling_week_visits AS rolling_week_conversion_rate

FROM

(
SELECT
        DATE(visit_time) AS date,
	(SELECT COUNT(1) AS num_visits FROM external_visits WHERE visit_time BETWEEN DATE(DATE_SUB(date, INTERVAL 30 DAY)) AND date AND ip_address NOT IN (SELECT ip_address FROM filtered_ips) AND account_id =:account_id) AS rolling_visits,
        (SELECT COUNT(1) AS num_visits FROM external_visits WHERE visit_time BETWEEN DATE(DATE_SUB(date, INTERVAL 7 DAY)) AND DATE_ADD(date, INTERVAL 1 DAY) AND ip_address NOT IN (SELECT ip_address FROM filtered_ips) AND account_id =:account_id) AS rolling_week_visits,
        COUNT(1) AS num_visits
FROM
        external_visits
WHERE
        visit_time BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()
AND
        ip_address NOT IN (SELECT ip_address FROM filtered_ips)
AND
        account_id =:account_id
GROUP BY 1
) AS visits


INNER JOIN

(
SELECT
        DATE(timestamp) AS date,
	(SELECT COUNT(1) AS num_bookings FROM listing_rent_submission JOIN listing_avail USING (listing_avail_id) INNER JOIN corporations ON (listing_avail.corporation_id = corporations.corporation_id) WHERE timestamp BETWEEN DATE_SUB(date, INTERVAL 30 DAY) AND date AND booking_state IN ('PENDING', 'CONFIRMED') AND booking_widget = 1 AND corporations.account_id =:account_id) AS rolling_bookings,
        (SELECT COUNT(1) AS num_bookings FROM listing_rent_submission JOIN listing_avail USING (listing_avail_id) INNER JOIN corporations ON (listing_avail.corporation_id = corporations.corporation_id) WHERE timestamp BETWEEN DATE_SUB(date, INTERVAL 7 DAY) AND DATE_ADD(date, INTERVAL 1 DAY) AND booking_state IN ('PENDING', 'CONFIRMED') AND booking_widget = 1 AND corporations.account_id =:account_id) AS rolling_week_bookings,
        COUNT(1) AS num_bookings
FROM
        listing_rent_submission
JOIN
        listing_avail
USING
        (listing_avail_id)
INNER JOIN
        corporations
ON
        (listing_avail.corporation_id = corporations.corporation_id)
WHERE
        timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()
AND
        booking_state IN ('PENDING', 'CONFIRMED')
AND
        booking_widget = 1
AND
        corporations.account_id =:account_id
GROUP BY 1
) AS bookings

USING (date)

;

SQL;

        $params = ['account_id' => $this->accountId];
        $stmt = \Genesis_Db_Connection::getInstance()->prepare($sql);
        $stmt->execute($params);

        $ret = [];

        while ($r = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $ret[$r['date']]['conversion_rate'] = $r['conversion_rate'];
            $ret[$r['date']]['rolling_conversion_rate'] = $r['rolling_conversion_rate'];
            $ret[$r['date']]['rolling_week_conversion_rate'] = $r['rolling_week_conversion_rate'];
        }

        $arrayRet = [];

        foreach ($ret as $date => $value) {
            foreach ($value as $type => $num) {
                $arrayRet[$type][] = [date('M j', strtotime($date)), $num];
            }
        }

        return $arrayRet;
    }
}
