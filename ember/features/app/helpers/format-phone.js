import Ember from "ember";

/**
 * Handlebars helper to format phone numbers into US standard format (XXX-XXX-XXXX)
 * 
 * Takes a phone number (string or number) and formats it with dashes.
 * Works with 10-digit US phone numbers.
 * 
 * @param {string|number} phone - The phone number to format
 * @returns {string|null} Formatted phone number or null if invalid
 * 
 * Examples:
 *   {{format-phone "6315139916"}} → "************"
 *   {{format-phone 6315139916}} → "************"
 *   {{format-phone "5551234567"}} → "************"
 *   {{format-phone null}} → null
 *   {{format-phone ""}} → null
 * 
 * Template usage:
 *   {{#if model.phone}}{{format-phone model.phone}}{{/if}}
 */
export default Ember.Helper.helper(function(context) {
    if (!context || context.length === 0) {
        return null;
    }
    
    var text = context[0];
    
    if (!text && text !== 0) {
        return null;
    }
    
    try {
        // Convert to string if it's a number (e.g., 6315139916 → "6315139916")
        var phoneString = String(text);
        
        // Regex pattern breakdown:
        // (\d{3}) - Group 1: First 3 digits (area code)
        // (\d{3}) - Group 2: Next 3 digits (exchange)
        // (\d{4}) - Group 3: Last 4 digits (number)
        // Replacement: $1-$2-$3 combines groups with dashes
        // Example: "6315139916" → "************"
        var result = phoneString.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
        return result;
    } catch (error) {
        console.error('format-phone: Error formatting phone:', error);
        return String(text); // Return original value as string if formatting fails
    }
});
