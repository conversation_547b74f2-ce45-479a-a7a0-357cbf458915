import Ember from 'ember';

export default Ember.Controller.extend({
    queryParams: ['fid'], // Facility ID
    fid: null,
    defaultSpecials: {},
    accountSpecials: {}, // list of specials created by account
    newDiscount: null, // used as a placeholder for new discount modal
    newPromo: null, // used as a placeholder for new promo modal
    unitModal: null,
    unitModalUnit: null,
    customClosuresFormOn: CONFIG.featureFlags['myfoot.custom_closures_form'],
    isLoading: false,
    hasError: false,
    errorMessage: '',
    
    isFullService: function() {
        const facility = this.get('model.facility');
        if (!facility) {
            return false;
        }
        return parseInt(facility.get('type')) === 4;
    }.property('model.facility'),
    init: function() {
        var _this = this;
        this.store.findAll('special')
            // grabs all default specials
            .then(function(specials) {
                return _this.set('defaultSpecials', {
                    discounts: specials.filter(function(item) {
                        return item.get('special_type').indexOf('discount_') === 0;
                    }),
                    promos: specials.filter(function(item) {
                        return item.get('special_type').indexOf('promo_') === 0;
                    }),
                    free: specials.filter(function(item) {
                        return item.get('special_type') === 'free_item';
                    })
                });
            })
            // grabs all special belonging to account
            .then(function() {
                _this.store.findAll('account-special').then(function(specials) {
                    _this.set('accountSpecials', {
                        discounts: specials.filter(function(item) { return item.get('special_type').indexOf('discount_') === 0; }),
                        promos: specials.filter(function(item) { return item.get('special_type').indexOf('promo_') === 0; }),
                        free: specials.filter(function(item) { return item.get('special_type') === 'free_item'; })
                    });
                });
            })
            // creates new promo placeholder for form
            .then(function() {
                _this.set('newPromo', _this.store.createRecord('special', {}));
            })
            // creates new discount placeholder for form
            .then(function() {
                _this.set('newDiscount', _this.store.createRecord('special', {}));
            })
            // creates new unit placeholder for form
            .then(function() {
                _this.set('newUnit', _this.store.createRecord('unit', {}));
            });
    },
    actions: {
        resetNewUnit: function() {
            this.set('newUnit', this.store.createRecord('unit', {}));
        }
    }
});
