import DS from 'ember-data';
import Ember from 'ember';

export default DS.RESTAdapter.extend({
    coalesceFindRequests: true,
    namespace: 'api',

    headers: Ember.computed(function() {
        var result;
        // Booking Token is needed for calls from booking routes
        if (App.AUTH_BOOKING_TOKEN) {
            result = {
                'X-AUTH-BOOKING': JSON.stringify(App.AUTH_BOOKING_TOKEN)
            };
        }
        return result;
    }).volatile(),

    findMany(store, type, ids) {
        var newUrl = [];
        var query = {};
        var idStr = ids.join('%2C');
        
        // More robust facility ID extraction
        var facilityIdMatch = window.location.search.match(/fid=([0-9]+)/);
        var facilityId = facilityIdMatch ? facilityIdMatch[1] : null;
        
        if (!facilityId) {
            console.warn('No facility ID found in URL for findMany');
        }

        if (this.urlPrefix()) { newUrl.push(this.urlPrefix()); }
        if (type.modelName) {
            var path = this.pathForType(type.modelName);
            if (path) { newUrl.push(path); }
        }
        newUrl.push(idStr);
        newUrl = '/'+newUrl.join('/');

        switch (type.modelName) {
            case 'unit':
                if (facilityId) {
                    query = { facility_id: facilityId };
                }
                break;
        }

        console.log('FindMany request:', newUrl, query);
        return this.ajax(newUrl, 'GET', { data: query });
    },
    shouldReloadAll() {
        return true;
    }
});
