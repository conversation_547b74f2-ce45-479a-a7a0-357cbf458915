import Ember from 'ember';
import DS from 'ember-data';
import config from '../config/environment';

export default DS.RESTAdapter.extend({
    host: '//reporting.' + App.servicesBaseUrl,
    urlForFindRecord(id) {
        return this.get('host') + '/accounts/'+id;
    },
    findRecord: function(store, type, id, snapshot) {
        var baseURL = this.buildURL(type.modelName, id, snapshot, 'findRecord');
        var adapterOptions = snapshot.adapterOptions;

        var dataParams = {};
        // Set start and end date for calls
        if (adapterOptions && adapterOptions.start_date && adapterOptions.end_date) {
            dataParams.start_date = adapterOptions.start_date;
            dataParams.end_date = adapterOptions.end_date;
        } else {
            var now = moment();
            dataParams.start_date = now.clone().startOf('month').toISOString();
            dataParams.end_date = now.toISOString();
        }

        var options = {
            type: 'GET',
            data: dataParams,
            headers: {
                'Authorization': App.authBearerToken
            }
        };
        console.log('32 AccountOverviewAdapter: findRecord', baseURL, options);
        
        // Determine if we're in local development

        var isLocal = window.location.hostname.includes('localhost');
        
        // Make API calls - use fake local endpoints for development
        var overview, inventory, bookings;
        
        if (isLocal) {
            // Log warning that we're using local fake endpoints
            console.warn('AccountOverviewAdapter: Using local fake endpoints for development - /fakelocalreporting/overview');
            console.log('42 AccountOverviewAdapter: using local fake endpoints /fakelocalreporting/overview');
            overview = $.ajax(_.extend(options, {url: '/fakelocalreporting/overview'}));
            inventory = $.ajax(_.extend(options, {url: '/fakelocalreporting/inventory'}));
            bookings = $.ajax(_.extend(options, {url: '/fakelocalreporting/bookings'}));
        } else {
            overview = $.ajax(_.extend(options, {url: baseURL + '/overview'}));
            inventory = $.ajax(_.extend(options, {url: baseURL + '/inventory'}));
            bookings = $.ajax(_.extend(options, {url: baseURL + '/bookings'}));
        }

        // Wait for ALL responses to resolve
        return Ember.RSVP.all([overview, inventory, bookings]);
    }
});
