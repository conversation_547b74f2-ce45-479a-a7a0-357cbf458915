import Ember from 'ember';
import GAMixin from '../mixins/google-analytics-tracking-mixin';
import BufferedProxy from 'ember-buffered-proxy/proxy';

export default Ember.Component.extend(GAMixin, {
    classNames: 'facility-summary ui basic segment',
    isEditing: false,
    
    init: function() {
        this._super.apply(this, arguments);
        if (!this.get('model')) {
            console.warn('Facility-summary component initialized without a model');
            // Set a flag to prevent rendering issues
            this.set('hasValidModel', false);
        } else {
            this.set('hasValidModel', true);
        }
    },
    
    isEditingChanged: function() {
        // Attach Plugins
        Ember.run.scheduleOnce('afterRender', this, this.attachPlugins);
    }.observes('isEditing'),

    isAltAddressChanged: function() {
        if (this.get('has_alt_address') === false) {
            this.bufferAlternateAddress.set('content', {});
        }
    }.observes('has_alt_address'),

    resWindowDays: [
        { name:'-- Days', value:'null' },
        { name:'60 Days', value:60 },
        { name:'45 Days', value:45 },
        { name:'30 Days', value:30 },
        { name:'21 Days', value:21 },
        { name:'14 Days', value:14 },
        { name:'13 Days', value:13 },
        { name:'12 Days', value:12 },
        { name:'11 Days', value:11 },
        { name:'10 Days', value:10 },
        { name:'9 Days', value:9 },
        { name:'8 Days', value:8 },
        { name:'7 Days', value:7 },
        { name:'6 Days', value:6 },
        { name:'5 Days', value:5 },
        { name:'4 Days', value:4 },
        { name:'3 Days', value:3 },
        { name:'2 Days', value:2 },
        { name:'1 Day', value:1 }
    ],

    has_alt_address: false,

    createBuffer: function() {
        var model = this.get('model');
        
        // Safety check - don't create buffer if no model
        if (!model) {
            console.warn('Cannot create buffer: no model available');
            return;
        }
        
        var altAddress = model.get('alternate_address');

        this.set('has_alt_address', altAddress ? true : false);

        // BufferedProxy gives the ability to throw away changes while keeping 2-way binding
        this.buffer = BufferedProxy.create({
            content: model
        });

        // Buffer the nested objects
        this.bufferLocation = BufferedProxy.create({
            content: model.get('location') || {}
        });

        this.bufferAlternateAddress = BufferedProxy.create({
            content: altAddress || {}
        });

        this.bufferAmenities = BufferedProxy.create({
            content: model.get('amenities') || {}
        });
    },

    actions: {
        showHideFacilityModal: function(values) {
            var _this = this;
            var desireChangeToHide = !values.value;
            if (desireChangeToHide) {
                var $modal = $('.facility-hide-modal');
                $modal.modal('setting', 'closable', false);
                $modal.modal('setting', 'onDeny', function() {
                    _this.trackEvent('button', 'click', 'Facility Why Hide Cancel', 1);
                    _this.buffer.set('active', true);
                });
                $modal.modal('show');
            }
        },
        activate: function() {
            var model = this.get('model');
            if (!model) {
                console.warn('Cannot activate: no model available');
                return;
            }
            model.set('active', true);
            model.save().then(function() {
                    // Saved!
                })
                .catch(function(error) {
                    model.set('active', false);
                    alert('Error Occurred: ' + error);
                });
        },
        createAltAddress: function() {
            var model = this.get('model');
            if (!model || !model.resetAltAddress) {
                console.warn('Cannot create alt address: no model or resetAltAddress method available');
                return;
            }
            model.resetAltAddress();
        },
        edit: function() {
            if (!this.get('model')) {
                console.warn('Cannot edit: no model available');
                return;
            }
            this.trackEvent('button', 'click', 'Edit Facility', 1);
            this.createBuffer();
            this.set('isEditing', true);
        },
        cancel: function() {
            // Scroll to the top
            $('#wrapper').animate({
                scrollTop: this.$(this.element).position().top
            });

            this.set('isEditing', false);
        },
        save: function() {
            var self = this,
                model = this.get('model'),
                bufferAltAddress = this.bufferAlternateAddress,
                $form = this.$('.facility-info-form'),
                $url = $('[name="url"]', $form);
            
            if ($url.val() && $url.val().indexOf('@') > -1) {
                $form.form('add').prompt('url', 'Invalid URL');
                return false;
            }

            if (!$form.form('validate form')) {
                return false;
            }

            this.trackEvent('button', 'click', 'Save Facility', 1);
            this.set('isSaving', true);

            // Apply all the changes from the buffer
            this.buffer.applyChanges();
            this.bufferLocation.applyChanges();
            this.bufferAmenities.applyChanges();

            // Manual applying buffer alternate address to model if set
            if( ! _.isEmpty(bufferAltAddress.get('address'))) {
                model.set('alternate_address', {
                    address: bufferAltAddress.get('address'),
                    city: bufferAltAddress.get('city'),
                    state: bufferAltAddress.get('state'),
                    zip: bufferAltAddress.get('zip')
                });
            }

            // If cell_phone is null, make sure we record that
            if ( ! this.buffer.get('cell_phone')) {
                model.set('cell_phone', null);
            }

            // Reset alternate address if user says it doesn't have alternate address
            if ( ! this.get('has_alt_address')) {
                model.set('alternate_address', null);
            }

            // Save the model
            let options = {
                adapterOptions: {
                    toSave: ['location', 'alternate_address']
                }
            };
            model.save(options).then(function() {
                // Scroll to the top
                $('#wrapper').animate({
                    scrollTop: self.$(self.element).position().top
                });

                // Toggle edit & reset isSaving flags
                self.set('isEditing', false);
                self.set('isSaving', false);
            }).catch(function(error) {
                self.set('isSaving', false);
                alert('Error Occurred: ' + error);
            });
        },
        saveResWindow: function(resWindowDays) {
            var model = this.get('model');
            if (!model || !model.updateReservationWindowDays) {
                console.warn('Cannot save reservation window: no model or updateReservationWindowDays method available');
                return;
            }
            this.trackEvent('button', 'click', 'Save Facility Res Window', 1);
            model.updateReservationWindowDays(resWindowDays);

            // Save
            let options = {
                adapterOptions: {
                    toSave: ['reservation_window_rules']
                }
            };
            model.save(options);
        }
    },

    attachPlugins: function() {
        // Initialize Semantic controls
        this.$('i.info.icon').popup();
        this.$('.facility-info-form').form({ // validation rules
            title: {
                identifier: 'title',
                rules: [{
                    type: 'empty',
                    prompt: 'Facility name is required'
                }]
            },
            street: {
                identifier: 'street',
                rules: [{
                    type: 'empty',
                    prompt: 'Street is required'
                }]
            },
            city: {
                identifier: 'city',
                rules: [{
                    type: 'empty',
                    prompt: 'City is required'
                }]
            },
            state: {
                identifier: 'state',
                rules: [{
                    type: 'empty',
                    prompt: 'State is required'
                }]
            },
            zip: {
                identifier: 'zip',
                rules: [{
                    type: 'empty',
                    prompt: 'Zipcode is required'
                }]
            }
        }, { // form options
            inline: true
        });
    },

    didInsertElement: function() {
        this.attachPlugins();
    }
});
