import Ember from 'ember';

export default Ember.Route.extend({
    queryParams: {
        fid: {
            refreshModel: true
        }
    },
    model: function(params) {
        // Get fid from query params
        const facilityId = params.fid;
        
        // Ensure we have a facility ID
        if (!facilityId) {
            console.error('No facility ID provided');
            return this.transitionTo('overview');
        }
        
        return Ember.RSVP.hash({
            facility: this.store.findRecord('facility', facilityId),
            units: this.store.query('unit', {facility_id: facilityId})
        }).catch((error) => {
            console.error('Error loading facility data:', error);
            throw error;
        });
    }
});
