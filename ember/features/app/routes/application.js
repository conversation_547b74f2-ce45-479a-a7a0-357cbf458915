import Ember from 'ember';

export default Ember.Route.extend({
    actions: {
        error: function(error, transition) {
            console.error('Application route error:', error);
            // substate implementation when returning `true`
            return true;
        },
        
        loading: function(transition, originRoute) {
            return true; // allows the loading substate to be shown
        }
    }
});
