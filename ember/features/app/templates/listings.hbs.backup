<div class="ui grid stackable">
    <div class="ten wide column main-column">
        {{#unless isFullService}}{{component "setup-progress" model=model.facility}}{{/unless}}
        {{component "facility-summary"
            isFullService=isFullService
            model=model.facility}}
        {{component "units-list"
            accessHourRestrictions=model.facility.hourAccessTypeForUnits
            accountSpecials=accountSpecials
            content=model.facility.units
            defaultSpecials=defaultSpecials
            isFullService=isFullService
            newUnit=newUnit
            resetNewUnit="resetNewUnit"
            units=model.facility.units
            modalShowing=false}}
    </div>

    <div class="six wide column right-column">

        {{#if customClosuresFormOn}}
          {{component "custom-closures"
              facility=model.facility}}
        {{/if}}

        {{component "photos-summary"
            model=model.facility
            photos=model.facility.photos}}
        {{#unless isFullService}}{{component "amenities-summary" model=model.facility}}{{/unless}}
        {{component "hours-summary"
            office_hours=model.facility.office_hours
            access_hours=model.facility.access_hours}}
        {{#unless isFullService}}{{component "bidding-summary"}}{{/unless}}
        {{#if isFullService}}{{component "facility-service-area" model=model.facility}}{{/if}}
    </div>

    {{component "facility-hide-modal" model=model.facility}}
    {{component "units-delete-modal"}}
    {{component "units-discount-modal" special=newDiscount}}
    {{component "units-promo-modal" special=newPromo}}
    {{component "covid19-units-hide-modal" modalShowing=false}}
</div>
