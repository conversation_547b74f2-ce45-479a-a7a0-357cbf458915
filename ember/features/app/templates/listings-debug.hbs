<div class="debug-container" style="border: 2px solid red; padding: 20px; margin: 20px;">
    <h1>DEBUG: Listings Template</h1>
    
    <div class="model-debug">
        <h2>Model Debug Info:</h2>
        <p>Model exists: {{if model "YES" "NO"}}</p>
        <p>Facility exists: {{if model.facility "YES" "NO"}}</p>
        <p>Units exists: {{if model.units "YES" "NO"}}</p>
        <p>IsFullService: {{isFullService}}</p>
        
        {{#if model.facility}}
            <h3>Facility Info:</h3>
            <p>Title: {{model.facility.title}}</p>
            <p>ID: {{model.facility.id}}</p>
            <p>Type: {{model.facility.type}}</p>
            <p>Active: {{model.facility.active}}</p>
        {{/if}}
    </div>

    <div class="basic-test">
        <h2>Basic Component Test:</h2>
        <div style="border: 1px solid blue; padding: 10px; margin: 10px;">
            <h3>Testing facility-summary component:</h3>
            {{component "facility-summary" model=model.facility}}
        </div>
    </div>
</div>