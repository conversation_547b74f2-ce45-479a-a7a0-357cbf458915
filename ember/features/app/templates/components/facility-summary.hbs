{{#if model}}
{{#unless model.active}}
<div class="ui message warning">
    <p>This facility is currently inactive and will not receive reservations on the SpareFoot network.</p>
    <a {{action "activate"}} class="ui primary button compact">Activate Facility</a>
</div>
{{/unless}}

<a href="/facilities/{{model.id}}/viewOn/sfdc" target="_blank" class="ui view-on-sparefoot-link">View on SpareFoot.com <i class="external icon"></i></a>
<h3>Information</h3>

{{#if isEditing}}
<div class="facility-info-form ui form">
    <div class="fields">
        <div class="twelve wide field">
            <label>Facility Name</label>
            {{input type="text" name="title" value=buffer.title placeholder="Facility Name"}}
        </div>
        <div class="four wide field">
            <label>Company Code</label>
            <div class="ui icon input">
                {{input type="text" name="company_code" value=buffer.company_code}}
                <i class="info circle icon"
                    data-position="top right"
                    data-content="If you use a code to reference this facility, you can enter it here and we will surface it on your statements to help you with accounting."></i>
            </div>
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <label>Facility Address</label>
            {{input type="text" name="street" value=bufferLocation.address1 placeholder="Street Address"}}
        </div>
    </div>
    <div class="fields">
        <div class="eight wide field">
            {{input type="text" name="city" value=bufferLocation.city placeholder="City"}}
        </div>
        <div class="four wide field">
            {{input type="text" name="state" value=bufferLocation.state placeholder="State"}}
        </div>
        <div class="four wide field">
            {{input type="text" name="zip" value=bufferLocation.zip placeholder="Zip"}}
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <div class="inline fields">
                <label for="alone">Is there an on-site office at the above address?</label>
                <div class="field">
                    {{sui-radio label="Yes" name="on_site_office" value="true" current=bufferAmenities.onsite_office_at_facility}}
                </div>
                <div class="field">
                    {{sui-radio label="No" name="on_site_office" value="false" current=bufferAmenities.onsite_office_at_facility}}
                </div>
            </div>
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <div class="grouped fields">
                <label for="alone">SpareFoot will occasionally send things to you in the mail. Can you receive mail at the above address?</label>
                <div class="field">
                    {{sui-radio label="Yes" name="has_alt_address" value="false" current=has_alt_address}}
                </div>
                <div class="field">
                    {{sui-radio label="No" name="has_alt_address" value="true" action="createAltAddress" current=has_alt_address}}
                </div>
            </div>
        </div>
    </div>
    {{#if has_alt_address}}
    <div class="ui segment">
        <div class="field">
            <div class="sixteen wide field">
                <label>Alternate Mailing Address</label>
                {{input type="text" value=bufferAlternateAddress.address placeholder="Street Address"}}
            </div>
        </div>
        <div class="fields">
            <div class="eight wide field">
                {{input type="text" value=bufferAlternateAddress.city placeholder="City"}}
            </div>
            <div class="four wide field">
                {{input type="text" value=bufferAlternateAddress.state placeholder="State"}}
            </div>
            <div class="four wide field">
                {{input type="text" value=bufferAlternateAddress.zip placeholder="Zip"}}
            </div>
        </div>
    </div>
    {{/if}}
    <div class="fields">
        <div class="four wide field">
            <label>Phone</label>
            {{input type="text" value=buffer.phone placeholder="************"}}
        </div>
        <div class="four wide field">
            <label>Cell Phone</label>
            <div class="ui icon input">
                {{input type="text" value=buffer.cell_phone placeholder="************"}}
                <i class="info circle icon"
                    data-content="We'll send a text message when you get a new reservation! The message will include the customer's phone # so you can follow up with them immediately."></i>
            </div>
        </div>
    </div>
    <div class="field">
        <div class="four wide field">
            <label>Admin Fee</label>
            <div class="ui labeled icon input">
                <div class="ui label">$</div>
                {{input type="text" value=buffer.admin_fee placeholder="Admin Fee"}}
                <i class="info circle icon"
                    data-content="How much you charge a customer on their initial move-in (not including rent fees)."></i>
            </div>
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <label>URL</label>
            {{input name="url" type="text" value=buffer.url placeholder="http://your-facility.com"}}
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <label>Facility Description</label>
            {{textarea value=buffer.description}}
        </div>
    </div>
    <div class="field">
        <div class="sixteen wide field">
            <div class="inline fields">
                <label for="alone">Available for bookings on the SpareFoot network?</label>
                <div class="field">
                    {{sui-radio label="Yes" name="active_onsf" value="true" current=buffer.active}}
                </div>
                <div class="field">
                    {{sui-radio label="No" name="active_onsf" value="false"
                        action="showHideFacilityModal"
                        current=buffer.active}}
                </div>
            </div>
        </div>
    </div>
    <div class="footer-controls">
        <div {{action "save"}} class={{if isSaving 'ui submit primary button loading' 'ui submit primary button'}}>Save</div>
        <div {{action "cancel"}} class="ui basic button">Cancel</div>
    </div>
</div>
{{else}}
<div>
    <div class="ui list">
        {{#if model.location}}
        {{#if model.location.address1}}
        <div class="item address-item">
            <span class="street">{{model.location.address1}}</span>,
            <span class="city">{{model.location.city}}</span>,
            <span class="state">{{model.location.state}}</span>
            <span class="zip">{{model.location.zip}}</span>
            <i class="call icon"></i>
            <span class="phone">{{#if model.phone}}{{format-phone model.phone}}{{/if}}</span>
        </div>
        {{else}}
        <div class="ui message warning">
            Please update your facility's address.
        </div>
        {{/if}}
        {{else}}
        <div class="ui message warning">
            Please update your facility's address.
        </div>
        {{/if}}

        {{#unless isFullService}}
        <div class="item res-item">
            <label>Reservation Window:</label>
            {{select-box
                content=resWindowDays
                selectedValue=model.reservationWindowDays
                action="saveResWindow"}}
            <i class="info circle icon"
                data-content="The maximum number of days you allow for making reservations."></i>
        </div>
        {{/unless}}

        {{#if model.url}}
        <div><a href="{{model.url}}" target="_target">Facility Website</a></div>
        {{/if}}
    </div>
    <a {{action "edit"}} class="ui button primary pull-right compact edit-facility-button">Edit Facility</a>
</div>
{{/if}}
{{else}}
<div class="ui message info">
    <p>No facility information available.</p>
</div>
{{/if}}