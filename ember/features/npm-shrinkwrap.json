{"name": "features", "version": "0.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"abbrev": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "dev": true}, "abort-controller": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dev": true, "requires": {"event-target-shim": "5.0.1"}}, "accepts": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.5.tgz", "integrity": "sha1-63d99gEXI6OxTopywIBcjoZ0a9I=", "dev": true, "requires": {"mime-types": "2.1.21", "negotiator": "0.6.1"}}, "acorn": {"version": "5.7.3", "resolved": "https://registry.npmjs.org/acorn/-/acorn-5.7.3.tgz", "integrity": "sha512-T/zvzYRfbVojPWahDsE5evJdHb3oJoQfFbsrKM7w5Zcs++Tr257tia3BmMP8XYVjp1S9RZXQMh7gao96BlqZOw==", "dev": true}, "after": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/after/-/after-0.8.1.tgz", "integrity": "sha1-q11PuIP1loFtNRX495HAr0ht1ic=", "dev": true}, "ajv": {"version": "6.5.5", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.5.5.tgz", "integrity": "sha512-7q7gtRQDJSyuEHjuVgHoUa2VuemFiCMrfQc9Tc08XTAc4Zj/5U1buQJ0HU6i7fKjXU09SVgSmxa4sLvuvS8Iyg==", "dev": true, "optional": true, "requires": {"fast-deep-equal": "2.0.1", "fast-json-stable-stringify": "2.0.0", "json-schema-traverse": "0.4.1", "uri-js": "4.2.2"}}, "align-text": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "dev": true, "requires": {"kind-of": "3.2.2", "longest": "1.0.1", "repeat-string": "1.6.1"}}, "alter": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/alter/-/alter-0.2.0.tgz", "integrity": "sha1-x1iICGF1cgNKrmJICvJrHU0cs80=", "dev": true, "requires": {"stable": "0.1.8"}}, "amd-name-resolver": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/amd-name-resolver/-/amd-name-resolver-0.0.2.tgz", "integrity": "sha1-e+5OESqr7swuFEKcTKdQxV2OXs0=", "dev": true}, "amdefine": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "dev": true}, "ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "ansicolors": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/ansicolors/-/ansicolors-0.2.1.tgz", "integrity": "sha1-vgiVmQl7dKXJxKhKDNvNtivYeu8=", "dev": true}, "anymatch": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "dev": true, "requires": {"micromatch": "2.3.11", "normalize-path": "2.1.1"}}, "aproba": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "dev": true}, "are-we-there-yet": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz", "integrity": "sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w==", "dev": true, "requires": {"delegates": "1.0.0", "readable-stream": "2.3.6"}}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "dev": true, "requires": {"sprintf-js": "1.0.3"}, "dependencies": {"sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}}}, "arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "dev": true, "requires": {"arr-flatten": "1.1.0"}}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "dev": true}, "array-equal": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/array-equal/-/array-equal-1.0.0.tgz", "integrity": "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=", "dev": true}, "array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true}, "array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=", "dev": true}, "arraybuffer.slice": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/arraybuffer.slice/-/arraybuffer.slice-0.0.6.tgz", "integrity": "sha1-8zshWfBTKj8xB6JywMz70a0peco=", "dev": true}, "asn1": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz", "integrity": "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==", "dev": true, "optional": true, "requires": {"safer-buffer": "2.1.2"}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}, "ast-traverse": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ast-traverse/-/ast-traverse-0.1.1.tgz", "integrity": "sha1-ac8rg4bxnc2hux4F1o/jWdiJfeY=", "dev": true}, "ast-types": {"version": "0.9.6", "resolved": "https://registry.npmjs.org/ast-types/-/ast-types-0.9.6.tgz", "integrity": "sha1-ECyenpAF0+fjgpvwxPok7oYu6bk=", "dev": true}, "async": {"version": "2.6.1", "resolved": "https://registry.npmjs.org/async/-/async-2.6.1.tgz", "integrity": "sha512-fNEiL2+AZt6AlAw/29Cr0UDe4sRAHCpEHh54WMz+Bb7QfNcFw4h3loofyJpLeQs4Yx7yuqu/2dLgM5hKOs6HlQ==", "dev": true, "requires": {"lodash": "4.17.11"}}, "async-disk-cache": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/async-disk-cache/-/async-disk-cache-1.3.3.tgz", "integrity": "sha512-GyaWSbDAZCltxSobtj1m1ptXa0+zSdjWs3sM4IqnvhoRwMDHW5786sXQ1RiXbR3ZGuQe6NXMB4N0vUmW163cew==", "dev": true, "requires": {"debug": "2.6.9", "heimdalljs": "0.2.6", "istextorbinary": "2.1.0", "mkdirp": "0.5.1", "rimraf": "2.6.2", "rsvp": "3.6.2", "username-sync": "1.0.1"}}, "async-promise-queue": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/async-promise-queue/-/async-promise-queue-1.0.4.tgz", "integrity": "sha512-GQ5X3DT+TefYuFPHdvIPXFTlKnh39U7dwtl+aUBGeKjMea9nBpv3c91DXgeyBQmY07vQ97f3Sr9XHqkamEameQ==", "dev": true, "requires": {"async": "2.6.1", "debug": "2.6.9"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true, "optional": true}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "dev": true, "optional": true}, "aws4": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.8.0.tgz", "integrity": "sha512-ReZxvNHIOv88FlT7rxcXIIC0fPt4KZqZbOlivyWtXLt8ESx84zd3kMC6iK5jVeS2qt+g7ftS7ye4fi06X5rtRQ==", "dev": true, "optional": true}, "babel-core": {"version": "5.8.38", "resolved": "http://registry.npmjs.org/babel-core/-/babel-core-5.8.38.tgz", "integrity": "sha1-H8ruedfmG3ULALjlT238nQr4ZVg=", "dev": true, "requires": {"babel-plugin-constant-folding": "1.0.1", "babel-plugin-dead-code-elimination": "1.0.2", "babel-plugin-eval": "1.0.1", "babel-plugin-inline-environment-variables": "1.0.1", "babel-plugin-jscript": "1.0.4", "babel-plugin-member-expression-literals": "1.0.1", "babel-plugin-property-literals": "1.0.1", "babel-plugin-proto-to-assign": "1.0.4", "babel-plugin-react-constant-elements": "1.0.3", "babel-plugin-react-display-name": "1.0.3", "babel-plugin-remove-console": "1.0.1", "babel-plugin-remove-debugger": "1.0.1", "babel-plugin-runtime": "1.0.7", "babel-plugin-undeclared-variables-check": "1.0.2", "babel-plugin-undefined-to-void": "1.1.6", "babylon": "5.8.38", "bluebird": "2.11.0", "chalk": "1.1.0", "convert-source-map": "1.6.0", "core-js": "1.2.7", "debug": "2.6.9", "detect-indent": "3.0.1", "esutils": "2.0.2", "fs-readdir-recursive": "0.1.2", "globals": "6.4.1", "home-or-tmp": "1.0.0", "is-integer": "1.0.7", "js-tokens": "1.0.1", "json5": "0.4.0", "lodash": "3.10.1", "minimatch": "2.0.10", "output-file-sync": "1.1.2", "path-exists": "1.0.0", "path-is-absolute": "1.0.1", "private": "0.1.8", "regenerator": "0.8.40", "regexpu": "1.3.0", "repeating": "1.1.3", "resolve": "1.8.1", "shebang-regex": "1.0.0", "slash": "1.0.0", "source-map": "0.5.7", "source-map-support": "0.2.10", "to-fast-properties": "1.0.3", "trim-right": "1.0.1", "try-resolve": "1.0.1"}, "dependencies": {"lodash": {"version": "3.10.1", "resolved": "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}, "minimatch": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha1-jQh8OcazjAAbl/ynzm0OHoCvusc=", "dev": true, "requires": {"brace-expansion": "1.1.11"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-plugin-constant-folding": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-constant-folding/-/babel-plugin-constant-folding-1.0.1.tgz", "integrity": "sha1-g2HTZMmORJw2kr26Ue/whEKQqo4=", "dev": true}, "babel-plugin-dead-code-elimination": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/babel-plugin-dead-code-elimination/-/babel-plugin-dead-code-elimination-1.0.2.tgz", "integrity": "sha1-X3xFEnTc18zNv7s+C4XdKBIfD2U=", "dev": true}, "babel-plugin-eval": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-eval/-/babel-plugin-eval-1.0.1.tgz", "integrity": "sha1-ovrtJc5r5preS/7CY/cBaRlZUNo=", "dev": true}, "babel-plugin-htmlbars-inline-precompile": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/babel-plugin-htmlbars-inline-precompile/-/babel-plugin-htmlbars-inline-precompile-0.0.5.tgz", "integrity": "sha1-YPwqOkU2ZMtSSyGGaJLCEu5j/3A=", "dev": true}, "babel-plugin-inline-environment-variables": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-inline-environment-variables/-/babel-plugin-inline-environment-variables-1.0.1.tgz", "integrity": "sha1-H1jOkSB61qgmqL9kX6/mj/X+P/4=", "dev": true}, "babel-plugin-jscript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/babel-plugin-jscript/-/babel-plugin-jscript-1.0.4.tgz", "integrity": "sha1-jzQsOCduh6R9X6CovT1etsytj8w=", "dev": true}, "babel-plugin-member-expression-literals": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-member-expression-literals/-/babel-plugin-member-expression-literals-1.0.1.tgz", "integrity": "sha1-zF7bD6qNyScXDnTW0cAkQAIWJNM=", "dev": true}, "babel-plugin-property-literals": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-property-literals/-/babel-plugin-property-literals-1.0.1.tgz", "integrity": "sha1-AlIwGQAZKYCxwRjv6kjOk6q4MzY=", "dev": true}, "babel-plugin-proto-to-assign": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/babel-plugin-proto-to-assign/-/babel-plugin-proto-to-assign-1.0.4.tgz", "integrity": "sha1-xJ56/QL1d7xNoF6i3wAiUM980SM=", "dev": true, "requires": {"lodash": "3.10.1"}, "dependencies": {"lodash": {"version": "3.10.1", "resolved": "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}}}, "babel-plugin-react-constant-elements": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/babel-plugin-react-constant-elements/-/babel-plugin-react-constant-elements-1.0.3.tgz", "integrity": "sha1-lGc26DeEKcvDSdz/YvUcFDs041o=", "dev": true}, "babel-plugin-react-display-name": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/babel-plugin-react-display-name/-/babel-plugin-react-display-name-1.0.3.tgz", "integrity": "sha1-dU/jiSboQkpOexWrbqYTne4FFPw=", "dev": true}, "babel-plugin-remove-console": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-remove-console/-/babel-plugin-remove-console-1.0.1.tgz", "integrity": "sha1-2PJFVsOgUAXUKqqv0neH9T/wE6c=", "dev": true}, "babel-plugin-remove-debugger": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-remove-debugger/-/babel-plugin-remove-debugger-1.0.1.tgz", "integrity": "sha1-/S6jzWGkKK0fO5yJiC/0KT6MFMc=", "dev": true}, "babel-plugin-runtime": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/babel-plugin-runtime/-/babel-plugin-runtime-1.0.7.tgz", "integrity": "sha1-v3x9lm3Vbs1cF/ocslPJrLflSq8=", "dev": true}, "babel-plugin-undeclared-variables-check": {"version": "1.0.2", "resolved": "http://registry.npmjs.org/babel-plugin-undeclared-variables-check/-/babel-plugin-undeclared-variables-check-1.0.2.tgz", "integrity": "sha1-XPGqU52BP/ZOmWQSkK9iCWX2Xe4=", "dev": true, "requires": {"leven": "1.0.2"}}, "babel-plugin-undefined-to-void": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/babel-plugin-undefined-to-void/-/babel-plugin-undefined-to-void-1.1.6.tgz", "integrity": "sha1-f1eO+LeN+uYAM4XYQXph7aBuL4E=", "dev": true}, "babylon": {"version": "5.8.38", "resolved": "http://registry.npmjs.org/babylon/-/babylon-5.8.38.tgz", "integrity": "sha1-7JsSCxG/bM1Bc6GL8hfmC3mFn/0=", "dev": true}, "backbone": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/backbone/-/backbone-1.3.3.tgz", "integrity": "sha1-TMgOp8sWMaxHSInOQPL4vGg7KZk=", "dev": true, "requires": {"underscore": "1.9.1"}}, "backo2": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/backo2/-/backo2-1.0.2.tgz", "integrity": "sha1-MasayLEpNjRj41s+u2n038+6eUc=", "dev": true}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "base64-arraybuffer": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz", "integrity": "sha1-c5JncZI7Whl0etZmqlzUv5xunOg=", "dev": true}, "base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true}, "base64id": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/base64id/-/base64id-0.1.0.tgz", "integrity": "sha1-As4P3u4M709ACA4ec+g08LG/zj8=", "dev": true}, "basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "requires": {"safe-buffer": "5.1.2"}}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "dev": true, "optional": true, "requires": {"tweetnacl": "0.14.5"}}, "better-assert": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/better-assert/-/better-assert-1.0.2.tgz", "integrity": "sha1-QIZrnhueC1W0gYlDEeaPr/rrxSI=", "dev": true, "requires": {"callsite": "1.0.0"}}, "binaryextensions": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/binaryextensions/-/binaryextensions-2.1.2.tgz", "integrity": "sha512-xVNN69YGDghOqCCtA6FI7avYrr02mTJjOgB0/f1VPD3pJC8QEvjTKWc4epDx8AqxxA75NI0QpVM2gPJXUbE4Tg==", "dev": true}, "blank-object": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/blank-object/-/blank-object-1.0.2.tgz", "integrity": "sha1-+ZB5P76ajI3QE/syGUIL7IHV9Lk=", "dev": true}, "blob": {"version": "0.0.4", "resolved": "http://registry.npmjs.org/blob/-/blob-0.0.4.tgz", "integrity": "sha1-vPEwUspURj8w+fx+lbmkdjCpSSE=", "dev": true}, "bluebird": {"version": "2.11.0", "resolved": "http://registry.npmjs.org/bluebird/-/bluebird-2.11.0.tgz", "integrity": "sha1-U0uQM8AiyVecVro7Plpcqvu2UOE=", "dev": true}, "body-parser": {"version": "1.18.3", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.3.tgz", "integrity": "sha1-WykhmP/dVTs6DyDe0FkrlWlVyLQ=", "dev": true, "requires": {"bytes": "3.0.0", "content-type": "1.0.4", "debug": "2.6.9", "depd": "1.1.2", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "on-finished": "2.3.0", "qs": "6.5.2", "raw-body": "2.3.3", "type-is": "1.6.16"}, "dependencies": {"iconv-lite": {"version": "0.4.23", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "dev": true, "requires": {"safer-buffer": "2.1.2"}}}}, "bower": {"version": "1.8.8", "resolved": "https://registry.npmjs.org/bower/-/bower-1.8.8.tgz", "integrity": "sha512-1SrJnXnkP9soITHptSO+ahx3QKp3cVzn8poI6ujqc5SeOkg5iqM1pK9H+DSc2OQ8SnO0jC/NG4Ur/UIwy7574A=="}, "bower-config": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/bower-config/-/bower-config-0.6.1.tgz", "integrity": "sha1-cJMVVoi+9EB5v0yzLRiTEsh97WA=", "dev": true, "requires": {"graceful-fs": "2.0.3", "mout": "0.9.1", "optimist": "0.6.1", "osenv": "0.0.3"}}, "bower-endpoint-parser": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/bower-endpoint-parser/-/bower-endpoint-parser-0.2.2.tgz", "integrity": "sha1-ALVlrb+rby01rd3pd+l5Yqy8s/Y=", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "dev": true, "requires": {"expand-range": "1.8.2", "preserve": "0.2.0", "repeat-element": "1.1.3"}}, "breakable": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/breakable/-/breakable-1.0.0.tgz", "integrity": "sha1-eEp5eRWjjq0nutRWtVcstLuqeME=", "dev": true}, "broccoli": {"version": "0.16.8", "resolved": "http://registry.npmjs.org/broccoli/-/broccoli-0.16.8.tgz", "integrity": "sha1-KgD2uCqBBuyc+zgKitpK1JC4NtU=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-slow-trees": "1.1.0", "commander": "2.19.0", "connect": "3.6.6", "copy-dereference": "1.0.0", "findup-sync": "0.2.1", "handlebars": "3.0.3", "mime": "1.6.0", "promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rimraf": "2.6.2", "rsvp": "3.6.2"}, "dependencies": {"broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}}}}, "broccoli-asset-rev": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/broccoli-asset-rev/-/broccoli-asset-rev-2.7.0.tgz", "integrity": "sha512-GZ7gU3Qo6HMAUqDeh1q+4UVCQPJPjCyGcpIY5s9Qp58a244FT4nZSiy8qYkVC4LLIWTZt59G7jFFsUcj/13IPQ==", "dev": true, "requires": {"broccoli-asset-rewrite": "1.1.0", "broccoli-filter": "1.3.0", "broccoli-persistent-filter": "1.4.6", "json-stable-stringify": "1.0.1", "minimatch": "3.0.4", "rsvp": "3.6.2"}}, "broccoli-asset-rewrite": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-asset-rewrite/-/broccoli-asset-rewrite-1.1.0.tgz", "integrity": "sha1-d6XaVhV6oxjFkRMkXouvtGF/iDA=", "dev": true, "requires": {"broccoli-filter": "1.3.0"}}, "broccoli-babel-transpiler": {"version": "5.7.4", "resolved": "http://registry.npmjs.org/broccoli-babel-transpiler/-/broccoli-babel-transpiler-5.7.4.tgz", "integrity": "sha512-gI14Pqc4qbmn5RW4SuAmybLiOoYW59D+HzQyhY6WdaGMAjikKBwJN0p17phyvafQ+kvG0mUiMd83lgHLeATnEA==", "dev": true, "requires": {"babel-core": "5.8.38", "broccoli-funnel": "1.2.0", "broccoli-merge-trees": "1.2.4", "broccoli-persistent-filter": "1.4.6", "clone": "0.2.0", "hash-for-dep": "1.2.3", "heimdalljs-logger": "0.1.10", "json-stable-stringify": "1.0.1", "rsvp": "3.6.2", "workerpool": "2.3.3"}}, "broccoli-caching-writer": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/broccoli-caching-writer/-/broccoli-caching-writer-3.0.3.tgz", "integrity": "sha1-C9LJapc41qarWQ8HujXFFX19tHY=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.3.1", "broccoli-plugin": "1.3.1", "debug": "2.6.9", "rimraf": "2.6.2", "rsvp": "3.6.2", "walk-sync": "0.3.3"}}, "broccoli-clean-css": {"version": "0.2.0", "resolved": "http://registry.npmjs.org/broccoli-clean-css/-/broccoli-clean-css-0.2.0.tgz", "integrity": "sha1-FfHCZaaYZYWpcr+wcL9S6cBUyGE=", "dev": true, "requires": {"broccoli-filter": "0.1.14", "clean-css": "2.2.23"}, "dependencies": {"broccoli-filter": {"version": "0.1.14", "resolved": "https://registry.npmjs.org/broccoli-filter/-/broccoli-filter-0.1.14.tgz", "integrity": "sha1-I8rjiR/567e019sAxtzwNTXa960=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-writer": "0.1.1", "mkdirp": "0.3.5", "promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rsvp": "3.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.1.3"}}, "broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}, "dependencies": {"mkdirp": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}}}, "mkdirp": {"version": "0.3.5", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.3.5.tgz", "integrity": "sha1-3j5fiWHIjHh+4TaN+EmsRBPsqNc=", "dev": true}, "walk-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.1.3.tgz", "integrity": "sha1-igcmGgC9ps+xviXp8QD61XVG9YM=", "dev": true}}}, "broccoli-concat": {"version": "2.3.8", "resolved": "http://registry.npmjs.org/broccoli-concat/-/broccoli-concat-2.3.8.tgz", "integrity": "sha1-WQzcwCG7kFtsEh2HwtHVffRKKkg=", "dev": true, "requires": {"broccoli-caching-writer": "2.3.1", "broccoli-kitchen-sink-helpers": "0.3.1", "broccoli-stew": "1.6.0", "fast-sourcemap-concat": "1.4.0", "fs-extra": "0.30.0", "lodash.merge": "4.6.1", "lodash.omit": "4.5.0", "lodash.uniq": "4.5.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "1.9.3"}}, "broccoli-caching-writer": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/broccoli-caching-writer/-/broccoli-caching-writer-2.3.1.tgz", "integrity": "sha1-uTz1j5Jk8AMHWGjbBXdPTn8lvQc=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-plugin": "1.1.0", "debug": "2.6.9", "rimraf": "2.6.2", "rsvp": "3.6.2", "walk-sync": "0.2.7"}, "dependencies": {"broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}}}}, "broccoli-plugin": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-plugin/-/broccoli-plugin-1.1.0.tgz", "integrity": "sha1-c+LPoF+OoeP8FCDEDD2efcckvwI=", "dev": true, "requires": {"promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "chalk": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "dev": true, "requires": {"ansi-styles": "3.2.1", "escape-string-regexp": "1.0.5", "supports-color": "5.5.0"}}, "fast-sourcemap-concat": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/fast-sourcemap-concat/-/fast-sourcemap-concat-1.4.0.tgz", "integrity": "sha512-x90Wlx/2C83lfyg7h4oguTZN4MyaVfaiUSJQNpU+YEA0Odf9u659Opo44b0LfoVg9G/bOE++GdID/dkyja+XcA==", "dev": true, "requires": {"chalk": "2.4.1", "fs-extra": "5.0.0", "heimdalljs-logger": "0.1.10", "memory-streams": "0.1.3", "mkdirp": "0.5.1", "source-map": "0.4.4", "source-map-url": "0.3.0", "sourcemap-validator": "1.1.0"}, "dependencies": {"fs-extra": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-5.0.0.tgz", "integrity": "sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==", "dev": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "4.0.0", "universalify": "0.1.2"}}}}, "fs-extra": {"version": "0.30.0", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.30.0.tgz", "integrity": "sha1-8jP/zAjU2n1DLapEl3aYnbHfk/A=", "dev": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "2.4.0", "klaw": "1.3.1", "path-is-absolute": "1.0.1", "rimraf": "2.6.2"}, "dependencies": {"jsonfile": {"version": "2.4.0", "resolved": "http://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz", "integrity": "sha1-NzaitCi4e72gzIO1P6PWM6NcKug=", "dev": true, "requires": {"graceful-fs": "4.1.15"}}}}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}, "jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "dev": true, "requires": {"graceful-fs": "4.1.15"}}, "lodash.merge": {"version": "4.6.1", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.1.tgz", "integrity": "sha512-AOYza4+Hf5z1/0Hztxpm2/xiPZgi/cjMqdnKTUWTBSKchJlxXXuUSxCCl8rJlf4g6yww/j6mA8nC8Hw/EZWxKQ==", "dev": true}, "lodash.uniq": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=", "dev": true}, "source-map": {"version": "0.4.4", "resolved": "http://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "requires": {"amdefine": "1.0.1"}}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "3.0.0"}}, "walk-sync": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.2.7.tgz", "integrity": "sha1-tJvk7mhnZXrrc2l4tWop0Q+jmWk=", "dev": true, "requires": {"ensure-posix-path": "1.0.2", "matcher-collection": "1.0.5"}}}}, "broccoli-config-loader": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/broccoli-config-loader/-/broccoli-config-loader-1.0.1.tgz", "integrity": "sha512-MDKYQ50rxhn+g17DYdfzfEM9DjTuSGu42Db37A8TQHQe8geYEcUZ4SQqZRgzdAI3aRQNlA1yBHJfOeGmOjhLIg==", "dev": true, "requires": {"broccoli-caching-writer": "3.0.3"}}, "broccoli-config-replace": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/broccoli-config-replace/-/broccoli-config-replace-1.1.2.tgz", "integrity": "sha1-bqh52SpbrWNNETKbUfxfSq/anAA=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.3.1", "broccoli-plugin": "1.3.1", "debug": "2.6.9", "fs-extra": "0.24.0"}, "dependencies": {"fs-extra": {"version": "0.24.0", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.24.0.tgz", "integrity": "sha1-1OQ0KpZnXLeEZjOmCZJJMytTmVI=", "dev": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "2.4.0", "path-is-absolute": "1.0.1", "rimraf": "2.6.2"}}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}}}, "broccoli-debug": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/broccoli-debug/-/broccoli-debug-0.6.5.tgz", "integrity": "sha512-RIVjHvNar9EMCLDW/FggxFRXqpjhncM/3qq87bn/y+/zR9tqEkHvTqbyOc4QnB97NO2m6342w4wGkemkaeOuWg==", "dev": true, "requires": {"broccoli-plugin": "1.3.1", "fs-tree-diff": "0.5.9", "heimdalljs": "0.2.6", "heimdalljs-logger": "0.1.10", "symlink-or-copy": "1.2.0", "tree-sync": "1.2.2"}}, "broccoli-filter": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/broccoli-filter/-/broccoli-filter-1.3.0.tgz", "integrity": "sha512-VXJXw7eBfG82CFxaBDjYmyN7V72D4In2zwLVQJd/h3mBfF3CMdRTsv2L20lmRTtCv1sAHcB+LgMso90e/KYiLw==", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.3.1", "broccoli-plugin": "1.3.1", "copy-dereference": "1.0.0", "debug": "2.6.9", "mkdirp": "0.5.1", "promise-map-series": "0.2.3", "rsvp": "3.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.3.3"}}, "broccoli-funnel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/broccoli-funnel/-/broccoli-funnel-1.2.0.tgz", "integrity": "sha1-zdw6/F/xaFqAI0iP/3TOb7WlEpY=", "dev": true, "requires": {"array-equal": "1.0.0", "blank-object": "1.0.2", "broccoli-plugin": "1.3.1", "debug": "2.6.9", "exists-sync": "0.0.4", "fast-ordered-set": "1.0.3", "fs-tree-diff": "0.5.9", "heimdalljs": "0.2.6", "minimatch": "3.0.4", "mkdirp": "0.5.1", "path-posix": "1.0.0", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.3.3"}, "dependencies": {"exists-sync": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/exists-sync/-/exists-sync-0.0.4.tgz", "integrity": "sha1-l0TCxCjMA7AQYNtFTUsS8O88iHk=", "dev": true}}}, "broccoli-jshint": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/broccoli-jshint/-/broccoli-jshint-1.2.0.tgz", "integrity": "sha1-jNVl0RoEv9MsuPhaD37eHlvnpqI=", "dev": true, "requires": {"broccoli-persistent-filter": "1.4.6", "chalk": "0.4.0", "findup-sync": "0.3.0", "jshint": "2.9.6", "json-stable-stringify": "1.0.1", "mkdirp": "0.4.2"}, "dependencies": {"ansi-styles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz", "integrity": "sha1-yxAt8cVvUSPquLZ817mAJ6AnkXg=", "dev": true}, "chalk": {"version": "0.4.0", "resolved": "http://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha1-UZmj3c0MHv4jvAjBsCewYXbgxk8=", "dev": true, "requires": {"ansi-styles": "1.0.0", "has-color": "0.1.7", "strip-ansi": "0.1.1"}}, "findup-sync": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.3.0.tgz", "integrity": "sha1-N5MKpdgWt3fANEXhlmzGeQpMCxY=", "dev": true, "requires": {"glob": "5.0.15"}}, "mkdirp": {"version": "0.4.2", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.4.2.tgz", "integrity": "sha1-QnyMGOzjmLky9vZm9OHlt3QOeMg=", "dev": true, "requires": {"minimist": "0.0.8"}}, "strip-ansi": {"version": "0.1.1", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz", "integrity": "sha1-OeipjQRNFQZgq+SmgIrPcLt7yZE=", "dev": true}}}, "broccoli-kitchen-sink-helpers": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.3.1.tgz", "integrity": "sha1-d8fBgZS5ZkFj7E/O4nk0RJJuDAY=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}}, "broccoli-merge-trees": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/broccoli-merge-trees/-/broccoli-merge-trees-1.2.4.tgz", "integrity": "sha1-oAFRm7UGfwZYnZGvopQkRaLQ/bU=", "dev": true, "requires": {"broccoli-plugin": "1.3.1", "can-symlink": "1.0.0", "fast-ordered-set": "1.0.3", "fs-tree-diff": "0.5.9", "heimdalljs": "0.2.6", "heimdalljs-logger": "0.1.10", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "broccoli-persistent-filter": {"version": "1.4.6", "resolved": "https://registry.npmjs.org/broccoli-persistent-filter/-/broccoli-persistent-filter-1.4.6.tgz", "integrity": "sha512-0RejLwoC95kv4kta8KAa+FmECJCK78Qgm8SRDEK7YyU0N9Cx6KpY3UCDy9WELl3mCXLN8TokNxc7/hp3lL4lfw==", "dev": true, "requires": {"async-disk-cache": "1.3.3", "async-promise-queue": "1.0.4", "broccoli-plugin": "1.3.1", "fs-tree-diff": "0.5.9", "hash-for-dep": "1.2.3", "heimdalljs": "0.2.6", "heimdalljs-logger": "0.1.10", "mkdirp": "0.5.1", "promise-map-series": "0.2.3", "rimraf": "2.6.2", "rsvp": "3.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.3.3"}}, "broccoli-plugin": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/broccoli-plugin/-/broccoli-plugin-1.3.1.tgz", "integrity": "sha512-DW8XASZkmorp+q7J4EeDEZz+LoyKLAd2XZULXyD9l4m9/hAKV3vjHmB1kiUshcWAYMgTP1m2i4NnqCE/23h6AQ==", "dev": true, "requires": {"promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "broccoli-sane-watcher": {"version": "1.1.5", "resolved": "http://registry.npmjs.org/broccoli-sane-watcher/-/broccoli-sane-watcher-1.1.5.tgz", "integrity": "sha1-8rCvnPCvt0x6Sc2I6xHGhp7owMA=", "dev": true, "requires": {"broccoli-slow-trees": "1.1.0", "debug": "2.6.9", "rsvp": "3.6.2", "sane": "1.7.0"}}, "broccoli-slow-trees": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-slow-trees/-/broccoli-slow-trees-1.1.0.tgz", "integrity": "sha1-QmxXJOAIEH5Fc/c+ipynApFrePc=", "dev": true}, "broccoli-source": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-source/-/broccoli-source-1.1.0.tgz", "integrity": "sha1-VPDoLItz9GWAy7xPV48LMvyo+Ak=", "dev": true}, "broccoli-sourcemap-concat": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/broccoli-sourcemap-concat/-/broccoli-sourcemap-concat-2.0.2.tgz", "integrity": "sha1-ZNvqT52kc3w/xVAu+iC7YyLNBqI=", "dev": true, "requires": {"broccoli-caching-writer": "2.3.1", "broccoli-kitchen-sink-helpers": "0.2.9", "fast-sourcemap-concat": "0.2.7", "lodash-node": "2.4.1", "lodash.uniq": "3.2.2", "minimatch": "2.0.10", "mkdirp": "0.5.1"}, "dependencies": {"broccoli-caching-writer": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/broccoli-caching-writer/-/broccoli-caching-writer-2.3.1.tgz", "integrity": "sha1-uTz1j5Jk8AMHWGjbBXdPTn8lvQc=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-plugin": "1.1.0", "debug": "2.6.9", "rimraf": "2.6.2", "rsvp": "3.6.2", "walk-sync": "0.2.7"}}, "broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}}, "broccoli-plugin": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-plugin/-/broccoli-plugin-1.1.0.tgz", "integrity": "sha1-c+LPoF+OoeP8FCDEDD2efcckvwI=", "dev": true, "requires": {"promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "minimatch": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha1-jQh8OcazjAAbl/ynzm0OHoCvusc=", "dev": true, "requires": {"brace-expansion": "1.1.11"}}, "walk-sync": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.2.7.tgz", "integrity": "sha1-tJvk7mhnZXrrc2l4tWop0Q+jmWk=", "dev": true, "requires": {"ensure-posix-path": "1.0.2", "matcher-collection": "1.0.5"}}}}, "broccoli-sri-hash": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/broccoli-sri-hash/-/broccoli-sri-hash-1.2.2.tgz", "integrity": "sha1-ZOVEAawC6knr8nARaa4hTAdYhJM=", "dev": true, "requires": {"broccoli-caching-writer": "2.3.1", "mkdirp": "0.5.1", "rsvp": "3.6.2", "sri-toolbox": "0.2.0", "symlink-or-copy": "1.2.0"}, "dependencies": {"broccoli-caching-writer": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/broccoli-caching-writer/-/broccoli-caching-writer-2.3.1.tgz", "integrity": "sha1-uTz1j5Jk8AMHWGjbBXdPTn8lvQc=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-plugin": "1.1.0", "debug": "2.6.9", "rimraf": "2.6.2", "rsvp": "3.6.2", "walk-sync": "0.2.7"}}, "broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}}, "broccoli-plugin": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/broccoli-plugin/-/broccoli-plugin-1.1.0.tgz", "integrity": "sha1-c+LPoF+OoeP8FCDEDD2efcckvwI=", "dev": true, "requires": {"promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "walk-sync": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.2.7.tgz", "integrity": "sha1-tJvk7mhnZXrrc2l4tWop0Q+jmWk=", "dev": true, "requires": {"ensure-posix-path": "1.0.2", "matcher-collection": "1.0.5"}}}}, "broccoli-stew": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/broccoli-stew/-/broccoli-stew-1.6.0.tgz", "integrity": "sha512-sUwCJNnYH4Na690By5xcEMAZqKgquUQnMAEuIiL3Z2k63mSw9Xg+7Ew4wCrFrMmXMcLpWjZDOm6Yqnq268N+ZQ==", "dev": true, "requires": {"broccoli-debug": "0.6.5", "broccoli-funnel": "2.0.1", "broccoli-merge-trees": "2.0.1", "broccoli-persistent-filter": "1.4.6", "broccoli-plugin": "1.3.1", "chalk": "2.4.1", "debug": "3.2.6", "ensure-posix-path": "1.0.2", "fs-extra": "5.0.0", "minimatch": "3.0.4", "resolve": "1.8.1", "rsvp": "4.8.4", "symlink-or-copy": "1.2.0", "walk-sync": "0.3.3"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "1.9.3"}}, "broccoli-funnel": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/broccoli-funnel/-/broccoli-funnel-2.0.1.tgz", "integrity": "sha512-C8Lnp9TVsSSiZMGEF16C0dCiNg2oJqUKwuZ1K4kVC6qRPG/2Cj/rtB5kRCC9qEbwqhX71bDbfHROx0L3J7zXQg==", "dev": true, "requires": {"array-equal": "1.0.0", "blank-object": "1.0.2", "broccoli-plugin": "1.3.1", "debug": "2.6.9", "fast-ordered-set": "1.0.3", "fs-tree-diff": "0.5.9", "heimdalljs": "0.2.6", "minimatch": "3.0.4", "mkdirp": "0.5.1", "path-posix": "1.0.0", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.3.3"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}}}, "broccoli-merge-trees": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/broccoli-merge-trees/-/broccoli-merge-trees-2.0.1.tgz", "integrity": "sha512-WjaexJ+I8BxP5V5RNn6um/qDRSmKoiBC/QkRi79FT9ClHfldxRyCDs9mcV7mmoaPlsshmmPaUz5jdtcKA6DClQ==", "dev": true, "requires": {"broccoli-plugin": "1.3.1", "merge-trees": "1.0.1"}}, "chalk": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "dev": true, "requires": {"ansi-styles": "3.2.1", "escape-string-regexp": "1.0.5", "supports-color": "5.5.0"}}, "debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "dev": true, "requires": {"ms": "2.1.1"}, "dependencies": {"ms": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz", "integrity": "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==", "dev": true}}}, "fs-extra": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-5.0.0.tgz", "integrity": "sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==", "dev": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "4.0.0", "universalify": "0.1.2"}}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}, "jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "dev": true, "requires": {"graceful-fs": "4.1.15"}}, "rsvp": {"version": "4.8.4", "resolved": "https://registry.npmjs.org/rsvp/-/rsvp-4.8.4.tgz", "integrity": "sha512-6FomvYPfs+Jy9TfXmBpBuMWNH94SgCsZmJKcanySzgNNP6LjWxBvyLTa9KaMfDDM5oxRfrKDB0r/qeRsLwnBfA==", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "3.0.0"}}}}, "broccoli-uglify-sourcemap": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/broccoli-uglify-sourcemap/-/broccoli-uglify-sourcemap-1.5.2.tgz", "integrity": "sha1-BPhKsNtTkDH6hozPpWPJky1Qzts=", "dev": true, "requires": {"broccoli-plugin": "1.3.1", "debug": "2.6.9", "lodash.merge": "4.6.1", "matcher-collection": "1.0.5", "mkdirp": "0.5.1", "source-map-url": "0.3.0", "symlink-or-copy": "1.2.0", "uglify-js": "2.8.29", "walk-sync": "0.1.3"}, "dependencies": {"lodash.merge": {"version": "4.6.1", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.1.tgz", "integrity": "sha512-AOYza4+Hf5z1/0Hztxpm2/xiPZgi/cjMqdnKTUWTBSKchJlxXXuUSxCCl8rJlf4g6yww/j6mA8nC8Hw/EZWxKQ==", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "uglify-js": {"version": "2.8.29", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "integrity": "sha1-KcVzMUgFe7Th913zW3qcty5qWd0=", "dev": true, "requires": {"source-map": "0.5.7", "uglify-to-browserify": "1.0.2", "yargs": "3.10.0"}}, "walk-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.1.3.tgz", "integrity": "sha1-igcmGgC9ps+xviXp8QD61XVG9YM=", "dev": true}, "window-size": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=", "dev": true}, "yargs": {"version": "3.10.0", "resolved": "http://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "integrity": "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=", "dev": true, "requires": {"camelcase": "1.2.1", "cliui": "2.1.0", "decamelize": "1.2.0", "window-size": "0.1.0"}}}}, "broccoli-viz": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/broccoli-viz/-/broccoli-viz-2.0.1.tgz", "integrity": "sha1-Pz7S+4PjaKpTBvrkYIAd6lUuQNs=", "dev": true}, "broccoli-writer": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/broccoli-writer/-/broccoli-writer-0.1.1.tgz", "integrity": "sha1-1NcaqPKvvGejhmuRotp5CEuWqy0=", "dev": true, "requires": {"quick-temp": "0.1.8", "rsvp": "3.6.2"}}, "bser": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/bser/-/bser-2.0.0.tgz", "integrity": "sha1-mseNPtXZFYBP2HrLFYvHlxR6Fxk=", "dev": true, "requires": {"node-int64": "0.4.0"}}, "buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "dev": true, "requires": {"base64-js": "1.5.1", "ieee754": "1.2.1"}}, "buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "dev": true, "optional": true}, "bytes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true}, "callsite": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz", "integrity": "sha1-KAOY5dZkvXQDi28JBRU+borxvCA=", "dev": true}, "camelcase": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "dev": true}, "can-symlink": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/can-symlink/-/can-symlink-1.0.0.tgz", "integrity": "sha1-l7YH2KhLtsbiKLkC2GTstZS50hk=", "dev": true, "requires": {"tmp": "0.0.28"}}, "cardinal": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/cardinal/-/cardinal-0.5.0.tgz", "integrity": "sha1-ANX2YdvUqr/ffUHOSKWlm8o1opE=", "dev": true, "requires": {"ansicolors": "0.2.1", "redeyed": "0.5.0"}}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "dev": true, "optional": true}, "center-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "dev": true, "requires": {"align-text": "0.1.4", "lazy-cache": "1.0.4"}}, "chalk": {"version": "1.1.0", "resolved": "http://registry.npmjs.org/chalk/-/chalk-1.1.0.tgz", "integrity": "sha1-CbRTzsSXp1Ug5KYK5IIUqHAOCSE=", "dev": true, "requires": {"ansi-styles": "2.2.1", "escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "strip-ansi": "3.0.1", "supports-color": "2.0.0"}}, "charm": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/charm/-/charm-1.0.2.tgz", "integrity": "sha1-it02cVOm2aWBMxBSxAkJkdqZXjU=", "dev": true, "requires": {"inherits": "2.0.3"}}, "clean-base-url": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/clean-base-url/-/clean-base-url-1.0.0.tgz", "integrity": "sha1-yQHPCiC5ckNbDszVLQVoJKQ1G3s=", "dev": true}, "clean-css": {"version": "2.2.23", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-2.2.23.tgz", "integrity": "sha1-BZC1R4tRbEkD7cLYm9P9vdKGMow=", "dev": true, "requires": {"commander": "2.2.0"}, "dependencies": {"commander": {"version": "2.2.0", "resolved": "http://registry.npmjs.org/commander/-/commander-2.2.0.tgz", "integrity": "sha1-F1rUuTF/P/YV8gHB5XIk9Vo+kd8=", "dev": true}}}, "cli": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cli/-/cli-1.0.1.tgz", "integrity": "sha1-IoF1NPJL+klQw01TLUjsvGIbjBQ=", "dev": true, "requires": {"exit": "0.1.2", "glob": "7.1.3"}, "dependencies": {"glob": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.3.tgz", "integrity": "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ==", "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}}}, "cli-color": {"version": "0.3.3", "resolved": "http://registry.npmjs.org/cli-color/-/cli-color-0.3.3.tgz", "integrity": "sha1-EtW90Vj/igsNtAEZiRPAPfBp9vU=", "dev": true, "requires": {"d": "0.1.1", "es5-ext": "0.10.46", "memoizee": "0.3.10", "timers-ext": "0.1.7"}}, "cli-table": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cli-table/-/cli-table-0.3.1.tgz", "integrity": "sha1-9TsFJmqLGguTSz0IIebi3FkUriM=", "dev": true, "requires": {"colors": "1.0.3"}, "dependencies": {"colors": {"version": "1.0.3", "resolved": "http://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "integrity": "sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=", "dev": true}}}, "cliui": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=", "dev": true, "requires": {"center-align": "0.1.3", "right-align": "0.1.3", "wordwrap": "0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=", "dev": true}}}, "clone": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/clone/-/clone-0.2.0.tgz", "integrity": "sha1-xhJqkK1Pctv1rNskPMN3JP6T/B8=", "dev": true}, "code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "color-support": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "integrity": "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==", "dev": true}, "colors": {"version": "0.6.2", "resolved": "http://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha1-JCP+ZnisDF2uiFLl0OW+CMmXq8w=", "dev": true}, "combined-stream": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.7.tgz", "integrity": "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w==", "dev": true, "requires": {"delayed-stream": "1.0.0"}}, "commander": {"version": "2.19.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.19.0.tgz", "integrity": "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==", "dev": true}, "commoner": {"version": "0.10.8", "resolved": "https://registry.npmjs.org/commoner/-/commoner-0.10.8.tgz", "integrity": "sha1-NPw2cs0kOT6LtH5wyqApOBH08sU=", "dev": true, "requires": {"commander": "2.19.0", "detective": "4.7.1", "glob": "5.0.15", "graceful-fs": "4.1.15", "iconv-lite": "0.4.24", "mkdirp": "0.5.1", "private": "0.1.8", "q": "1.5.1", "recast": "0.11.23"}, "dependencies": {"esprima": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz", "integrity": "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=", "dev": true}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}, "recast": {"version": "0.11.23", "resolved": "https://registry.npmjs.org/recast/-/recast-0.11.23.tgz", "integrity": "sha1-RR/TAEqx5N+bTktmN2sqIZEkYtM=", "dev": true, "requires": {"ast-types": "0.9.6", "esprima": "3.1.3", "private": "0.1.8", "source-map": "0.5.7"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "component-bind": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/component-bind/-/component-bind-1.0.0.tgz", "integrity": "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E=", "dev": true}, "component-emitter": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.1.2.tgz", "integrity": "sha1-KWWU8nU9qmOZbSrwjRWpURbJrsM=", "dev": true}, "component-inherit": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/component-inherit/-/component-inherit-0.0.3.tgz", "integrity": "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM=", "dev": true}, "compressible": {"version": "2.0.15", "resolved": "https://registry.npmjs.org/compressible/-/compressible-2.0.15.tgz", "integrity": "sha512-4aE67DL33dSW9gw4CI2H/yTxqHLNcxp0yS6jB+4h+wr3e43+1z7vm0HU9qXOH8j+qjKuL8+UtkOxYQSMq60Ylw==", "dev": true, "requires": {"mime-db": "1.37.0"}}, "compression": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/compression/-/compression-1.7.3.tgz", "integrity": "sha512-HSjyBG5N1Nnz7tF2+O7A9XUhyjru71/fwgNb7oIsEVHR0WShfs2tIS/EySLgiTe98aOK18YDlMXpzjCXY/n9mg==", "dev": true, "requires": {"accepts": "1.3.5", "bytes": "3.0.0", "compressible": "2.0.15", "debug": "2.6.9", "on-headers": "1.0.1", "safe-buffer": "5.1.2", "vary": "1.1.2"}}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "dev": true, "optional": true, "requires": {"buffer-from": "1.1.1", "inherits": "2.0.3", "readable-stream": "2.3.6", "typedarray": "0.0.6"}}, "configstore": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/configstore/-/configstore-1.2.1.tgz", "integrity": "sha1-AK1ALA26AnvYtLcijcfULO/jyBo=", "dev": true, "requires": {"graceful-fs": "4.1.15", "mkdirp": "0.5.1", "object-assign": "3.0.0", "os-tmpdir": "1.0.2", "osenv": "0.1.5", "uuid": "2.0.3", "write-file-atomic": "1.3.4", "xdg-basedir": "2.0.0"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}, "object-assign": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha1-m+3VygiXlJvKR+f/QIBi1Un1h/I=", "dev": true}, "osenv": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/osenv/-/osenv-0.1.5.tgz", "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "dev": true, "requires": {"os-homedir": "1.0.2", "os-tmpdir": "1.0.2"}}}}, "connect": {"version": "3.6.6", "resolved": "https://registry.npmjs.org/connect/-/connect-3.6.6.tgz", "integrity": "sha1-Ce/2xVr3I24TcTWnJXSFi2eG9SQ=", "dev": true, "requires": {"debug": "2.6.9", "finalhandler": "1.1.0", "parseurl": "1.3.2", "utils-merge": "1.0.1"}}, "console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "requires": {"date-now": "0.1.4"}}, "console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "dev": true}, "consolidate": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/consolidate/-/consolidate-0.14.5.tgz", "integrity": "sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM=", "dev": true, "requires": {"bluebird": "3.5.3"}, "dependencies": {"bluebird": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.5.3.tgz", "integrity": "sha512-/qKPUQlaW1OyR51WeCPBvRnAlnZFUJkCSG5HzGnuIqhgyJtF+T94lFnn33eiazjRm2LAHVy2guNnaq48X9SJuw==", "dev": true}}}, "content-disposition": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.2.tgz", "integrity": "sha1-DPaLud318r55YcOoUXjLhdunjLQ=", "dev": true}, "content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "dev": true}, "convert-source-map": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.6.0.tgz", "integrity": "sha512-eFu7XigvxdZ1ETfbgPBohgyQ/Z++C0eEhTor0qRwBw9unw+L0/6V8wkSuGgzdThkiS5lSpdptOQPD8Ak40a+7A==", "dev": true, "requires": {"safe-buffer": "5.1.2"}}, "cookie": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "dev": true}, "cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true}, "copy-dereference": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/copy-dereference/-/copy-dereference-1.0.0.tgz", "integrity": "sha1-axMYZUIP2BtBO6mUtE02VTERUrY=", "dev": true}, "core-js": {"version": "1.2.7", "resolved": "http://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz", "integrity": "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=", "dev": true}, "core-object": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/core-object/-/core-object-0.0.2.tgz", "integrity": "sha1-yab+6PcS4oH6n2+6ECQ0Ceot68M=", "dev": true, "requires": {"lodash-node": "2.4.1"}}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "cpr": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/cpr/-/cpr-0.4.2.tgz", "integrity": "sha1-zFCD5tL6MfUrv+765QikRf5hgPI=", "dev": true, "requires": {"graceful-fs": "4.1.15", "mkdirp": "0.5.1", "rimraf": "2.4.5"}, "dependencies": {"glob": {"version": "6.0.4", "resolved": "https://registry.npmjs.org/glob/-/glob-6.0.4.tgz", "integrity": "sha1-DwiGD2oVUSey+t1PnOJLGqtuTSI=", "dev": true, "requires": {"inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}, "rimraf": {"version": "2.4.5", "resolved": "http://registry.npmjs.org/rimraf/-/rimraf-2.4.5.tgz", "integrity": "sha1-7nEM5dk6j9uFb7Xqj/Di11k0sto=", "dev": true, "requires": {"glob": "6.0.4"}}}}, "cross-spawn": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "requires": {"lru-cache": "4.1.4", "shebang-command": "1.2.0", "which": "1.3.1"}}, "cycle": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz", "integrity": "sha1-IegLK+hYD5i0aPN5QwZisEbDStI=", "dev": true, "optional": true}, "d": {"version": "0.1.1", "resolved": "http://registry.npmjs.org/d/-/d-0.1.1.tgz", "integrity": "sha1-2hhMU10Y2O57oqoim5FACfrhEwk=", "dev": true, "requires": {"es5-ext": "0.10.46"}}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0"}}, "date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "defined": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz", "integrity": "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM=", "dev": true}, "defs": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/defs/-/defs-1.1.1.tgz", "integrity": "sha1-siYJ8sehG6ej2xFoBcE5scr/qdI=", "dev": true, "requires": {"alter": "0.2.0", "ast-traverse": "0.1.1", "breakable": "1.0.0", "esprima-fb": "15001.1001.0-dev-harmony-fb", "simple-fmt": "0.1.0", "simple-is": "0.2.0", "stringmap": "0.2.2", "stringset": "0.2.1", "tryor": "0.1.2", "yargs": "3.27.0"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true}, "delegates": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "dev": true}, "depd": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true}, "destroy": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "dev": true}, "detect-indent": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-3.0.1.tgz", "integrity": "sha1-ncXl3bzu+DJXZLlFGwK8bVQIT3U=", "dev": true, "requires": {"get-stdin": "4.0.1", "minimist": "1.2.0", "repeating": "1.1.3"}, "dependencies": {"minimist": {"version": "1.2.0", "resolved": "http://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}}}, "detective": {"version": "4.7.1", "resolved": "https://registry.npmjs.org/detective/-/detective-4.7.1.tgz", "integrity": "sha512-H6PmeeUcZloWtdt4DAkFyzFL94arpHr3NOwwmVILFiy+9Qd4JTxxXrzfyGk/lmct2qVGBwTSwSXagqu2BxmWig==", "dev": true, "requires": {"acorn": "5.7.3", "defined": "1.0.0"}}, "diff": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/diff/-/diff-1.4.0.tgz", "integrity": "sha1-fyjS657nsVqX79ic5j3P2qPMur8=", "dev": true}, "dom-serializer": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz", "integrity": "sha1-BzxpdUbOB4DOI75KKOKT5AvDDII=", "dev": true, "requires": {"domelementtype": "1.1.3", "entities": "1.1.2"}, "dependencies": {"domelementtype": {"version": "1.1.3", "resolved": "http://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz", "integrity": "sha1-vSh3PiZCiBrsUVRJJCmcXNgiGFs=", "dev": true}}}, "domelementtype": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.2.1.tgz", "integrity": "sha512-SQVCLFS2E7G5CRCMdn6K9bIhRj1bS6QBWZfF0TUPh4V/BbqrQ619IdSS3/izn0FZ+9l+uODzaZjb08fjOfablA==", "dev": true}, "domhandler": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz", "integrity": "sha1-LeWaCCLVAn+r/28DLCsloqir5zg=", "dev": true, "requires": {"domelementtype": "1.2.1"}}, "domutils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "dev": true, "requires": {"dom-serializer": "0.1.0", "domelementtype": "1.2.1"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "dev": true, "optional": true, "requires": {"jsbn": "0.1.1", "safer-buffer": "2.1.2"}}, "editions": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/editions/-/editions-1.3.4.tgz", "integrity": "sha512-gzao+mxnYDzIysXKMQi/+M1mjy/rjestjg6OPoYTtI+3Izp23oiGZitsl9lPDPiTGXbcSIk1iJWhliSaglxnUg==", "dev": true}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true}, "ember-buffered-proxy": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/ember-buffered-proxy/-/ember-buffered-proxy-0.5.1.tgz", "integrity": "sha1-f1BTLe09jBm1tFfBJECrSiEboT0=", "dev": true}, "ember-cli": {"version": "1.13.15", "resolved": "http://registry.npmjs.org/ember-cli/-/ember-cli-1.13.15.tgz", "integrity": "sha1-eWm0SEazZqmqc8GCfY60QmmZT3I=", "dev": true, "requires": {"amd-name-resolver": "0.0.2", "bower": "1.8.8", "bower-config": "0.6.1", "bower-endpoint-parser": "0.2.2", "broccoli": "0.16.8", "broccoli-babel-transpiler": "5.7.4", "broccoli-config-loader": "1.0.1", "broccoli-config-replace": "1.1.2", "broccoli-funnel": "1.2.0", "broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-merge-trees": "1.2.4", "broccoli-plugin": "1.3.1", "broccoli-sane-watcher": "1.1.5", "broccoli-source": "1.1.0", "broccoli-sourcemap-concat": "2.0.2", "broccoli-viz": "2.0.1", "chalk": "1.1.0", "clean-base-url": "1.0.0", "compression": "1.7.3", "configstore": "1.2.1", "core-object": "0.0.2", "cpr": "0.4.2", "debug": "2.6.9", "diff": "1.4.0", "ember-cli-copy-dereference": "1.0.0", "ember-cli-get-dependency-depth": "1.0.0", "ember-cli-is-package-missing": "1.0.0", "ember-cli-normalize-entity-name": "1.0.0", "ember-cli-path-utils": "1.0.0", "ember-cli-preprocess-registry": "1.1.0", "ember-cli-string-utils": "1.1.0", "ember-cli-test-info": "1.0.0", "ember-router-generator": "1.2.3", "escape-string-regexp": "1.0.5", "exists-sync": "0.0.3", "exit": "0.1.2", "express": "4.16.4", "findup": "0.1.5", "findup-sync": "0.2.1", "fs-extra": "0.22.1", "fs-monitor-stack": "1.1.1", "git-repo-info": "1.4.1", "glob": "5.0.13", "http-proxy": "1.17.0", "inflection": "1.12.0", "inquirer": "0.5.1", "is-git-url": "0.2.3", "isbinaryfile": "2.0.4", "leek": "0.0.18", "lodash": "3.10.1", "markdown-it": "4.3.0", "markdown-it-terminal": "0.0.2", "merge-defaults": "0.2.1", "minimatch": "2.0.10", "morgan": "1.9.1", "node-modules-path": "1.0.2", "node-uuid": "1.4.8", "nopt": "3.0.6", "npm": "2.14.10", "pleasant-progress": "1.1.0", "portfinder": "0.4.0", "promise-map-series": "0.2.3", "quick-temp": "0.1.3", "readline2": "0.1.1", "resolve": "1.8.1", "rsvp": "3.6.2", "sane": "1.7.0", "semver": "4.3.6", "silent-error": "1.1.1", "symlink-or-copy": "1.2.0", "temp": "0.8.3", "testem": "1.18.5", "through": "2.3.8", "tiny-lr": "0.2.0", "walk-sync": "0.1.3", "yam": "0.0.18"}, "dependencies": {"broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.13", "mkdirp": "0.5.1"}}, "glob": {"version": "5.0.13", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.13.tgz", "integrity": "sha1-C2/8OsZOuQZp9yOgCg67coGzP48=", "dev": true, "requires": {"inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "2.0.10", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "lodash": {"version": "3.10.1", "resolved": "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}, "minimatch": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha1-jQh8OcazjAAbl/ynzm0OHoCvusc=", "dev": true, "requires": {"brace-expansion": "1.1.11"}}, "mktemp": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/mktemp/-/mktemp-0.3.5.tgz", "integrity": "sha1-oVBMcG0NKxmMag62Rff9r4GB994=", "dev": true}, "quick-temp": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/quick-temp/-/quick-temp-0.1.3.tgz", "integrity": "sha1-SMGIYRRpHXS5YAPekyKnyZICKvQ=", "dev": true, "requires": {"mktemp": "0.3.5", "rimraf": "2.2.8", "underscore.string": "2.3.3"}}, "rimraf": {"version": "2.2.8", "resolved": "http://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "integrity": "sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=", "dev": true}, "underscore.string": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz", "integrity": "sha1-ccCL9rQosRM/N+ePo6Icgvcymw0=", "dev": true}, "walk-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.1.3.tgz", "integrity": "sha1-igcmGgC9ps+xviXp8QD61XVG9YM=", "dev": true}}}, "ember-cli-app-version": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/ember-cli-app-version/-/ember-cli-app-version-0.5.1.tgz", "integrity": "sha1-N+z21Z5XJwGU6uOFs6k6VITeh1A=", "dev": true, "requires": {"ember-cli-babel": "5.2.4", "ember-cli-htmlbars": "0.7.9", "git-repo-version": "0.3.0"}, "dependencies": {"broccoli-filter": {"version": "0.1.14", "resolved": "https://registry.npmjs.org/broccoli-filter/-/broccoli-filter-0.1.14.tgz", "integrity": "sha1-I8rjiR/567e019sAxtzwNTXa960=", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.2.9", "broccoli-writer": "0.1.1", "mkdirp": "0.3.5", "promise-map-series": "0.2.3", "quick-temp": "0.1.8", "rsvp": "3.6.2", "symlink-or-copy": "1.2.0", "walk-sync": "0.1.3"}}, "broccoli-kitchen-sink-helpers": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/broccoli-kitchen-sink-helpers/-/broccoli-kitchen-sink-helpers-0.2.9.tgz", "integrity": "sha1-peCYbtjXb7WYS2jD8EUNOpbjbsw=", "dev": true, "requires": {"glob": "5.0.15", "mkdirp": "0.5.1"}, "dependencies": {"mkdirp": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}}}, "ember-cli-htmlbars": {"version": "0.7.9", "resolved": "http://registry.npmjs.org/ember-cli-htmlbars/-/ember-cli-htmlbars-0.7.9.tgz", "integrity": "sha1-FCzUMlqz9Ix2z43E06OADzjnIb4=", "dev": true, "requires": {"broccoli-filter": "0.1.14", "ember-cli-version-checker": "1.3.1"}}, "mkdirp": {"version": "0.3.5", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.3.5.tgz", "integrity": "sha1-3j5fiWHIjHh+4TaN+EmsRBPsqNc=", "dev": true}, "walk-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.1.3.tgz", "integrity": "sha1-igcmGgC9ps+xviXp8QD61XVG9YM=", "dev": true}}}, "ember-cli-babel": {"version": "5.2.4", "resolved": "https://registry.npmjs.org/ember-cli-babel/-/ember-cli-babel-5.2.4.tgz", "integrity": "sha1-XOT0awjtb20h6Hhhn7aJcZ1ujhM=", "dev": true, "requires": {"broccoli-babel-transpiler": "5.7.4", "broccoli-funnel": "1.2.0", "clone": "2.1.2", "ember-cli-version-checker": "1.3.1", "resolve": "1.8.1"}, "dependencies": {"clone": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=", "dev": true}}}, "ember-cli-content-security-policy": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/ember-cli-content-security-policy/-/ember-cli-content-security-policy-0.4.0.tgz", "integrity": "sha1-ceTyKOaLzvwxPw/64m82AKAJMnY=", "dev": true, "requires": {"body-parser": "1.18.3"}}, "ember-cli-copy-dereference": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-copy-dereference/-/ember-cli-copy-dereference-1.0.0.tgz", "integrity": "sha1-oXlb9scGUDF99KuGdN0C4L6l1P0=", "dev": true}, "ember-cli-dependency-checker": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/ember-cli-dependency-checker/-/ember-cli-dependency-checker-1.3.0.tgz", "integrity": "sha1-8OjLfw9DweVgSU6qk3KATnoIiio=", "dev": true, "requires": {"chalk": "0.5.1", "is-git-url": "0.2.0", "semver": "4.3.6"}, "dependencies": {"ansi-regex": {"version": "0.2.1", "resolved": "http://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha1-DY6UaWej2BQ/k+JOKYUl/BsiNfk=", "dev": true}, "ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha1-6uy/Zs1waIJ2Cy9GkVgrj1XXp94=", "dev": true}, "chalk": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha1-Zjs6ZItotV0EaQ1JFnqoN4WPIXQ=", "dev": true, "requires": {"ansi-styles": "1.1.0", "escape-string-regexp": "1.0.5", "has-ansi": "0.1.0", "strip-ansi": "0.3.0", "supports-color": "0.2.0"}}, "has-ansi": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-0.1.0.tgz", "integrity": "sha1-hPJlqujA5qiKEtcCKJS3VoiUxi4=", "dev": true, "requires": {"ansi-regex": "0.2.1"}}, "is-git-url": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-git-url/-/is-git-url-0.2.0.tgz", "integrity": "sha1-uc4PsESCHIiIAhPWAtsDvbJV2hs=", "dev": true}, "strip-ansi": {"version": "0.3.0", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha1-JfSOoiynkYfzF0pNuHWTR7sSYiA=", "dev": true, "requires": {"ansi-regex": "0.2.1"}}, "supports-color": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "integrity": "sha1-2S3iaU6z9nMjlz1649i1W0wiGQo=", "dev": true}}}, "ember-cli-get-dependency-depth": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-get-dependency-depth/-/ember-cli-get-dependency-depth-1.0.0.tgz", "integrity": "sha1-4K/s+CotUvAPKKtGgpUoGuw2jRE=", "dev": true}, "ember-cli-htmlbars": {"version": "0.7.10", "resolved": "http://registry.npmjs.org/ember-cli-htmlbars/-/ember-cli-htmlbars-0.7.10.tgz", "integrity": "sha1-ARxEEPKCbWEXeTTLN2KdSmUtT9k=", "dev": true, "requires": {"broccoli-filter": "1.3.0", "ember-cli-version-checker": "1.3.1"}}, "ember-cli-htmlbars-inline-precompile": {"version": "0.2.0", "resolved": "http://registry.npmjs.org/ember-cli-htmlbars-inline-precompile/-/ember-cli-htmlbars-inline-precompile-0.2.0.tgz", "integrity": "sha1-fpO77EM6vEvWOM7xLlClxeE2vOE=", "dev": true, "requires": {"babel-plugin-htmlbars-inline-precompile": "0.0.5", "ember-cli-babel": "5.2.4", "ember-cli-htmlbars": "0.7.10"}}, "ember-cli-ic-ajax": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/ember-cli-ic-ajax/-/ember-cli-ic-ajax-0.2.5.tgz", "integrity": "sha1-AAbiCKahtBqIZW+7qUze/4266L0=", "dev": true, "requires": {"ic-ajax": "2.0.2"}}, "ember-cli-inject-live-reload": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/ember-cli-inject-live-reload/-/ember-cli-inject-live-reload-1.6.1.tgz", "integrity": "sha1-grj1vkVIFadef21Cyc4LyIOpFKM=", "dev": true}, "ember-cli-inline-content": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/ember-cli-inline-content/-/ember-cli-inline-content-0.4.1.tgz", "integrity": "sha1-49PVLx5AWSQBGSExk6ygfqqEutY=", "dev": true}, "ember-cli-is-package-missing": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-is-package-missing/-/ember-cli-is-package-missing-1.0.0.tgz", "integrity": "sha1-bmGEyvuSY13ZPKbJRrEEKS1OM5A=", "dev": true}, "ember-cli-normalize-entity-name": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-normalize-entity-name/-/ember-cli-normalize-entity-name-1.0.0.tgz", "integrity": "sha1-CxT3vLxZmqEXtf3cgeT9A8S61bc=", "dev": true, "requires": {"silent-error": "1.1.1"}}, "ember-cli-path-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-path-utils/-/ember-cli-path-utils-1.0.0.tgz", "integrity": "sha1-Tjmvi1UwHN3FAXc5t3qAT7ogce0=", "dev": true}, "ember-cli-preprocess-registry": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ember-cli-preprocess-registry/-/ember-cli-preprocess-registry-1.1.0.tgz", "integrity": "sha1-Go+EiHbeKFFQeELkwMkFH2K0qsY=", "dev": true, "requires": {"broccoli-clean-css": "0.2.0", "broccoli-funnel": "1.2.0", "broccoli-merge-trees": "1.2.4", "debug": "2.6.9", "exists-sync": "0.0.3", "lodash": "3.10.1", "process-relative-require": "1.0.0", "silent-error": "1.1.1"}, "dependencies": {"lodash": {"version": "3.10.1", "resolved": "http://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}}}, "ember-cli-qunit": {"version": "1.4.2", "resolved": "http://registry.npmjs.org/ember-cli-qunit/-/ember-cli-qunit-1.4.2.tgz", "integrity": "sha1-fKJUlccMo0cQbUT8APDXrsoCdHU=", "dev": true, "requires": {"broccoli-babel-transpiler": "5.7.4", "broccoli-concat": "2.3.8", "broccoli-jshint": "1.2.0", "broccoli-merge-trees": "1.2.4", "ember-cli-babel": "5.2.4", "ember-cli-version-checker": "1.3.1", "ember-qunit": "0.4.24", "qunitjs": "1.23.1", "resolve": "1.8.1"}}, "ember-cli-release": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/ember-cli-release/-/ember-cli-release-0.2.3.tgz", "integrity": "sha1-AIK9+FZrRsLoHYd5SuvI4o0dHCw=", "dev": true, "requires": {"chalk": "1.1.0", "git-tools": "0.1.4", "merge": "1.2.1", "moment-timezone": "0.3.1", "rsvp": "3.6.2", "semver": "4.3.6"}}, "ember-cli-sri": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ember-cli-sri/-/ember-cli-sri-1.2.1.tgz", "integrity": "sha1-EFsfi/uI//iBfKoU0Hduywb4V+4=", "dev": true, "requires": {"broccoli-sri-hash": "1.2.2"}}, "ember-cli-string-utils": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ember-cli-string-utils/-/ember-cli-string-utils-1.1.0.tgz", "integrity": "sha1-ObZ3/CgF9VFzc1N2/O8njqpEUqE=", "dev": true}, "ember-cli-test-info": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ember-cli-test-info/-/ember-cli-test-info-1.0.0.tgz", "integrity": "sha1-7U6WDySel1I8+JHkrtIHLOhFd7Q=", "dev": true, "requires": {"ember-cli-string-utils": "1.1.0"}}, "ember-cli-uglify": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/ember-cli-uglify/-/ember-cli-uglify-1.2.0.tgz", "integrity": "sha1-MgjDK1S8J4MFbouw1c/pu68X/7I=", "dev": true, "requires": {"broccoli-uglify-sourcemap": "1.5.2"}}, "ember-cli-version-checker": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/ember-cli-version-checker/-/ember-cli-version-checker-1.3.1.tgz", "integrity": "sha1-C8LRNMgwFC2mS/lieg7e0QthrnI=", "dev": true, "requires": {"semver": "5.6.0"}, "dependencies": {"semver": {"version": "5.6.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.6.0.tgz", "integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg==", "dev": true}}}, "ember-data": {"version": "2.1.0", "resolved": "http://registry.npmjs.org/ember-data/-/ember-data-2.1.0.tgz", "integrity": "sha1-mqD5UEIBBRMlCBgRnWOs44iMfdk=", "dev": true, "requires": {"rsvp": "3.6.2"}}, "ember-disable-proxy-controllers": {"version": "1.0.1", "resolved": "http://registry.npmjs.org/ember-disable-proxy-controllers/-/ember-disable-proxy-controllers-1.0.1.tgz", "integrity": "sha1-ElTu7AugJcJOuejaYRr6ezh1QoE=", "dev": true, "requires": {"ember-cli-babel": "5.2.4"}}, "ember-export-application-global": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ember-export-application-global/-/ember-export-application-global-1.1.1.tgz", "integrity": "sha1-8lfVJxJokyqJ1zkmec5NuJ1xVK8=", "dev": true, "requires": {"ember-cli-babel": "5.2.4"}}, "ember-qunit": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/ember-qunit/-/ember-qunit-0.4.24.tgz", "integrity": "sha1-tUz2aIxELQfqzqR8MoWHnN18IWM=", "dev": true, "requires": {"ember-test-helpers": "0.5.34"}}, "ember-router-generator": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/ember-router-generator/-/ember-router-generator-1.2.3.tgz", "integrity": "sha1-jtLKhv8yM2MSD8FCeBkeno8TFe4=", "dev": true, "requires": {"recast": "0.11.23"}, "dependencies": {"esprima": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz", "integrity": "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=", "dev": true}, "recast": {"version": "0.11.23", "resolved": "https://registry.npmjs.org/recast/-/recast-0.11.23.tgz", "integrity": "sha1-RR/TAEqx5N+bTktmN2sqIZEkYtM=", "dev": true, "requires": {"ast-types": "0.9.6", "esprima": "3.1.3", "private": "0.1.8", "source-map": "0.5.7"}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "ember-test-helpers": {"version": "0.5.34", "resolved": "https://registry.npmjs.org/ember-test-helpers/-/ember-test-helpers-0.5.34.tgz", "integrity": "sha1-yEORCNHLodfYOMISIIpcQGFHG4M=", "dev": true, "requires": {"klassy": "0.1.3"}}, "ember-truth-helpers": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/ember-truth-helpers/-/ember-truth-helpers-1.3.0.tgz", "integrity": "sha1-btn4POmkn1K7QW1V4idCYzmmTGA=", "dev": true, "requires": {"ember-cli-babel": "5.2.4"}}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true}, "engine.io": {"version": "1.8.0", "resolved": "http://registry.npmjs.org/engine.io/-/engine.io-1.8.0.tgz", "integrity": "sha1-PutfJky3XbvsG6rqJtYfWk6s4qo=", "dev": true, "requires": {"accepts": "1.3.3", "base64id": "0.1.0", "cookie": "0.3.1", "debug": "2.3.3", "engine.io-parser": "1.3.1", "ws": "1.1.1"}, "dependencies": {"accepts": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz", "integrity": "sha1-w8p0NJOGSMPg2cHjKN1otiLChMo=", "dev": true, "requires": {"mime-types": "2.1.21", "negotiator": "0.6.1"}}, "debug": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w=", "dev": true, "requires": {"ms": "0.7.2"}}, "ms": {"version": "0.7.2", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}}}, "engine.io-client": {"version": "1.8.0", "resolved": "http://registry.npmjs.org/engine.io-client/-/engine.io-client-1.8.0.tgz", "integrity": "sha1-e3MOQSdBQIdZbZvjyI0rxf22z1w=", "dev": true, "requires": {"component-emitter": "1.2.1", "component-inherit": "0.0.3", "debug": "2.3.3", "engine.io-parser": "1.3.1", "has-cors": "1.1.0", "indexof": "0.0.1", "parsejson": "0.0.3", "parseqs": "0.0.5", "parseuri": "0.0.5", "ws": "1.1.1", "xmlhttprequest-ssl": "1.5.3", "yeast": "0.1.2"}, "dependencies": {"component-emitter": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "dev": true}, "debug": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w=", "dev": true, "requires": {"ms": "0.7.2"}}, "ms": {"version": "0.7.2", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}}}, "engine.io-parser": {"version": "1.3.1", "resolved": "http://registry.npmjs.org/engine.io-parser/-/engine.io-parser-1.3.1.tgz", "integrity": "sha1-lVTxrjMQfW+9FwylRm0vgz9qB88=", "dev": true, "requires": {"after": "0.8.1", "arraybuffer.slice": "0.0.6", "base64-arraybuffer": "0.1.5", "blob": "0.0.4", "has-binary": "0.1.6", "wtf-8": "1.0.0"}, "dependencies": {"has-binary": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/has-binary/-/has-binary-0.1.6.tgz", "integrity": "sha1-JTJvOc+k9hath4eJTjryz7x7bhA=", "dev": true, "requires": {"isarray": "0.0.1"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}}}, "ensure-posix-path": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/ensure-posix-path/-/ensure-posix-path-1.0.2.tgz", "integrity": "sha1-pls+QtC3HPxYXrd0+ZQ8jZuRsMI=", "dev": true}, "entities": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/entities/-/entities-1.1.2.tgz", "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==", "dev": true}, "es5-ext": {"version": "0.10.46", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.46.tgz", "integrity": "sha512-24XxRvJXNFwEMpJb3nOkiRJKRoupmjYmOPVlI65Qy2SrtxwOTB+g6ODjBKOtwEHbYrhWRty9xxOWLNdClT2djw==", "dev": true, "requires": {"es6-iterator": "2.0.3", "es6-symbol": "3.1.1", "next-tick": "1.0.0"}}, "es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha1-p96IkUGgWpSwhUQDstCg+/qY87c=", "dev": true, "requires": {"d": "1.0.0", "es5-ext": "0.10.46", "es6-symbol": "3.1.1"}, "dependencies": {"d": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/d/-/d-1.0.0.tgz", "integrity": "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=", "dev": true, "requires": {"es5-ext": "0.10.46"}}}}, "es6-promise": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.5.tgz", "integrity": "sha512-n6wvpdE43VFtJq+lUDYDBFUwV8TZbuGXLV4D6wKafg13ldznKsyEvatubnmUe31zcvelSzOHF+XbaT+Bl9ObDg==", "dev": true, "optional": true}, "es6-symbol": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz", "integrity": "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=", "dev": true, "requires": {"d": "1.0.0", "es5-ext": "0.10.46"}, "dependencies": {"d": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/d/-/d-1.0.0.tgz", "integrity": "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=", "dev": true, "requires": {"es5-ext": "0.10.46"}}}}, "es6-weak-map": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-0.1.4.tgz", "integrity": "sha1-cGzvnpmqI2undmwjnIueKG6n0ig=", "dev": true, "requires": {"d": "0.1.1", "es5-ext": "0.10.46", "es6-iterator": "0.1.3", "es6-symbol": "2.0.1"}, "dependencies": {"es6-iterator": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-0.1.3.tgz", "integrity": "sha1-1vWLjE/EE8JJtLqhl2j45NfIlE4=", "dev": true, "requires": {"d": "0.1.1", "es5-ext": "0.10.46", "es6-symbol": "2.0.1"}}, "es6-symbol": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-2.0.1.tgz", "integrity": "sha1-dhtcZ8/U8dGK+yNPaR1nhoLLO/M=", "dev": true, "requires": {"d": "0.1.1", "es5-ext": "0.10.46"}}}}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "esprima-fb": {"version": "15001.1001.0-dev-harmony-fb", "resolved": "https://registry.npmjs.org/esprima-fb/-/esprima-fb-15001.1001.0-dev-harmony-fb.tgz", "integrity": "sha1-Q761fsJujPI3092LM+QlM1d/Jlk=", "dev": true}, "esutils": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "dev": true}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true}, "event-emitter": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "integrity": "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=", "dev": true, "requires": {"d": "1.0.0", "es5-ext": "0.10.46"}, "dependencies": {"d": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/d/-/d-1.0.0.tgz", "integrity": "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=", "dev": true, "requires": {"es5-ext": "0.10.46"}}}}, "event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "dev": true}, "eventemitter3": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.0.tgz", "integrity": "sha512-ivIvhpq/Y0uSjcHDcOIccjmYjGLcP09MFGE7ysAwkAvkXfpZlC985pH2/ui64DKazbTW/4kN3yqozUxlXzI6cA==", "dev": true}, "events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "dev": true}, "events-to-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/events-to-array/-/events-to-array-1.1.2.tgz", "integrity": "sha1-LUH1Y+H+QA7Uli/hpNXGp1Od9/Y=", "dev": true}, "exec-sh": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/exec-sh/-/exec-sh-0.2.2.tgz", "integrity": "sha512-FIUCJz1RbuS0FKTdaAafAByGS0CPvU3R0MeHxgtl+djzCc//F8HakL8GzmVNZanasTbTAY/3DRFA0KpVqj/eAw==", "dev": true, "requires": {"merge": "1.2.1"}}, "exists-sync": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/exists-sync/-/exists-sync-0.0.3.tgz", "integrity": "sha1-uRAAC+27ETs3i4L19adjgQdiLc8=", "dev": true}, "exit": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true}, "expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "dev": true, "requires": {"is-posix-bracket": "0.1.1"}}, "expand-range": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "integrity": "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=", "dev": true, "requires": {"fill-range": "2.2.4"}}, "express": {"version": "4.16.4", "resolved": "https://registry.npmjs.org/express/-/express-4.16.4.tgz", "integrity": "sha512-j12Uuyb4FMrd/qQAm6uCHAkPtO8FDTRJZBDd5D2KOL2eLaz1yUNdUB/NOIyq0iU4q4cFarsUCrnFDPBcnksuOg==", "dev": true, "requires": {"accepts": "1.3.5", "array-flatten": "1.1.1", "body-parser": "1.18.3", "content-disposition": "0.5.2", "content-type": "1.0.4", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "1.1.2", "encodeurl": "1.0.2", "escape-html": "1.0.3", "etag": "1.8.1", "finalhandler": "1.1.1", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "1.1.2", "on-finished": "2.3.0", "parseurl": "1.3.2", "path-to-regexp": "0.1.7", "proxy-addr": "2.0.4", "qs": "6.5.2", "range-parser": "1.2.0", "safe-buffer": "5.1.2", "send": "0.16.2", "serve-static": "1.13.2", "setprototypeof": "1.1.0", "statuses": "1.4.0", "type-is": "1.6.16", "utils-merge": "1.0.1", "vary": "1.1.2"}, "dependencies": {"finalhandler": {"version": "1.1.1", "resolved": "http://registry.npmjs.org/finalhandler/-/finalhandler-1.1.1.tgz", "integrity": "sha512-Y1GUDo39ez4aHAw7MysnUD5JzYX+WaIj8I57kO3aEPT1fFRL4sr7mjei97FgnwhAyyzRYmQZaTHb2+9uZ1dPtg==", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "1.0.2", "escape-html": "1.0.3", "on-finished": "2.3.0", "parseurl": "1.3.2", "statuses": "1.4.0", "unpipe": "1.0.0"}}, "statuses": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz", "integrity": "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==", "dev": true}}}, "extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "dev": true, "optional": true}, "extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "dev": true, "requires": {"is-extglob": "1.0.0"}}, "extract-zip": {"version": "1.6.7", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.7.tgz", "integrity": "sha1-qEC0uK9kAyZMjbV/Txp0Mz74H+k=", "dev": true, "optional": true, "requires": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.1", "yauzl": "2.4.1"}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true}, "eyes": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz", "integrity": "sha1-Ys8SAjTGg3hdkCNIqADvPgzCC8A=", "dev": true, "optional": true}, "fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true, "optional": true}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I=", "dev": true, "optional": true}, "fast-ordered-set": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/fast-ordered-set/-/fast-ordered-set-1.0.3.tgz", "integrity": "sha1-P7s2Y097555PftvbSjV97iXRhOs=", "dev": true, "requires": {"blank-object": "1.0.2"}}, "fast-sourcemap-concat": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/fast-sourcemap-concat/-/fast-sourcemap-concat-0.2.7.tgz", "integrity": "sha1-tdaKbTPlL50yb+w4uDb6RNmw2Pw=", "dev": true, "requires": {"chalk": "0.5.1", "debug": "2.6.9", "mkdirp": "0.5.1", "rsvp": "3.6.2", "source-map": "0.4.4", "source-map-url": "0.3.0"}, "dependencies": {"ansi-regex": {"version": "0.2.1", "resolved": "http://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha1-DY6UaWej2BQ/k+JOKYUl/BsiNfk=", "dev": true}, "ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha1-6uy/Zs1waIJ2Cy9GkVgrj1XXp94=", "dev": true}, "chalk": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha1-Zjs6ZItotV0EaQ1JFnqoN4WPIXQ=", "dev": true, "requires": {"ansi-styles": "1.1.0", "escape-string-regexp": "1.0.5", "has-ansi": "0.1.0", "strip-ansi": "0.3.0", "supports-color": "0.2.0"}}, "has-ansi": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-0.1.0.tgz", "integrity": "sha1-hPJlqujA5qiKEtcCKJS3VoiUxi4=", "dev": true, "requires": {"ansi-regex": "0.2.1"}}, "source-map": {"version": "0.4.4", "resolved": "http://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "requires": {"amdefine": "1.0.1"}}, "strip-ansi": {"version": "0.3.0", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha1-JfSOoiynkYfzF0pNuHWTR7sSYiA=", "dev": true, "requires": {"ansi-regex": "0.2.1"}}, "supports-color": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "integrity": "sha1-2S3iaU6z9nMjlz1649i1W0wiGQo=", "dev": true}}}, "faye-websocket": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "dev": true, "requires": {"websocket-driver": "0.7.0"}}, "fb-watchman": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.0.tgz", "integrity": "sha1-VOmr99+i8mzZsWNsWIwa/AXeXVg=", "dev": true, "requires": {"bser": "2.0.0"}}, "fd-slicer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "integrity": "sha1-i1vL2ewyfFBBv5qwI/1nUPEXfmU=", "dev": true, "optional": true, "requires": {"pend": "1.2.0"}}, "filename-regex": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz", "integrity": "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=", "dev": true}, "fill-range": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.4.tgz", "integrity": "sha512-cnrcCbj01+j2gTG921VZPnHbjmdAf8oQV/iGeV2kZxGSyfYjjTyY79ErsK1WJWMpw6DaApEX72binqJE+/d+5Q==", "dev": true, "requires": {"is-number": "2.1.0", "isobject": "2.1.0", "randomatic": "3.1.1", "repeat-element": "1.1.3", "repeat-string": "1.6.1"}}, "finalhandler": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz", "integrity": "sha1-zgtoVbRYU+eRsvzGgARtiCU91/U=", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "1.0.2", "escape-html": "1.0.3", "on-finished": "2.3.0", "parseurl": "1.3.2", "statuses": "1.3.1", "unpipe": "1.0.0"}}, "findup": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/findup/-/findup-0.1.5.tgz", "integrity": "sha1-itkpozk7rGJ5V6fl3kYjsGsOLOs=", "dev": true, "requires": {"colors": "0.6.2", "commander": "2.1.0"}, "dependencies": {"commander": {"version": "2.1.0", "resolved": "http://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha1-0SG7roYNmZKj1Re6lvVliOR8Z4E=", "dev": true}}}, "findup-sync": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.2.1.tgz", "integrity": "sha1-4KkKRQB1xJRm7lE3MgV1FLgeh4w=", "dev": true, "requires": {"glob": "4.3.5"}, "dependencies": {"glob": {"version": "4.3.5", "resolved": "https://registry.npmjs.org/glob/-/glob-4.3.5.tgz", "integrity": "sha1-gPuwjKVA8jiszl0R0em8QedRc9M=", "dev": true, "requires": {"inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "2.0.10", "once": "1.4.0"}}, "minimatch": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha1-jQh8OcazjAAbl/ynzm0OHoCvusc=", "dev": true, "requires": {"brace-expansion": "1.1.11"}}}}, "fireworm": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/fireworm/-/fireworm-0.7.1.tgz", "integrity": "sha1-zPIPeUHxCIg/zduZOD2+bhhhx1g=", "dev": true, "requires": {"async": "0.2.10", "is-type": "0.0.1", "lodash.debounce": "3.1.1", "lodash.flatten": "3.0.2", "minimatch": "3.0.4"}, "dependencies": {"async": {"version": "0.2.10", "resolved": "http://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E=", "dev": true}}}, "follow-redirects": {"version": "1.5.10", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz", "integrity": "sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==", "dev": true, "requires": {"debug": "3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "dev": true, "requires": {"ms": "2.0.0"}}}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "for-own": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz", "integrity": "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=", "dev": true, "requires": {"for-in": "1.0.2"}}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true, "optional": true}, "form-data": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "dev": true, "optional": true, "requires": {"asynckit": "0.4.0", "combined-stream": "1.0.7", "mime-types": "2.1.21"}}, "forwarded": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "dev": true}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true}, "fs-extra": {"version": "0.22.1", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.22.1.tgz", "integrity": "sha1-X9b4BJ3JdsoZ6yNV1lgXPKvM4FY=", "dev": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "2.4.0", "rimraf": "2.6.2"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}}}, "fs-monitor-stack": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/fs-monitor-stack/-/fs-monitor-stack-1.1.1.tgz", "integrity": "sha1-xAONWXeTm2tOODltfnzQiVp6xrM=", "dev": true}, "fs-readdir-recursive": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/fs-readdir-recursive/-/fs-readdir-recursive-0.1.2.tgz", "integrity": "sha1-MVtPuMHKW4xH3v7zGdBz2tNWgFk=", "dev": true}, "fs-tree-diff": {"version": "0.5.9", "resolved": "https://registry.npmjs.org/fs-tree-diff/-/fs-tree-diff-0.5.9.tgz", "integrity": "sha512-872G8ax0kHh01m9n/2KDzgYwouKza0Ad9iFltBpNykvROvf2AGtoOzPJgGx125aolGPER3JuC7uZFrQ7bG1AZw==", "dev": true, "requires": {"heimdalljs-logger": "0.1.10", "object-assign": "4.1.1", "path-posix": "1.0.0", "symlink-or-copy": "1.2.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "gauge": {"version": "2.7.4", "resolved": "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz", "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "dev": true, "requires": {"aproba": "1.2.0", "console-control-strings": "1.1.0", "has-unicode": "2.0.1", "object-assign": "4.1.1", "signal-exit": "3.0.2", "string-width": "1.0.2", "strip-ansi": "3.0.1", "wide-align": "1.1.3"}}, "get-stdin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "integrity": "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0"}}, "git-repo-info": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/git-repo-info/-/git-repo-info-1.4.1.tgz", "integrity": "sha1-KgcoIyVKr2L88HZgB9e2ZRvUGUM=", "dev": true}, "git-repo-version": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/git-repo-version/-/git-repo-version-0.3.0.tgz", "integrity": "sha1-ybl9DSHENX1mncEmnCtqddpswOk=", "dev": true, "requires": {"git-repo-info": "1.4.1"}}, "git-tools": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/git-tools/-/git-tools-0.1.4.tgz", "integrity": "sha1-XkPllEO4pd7bOdumY9pJ55+UOXg=", "dev": true, "requires": {"spawnback": "1.0.0"}}, "glob": {"version": "5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "dev": true, "requires": {"inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}, "glob-base": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "integrity": "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=", "dev": true, "requires": {"glob-parent": "2.0.0", "is-glob": "2.0.1"}}, "glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "dev": true, "requires": {"is-glob": "2.0.1"}}, "globals": {"version": "6.4.1", "resolved": "http://registry.npmjs.org/globals/-/globals-6.4.1.tgz", "integrity": "sha1-hJgDKzttHMge68X3lpDY/in6v08=", "dev": true}, "graceful-fs": {"version": "2.0.3", "resolved": "http://registry.npmjs.org/graceful-fs/-/graceful-fs-2.0.3.tgz", "integrity": "sha1-fNLNsiiko/Nule+mzBQt59GhNtA=", "dev": true}, "growly": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/growly/-/growly-1.3.0.tgz", "integrity": "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=", "dev": true}, "handlebars": {"version": "3.0.3", "resolved": "http://registry.npmjs.org/handlebars/-/handlebars-3.0.3.tgz", "integrity": "sha1-DgllGi8Ps8lJFgWDcQ1VH5Lm0q0=", "dev": true, "requires": {"optimist": "0.6.1", "source-map": "0.1.43", "uglify-js": "2.3.6"}}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "dev": true, "optional": true}, "har-validator": {"version": "5.1.3", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.3.tgz", "integrity": "sha512-sNvOCzEQNr/qrvJgc3UG/kD4QtlHycrzwS+6mfTrrSq97BvaYcPZZI1ZSqGSPR73Cxn4LKTD4PttRwfU7jWq5g==", "dev": true, "optional": true, "requires": {"ajv": "6.5.5", "har-schema": "2.0.0"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "requires": {"ansi-regex": "2.1.1"}}, "has-binary": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/has-binary/-/has-binary-0.1.7.tgz", "integrity": "sha1-aOYesWIQyVRaClzOBqhzkS/h5ow=", "dev": true, "requires": {"isarray": "0.0.1"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}}}, "has-color": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/has-color/-/has-color-0.1.7.tgz", "integrity": "sha1-ZxRKUmDDT8PMpnfQQdr1L+e3iy8=", "dev": true}, "has-cors": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-cors/-/has-cors-1.1.0.tgz", "integrity": "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk=", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "dev": true}, "hash-for-dep": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/hash-for-dep/-/hash-for-dep-1.2.3.tgz", "integrity": "sha512-NE//rDaCFpWHViw30YM78OAGBShU+g4dnUGY3UWGyEzPOGYg/ptOjk32nEc+bC1xz+RfK5UIs6lOL6eQdrV4Ow==", "dev": true, "requires": {"broccoli-kitchen-sink-helpers": "0.3.1", "heimdalljs": "0.2.6", "heimdalljs-logger": "0.1.10", "resolve": "1.8.1"}}, "hasha": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/hasha/-/hasha-2.2.0.tgz", "integrity": "sha1-eNfL/B5tZjA/55g3NlmEUXsvbuE=", "dev": true, "optional": true, "requires": {"is-stream": "1.1.0", "pinkie-promise": "2.0.1"}}, "heimdalljs": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/heimdalljs/-/heimdalljs-0.2.6.tgz", "integrity": "sha512-o9bd30+5vLBvBtzCPwwGqpry2+n0Hi6H1+qwt6y+0kwRHGGF8TFIhJPmnuM0xO97zaKrDZMwO/V56fAnn8m/tA==", "dev": true, "requires": {"rsvp": "3.2.1"}, "dependencies": {"rsvp": {"version": "3.2.1", "resolved": "http://registry.npmjs.org/rsvp/-/rsvp-3.2.1.tgz", "integrity": "sha1-B8tKXfJa3Z6Cbrxn3Mn9idsn2Eo=", "dev": true}}}, "heimdalljs-logger": {"version": "0.1.10", "resolved": "https://registry.npmjs.org/heimdalljs-logger/-/heimdalljs-logger-0.1.10.tgz", "integrity": "sha512-pO++cJbhIufVI/fmB/u2Yty3KJD0TqNPecehFae0/eps0hkZ3b4Zc/PezUMOpYuHFQbA7FxHZxa305EhmjLj4g==", "dev": true, "requires": {"debug": "2.6.9", "heimdalljs": "0.2.6"}}, "home-or-tmp": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-1.0.0.tgz", "integrity": "sha1-S58eQIAMPlDGwn94FnavzOcfOYU=", "dev": true, "requires": {"os-tmpdir": "1.0.2", "user-home": "1.1.1"}}, "htmlparser2": {"version": "3.8.3", "resolved": "http://registry.npmjs.org/htmlparser2/-/htmlparser2-3.8.3.tgz", "integrity": "sha1-mWwosZFRaovoZQGn15dX5ccMEGg=", "dev": true, "requires": {"domelementtype": "1.2.1", "domhandler": "2.3.0", "domutils": "1.5.1", "entities": "1.0.0", "readable-stream": "1.1.14"}, "dependencies": {"entities": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/entities/-/entities-1.0.0.tgz", "integrity": "sha1-sph6o4ITR/zeZCsk/fyeT7cSvyY=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "readable-stream": {"version": "1.1.14", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dev": true, "requires": {"core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "0.0.1", "string_decoder": "0.10.31"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}}}, "http-errors": {"version": "1.6.3", "resolved": "http://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "requires": {"depd": "1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": "1.5.0"}, "dependencies": {"statuses": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "dev": true}}}, "http-parser-js": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.0.tgz", "integrity": "sha512-cZdEF7r4gfRIq7ezX9J0T+kQmJNOub71dWbgAXVHDct80TKP4MCETtZQ31xyv38UwgzkWPYF/Xc0ge55dW9Z9w==", "dev": true}, "http-proxy": {"version": "1.17.0", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.17.0.tgz", "integrity": "sha512-Taqn+3nNvYRfJ3bGvKfBSRwy1v6eePlm3oc/aWVxZp57DQr5Eq3xhKJi7Z4hZpS8PC3H4qI+Yly5EmFacGuA/g==", "dev": true, "requires": {"eventemitter3": "3.1.0", "follow-redirects": "1.5.10", "requires-port": "1.0.0"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0", "jsprim": "1.4.1", "sshpk": "1.15.2"}}, "ic-ajax": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/ic-ajax/-/ic-ajax-2.0.2.tgz", "integrity": "sha1-UMirHElDIPrGaDDOHuOZCrgn9ZY=", "dev": true}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "requires": {"safer-buffer": "2.1.2"}}, "ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indexof": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=", "dev": true}, "inflection": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/inflection/-/inflection-1.12.0.tgz", "integrity": "sha1-ogCTVlbW9fa8TcdQLhrstwMihBY=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "1.4.0", "wrappy": "1.0.2"}}, "inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "inquirer": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/inquirer/-/inquirer-0.5.1.tgz", "integrity": "sha1-6fLNHuFyx6MuBUt4oD1N2w13B/E=", "dev": true, "requires": {"async": "0.8.0", "chalk": "0.4.0", "cli-color": "0.3.3", "lodash": "2.4.2", "mute-stream": "0.0.4", "readline2": "0.1.1", "through": "2.3.8"}, "dependencies": {"ansi-styles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz", "integrity": "sha1-yxAt8cVvUSPquLZ817mAJ6AnkXg=", "dev": true}, "async": {"version": "0.8.0", "resolved": "http://registry.npmjs.org/async/-/async-0.8.0.tgz", "integrity": "sha1-7mXsdymML/FFa8RBigUtDwZDURI=", "dev": true}, "chalk": {"version": "0.4.0", "resolved": "http://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha1-UZmj3c0MHv4jvAjBsCewYXbgxk8=", "dev": true, "requires": {"ansi-styles": "1.0.0", "has-color": "0.1.7", "strip-ansi": "0.1.1"}}, "lodash": {"version": "2.4.2", "resolved": "http://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true}, "strip-ansi": {"version": "0.1.1", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz", "integrity": "sha1-OeipjQRNFQZgq+SmgIrPcLt7yZE=", "dev": true}}}, "invert-kv": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY=", "dev": true}, "ipaddr.js": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.8.0.tgz", "integrity": "sha1-6qM9bd16zo9/b+DJygRA5wZzix4=", "dev": true}, "is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "is-dotfile": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz", "integrity": "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=", "dev": true}, "is-equal-shallow": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "integrity": "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=", "dev": true, "requires": {"is-primitive": "2.0.0"}}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=", "dev": true}, "is-finite": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz", "integrity": "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko=", "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "1.0.1"}}, "is-git-url": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/is-git-url/-/is-git-url-0.2.3.tgz", "integrity": "sha1-RFIA1vvW2gKPteAUQNmvyT88y2Q=", "dev": true}, "is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "dev": true, "requires": {"is-extglob": "1.0.0"}}, "is-integer": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/is-integer/-/is-integer-1.0.7.tgz", "integrity": "sha1-a96Bqs3feLZZtmKdYpytxRqIbVw=", "dev": true, "requires": {"is-finite": "1.0.2"}}, "is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "dev": true, "requires": {"kind-of": "3.2.2"}}, "is-posix-bracket": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "integrity": "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=", "dev": true}, "is-primitive": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "integrity": "sha1-IHurkWOEmcB7Kt8kCkGochADRXU=", "dev": true}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true, "optional": true}, "is-type": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/is-type/-/is-type-0.0.1.tgz", "integrity": "sha1-9lHYXDZdRJVdFKUdjXBh8/a0d5w=", "dev": true, "requires": {"core-util-is": "1.0.2"}}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true, "optional": true}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isbinaryfile": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-2.0.4.tgz", "integrity": "sha1-0jWS5qbwk++4TC5hUgVr4pTkFKE=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "istextorbinary": {"version": "2.1.0", "resolved": "http://registry.npmjs.org/istextorbinary/-/istextorbinary-2.1.0.tgz", "integrity": "sha1-2+0qb1G+L3R1to+JRlgRFBt1iHQ=", "dev": true, "requires": {"binaryextensions": "2.1.2", "editions": "1.3.4", "textextensions": "2.4.0"}}, "js-tokens": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-1.0.1.tgz", "integrity": "sha1-zENaXIuUrRWst5gxQPyAGCyJrq4=", "dev": true}, "js-yaml": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.12.0.tgz", "integrity": "sha512-PIt2cnwmPfL4hKNwqeiuz4bKfnzHTBv6HyVgjahA6mPLwPDzjDWrplJBMjHUFxku/N3FlmrbyPclad+I+4mJ3A==", "dev": true, "requires": {"argparse": "1.0.10", "esprima": "4.0.1"}, "dependencies": {"esprima": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "dev": true}}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true}, "jsesc": {"version": "0.5.0", "resolved": "http://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}, "jshint": {"version": "2.9.6", "resolved": "https://registry.npmjs.org/jshint/-/jshint-2.9.6.tgz", "integrity": "sha512-<PERSON><PERSON>9SIAKTlJQOM4lE64GQUtGBRpTOuvbrRrSZw3AhUxMNG266nX9hK2cKA4SBhXOj0irJGyNyGSLT62HGOVDEOA==", "dev": true, "requires": {"cli": "1.0.1", "console-browserify": "1.1.0", "exit": "0.1.2", "htmlparser2": "3.8.3", "lodash": "4.17.11", "minimatch": "3.0.4", "phantom": "4.0.12", "phantomjs-prebuilt": "2.1.16", "shelljs": "0.3.0", "strip-json-comments": "1.0.4", "unicode-5.2.0": "0.7.5"}}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true, "optional": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true, "optional": true}, "json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "dev": true, "requires": {"jsonify": "0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true, "optional": true}, "json3": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/json3/-/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "dev": true}, "json5": {"version": "0.4.0", "resolved": "http://registry.npmjs.org/json5/-/json5-0.4.0.tgz", "integrity": "sha1-BUNS5MTIDIbAkjh31EneF2pzLI0=", "dev": true}, "jsonfile": {"version": "2.4.0", "resolved": "http://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz", "integrity": "sha1-NzaitCi4e72gzIO1P6PWM6NcKug=", "dev": true, "requires": {"graceful-fs": "4.1.15"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true, "optional": true}}}, "jsonify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "dev": true}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "kew": {"version": "0.7.0", "resolved": "http://registry.npmjs.org/kew/-/kew-0.7.0.tgz", "integrity": "sha1-edk9LTM2PW/dKXCzNdkUGtWR15s=", "dev": true, "optional": true}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "1.1.6"}}, "klassy": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/klassy/-/klassy-0.1.3.tgz", "integrity": "sha1-wx1XVtWDGX119YK25pKHK+SXBn8=", "dev": true}, "klaw": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/klaw/-/klaw-1.3.1.tgz", "integrity": "sha1-QIhDO0azsbolnXh4XY6W9zugJDk=", "dev": true, "requires": {"graceful-fs": "4.1.15"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true, "optional": true}}}, "lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "dev": true}, "lcid": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "dev": true, "requires": {"invert-kv": "1.0.0"}}, "leek": {"version": "0.0.18", "resolved": "https://registry.npmjs.org/leek/-/leek-0.0.18.tgz", "integrity": "sha1-ErSgxwqlAOkJm0HHP3O32bI8k9E=", "dev": true, "requires": {"debug": "2.1.1", "lodash-node": "2.4.1", "request": "2.53.0", "rsvp": "3.0.17"}, "dependencies": {"debug": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"ms": "0.6.2"}, "dependencies": {"ms": {"version": "0.6.2", "bundled": true, "dev": true}}}, "lodash-node": {"version": "2.4.1", "bundled": true, "dev": true}, "request": {"version": "2.53.0", "bundled": true, "dev": true, "requires": {"aws-sign2": "0.5.0", "bl": "0.9.4", "caseless": "0.9.0", "combined-stream": "0.0.7", "forever-agent": "0.5.2", "form-data": "0.2.0", "hawk": "2.3.1", "http-signature": "0.10.1", "isstream": "0.1.1", "json-stringify-safe": "5.0.0", "mime-types": "2.0.9", "node-uuid": "1.4.2", "oauth-sign": "0.6.0", "qs": "2.3.3", "stringstream": "0.0.4", "tough-cookie": "0.12.1", "tunnel-agent": "0.4.0"}, "dependencies": {"aws-sign2": {"version": "0.5.0", "bundled": true, "dev": true}, "bl": {"version": "0.9.4", "bundled": true, "dev": true, "requires": {"readable-stream": "1.0.33"}, "dependencies": {"readable-stream": {"version": "1.0.33", "bundled": true, "dev": true, "requires": {"core-util-is": "1.0.1", "inherits": "2.0.1", "isarray": "0.0.1", "string_decoder": "0.10.31"}, "dependencies": {"core-util-is": {"version": "1.0.1", "bundled": true, "dev": true}, "inherits": {"version": "2.0.1", "bundled": true, "dev": true}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}}}}}, "caseless": {"version": "0.9.0", "bundled": true, "dev": true}, "combined-stream": {"version": "0.0.7", "bundled": true, "dev": true, "requires": {"delayed-stream": "0.0.5"}, "dependencies": {"delayed-stream": {"version": "0.0.5", "bundled": true, "dev": true}}}, "forever-agent": {"version": "0.5.2", "bundled": true, "dev": true}, "form-data": {"version": "0.2.0", "bundled": true, "dev": true, "requires": {"async": "0.9.0", "combined-stream": "0.0.7", "mime-types": "2.0.9"}, "dependencies": {"async": {"version": "0.9.0", "bundled": true, "dev": true}}}, "hawk": {"version": "2.3.1", "bundled": true, "dev": true, "requires": {"boom": "2.6.1", "cryptiles": "2.0.4", "hoek": "2.11.1", "sntp": "1.0.9"}, "dependencies": {"boom": {"version": "2.6.1", "bundled": true, "dev": true, "requires": {"hoek": "2.11.1"}}, "cryptiles": {"version": "2.0.4", "bundled": true, "dev": true, "requires": {"boom": "2.6.1"}}, "hoek": {"version": "2.11.1", "bundled": true, "dev": true}, "sntp": {"version": "1.0.9", "bundled": true, "dev": true, "requires": {"hoek": "2.11.1"}}}}, "http-signature": {"version": "0.10.1", "bundled": true, "dev": true, "requires": {"asn1": "0.1.11", "assert-plus": "0.1.5", "ctype": "0.5.3"}, "dependencies": {"asn1": {"version": "0.1.11", "bundled": true, "dev": true}, "assert-plus": {"version": "0.1.5", "bundled": true, "dev": true}, "ctype": {"version": "0.5.3", "bundled": true, "dev": true}}}, "isstream": {"version": "0.1.1", "bundled": true, "dev": true}, "json-stringify-safe": {"version": "5.0.0", "bundled": true, "dev": true}, "mime-types": {"version": "2.0.9", "bundled": true, "dev": true, "requires": {"mime-db": "1.7.0"}, "dependencies": {"mime-db": {"version": "1.7.0", "bundled": true, "dev": true}}}, "node-uuid": {"version": "1.4.2", "bundled": true, "dev": true}, "oauth-sign": {"version": "0.6.0", "bundled": true, "dev": true}, "qs": {"version": "2.3.3", "bundled": true, "dev": true}, "stringstream": {"version": "0.0.4", "bundled": true, "dev": true}, "tough-cookie": {"version": "0.12.1", "bundled": true, "dev": true, "requires": {"punycode": "1.3.2"}, "dependencies": {"punycode": {"version": "1.3.2", "bundled": true, "dev": true}}}, "tunnel-agent": {"version": "0.4.0", "bundled": true, "dev": true}}}, "rsvp": {"version": "3.0.17", "bundled": true, "dev": true}}}, "leven": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/leven/-/leven-1.0.2.tgz", "integrity": "sha1-kUS27ryl8dBoAWnxpncNzqYLdcM=", "dev": true}, "linkify-it": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-1.2.4.tgz", "integrity": "sha1-B3NSbDF8j9E71TTuHRgP+Iq/iBo=", "dev": true, "requires": {"uc.micro": "1.0.5"}}, "livereload-js": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/livereload-js/-/livereload-js-2.4.0.tgz", "integrity": "sha512-XPQH8Z2GDP/Hwz2PCDrh2mth4yFejwA1OZ/81Ti3LgKyhDcEjsSsqFWZojHG0va/duGd+WyosY7eXLDoOyqcPw==", "dev": true}, "lodash": {"version": "4.17.11", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.11.tgz", "integrity": "sha512-cQKh8igo5QUhZ7lg38DYWAxMvjSAKG0A8wGSVimP07SIUEK2UO+arSRKbRZWtelMtN5V0Hkwh5ryOto/SshYIg==", "dev": true}, "lodash-node": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash-node/-/lodash-node-2.4.1.tgz", "integrity": "sha1-6oL3sQDHM9GkKvdoAeUGEF4qgOw=", "dev": true}, "lodash._arraycopy": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._arraycopy/-/lodash._arraycopy-3.0.0.tgz", "integrity": "sha1-due3wfH7klRzdIeKVi7Qaj5Q9uE=", "dev": true}, "lodash._arrayeach": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._arrayeach/-/lodash._arrayeach-3.0.0.tgz", "integrity": "sha1-urFWsqkNPxu9XGU0AzSeXlkz754=", "dev": true}, "lodash._basebind": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._basebind/-/lodash._basebind-2.3.0.tgz", "integrity": "sha1-K1vEUqDhBhQ7IYafIzvbWHQX0kg=", "dev": true, "requires": {"lodash._basecreate": "2.3.0", "lodash._setbinddata": "2.3.0", "lodash.isobject": "2.3.0"}}, "lodash._basecallback": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/lodash._basecallback/-/lodash._basecallback-3.3.1.tgz", "integrity": "sha1-t7K7Q9whYEJKIczybFfkQ3cqjic=", "dev": true, "requires": {"lodash._baseisequal": "3.0.7", "lodash._bindcallback": "3.0.1", "lodash.isarray": "3.0.4", "lodash.pairs": "3.0.1"}}, "lodash._basecopy": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz", "integrity": "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=", "dev": true}, "lodash._basecreate": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-2.3.0.tgz", "integrity": "sha1-m4ioak3P97fzxh2Dovz8BnHsneA=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash.isobject": "2.3.0", "lodash.noop": "2.3.0"}}, "lodash._basecreatecallback": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._basecreatecallback/-/lodash._basecreatecallback-2.3.0.tgz", "integrity": "sha1-N7KrF1kaM56YjbMln81GAZ16w2I=", "dev": true, "requires": {"lodash._setbinddata": "2.3.0", "lodash.bind": "2.3.0", "lodash.identity": "2.3.0", "lodash.support": "2.3.0"}}, "lodash._basecreatewrapper": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._basecreatewrapper/-/lodash._basecreatewrapper-2.3.0.tgz", "integrity": "sha1-qgxhrZYETDkzN2ExSDqXWcNlEkc=", "dev": true, "requires": {"lodash._basecreate": "2.3.0", "lodash._setbinddata": "2.3.0", "lodash._slice": "2.3.0", "lodash.isobject": "2.3.0"}}, "lodash._baseflatten": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/lodash._baseflatten/-/lodash._baseflatten-3.1.4.tgz", "integrity": "sha1-B3D/gBMa9uNPO1EXlqe6UhTmX/c=", "dev": true, "requires": {"lodash.isarguments": "3.1.0", "lodash.isarray": "3.0.4"}}, "lodash._basefor": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash._basefor/-/lodash._basefor-3.0.3.tgz", "integrity": "sha1-dVC06SGO8J+tJDQ7YSAhx5tMIMI=", "dev": true}, "lodash._baseindexof": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash._baseindexof/-/lodash._baseindexof-3.1.0.tgz", "integrity": "sha1-/lK1OhxnYeQmGNZU5KJXie1hgiw=", "dev": true}, "lodash._baseisequal": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/lodash._baseisequal/-/lodash._baseisequal-3.0.7.tgz", "integrity": "sha1-2AJfdjOdKTQnZ9zIh85cuVpbUfE=", "dev": true, "requires": {"lodash.isarray": "3.0.4", "lodash.istypedarray": "3.0.6", "lodash.keys": "3.1.2"}}, "lodash._baseuniq": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash._baseuniq/-/lodash._baseuniq-3.0.3.tgz", "integrity": "sha1-ISP6DbLWnCjVvrHB821hUip0AjQ=", "dev": true, "requires": {"lodash._baseindexof": "3.1.0", "lodash._cacheindexof": "3.0.2", "lodash._createcache": "3.1.2"}}, "lodash._bindcallback": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz", "integrity": "sha1-5THCdkTPi1epnhftlbNcdIeJOS4=", "dev": true}, "lodash._cacheindexof": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/lodash._cacheindexof/-/lodash._cacheindexof-3.0.2.tgz", "integrity": "sha1-PcaayCSY0u5ePOVgkbr9Ktx73pI=", "dev": true}, "lodash._createassigner": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash._createassigner/-/lodash._createassigner-3.1.1.tgz", "integrity": "sha1-g4pbri/aymOsIt7o4Z+k5taXCxE=", "dev": true, "requires": {"lodash._bindcallback": "3.0.1", "lodash._isiterateecall": "3.0.9", "lodash.restparam": "3.6.1"}}, "lodash._createcache": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash._createcache/-/lodash._createcache-3.1.2.tgz", "integrity": "sha1-VtagZAF2JeeevKa4AY4XRAvc8JM=", "dev": true, "requires": {"lodash._getnative": "3.9.1"}}, "lodash._createwrapper": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._createwrapper/-/lodash._createwrapper-2.3.0.tgz", "integrity": "sha1-0arhEC2t9EDo4G/BM6bt1/4UYHU=", "dev": true, "requires": {"lodash._basebind": "2.3.0", "lodash._basecreatewrapper": "2.3.0", "lodash.isfunction": "2.3.0"}}, "lodash._escapehtmlchar": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._escapehtmlchar/-/lodash._escapehtmlchar-2.3.0.tgz", "integrity": "sha1-0D2mvYLu3zjcCltQPXQOzQ6JRZI=", "dev": true, "requires": {"lodash._htmlescapes": "2.3.0"}}, "lodash._escapestringchar": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._escapestringchar/-/lodash._escapestringchar-2.3.0.tgz", "integrity": "sha1-zOc65g/G2lXSv4oGecI8orqxSfw=", "dev": true}, "lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=", "dev": true}, "lodash._htmlescapes": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._htmlescapes/-/lodash._htmlescapes-2.3.0.tgz", "integrity": "sha1-HKmIY8rfH6HYLITzXzHkBVagTzo=", "dev": true}, "lodash._isiterateecall": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz", "integrity": "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=", "dev": true}, "lodash._objecttypes": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._objecttypes/-/lodash._objecttypes-2.3.0.tgz", "integrity": "sha1-aj6jmH3W7rgCGy1cnDA1Scwrrh4=", "dev": true}, "lodash._reinterpolate": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-2.3.0.tgz", "integrity": "sha1-A+6dhcDlXL1ZDXFgiilb3aURKOw=", "dev": true}, "lodash._renative": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._renative/-/lodash._renative-2.3.0.tgz", "integrity": "sha1-d9jt1M7SbdWXH54Vpfdy5OMX+9M=", "dev": true}, "lodash._reunescapedhtml": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._reunescapedhtml/-/lodash._reunescapedhtml-2.3.0.tgz", "integrity": "sha1-25ILVax/P/glk5rOubosIxcT0k0=", "dev": true, "requires": {"lodash._htmlescapes": "2.3.0", "lodash.keys": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "lodash._setbinddata": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._setbinddata/-/lodash._setbinddata-2.3.0.tgz", "integrity": "sha1-5WEEkKzRMnfVmFjZW18nJ/FQjwQ=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash.noop": "2.3.0"}}, "lodash._shimkeys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._shimkeys/-/lodash._shimkeys-2.3.0.tgz", "integrity": "sha1-YR+TFJ4+bHIQlrSHae8pU3rai6k=", "dev": true, "requires": {"lodash._objecttypes": "2.3.0"}}, "lodash._slice": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash._slice/-/lodash._slice-2.3.0.tgz", "integrity": "sha1-FHGYEyhZly5GgMoppZkshVZpqlw=", "dev": true}, "lodash.assignin": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.assignin/-/lodash.assignin-4.2.0.tgz", "integrity": "sha1-uo31+4QesKPoBEIysOJjqNxqKKI=", "dev": true}, "lodash.bind": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.bind/-/lodash.bind-2.3.0.tgz", "integrity": "sha1-wqjhi2jl7MFS4rFoJmEW/qWwFsw=", "dev": true, "requires": {"lodash._createwrapper": "2.3.0", "lodash._renative": "2.3.0", "lodash._slice": "2.3.0"}}, "lodash.clonedeep": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz", "integrity": "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=", "dev": true}, "lodash.debounce": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-3.1.1.tgz", "integrity": "sha1-gSIRw3ipTMKdWqTjNGzwv846ffU=", "dev": true, "requires": {"lodash._getnative": "3.9.1"}}, "lodash.defaults": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-2.3.0.tgz", "integrity": "sha1-qDKwAfE487uXIcKBmip8xa4h7SU=", "dev": true, "requires": {"lodash._objecttypes": "2.3.0", "lodash.keys": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "lodash.escape": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.escape/-/lodash.escape-2.3.0.tgz", "integrity": "sha1-hEw4xY+EThNi6+lnJhWbYs9fKlg=", "dev": true, "requires": {"lodash._escapehtmlchar": "2.3.0", "lodash._reunescapedhtml": "2.3.0", "lodash.keys": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "lodash.find": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.find/-/lodash.find-4.6.0.tgz", "integrity": "sha1-ywcE1Hq3F4n/oN6Ll92Sb7iLE7E=", "dev": true}, "lodash.flatten": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-3.0.2.tgz", "integrity": "sha1-***************************=", "dev": true, "requires": {"lodash._baseflatten": "3.1.4", "lodash._isiterateecall": "3.0.9"}}, "lodash.foreach": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-2.3.0.tgz", "integrity": "sha1-CDQEyR6EbudyRf3512UZxosq8Wg=", "dev": true, "requires": {"lodash._basecreatecallback": "2.3.0", "lodash.forown": "2.3.0"}}, "lodash.forown": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.forown/-/lodash.forown-2.3.0.tgz", "integrity": "sha1-JPtKr4ANRfwtxgv+w84EyDajrX8=", "dev": true, "requires": {"lodash._basecreatecallback": "2.3.0", "lodash._objecttypes": "2.3.0", "lodash.keys": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "lodash.identity": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.identity/-/lodash.identity-2.3.0.tgz", "integrity": "sha1-awGiEMlIU1XCqRO0i2cRIZoXPe0=", "dev": true}, "lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=", "dev": true}, "lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=", "dev": true}, "lodash.isfunction": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.isfunction/-/lodash.isfunction-2.3.0.tgz", "integrity": "sha1-aylz5HpkfPEucNZ2rqE2Q3BuUmc=", "dev": true}, "lodash.isobject": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.isobject/-/lodash.isobject-2.3.0.tgz", "integrity": "sha1-LhbT/Fg9qYMZaJU/LY5tc0NPZ5k=", "dev": true, "requires": {"lodash._objecttypes": "2.3.0"}}, "lodash.isplainobject": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-3.2.0.tgz", "integrity": "sha1-moI4rhayAEMpYM1zRlEtASP79MU=", "dev": true, "requires": {"lodash._basefor": "3.0.3", "lodash.isarguments": "3.1.0", "lodash.keysin": "3.0.8"}}, "lodash.istypedarray": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/lodash.istypedarray/-/lodash.istypedarray-3.0.6.tgz", "integrity": "sha1-yaR3SYYHUB2OhJTSg7h8OSgc72I=", "dev": true}, "lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "dev": true, "requires": {"lodash._getnative": "3.9.1", "lodash.isarguments": "3.1.0", "lodash.isarray": "3.0.4"}}, "lodash.keysin": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/lodash.keysin/-/lodash.keysin-3.0.8.tgz", "integrity": "sha1-IsRJPrvtsUJ5YqVLRFssinZ/tH8=", "dev": true, "requires": {"lodash.isarguments": "3.1.0", "lodash.isarray": "3.0.4"}}, "lodash.merge": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-3.3.2.tgz", "integrity": "sha1-DZDZPtY3sYeEN7s+IWASYNev6ZQ=", "dev": true, "requires": {"lodash._arraycopy": "3.0.0", "lodash._arrayeach": "3.0.0", "lodash._createassigner": "3.1.1", "lodash._getnative": "3.9.1", "lodash.isarguments": "3.1.0", "lodash.isarray": "3.0.4", "lodash.isplainobject": "3.2.0", "lodash.istypedarray": "3.0.6", "lodash.keys": "3.1.2", "lodash.keysin": "3.0.8", "lodash.toplainobject": "3.0.0"}}, "lodash.noop": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.noop/-/lodash.noop-2.3.0.tgz", "integrity": "sha1-MFnWKNUbv5N80qC2/Dp/ISpmnCw=", "dev": true}, "lodash.omit": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.omit/-/lodash.omit-4.5.0.tgz", "integrity": "sha1-brGa5aHuHdnfC5aeZs4Lf6MLXmA=", "dev": true}, "lodash.pairs": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash.pairs/-/lodash.pairs-3.0.1.tgz", "integrity": "sha1-u+CNV4bu6qCaFckevw3LfSvjJqk=", "dev": true, "requires": {"lodash.keys": "3.1.2"}}, "lodash.restparam": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/lodash.restparam/-/lodash.restparam-3.6.1.tgz", "integrity": "sha1-k2pOMJ7zMKdkXtQUWYbIWuWyCAU=", "dev": true}, "lodash.support": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.support/-/lodash.support-2.3.0.tgz", "integrity": "sha1-fq8DivTw1qq3drRKptz8gDNMm/0=", "dev": true, "requires": {"lodash._renative": "2.3.0"}}, "lodash.template": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.template/-/lodash.template-2.3.0.tgz", "integrity": "sha1-Tj4pxDO0z+pnXsg15vEjkcYf0is=", "dev": true, "requires": {"lodash._escapestringchar": "2.3.0", "lodash._reinterpolate": "2.3.0", "lodash.defaults": "2.3.0", "lodash.escape": "2.3.0", "lodash.keys": "2.3.0", "lodash.templatesettings": "2.3.0", "lodash.values": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "lodash.templatesettings": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-2.3.0.tgz", "integrity": "sha1-MD0TLDQnEAQNWhjvqi1XL9A/jNw=", "dev": true, "requires": {"lodash._reinterpolate": "2.3.0", "lodash.escape": "2.3.0"}}, "lodash.toplainobject": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash.toplainobject/-/lodash.toplainobject-3.0.0.tgz", "integrity": "sha1-KHkK2ULSk9eKpmOgfs9/UsoEGY0=", "dev": true, "requires": {"lodash._basecopy": "3.0.1", "lodash.keysin": "3.0.8"}}, "lodash.uniq": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-3.2.2.tgz", "integrity": "sha1-FGw28l510ZUBukAuiLoUk39jzYs=", "dev": true, "requires": {"lodash._basecallback": "3.3.1", "lodash._baseuniq": "3.0.3", "lodash._getnative": "3.9.1", "lodash._isiterateecall": "3.0.9", "lodash.isarray": "3.0.4"}}, "lodash.uniqby": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/lodash.uniqby/-/lodash.uniqby-4.7.0.tgz", "integrity": "sha1-2ZwHpmnp5tJOE2Lf4mbGdhavEwI=", "dev": true}, "lodash.values": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.values/-/lodash.values-2.3.0.tgz", "integrity": "sha1-ypb75gogsLDsK6K6X8anZb0Uo7o=", "dev": true, "requires": {"lodash.keys": "2.3.0"}, "dependencies": {"lodash.keys": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.3.0.tgz", "integrity": "sha1-s1D0+Syqn0WkouzwGEVM8vKK4lM=", "dev": true, "requires": {"lodash._renative": "2.3.0", "lodash._shimkeys": "2.3.0", "lodash.isobject": "2.3.0"}}}}, "longest": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=", "dev": true}, "lru-cache": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.4.tgz", "integrity": "sha512-EPstzZ23znHUVLKj+lcXO1KvZkrlw+ZirdwvOmnAnA/1PB4ggyXJ77LRkCqkff+ShQ+cqoxCxLQOh4cKITO5iA==", "dev": true, "requires": {"pseudomap": "1.0.2", "yallist": "3.0.2"}}, "lru-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/lru-queue/-/lru-queue-0.1.0.tgz", "integrity": "sha1-Jzi9nw089PhEkMVzbEhpmsYyzaM=", "dev": true, "requires": {"es5-ext": "0.10.46"}}, "makeerror": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.11.tgz", "integrity": "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=", "dev": true, "requires": {"tmpl": "1.0.4"}}, "markdown-it": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-4.3.0.tgz", "integrity": "sha1-DuKwckB50Yaz8EtzRc45WuR8xHQ=", "dev": true, "requires": {"argparse": "1.0.10", "entities": "1.1.2", "linkify-it": "1.2.4", "mdurl": "1.0.1", "uc.micro": "1.0.5"}}, "markdown-it-terminal": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/markdown-it-terminal/-/markdown-it-terminal-0.0.2.tgz", "integrity": "sha1-//LEpd8jeABjm0aKX9144hkXxPI=", "dev": true, "requires": {"ansi-styles": "2.2.1", "cardinal": "0.5.0", "cli-table": "0.3.1", "lodash-node": "3.10.2", "markdown-it": "4.3.0"}, "dependencies": {"lodash-node": {"version": "3.10.2", "resolved": "https://registry.npmjs.org/lodash-node/-/lodash-node-3.10.2.tgz", "integrity": "sha1-JZjVsbVOami0y1ROXHMJU8v2Mvc=", "dev": true}}}, "matcher-collection": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/matcher-collection/-/matcher-collection-1.0.5.tgz", "integrity": "sha512-nUCmzKipcJEwYsBVAFh5P+d7JBuhJaW1xs85Hara9xuMLqtCVUrW6DSC0JVIkluxEH2W45nPBM/wjHtBXa/tYA==", "dev": true, "requires": {"minimatch": "3.0.4"}}, "math-random": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/math-random/-/math-random-1.0.1.tgz", "integrity": "sha1-izqsWIuKZuSXXjzepn97sylgH6w=", "dev": true}, "mdurl": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz", "integrity": "sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=", "dev": true}, "media-typer": {"version": "0.3.0", "resolved": "http://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true}, "memoizee": {"version": "0.3.10", "resolved": "https://registry.npmjs.org/memoizee/-/memoizee-0.3.10.tgz", "integrity": "sha1-TsoNiu057J0Bf0xcLy9kMvQuXI8=", "dev": true, "requires": {"d": "0.1.1", "es5-ext": "0.10.46", "es6-weak-map": "0.1.4", "event-emitter": "0.3.5", "lru-queue": "0.1.0", "next-tick": "0.2.2", "timers-ext": "0.1.7"}, "dependencies": {"next-tick": {"version": "0.2.2", "resolved": "http://registry.npmjs.org/next-tick/-/next-tick-0.2.2.tgz", "integrity": "sha1-ddpKkn7liH45BliABltzNkE7MQ0=", "dev": true}}}, "memory-streams": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/memory-streams/-/memory-streams-0.1.3.tgz", "integrity": "sha512-qVQ/CjkMyMInPaaRMrwWNDvf6boRZXaT/DbQeMYcCWuXPEBf1v8qChOc9OlEVQp2uOvRXa1Qu30fLmKhY6NipA==", "dev": true, "requires": {"readable-stream": "1.0.34"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "0.0.1", "string_decoder": "0.10.31"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}}}, "merge": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/merge/-/merge-1.2.1.tgz", "integrity": "sha512-VjFo4P5Whtj4vsLzsYBu5ayHhoHJ0UqNm7ibvShmbmoz7tGi0vXaoJbGdB+GmDMLUdg8DpQXEIeVDAe8MaABvQ==", "dev": true}, "merge-defaults": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/merge-defaults/-/merge-defaults-0.2.1.tgz", "integrity": "sha1-3UIkjrlrtqUVIXJDIccv+Vg93oA=", "dev": true, "requires": {"lodash": "2.4.2"}, "dependencies": {"lodash": {"version": "2.4.2", "resolved": "http://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true}}}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "dev": true}, "merge-trees": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-trees/-/merge-trees-1.0.1.tgz", "integrity": "sha1-zL5nRWl4f53vF/1G5lJfVwC70j4=", "dev": true, "requires": {"can-symlink": "1.0.0", "fs-tree-diff": "0.5.9", "heimdalljs": "0.2.6", "heimdalljs-logger": "0.1.10", "rimraf": "2.6.2", "symlink-or-copy": "1.2.0"}}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true}, "micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "dev": true, "requires": {"arr-diff": "2.0.0", "array-unique": "0.2.1", "braces": "1.8.5", "expand-brackets": "0.1.5", "extglob": "0.3.2", "filename-regex": "2.0.1", "is-extglob": "1.0.0", "is-glob": "2.0.1", "kind-of": "3.2.2", "normalize-path": "2.1.1", "object.omit": "2.0.1", "parse-glob": "3.0.4", "regex-cache": "0.4.4"}}, "mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true}, "mime-db": {"version": "1.37.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.37.0.tgz", "integrity": "sha512-R3C4db6bgQhlIhPU48fUtdVmKnflq+hRdad7IyKhtFj06VPNVdk2RhiYL3UjQIlso8L+YxAtFkobT0VK+S/ybg==", "dev": true}, "mime-types": {"version": "2.1.21", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.21.tgz", "integrity": "sha512-3iL6DbwpyLzjR3xHSFNFeb9Nz/M8WDkX33t1GFQnFOllWk8pOrh/LSrB5OXlnlW5P9LH73X6loW/eogc+F5lJg==", "dev": true, "requires": {"mime-db": "1.37.0"}}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "requires": {"brace-expansion": "1.1.11"}}, "minimist": {"version": "0.0.8", "resolved": "http://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "mkdirp": {"version": "0.5.1", "resolved": "http://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}, "mktemp": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/mktemp/-/mktemp-0.4.0.tgz", "integrity": "sha1-bQUVYRyKjITkhKogABKbmOmB/ws=", "dev": true}, "moment": {"version": "2.22.2", "resolved": "https://registry.npmjs.org/moment/-/moment-2.22.2.tgz", "integrity": "sha1-PCV/mDn8DpP/UxSWMiOeuQeD/2Y=", "dev": true}, "moment-timezone": {"version": "0.3.1", "resolved": "http://registry.npmjs.org/moment-timezone/-/moment-timezone-0.3.1.tgz", "integrity": "sha1-PvR4VrAtU7cYoQpewgI6opnge/U=", "dev": true, "requires": {"moment": "2.22.2"}}, "morgan": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.9.1.tgz", "integrity": "sha512-HQStPIV4y3afTiCYVxirakhlCfGkI161c76kKFca7Fk1JusM//Qeo1ej2XaMniiNeaZklMVrh3vTtIzpzwbpmA==", "dev": true, "requires": {"basic-auth": "2.0.1", "debug": "2.6.9", "depd": "1.1.2", "on-finished": "2.3.0", "on-headers": "1.0.1"}}, "mout": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/mout/-/mout-0.9.1.tgz", "integrity": "sha1-hPDz/WrMcxf2PeKv/cwM7gCbBHc=", "dev": true}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "mustache": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/mustache/-/mustache-2.3.2.tgz", "integrity": "sha512-KpMNwdQsYz3O/SBS1qJ/o3sqUJ5wSb8gb0pul8CO0S56b9Y2ALm8zCfsjPXsqGFfoNBkDwZuZIAjhsZI03gYVQ==", "dev": true}, "mute-stream": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.4.tgz", "integrity": "sha1-qSGZYKbV1dBGWXruUSUsZlX3F34=", "dev": true}, "natives": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/natives/-/natives-1.1.6.tgz", "integrity": "sha512-6+TDFewD4yxY14ptjKaS63GVdtKiES1pTPyxn9Jb0rBqPMZ7VcCiooEhPNsr+mqHtMGxa/5c/HhcC4uPEUw/nA==", "dev": true}, "negotiator": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "dev": true}, "next-tick": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz", "integrity": "sha1-yobR/ogoFpsBICCOPchCS524NCw=", "dev": true}, "node-int64": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node-modules-path": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/node-modules-path/-/node-modules-path-1.0.2.tgz", "integrity": "sha512-6Gbjq+d7uhkO7epaKi5DNgUJn7H0gEyA4Jg0Mo1uQOi3Rk50G83LtmhhFyw0LxnAFhtlspkiiw52ISP13qzcBg==", "dev": true}, "node-notifier": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.3.0.tgz", "integrity": "sha512-AhENzCSGZnZJgBARsUjnQ7DnZbzyP+HxlVXuD0xqAnvL8q+OqtSX7lGg9e8nHzwXkMMXNdVeqq4E2M3EUAqX6Q==", "dev": true, "requires": {"growly": "1.3.0", "semver": "5.6.0", "shellwords": "0.1.1", "which": "1.3.1"}, "dependencies": {"semver": {"version": "5.6.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.6.0.tgz", "integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg==", "dev": true}}}, "node-uuid": {"version": "1.4.8", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.8.tgz", "integrity": "sha1-sEDrCSOWivq/jTL7HxfxFn/auQc=", "dev": true}, "nopt": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha1-xkZdvwirzU2zWTF/eaxopkayj/k=", "dev": true, "requires": {"abbrev": "1.1.1"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "1.1.0"}}, "npm": {"version": "2.14.10", "resolved": "http://registry.npmjs.org/npm/-/npm-2.14.10.tgz", "integrity": "sha1-lll+4eXv7r3PX5G1d2P+muF7mQM=", "dev": true, "requires": {"abbrev": "1.0.7", "ansi": "0.3.0", "ansi-regex": "2.0.0", "ansicolors": "0.3.2", "ansistyles": "0.1.3", "archy": "1.0.0", "async-some": "1.0.2", "block-stream": "0.0.8", "char-spinner": "1.0.1", "chmodr": "1.0.2", "chownr": "1.0.1", "cmd-shim": "2.0.1", "columnify": "1.5.2", "config-chain": "1.1.9", "dezalgo": "1.0.3", "editor": "1.0.0", "fs-vacuum": "1.2.7", "fs-write-stream-atomic": "1.0.4", "fstream": "1.0.8", "fstream-npm": "1.0.7", "github-url-from-git": "1.4.0", "github-url-from-username-repo": "1.0.2", "glob": "5.0.15", "graceful-fs": "4.1.2", "hosted-git-info": "2.1.4", "inflight": "1.0.4", "inherits": "2.0.1", "ini": "1.3.4", "init-package-json": "1.9.1", "lockfile": "1.0.1", "lru-cache": "2.7.0", "minimatch": "3.0.0", "mkdirp": "0.5.1", "node-gyp": "3.0.3", "nopt": "3.0.4", "normalize-git-url": "3.0.1", "normalize-package-data": "2.3.5", "npm-cache-filename": "1.0.2", "npm-install-checks": "1.0.6", "npm-package-arg": "4.0.2", "npm-registry-client": "7.0.7", "npm-user-validate": "0.1.2", "npmlog": "2.0.0", "once": "1.3.2", "opener": "1.4.1", "osenv": "0.1.3", "path-is-inside": "1.0.1", "read": "1.0.7", "read-installed": "4.0.3", "read-package-json": "2.0.2", "readable-stream": "1.1.13", "realize-package-specifier": "3.0.1", "request": "2.65.0", "retry": "0.8.0", "rimraf": "2.4.3", "semver": "5.0.3", "sha": "2.0.1", "slide": "1.1.6", "sorted-object": "1.0.0", "spdx": "0.4.1", "spdx-license-ids": "1.1.0", "strip-ansi": "3.0.0", "tar": "2.2.1", "text-table": "0.2.0", "uid-number": "0.0.6", "umask": "1.1.0", "validate-npm-package-license": "3.0.1", "validate-npm-package-name": "2.2.2", "which": "1.2.0", "wrappy": "1.0.1", "write-file-atomic": "1.1.3"}, "dependencies": {"abbrev": {"version": "1.0.7", "bundled": true, "dev": true}, "ansi": {"version": "0.3.0", "bundled": true, "dev": true}, "ansi-regex": {"version": "2.0.0", "bundled": true, "dev": true}, "ansicolors": {"version": "0.3.2", "bundled": true, "dev": true}, "ansistyles": {"version": "0.1.3", "bundled": true, "dev": true}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "are-we-there-yet": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.1.tgz", "integrity": "sha512-2zuA+jpOYBRgoBCfa+fB87Rk0oGJjDX6pxGzqH6f33NzUhG25Xur6R0u0Z9VVAq8Z5JvQpQI6j6rtonuivC8QA==", "dev": true, "requires": {"delegates": "1.0.0", "readable-stream": "4.4.2"}, "dependencies": {"readable-stream": {"version": "4.4.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.4.2.tgz", "integrity": "sha512-Lk/fICSyIhodxy1IDK2HazkeGjSmezAWX2egdtJnYhtzKEsBPJowlI6F6LPb5tqIQILrMbx22S5o3GuJavPusA==", "dev": true, "requires": {"abort-controller": "3.0.0", "buffer": "6.0.3", "events": "3.3.0", "process": "0.11.10", "string_decoder": "1.3.0"}}}}, "async-some": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"dezalgo": "1.0.3"}}, "block-stream": {"version": "0.0.8", "bundled": true, "dev": true, "requires": {"inherits": "2.0.1"}}, "char-spinner": {"version": "1.0.1", "bundled": true, "dev": true}, "chmodr": {"version": "1.0.2", "bundled": true, "dev": true}, "chownr": {"version": "1.0.1", "bundled": true, "dev": true}, "cmd-shim": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"graceful-fs": "3.0.8", "mkdirp": "0.5.1"}, "dependencies": {"graceful-fs": {"version": "3.0.8", "bundled": true, "dev": true}}}, "columnify": {"version": "1.5.2", "bundled": true, "dev": true, "requires": {"strip-ansi": "3.0.0", "wcwidth": "1.0.0"}, "dependencies": {"wcwidth": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"defaults": "1.0.2"}, "dependencies": {"defaults": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"clone": "0.1.19"}, "dependencies": {"clone": {"version": "0.1.19", "bundled": true, "dev": true}}}}}}}, "config-chain": {"version": "1.1.9", "bundled": true, "dev": true, "requires": {"ini": "1.3.4", "proto-list": "1.2.4"}, "dependencies": {"proto-list": {"version": "1.2.4", "bundled": true, "dev": true}}}, "dezalgo": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"asap": "2.0.3", "wrappy": "1.0.1"}, "dependencies": {"asap": {"version": "2.0.3", "bundled": true, "dev": true}}}, "editor": {"version": "1.0.0", "bundled": true, "dev": true}, "fs-vacuum": {"version": "1.2.7", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.2", "path-is-inside": "1.0.1", "rimraf": "2.4.3"}}, "fs-write-stream-atomic": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.2"}}, "fstream": {"version": "1.0.8", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.2", "inherits": "2.0.1", "mkdirp": "0.5.1", "rimraf": "2.4.3"}}, "fstream-npm": {"version": "1.0.7", "bundled": true, "dev": true, "requires": {"fstream-ignore": "1.0.3", "inherits": "2.0.1"}, "dependencies": {"fstream-ignore": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"fstream": "1.0.8", "inherits": "2.0.1", "minimatch": "3.0.0"}}}}, "gauge": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/gauge/-/gauge-5.0.1.tgz", "integrity": "sha512-CmykPMJGuNan/3S4kZOpvvPYSNqSHANiWnh9XcMU2pSjtBfF0XzZ2p1bFAxTbnFxyBuPxQYHhzwaoOmUdqzvxQ==", "dev": true, "requires": {"aproba": "1.2.0", "color-support": "1.1.3", "console-control-strings": "1.1.0", "has-unicode": "2.0.1", "signal-exit": "4.0.2", "string-width": "4.2.3", "strip-ansi": "6.0.1", "wide-align": "1.1.5"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "requires": {"ansi-regex": "5.0.1"}}}}, "github-url-from-git": {"version": "1.4.0", "bundled": true, "dev": true}, "github-url-from-username-repo": {"version": "1.0.2", "bundled": true, "dev": true}, "glob": {"version": "5.0.15", "bundled": true, "dev": true, "requires": {"inflight": "1.0.4", "inherits": "2.0.1", "minimatch": "3.0.0", "once": "1.3.2", "path-is-absolute": "1.0.0"}, "dependencies": {"path-is-absolute": {"version": "1.0.0", "bundled": true, "dev": true}}}, "graceful-fs": {"version": "4.1.2", "bundled": true, "dev": true}, "hosted-git-info": {"version": "2.1.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"once": "1.3.2", "wrappy": "1.0.1"}}, "inherits": {"version": "2.0.1", "bundled": true, "dev": true}, "ini": {"version": "1.3.4", "bundled": true, "dev": true}, "init-package-json": {"version": "1.9.1", "bundled": true, "dev": true, "requires": {"glob": "5.0.15", "npm-package-arg": "4.0.2", "promzard": "0.3.0", "read": "1.0.7", "read-package-json": "2.0.2", "semver": "5.0.3", "validate-npm-package-license": "3.0.1", "validate-npm-package-name": "2.2.2"}, "dependencies": {"promzard": {"version": "0.3.0", "bundled": true, "dev": true, "requires": {"read": "1.0.7"}}}}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true}, "lockfile": {"version": "1.0.1", "bundled": true, "dev": true}, "lru-cache": {"version": "2.7.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"brace-expansion": "1.1.1"}, "dependencies": {"brace-expansion": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"balanced-match": "0.2.1", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "0.2.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}}}}}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "bundled": true, "dev": true}}}, "node-gyp": {"version": "3.0.3", "bundled": true, "dev": true, "requires": {"fstream": "1.0.8", "glob": "4.5.3", "graceful-fs": "4.1.2", "minimatch": "1.0.0", "mkdirp": "0.5.1", "nopt": "3.0.4", "npmlog": "1.2.1", "osenv": "0.1.3", "path-array": "1.0.0", "request": "2.65.0", "rimraf": "2.4.3", "semver": "5.0.3", "tar": "1.0.3", "which": "1.2.0"}, "dependencies": {"glob": {"version": "4.5.3", "bundled": true, "dev": true, "requires": {"inflight": "1.0.4", "inherits": "2.0.1", "minimatch": "2.0.10", "once": "1.3.2"}, "dependencies": {"minimatch": {"version": "2.0.10", "bundled": true, "dev": true, "requires": {"brace-expansion": "1.1.1"}, "dependencies": {"brace-expansion": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"balanced-match": "0.2.1", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "0.2.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}}}}}}}, "minimatch": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"lru-cache": "2.7.0", "sigmund": "1.0.1"}, "dependencies": {"sigmund": {"version": "1.0.1", "bundled": true, "dev": true}}}, "npmlog": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "are-we-there-yet": "1.0.4", "gauge": "1.2.2"}, "dependencies": {"are-we-there-yet": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"delegates": "0.1.0", "readable-stream": "1.1.13"}, "dependencies": {"delegates": {"version": "0.1.0", "bundled": true, "dev": true}}}, "gauge": {"version": "1.2.2", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "has-unicode": "1.0.1", "lodash.pad": "3.1.1", "lodash.padleft": "3.1.1", "lodash.padright": "3.1.1"}, "dependencies": {"has-unicode": {"version": "1.0.1", "bundled": true, "dev": true}, "lodash.pad": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padleft": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padright": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}}}}}, "path-array": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"array-index": "0.1.1"}, "dependencies": {"array-index": {"version": "0.1.1", "bundled": true, "dev": true, "requires": {"debug": "2.2.0"}, "dependencies": {"debug": {"version": "2.2.0", "bundled": true, "dev": true, "requires": {"ms": "0.7.1"}, "dependencies": {"ms": {"version": "0.7.1", "bundled": true, "dev": true}}}}}}}, "tar": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"block-stream": "0.0.8", "fstream": "1.0.8", "inherits": "2.0.1"}}}}, "nopt": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"abbrev": "1.0.7"}}, "normalize-git-url": {"version": "3.0.1", "bundled": true, "dev": true}, "normalize-package-data": {"version": "2.3.5", "bundled": true, "dev": true, "requires": {"hosted-git-info": "2.1.4", "is-builtin-module": "1.0.0", "semver": "5.0.3", "validate-npm-package-license": "3.0.1"}, "dependencies": {"is-builtin-module": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"builtin-modules": "1.1.0"}, "dependencies": {"builtin-modules": {"version": "1.1.0", "bundled": true, "dev": true}}}}}, "npm-cache-filename": {"version": "1.0.2", "bundled": true, "dev": true}, "npm-install-checks": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"npmlog": "1.2.1", "semver": "5.0.3"}, "dependencies": {"npmlog": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "are-we-there-yet": "1.0.4", "gauge": "1.2.2"}, "dependencies": {"are-we-there-yet": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"delegates": "0.1.0", "readable-stream": "1.1.13"}, "dependencies": {"delegates": {"version": "0.1.0", "bundled": true, "dev": true}}}, "gauge": {"version": "1.2.2", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "has-unicode": "1.0.1", "lodash.pad": "3.1.1", "lodash.padleft": "3.1.1", "lodash.padright": "3.1.1"}, "dependencies": {"has-unicode": {"version": "1.0.1", "bundled": true, "dev": true}, "lodash.pad": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padleft": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padright": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}}}}}}}, "npm-package-arg": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"hosted-git-info": "2.1.4", "semver": "5.0.3"}}, "npm-registry-client": {"version": "7.0.7", "bundled": true, "dev": true, "requires": {"chownr": "1.0.1", "concat-stream": "1.5.0", "graceful-fs": "4.1.2", "mkdirp": "0.5.1", "normalize-package-data": "2.3.5", "npm-package-arg": "4.0.2", "npmlog": "7.0.1", "once": "1.3.2", "request": "2.65.0", "retry": "0.8.0", "rimraf": "2.4.3", "semver": "5.0.3", "slide": "1.1.6"}, "dependencies": {"concat-stream": {"version": "1.5.0", "bundled": true, "dev": true, "requires": {"inherits": "2.0.1", "readable-stream": "2.0.2", "typedarray": "0.0.6"}, "dependencies": {"readable-stream": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"core-util-is": "1.0.1", "inherits": "2.0.1", "isarray": "0.0.1", "process-nextick-args": "1.0.3", "string_decoder": "0.10.31", "util-deprecate": "1.0.1"}, "dependencies": {"core-util-is": {"version": "1.0.1", "bundled": true, "dev": true}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "process-nextick-args": {"version": "1.0.3", "bundled": true, "dev": true}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}, "util-deprecate": {"version": "1.0.1", "bundled": true, "dev": true}}}, "typedarray": {"version": "0.0.6", "bundled": true, "dev": true}}}, "npmlog": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-7.0.1.tgz", "integrity": "sha512-uJ0YFk/mCQpLBt+bxN88AKd+gyqZvZDbtiNxk6Waqcj2aPRyfVx8ITawkyQynxUagInjdYT1+qj4NfA5KJJUxg==", "dev": true, "requires": {"are-we-there-yet": "4.0.1", "console-control-strings": "1.1.0", "gauge": "5.0.1", "set-blocking": "2.0.0"}}}}, "npm-user-validate": {"version": "0.1.2", "bundled": true, "dev": true}, "npmlog": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "are-we-there-yet": "1.0.4", "gauge": "1.2.2"}, "dependencies": {"are-we-there-yet": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"delegates": "0.1.0", "readable-stream": "1.1.13"}, "dependencies": {"delegates": {"version": "0.1.0", "bundled": true, "dev": true}}}, "gauge": {"version": "1.2.2", "bundled": true, "dev": true, "requires": {"ansi": "0.3.0", "has-unicode": "1.0.1", "lodash.pad": "3.1.1", "lodash.padleft": "3.1.1", "lodash.padright": "3.1.1"}, "dependencies": {"has-unicode": {"version": "1.0.1", "bundled": true, "dev": true}, "lodash.pad": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padleft": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}, "lodash.padright": {"version": "3.1.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1", "lodash._createpadding": "3.6.1"}, "dependencies": {"lodash._basetostring": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._createpadding": {"version": "3.6.1", "bundled": true, "dev": true, "requires": {"lodash.repeat": "3.0.1"}, "dependencies": {"lodash.repeat": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"lodash._basetostring": "3.0.1"}}}}}}}}}}, "once": {"version": "1.3.2", "bundled": true, "dev": true, "requires": {"wrappy": "1.0.1"}}, "opener": {"version": "1.4.1", "bundled": true, "dev": true}, "osenv": {"version": "0.1.3", "bundled": true, "dev": true, "requires": {"os-homedir": "1.0.0", "os-tmpdir": "1.0.1"}, "dependencies": {"os-homedir": {"version": "1.0.0", "bundled": true, "dev": true}, "os-tmpdir": {"version": "1.0.1", "bundled": true, "dev": true}}}, "path-is-inside": {"version": "1.0.1", "bundled": true, "dev": true}, "read": {"version": "1.0.7", "bundled": true, "dev": true, "requires": {"mute-stream": "0.0.5"}, "dependencies": {"mute-stream": {"version": "0.0.5", "bundled": true, "dev": true}}}, "read-installed": {"version": "4.0.3", "bundled": true, "dev": true, "requires": {"debuglog": "1.0.1", "graceful-fs": "4.1.2", "read-package-json": "2.0.2", "readdir-scoped-modules": "1.0.2", "semver": "5.0.3", "slide": "1.1.6", "util-extend": "1.0.1"}, "dependencies": {"debuglog": {"version": "1.0.1", "bundled": true, "dev": true}, "readdir-scoped-modules": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"debuglog": "1.0.1", "dezalgo": "1.0.3", "graceful-fs": "4.1.2", "once": "1.3.2"}}, "util-extend": {"version": "1.0.1", "bundled": true, "dev": true}}}, "read-package-json": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"glob": "5.0.15", "graceful-fs": "4.1.2", "json-parse-helpfulerror": "1.0.3", "normalize-package-data": "2.3.5"}, "dependencies": {"json-parse-helpfulerror": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"jju": "1.2.1"}, "dependencies": {"jju": {"version": "1.2.1", "bundled": true, "dev": true}}}}}, "readable-stream": {"version": "1.1.13", "bundled": true, "dev": true, "requires": {"core-util-is": "1.0.1", "inherits": "2.0.1", "isarray": "0.0.1", "string_decoder": "0.10.31"}, "dependencies": {"core-util-is": {"version": "1.0.1", "bundled": true, "dev": true}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}}}, "realize-package-specifier": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"dezalgo": "1.0.3", "npm-package-arg": "4.0.2"}}, "request": {"version": "2.65.0", "bundled": true, "dev": true, "requires": {"aws-sign2": "0.6.0", "bl": "1.0.0", "caseless": "0.11.0", "combined-stream": "1.0.5", "extend": "3.0.0", "forever-agent": "0.6.1", "form-data": "1.0.0-rc3", "har-validator": "2.0.2", "hawk": "3.1.0", "http-signature": "0.11.0", "isstream": "0.1.2", "json-stringify-safe": "5.0.1", "mime-types": "2.1.7", "node-uuid": "1.4.3", "oauth-sign": "0.8.0", "qs": "5.2.0", "stringstream": "0.0.5", "tough-cookie": "2.2.0", "tunnel-agent": "0.4.1"}, "dependencies": {"aws-sign2": {"version": "0.6.0", "bundled": true, "dev": true}, "bl": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"readable-stream": "2.0.3"}, "dependencies": {"readable-stream": {"version": "2.0.3", "bundled": true, "dev": true, "requires": {"core-util-is": "1.0.1", "inherits": "2.0.1", "isarray": "0.0.1", "process-nextick-args": "1.0.3", "string_decoder": "0.10.31", "util-deprecate": "1.0.2"}, "dependencies": {"core-util-is": {"version": "1.0.1", "bundled": true, "dev": true}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "process-nextick-args": {"version": "1.0.3", "bundled": true, "dev": true}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}, "util-deprecate": {"version": "1.0.2", "bundled": true, "dev": true}}}}}, "caseless": {"version": "0.11.0", "bundled": true, "dev": true}, "combined-stream": {"version": "1.0.5", "bundled": true, "dev": true, "requires": {"delayed-stream": "1.0.0"}, "dependencies": {"delayed-stream": {"version": "1.0.0", "bundled": true, "dev": true}}}, "extend": {"version": "3.0.0", "bundled": true, "dev": true}, "forever-agent": {"version": "0.6.1", "bundled": true, "dev": true}, "form-data": {"version": "1.0.0-rc3", "bundled": true, "dev": true, "requires": {"async": "1.5.0", "combined-stream": "1.0.5", "mime-types": "2.1.7"}, "dependencies": {"async": {"version": "1.5.0", "bundled": true, "dev": true}}}, "har-validator": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"chalk": "1.1.1", "commander": "2.9.0", "is-my-json-valid": "2.12.2", "pinkie-promise": "1.0.0"}, "dependencies": {"chalk": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"ansi-styles": "2.1.0", "escape-string-regexp": "1.0.3", "has-ansi": "2.0.0", "strip-ansi": "3.0.0", "supports-color": "2.0.0"}, "dependencies": {"ansi-styles": {"version": "2.1.0", "bundled": true, "dev": true}, "escape-string-regexp": {"version": "1.0.3", "bundled": true, "dev": true}, "has-ansi": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "2.0.0"}}, "supports-color": {"version": "2.0.0", "bundled": true, "dev": true}}}, "commander": {"version": "2.9.0", "bundled": true, "dev": true, "requires": {"graceful-readlink": "1.0.1"}, "dependencies": {"graceful-readlink": {"version": "1.0.1", "bundled": true, "dev": true}}}, "is-my-json-valid": {"version": "2.12.2", "bundled": true, "dev": true, "requires": {"generate-function": "2.0.0", "generate-object-property": "1.2.0", "jsonpointer": "2.0.0", "xtend": "4.0.0"}, "dependencies": {"generate-function": {"version": "2.0.0", "bundled": true, "dev": true}, "generate-object-property": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"is-property": "1.0.2"}, "dependencies": {"is-property": {"version": "1.0.2", "bundled": true, "dev": true}}}, "jsonpointer": {"version": "2.0.0", "bundled": true, "dev": true}, "xtend": {"version": "4.0.0", "bundled": true, "dev": true}}}, "pinkie-promise": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"pinkie": "1.0.0"}, "dependencies": {"pinkie": {"version": "1.0.0", "bundled": true, "dev": true}}}}}, "hawk": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"boom": "2.10.0", "cryptiles": "2.0.5", "hoek": "2.16.3", "sntp": "1.0.9"}, "dependencies": {"boom": {"version": "2.10.0", "bundled": true, "dev": true, "requires": {"hoek": "2.16.3"}}, "cryptiles": {"version": "2.0.5", "bundled": true, "dev": true, "requires": {"boom": "2.10.0"}}, "hoek": {"version": "2.16.3", "bundled": true, "dev": true}, "sntp": {"version": "1.0.9", "bundled": true, "dev": true, "requires": {"hoek": "2.16.3"}}}}, "http-signature": {"version": "0.11.0", "bundled": true, "dev": true, "requires": {"asn1": "0.1.11", "assert-plus": "0.1.5", "ctype": "0.5.3"}, "dependencies": {"asn1": {"version": "0.1.11", "bundled": true, "dev": true}, "assert-plus": {"version": "0.1.5", "bundled": true, "dev": true}, "ctype": {"version": "0.5.3", "bundled": true, "dev": true}}}, "isstream": {"version": "0.1.2", "bundled": true, "dev": true}, "json-stringify-safe": {"version": "5.0.1", "bundled": true, "dev": true}, "mime-types": {"version": "2.1.7", "bundled": true, "dev": true, "requires": {"mime-db": "1.19.0"}, "dependencies": {"mime-db": {"version": "1.19.0", "bundled": true, "dev": true}}}, "node-uuid": {"version": "1.4.3", "bundled": true, "dev": true}, "oauth-sign": {"version": "0.8.0", "bundled": true, "dev": true}, "qs": {"version": "5.2.0", "bundled": true, "dev": true}, "stringstream": {"version": "0.0.5", "bundled": true, "dev": true}, "tough-cookie": {"version": "2.2.0", "bundled": true, "dev": true}, "tunnel-agent": {"version": "0.4.1", "bundled": true, "dev": true}}}, "retry": {"version": "0.8.0", "bundled": true, "dev": true}, "rimraf": {"version": "2.4.3", "bundled": true, "dev": true, "requires": {"glob": "5.0.15"}}, "safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true}, "semver": {"version": "5.0.3", "bundled": true, "dev": true}, "sha": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.2", "readable-stream": "2.0.2"}, "dependencies": {"readable-stream": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"core-util-is": "1.0.1", "inherits": "2.0.1", "isarray": "0.0.1", "process-nextick-args": "1.0.3", "string_decoder": "0.10.31", "util-deprecate": "1.0.1"}, "dependencies": {"core-util-is": {"version": "1.0.1", "bundled": true, "dev": true}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "process-nextick-args": {"version": "1.0.3", "bundled": true, "dev": true}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}, "util-deprecate": {"version": "1.0.1", "bundled": true, "dev": true}}}}}, "signal-exit": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.0.2.tgz", "integrity": "sha512-MY2/qGx4enyjprQnFaZsHib3Yadh3IXyV2C321GY0pjGfVBu4un0uDJkwgdxqO+Rdx8JMT8IfJIRwbYVz3Ob3Q==", "dev": true}, "slide": {"version": "1.1.6", "bundled": true, "dev": true}, "sorted-object": {"version": "1.0.0", "bundled": true, "dev": true}, "spdx": {"version": "0.4.1", "bundled": true, "dev": true, "requires": {"spdx-license-ids": "1.1.0"}}, "spdx-license-ids": {"version": "1.1.0", "bundled": true, "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "requires": {"emoji-regex": "8.0.0", "is-fullwidth-code-point": "3.0.0", "strip-ansi": "6.0.1"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "requires": {"ansi-regex": "5.0.1"}}}}, "string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dev": true, "requires": {"safe-buffer": "5.2.1"}}, "strip-ansi": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "2.0.0"}}, "tar": {"version": "2.2.1", "bundled": true, "dev": true, "requires": {"block-stream": "0.0.8", "fstream": "1.0.8", "inherits": "2.0.1"}}, "text-table": {"version": "0.2.0", "bundled": true, "dev": true}, "uid-number": {"version": "0.0.6", "bundled": true, "dev": true}, "umask": {"version": "1.1.0", "bundled": true, "dev": true}, "validate-npm-package-license": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"spdx-correct": "1.0.1", "spdx-expression-parse": "1.0.0"}, "dependencies": {"spdx-correct": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"spdx-license-ids": "1.1.0"}}, "spdx-expression-parse": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"spdx-exceptions": "1.0.2", "spdx-license-ids": "1.1.0"}, "dependencies": {"spdx-exceptions": {"version": "1.0.2", "bundled": true, "dev": true}}}}}, "validate-npm-package-name": {"version": "2.2.2", "bundled": true, "dev": true, "requires": {"builtins": "0.0.7"}, "dependencies": {"builtins": {"version": "0.0.7", "bundled": true, "dev": true}}}, "which": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"is-absolute": "0.1.7"}, "dependencies": {"is-absolute": {"version": "0.1.7", "bundled": true, "dev": true, "requires": {"is-relative": "0.1.3"}, "dependencies": {"is-relative": {"version": "0.1.3", "bundled": true, "dev": true}}}}}, "wide-align": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "dev": true, "requires": {"string-width": "4.2.3"}}, "wrappy": {"version": "1.0.1", "bundled": true, "dev": true}, "write-file-atomic": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"graceful-fs": "4.1.2", "slide": "1.1.6"}}}}, "npmlog": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "dev": true, "requires": {"are-we-there-yet": "1.1.5", "console-control-strings": "1.1.0", "gauge": "2.7.4", "set-blocking": "2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true}, "oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object-component": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/object-component/-/object-component-0.0.3.tgz", "integrity": "sha1-8MaapQ78lbhmwYb0AKM3acsvEpE=", "dev": true}, "object.omit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "integrity": "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=", "dev": true, "requires": {"for-own": "0.1.5", "is-extendable": "0.1.1"}}, "on-finished": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dev": true, "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz", "integrity": "sha1-ko9dD0cNSTQmUepnlLCFfBAGk/c=", "dev": true}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1.0.2"}}, "optimist": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz", "integrity": "sha1-2j6nRob6IaGaERwybpDrFaAZZoY=", "dev": true, "requires": {"minimist": "0.0.8", "wordwrap": "0.0.3"}}, "options": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/options/-/options-0.0.6.tgz", "integrity": "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=", "dev": true}, "os-homedir": {"version": "1.0.2", "resolved": "http://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true}, "os-locale": {"version": "1.4.0", "resolved": "http://registry.npmjs.org/os-locale/-/os-locale-1.4.0.tgz", "integrity": "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=", "dev": true, "requires": {"lcid": "1.0.0"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "osenv": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/osenv/-/osenv-0.0.3.tgz", "integrity": "sha1-zWrY3bKQkVrZ4idlV2Al1BHynLY=", "dev": true}, "output-file-sync": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/output-file-sync/-/output-file-sync-1.1.2.tgz", "integrity": "sha1-0KM+7+YaIF+suQCS6CZZjVJFznY=", "dev": true, "requires": {"graceful-fs": "4.1.15", "mkdirp": "0.5.1", "object-assign": "4.1.1"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}}}, "parse-glob": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "integrity": "sha1-ssN2z7EfNVE7rdFz7wu246OIORw=", "dev": true, "requires": {"glob-base": "0.3.0", "is-dotfile": "1.0.3", "is-extglob": "1.0.0", "is-glob": "2.0.1"}}, "parsejson": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/parsejson/-/parsejson-0.0.3.tgz", "integrity": "sha1-q343WfIJ7OmUN5c/fQ8fZK4OZKs=", "dev": true, "requires": {"better-assert": "1.0.2"}}, "parseqs": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/parseqs/-/parseqs-0.0.5.tgz", "integrity": "sha1-1SCKNzjkZ2bikbouoXNoSSGouJ0=", "dev": true, "requires": {"better-assert": "1.0.2"}}, "parseuri": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/parseuri/-/parseuri-0.0.5.tgz", "integrity": "sha1-gCBKUNTbt3m/3G6+J3jZDkvOMgo=", "dev": true, "requires": {"better-assert": "1.0.2"}}, "parseurl": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.2.tgz", "integrity": "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M=", "dev": true}, "path-exists": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-1.0.0.tgz", "integrity": "sha1-1aiZjrce83p0w06w2eum6HjuoIE=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-parse": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==", "dev": true}, "path-posix": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/path-posix/-/path-posix-1.0.0.tgz", "integrity": "sha1-BrJhE/Vr6rBCVFojv6iAA8ysJg8=", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "dev": true}, "pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA=", "dev": true, "optional": true}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "dev": true, "optional": true}, "phantom": {"version": "4.0.12", "resolved": "https://registry.npmjs.org/phantom/-/phantom-4.0.12.tgz", "integrity": "sha512-Tz82XhtPmwCk1FFPmecy7yRGZG2btpzY2KI9fcoPT7zT9det0CcMyfBFPp1S8DqzsnQnm8ZYEfdy528mwVtksA==", "dev": true, "optional": true, "requires": {"phantomjs-prebuilt": "2.1.16", "split": "1.0.1", "winston": "2.4.4"}}, "phantomjs-prebuilt": {"version": "2.1.16", "resolved": "https://registry.npmjs.org/phantomjs-prebuilt/-/phantomjs-prebuilt-2.1.16.tgz", "integrity": "sha1-79ISpKOWbTZHaE6ouniFSb4q7+8=", "dev": true, "optional": true, "requires": {"es6-promise": "4.2.5", "extract-zip": "1.6.7", "fs-extra": "1.0.0", "hasha": "2.2.0", "kew": "0.7.0", "progress": "1.1.8", "request": "2.88.0", "request-progress": "2.0.1", "which": "1.3.1"}, "dependencies": {"fs-extra": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-1.0.0.tgz", "integrity": "sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA=", "dev": true, "optional": true, "requires": {"graceful-fs": "4.1.15", "jsonfile": "2.4.0", "klaw": "1.3.1"}}, "graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true, "optional": true}}}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true, "optional": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "optional": true, "requires": {"pinkie": "2.0.4"}}, "pleasant-progress": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/pleasant-progress/-/pleasant-progress-1.1.0.tgz", "integrity": "sha1-yZzXMKLlDP/dO63/hF/E1SguJms=", "dev": true}, "portfinder": {"version": "0.4.0", "resolved": "http://registry.npmjs.org/portfinder/-/portfinder-0.4.0.tgz", "integrity": "sha1-o/+t/6/k+5jgYBqF7aJ8J86Eyh4=", "dev": true, "requires": {"async": "0.9.0", "mkdirp": "0.5.1"}, "dependencies": {"async": {"version": "0.9.0", "resolved": "http://registry.npmjs.org/async/-/async-0.9.0.tgz", "integrity": "sha1-rDYTsdqb7RtHUQu0ZRuJMeRxRsc=", "dev": true}}}, "preserve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "integrity": "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=", "dev": true}, "printf": {"version": "0.2.5", "resolved": "http://registry.npmjs.org/printf/-/printf-0.2.5.tgz", "integrity": "sha1-xDjKLKM+OSdnHbSracDlL5NqTw8=", "dev": true}, "private": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/private/-/private-0.1.8.tgz", "integrity": "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==", "dev": true}, "process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "dev": true}, "process-nextick-args": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.0.tgz", "integrity": "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw==", "dev": true}, "process-relative-require": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/process-relative-require/-/process-relative-require-1.0.0.tgz", "integrity": "sha1-FZDfz1uPKYO6U+OYRGtoJAtMxoo=", "dev": true, "requires": {"node-modules-path": "1.0.2"}}, "progress": {"version": "1.1.8", "resolved": "http://registry.npmjs.org/progress/-/progress-1.1.8.tgz", "integrity": "sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74=", "dev": true, "optional": true}, "promise-map-series": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/promise-map-series/-/promise-map-series-0.2.3.tgz", "integrity": "sha1-wtN3r8kyU/a9A9u3d1XriKsgqEc=", "dev": true, "requires": {"rsvp": "3.6.2"}}, "proxy-addr": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.4.tgz", "integrity": "sha512-5erio2h9jp5CHGwcybmxmVqHmnCBZeewlfJ0pex+UW7Qny7OOZXTtH56TGNyBizkgiOwhJtMKrVzDTeKcySZwA==", "dev": true, "requires": {"forwarded": "0.1.2", "ipaddr.js": "1.8.0"}}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "psl": {"version": "1.1.29", "resolved": "https://registry.npmjs.org/psl/-/psl-1.1.29.tgz", "integrity": "sha512-AeUmQ0oLN02flVHXWh9sSJF7mcdFq0ppid/JkErufc3hGIV/AMa8Fo9VgDo/cT2jFdOWoFvHp90qqBH54W+gjQ==", "dev": true, "optional": true}, "punycode": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "dev": true, "optional": true}, "q": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "integrity": "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=", "dev": true}, "qs": {"version": "6.5.2", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz", "integrity": "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==", "dev": true}, "quick-temp": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/quick-temp/-/quick-temp-0.1.8.tgz", "integrity": "sha1-urAqJCq4+w3XWKPJd2sy+aXZRAg=", "dev": true, "requires": {"mktemp": "0.4.0", "rimraf": "2.6.2", "underscore.string": "3.3.5"}}, "qunitjs": {"version": "1.23.1", "resolved": "https://registry.npmjs.org/qunitjs/-/qunitjs-1.23.1.tgz", "integrity": "sha1-GXHPl6yb4Bpk0jFVCNLkjm/U5xk=", "dev": true}, "randomatic": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-3.1.1.tgz", "integrity": "sha512-TuDE5KxZ0J461RVjrJZCJc+J+zCkTb1MbH9AQUq68sMhOMcy9jLcb3BrZKgp9q9Ncltdg4QVqWrH02W2EFFVYw==", "dev": true, "requires": {"is-number": "4.0.0", "kind-of": "6.0.2", "math-random": "1.0.1"}, "dependencies": {"is-number": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "integrity": "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ==", "dev": true}, "kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA==", "dev": true}}}, "range-parser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4=", "dev": true}, "raw-body": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.3.tgz", "integrity": "sha512-9esiElv1BrZoI3rCDuOuKCBRbuApGGaDPQfjSflGxdy4oyzqghxu6klEkkVIvBje+FF0BX9coEv8KqW6X/7njw==", "dev": true, "requires": {"bytes": "3.0.0", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "unpipe": "1.0.0"}, "dependencies": {"iconv-lite": {"version": "0.4.23", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "dev": true, "requires": {"safer-buffer": "2.1.2"}}}}, "readable-stream": {"version": "2.3.6", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "dev": true, "requires": {"core-util-is": "1.0.2", "inherits": "2.0.3", "isarray": "1.0.0", "process-nextick-args": "2.0.0", "safe-buffer": "5.1.2", "string_decoder": "1.1.1", "util-deprecate": "1.0.2"}}, "readline2": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/readline2/-/readline2-0.1.1.tgz", "integrity": "sha1-mUQ7pug7gw7zBRv9fcJBqCco1Wg=", "dev": true, "requires": {"mute-stream": "0.0.4", "strip-ansi": "2.0.1"}, "dependencies": {"ansi-regex": {"version": "1.1.1", "resolved": "http://registry.npmjs.org/ansi-regex/-/ansi-regex-1.1.1.tgz", "integrity": "sha1-QchHGUZGN15qGl0Qw8oFTvn8mA0=", "dev": true}, "strip-ansi": {"version": "2.0.1", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-2.0.1.tgz", "integrity": "sha1-32LBqpTtLxFOHQ8h/R1QSCt5pg4=", "dev": true, "requires": {"ansi-regex": "1.1.1"}}}}, "recast": {"version": "0.10.33", "resolved": "http://registry.npmjs.org/recast/-/recast-0.10.33.tgz", "integrity": "sha1-lCgI96oBbx+nFCxGHX5XBKqo1pc=", "dev": true, "requires": {"ast-types": "0.8.12", "esprima-fb": "15001.1001.0-dev-harmony-fb", "private": "0.1.8", "source-map": "0.5.7"}, "dependencies": {"ast-types": {"version": "0.8.12", "resolved": "http://registry.npmjs.org/ast-types/-/ast-types-0.8.12.tgz", "integrity": "sha1-oNkOQ1G7iHcWyD/WN+v4GK9K38w=", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "redeyed": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/redeyed/-/redeyed-0.5.0.tgz", "integrity": "sha1-erAA5g7jh1rBFdKe2zLBQDxsJdE=", "dev": true, "requires": {"esprima-fb": "12001.1.0-dev-harmony-fb"}, "dependencies": {"esprima-fb": {"version": "12001.1.0-dev-harmony-fb", "resolved": "https://registry.npmjs.org/esprima-fb/-/esprima-fb-12001.1.0-dev-harmony-fb.tgz", "integrity": "sha1-2EQAOEupXOJnjGF60kp/QICNqRU=", "dev": true}}}, "regenerate": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz", "integrity": "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg==", "dev": true}, "regenerator": {"version": "0.8.40", "resolved": "http://registry.npmjs.org/regenerator/-/regenerator-0.8.40.tgz", "integrity": "sha1-oORXxY69uuV1yfjNdRJ+k3VkNdg=", "dev": true, "requires": {"commoner": "0.10.8", "defs": "1.1.1", "esprima-fb": "15001.1001.0-dev-harmony-fb", "private": "0.1.8", "recast": "0.10.33", "through": "2.3.8"}}, "regex-cache": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "dev": true, "requires": {"is-equal-shallow": "0.1.3"}}, "regexpu": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/regexpu/-/regexpu-1.3.0.tgz", "integrity": "sha1-5TTcmRqeWEYFDJjebX3UpVyeoW0=", "dev": true, "requires": {"esprima": "2.7.3", "recast": "0.10.33", "regenerate": "1.4.0", "regjsgen": "0.2.0", "regjsparser": "0.1.5"}, "dependencies": {"esprima": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "integrity": "sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE=", "dev": true}}}, "regjsgen": {"version": "0.2.0", "resolved": "http://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz", "integrity": "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=", "dev": true}, "regjsparser": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz", "integrity": "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=", "dev": true, "requires": {"jsesc": "0.5.0"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g==", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "repeating": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/repeating/-/repeating-1.1.3.tgz", "integrity": "sha1-PUEUIYh3U3SU+X93+Xhfq4EPpKw=", "dev": true, "requires": {"is-finite": "1.0.2"}}, "request": {"version": "2.88.0", "resolved": "https://registry.npmjs.org/request/-/request-2.88.0.tgz", "integrity": "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==", "dev": true, "optional": true, "requires": {"aws-sign2": "0.7.0", "aws4": "1.8.0", "caseless": "0.12.0", "combined-stream": "1.0.7", "extend": "3.0.2", "forever-agent": "0.6.1", "form-data": "2.3.3", "har-validator": "5.1.3", "http-signature": "1.2.0", "is-typedarray": "1.0.0", "isstream": "0.1.2", "json-stringify-safe": "5.0.1", "mime-types": "2.1.21", "oauth-sign": "0.9.0", "performance-now": "2.1.0", "qs": "6.5.2", "safe-buffer": "5.1.2", "tough-cookie": "2.4.3", "tunnel-agent": "0.6.0", "uuid": "3.3.2"}, "dependencies": {"uuid": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==", "dev": true, "optional": true}}}, "request-progress": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/request-progress/-/request-progress-2.0.1.tgz", "integrity": "sha1-XTa7V5YcZzqlt4jbyBQf3yO0Tgg=", "dev": true, "optional": true, "requires": {"throttleit": "1.0.0"}}, "requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true}, "resolve": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.8.1.tgz", "integrity": "sha512-AicPrAC7Qu1JxPCZ9ZgCZlY35QgFnNqc+0LtbRNxnVw4TXvjQ72wnuL9JQcEBgXkI9JM8MsT9kaQoHcpCRJOYA==", "dev": true, "requires": {"path-parse": "1.0.6"}}, "right-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "dev": true, "requires": {"align-text": "0.1.4"}}, "rimraf": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "dev": true, "requires": {"glob": "7.1.3"}, "dependencies": {"glob": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.3.tgz", "integrity": "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ==", "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}}}, "rsvp": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/rsvp/-/rsvp-3.6.2.tgz", "integrity": "sha512-OfWGQTb9vnwRjwtA2QwpG2ICclHC3pgXZO5xt8H2EfgDquO0qVdSb5T88L4qJVAEugbS56pAuV4XZM58UX8ulw==", "dev": true}, "safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true}, "sane": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/sane/-/sane-1.7.0.tgz", "integrity": "sha1-s1ebzLRclM8gNVzIESSZDf00bjA=", "dev": true, "requires": {"anymatch": "1.3.2", "exec-sh": "0.2.2", "fb-watchman": "2.0.0", "minimatch": "3.0.4", "minimist": "1.2.0", "walker": "1.0.7", "watch": "0.10.0"}, "dependencies": {"minimist": {"version": "1.2.0", "resolved": "http://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}}}, "semver": {"version": "4.3.6", "resolved": "http://registry.npmjs.org/semver/-/semver-4.3.6.tgz", "integrity": "sha1-MAvG4OhjdPe6YQaLWx7NV/xlMto=", "dev": true}, "send": {"version": "0.16.2", "resolved": "https://registry.npmjs.org/send/-/send-0.16.2.tgz", "integrity": "sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw==", "dev": true, "requires": {"debug": "2.6.9", "depd": "1.1.2", "destroy": "1.0.4", "encodeurl": "1.0.2", "escape-html": "1.0.3", "etag": "1.8.1", "fresh": "0.5.2", "http-errors": "1.6.3", "mime": "1.4.1", "ms": "2.0.0", "on-finished": "2.3.0", "range-parser": "1.2.0", "statuses": "1.4.0"}, "dependencies": {"mime": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz", "integrity": "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ==", "dev": true}, "statuses": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz", "integrity": "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==", "dev": true}}}, "serve-static": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz", "integrity": "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw==", "dev": true, "requires": {"encodeurl": "1.0.2", "escape-html": "1.0.3", "parseurl": "1.3.2", "send": "0.16.2"}}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==", "dev": true}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "shelljs": {"version": "0.3.0", "resolved": "http://registry.npmjs.org/shelljs/-/shelljs-0.3.0.tgz", "integrity": "sha1-NZbmMHp4FUT1kfN9phg2DzHbV7E=", "dev": true}, "shellwords": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/shellwords/-/shellwords-0.1.1.tgz", "integrity": "sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==", "dev": true}, "signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "silent-error": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/silent-error/-/silent-error-1.1.1.tgz", "integrity": "sha512-n4iEKyNcg4v6/jpb3c0/iyH2G1nzUNl7Gpqtn/mHIJK9S/q/7MCfoO4rwVOoO59qPFIc0hVHvMbiOJ0NdtxKKw==", "dev": true, "requires": {"debug": "2.6.9"}}, "simple-fmt": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/simple-fmt/-/simple-fmt-0.1.0.tgz", "integrity": "sha1-GRv1ZqWeZTBILLJatTtKjchcOms=", "dev": true}, "simple-is": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/simple-is/-/simple-is-0.2.0.tgz", "integrity": "sha1-Krt1qt453rXMgVzhDmGRFkhQuvA=", "dev": true}, "slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true}, "slide": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/slide/-/slide-1.1.6.tgz", "integrity": "sha1-VusCfWW00tzmyy4tMsTUr8nh1wc=", "dev": true}, "socket.io": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/socket.io/-/socket.io-1.6.0.tgz", "integrity": "sha1-PkDZMmN+a9kjmBslyvfFPoO24uE=", "dev": true, "requires": {"debug": "2.3.3", "engine.io": "1.8.0", "has-binary": "0.1.7", "object-assign": "4.1.0", "socket.io-adapter": "0.5.0", "socket.io-client": "1.6.0", "socket.io-parser": "2.3.1"}, "dependencies": {"debug": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w=", "dev": true, "requires": {"ms": "0.7.2"}}, "ms": {"version": "0.7.2", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}, "object-assign": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz", "integrity": "sha1-ejs9DpgGPUP0wD8uiubNUahog6A=", "dev": true}}}, "socket.io-adapter": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-0.5.0.tgz", "integrity": "sha1-y21LuL7IHhB4uZZ3+c7QBGBmu4s=", "dev": true, "requires": {"debug": "2.3.3", "socket.io-parser": "2.3.1"}, "dependencies": {"debug": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w=", "dev": true, "requires": {"ms": "0.7.2"}}, "ms": {"version": "0.7.2", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}}}, "socket.io-client": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-1.6.0.tgz", "integrity": "sha1-W2aPT3cTBN/u0XkGRwg4b6ZxeFM=", "dev": true, "requires": {"backo2": "1.0.2", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "2.3.3", "engine.io-client": "1.8.0", "has-binary": "0.1.7", "indexof": "0.0.1", "object-component": "0.0.3", "parseuri": "0.0.5", "socket.io-parser": "2.3.1", "to-array": "0.1.4"}, "dependencies": {"component-emitter": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "dev": true}, "debug": {"version": "2.3.3", "resolved": "http://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha1-QMRT5n5uE8kB3ewxeviYbNqe/4w=", "dev": true, "requires": {"ms": "0.7.2"}}, "ms": {"version": "0.7.2", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}}}, "socket.io-parser": {"version": "2.3.1", "resolved": "http://registry.npmjs.org/socket.io-parser/-/socket.io-parser-2.3.1.tgz", "integrity": "sha1-3VMgJRA85Clpcya+/WQAX8/ltKA=", "dev": true, "requires": {"component-emitter": "1.1.2", "debug": "2.2.0", "isarray": "0.0.1", "json3": "3.3.2"}, "dependencies": {"debug": {"version": "2.2.0", "resolved": "http://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "requires": {"ms": "0.7.1"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "ms": {"version": "0.7.1", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}}}, "source-map": {"version": "0.1.43", "resolved": "http://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dev": true, "requires": {"amdefine": "1.0.1"}}, "source-map-support": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.2.10.tgz", "integrity": "sha1-6lo5AKHByyUJagrozFwrSxDe09w=", "dev": true, "requires": {"source-map": "0.1.32"}, "dependencies": {"source-map": {"version": "0.1.32", "resolved": "http://registry.npmjs.org/source-map/-/source-map-0.1.32.tgz", "integrity": "sha1-yLbBZ3l7pHQKjqMyUhYv8IWRsmY=", "dev": true, "requires": {"amdefine": "1.0.1"}}}}, "source-map-url": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.3.0.tgz", "integrity": "sha1-fsrxO1e80J2opAxdJp2zN5nUqvk=", "dev": true}, "sourcemap-validator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/sourcemap-validator/-/sourcemap-validator-1.1.0.tgz", "integrity": "sha512-Hmdu39KL+EoAAZ69OTk7RXXJdPRRizJvOZOWhCW9jLGfEQflCNPTlSoCXFPdKWFwwf0uzLcGR/fc7EP/PT8vRQ==", "dev": true, "requires": {"jsesc": "0.3.0", "lodash.foreach": "2.3.0", "lodash.template": "2.3.0", "source-map": "0.1.43"}, "dependencies": {"jsesc": {"version": "0.3.0", "resolved": "http://registry.npmjs.org/jsesc/-/jsesc-0.3.0.tgz", "integrity": "sha1-G/XuY7RTn+LibQwemcJAuXpFeXI=", "dev": true}}}, "spawn-args": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/spawn-args/-/spawn-args-0.2.0.tgz", "integrity": "sha1-+30L0dcP1DFr2ePew4nmX51jYbs=", "dev": true}, "spawnback": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/spawnback/-/spawnback-1.0.0.tgz", "integrity": "sha1-9zZi9+VNlTZ+ynTWQmxnfdfqaG8=", "dev": true}, "split": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/split/-/split-1.0.1.tgz", "integrity": "sha512-mTyOoPbrivtXnwnIxZRFYRrPNtEFKlpB2fvjSnCQUiAA6qAZzqwna5envK4uk6OIeP17CsdF3rSBGYVBsU0Tkg==", "dev": true, "optional": true, "requires": {"through": "2.3.8"}}, "sprintf-js": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.1.tgz", "integrity": "sha1-Nr54Mgr+WAH2zqPueLblqrlA6gw=", "dev": true}, "sri-toolbox": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/sri-toolbox/-/sri-toolbox-0.2.0.tgz", "integrity": "sha1-p/6lw/3lXmdc8cjAbz67XCk1g14=", "dev": true}, "sshpk": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.15.2.tgz", "integrity": "sha512-Ra/OXQtuh0/enyl4ETZAfTaeksa6BXks5ZcjpSUNrjBr0DvrJKX+1fsKDPpT9TBXgHAFsa4510aNVgI8g/+SzA==", "dev": true, "optional": true, "requires": {"asn1": "0.2.4", "assert-plus": "1.0.0", "bcrypt-pbkdf": "1.0.2", "dashdash": "1.14.1", "ecc-jsbn": "0.1.2", "getpass": "0.1.7", "jsbn": "0.1.1", "safer-buffer": "2.1.2", "tweetnacl": "0.14.5"}}, "stable": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "integrity": "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==", "dev": true}, "stack-trace": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=", "dev": true, "optional": true}, "statuses": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz", "integrity": "sha1-+vUbnrdKrvOzrPStX2Gr8ky3uT4=", "dev": true}, "string-width": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "1.1.0", "is-fullwidth-code-point": "1.0.0", "strip-ansi": "3.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dev": true, "requires": {"safe-buffer": "5.1.2"}}, "stringmap": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/stringmap/-/stringmap-0.2.2.tgz", "integrity": "sha1-VWwTeyWPlCuHdvWy71gqoGnX0bE=", "dev": true}, "stringset": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/stringset/-/stringset-0.2.1.tgz", "integrity": "sha1-7yWcTjSTRDd/zRyRPdLoSMnAQrU=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "2.1.1"}}, "strip-json-comments": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "integrity": "sha1-HhX7ysl9Pumb8tc7TGVrCCu6+5E=", "dev": true}, "styled_string": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/styled_string/-/styled_string-0.0.1.tgz", "integrity": "sha1-0ieCvYEpVFm8Tx3xjEutjpTdEko=", "dev": true}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}, "symlink-or-copy": {"version": "1.2.0", "resolved": "http://registry.npmjs.org/symlink-or-copy/-/symlink-or-copy-1.2.0.tgz", "integrity": "sha512-W31+GLiBmU/ZR02Ii0mVZICuNEN9daZ63xZMPDsYgPgNjMtg+atqLEGI7PPI936jYSQZxoLb/63xos8Adrx4Eg==", "dev": true}, "tap-parser": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/tap-parser/-/tap-parser-5.4.0.tgz", "integrity": "sha512-BIsIaGqv7uTQgTW1KLTMNPSEQf4zDDPgYOBRdgOfuB+JFOLRBfEu6cLa/KvMvmqggu1FKXDfitjLwsq4827RvA==", "dev": true, "requires": {"events-to-array": "1.1.2", "js-yaml": "3.12.0", "readable-stream": "2.3.6"}}, "temp": {"version": "0.8.3", "resolved": "https://registry.npmjs.org/temp/-/temp-0.8.3.tgz", "integrity": "sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k=", "dev": true, "requires": {"os-tmpdir": "1.0.2", "rimraf": "2.2.8"}, "dependencies": {"rimraf": {"version": "2.2.8", "resolved": "http://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "integrity": "sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI=", "dev": true}}}, "testem": {"version": "1.18.5", "resolved": "https://registry.npmjs.org/testem/-/testem-1.18.5.tgz", "integrity": "sha512-0jMQquldcrSl1HwNAkePOr02g/3ZQVZYw7GVJNbOiGvR5esyXvRPX916CLWxcdZqB3yHM80NZiz5P5biogu2Vw==", "dev": true, "requires": {"backbone": "1.3.3", "bluebird": "3.5.3", "charm": "1.0.2", "commander": "2.19.0", "consolidate": "0.14.5", "cross-spawn": "5.1.0", "express": "4.16.4", "fireworm": "0.7.1", "glob": "7.1.3", "http-proxy": "1.17.0", "js-yaml": "3.12.0", "lodash.assignin": "4.2.0", "lodash.clonedeep": "4.5.0", "lodash.find": "4.6.0", "lodash.uniqby": "4.7.0", "mkdirp": "0.5.1", "mustache": "2.3.2", "node-notifier": "5.3.0", "npmlog": "4.1.2", "printf": "0.2.5", "rimraf": "2.6.2", "socket.io": "1.6.0", "spawn-args": "0.2.0", "styled_string": "0.0.1", "tap-parser": "5.4.0", "xmldom": "0.1.27"}, "dependencies": {"bluebird": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.5.3.tgz", "integrity": "sha512-/qKPUQlaW1OyR51WeCPBvRnAlnZFUJkCSG5HzGnuIqhgyJtF+T94lFnn33eiazjRm2LAHVy2guNnaq48X9SJuw==", "dev": true}, "glob": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.3.tgz", "integrity": "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ==", "dev": true, "requires": {"fs.realpath": "1.0.0", "inflight": "1.0.6", "inherits": "2.0.3", "minimatch": "3.0.4", "once": "1.4.0", "path-is-absolute": "1.0.1"}}}}, "textextensions": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/textextensions/-/textextensions-2.4.0.tgz", "integrity": "sha512-qftQXnX1DzpSV8EddtHIT0eDDEiBF8ywhFYR2lI9xrGtxqKN+CvLXhACeCIGbCpQfxxERbrkZEFb8cZcDKbVZA==", "dev": true}, "throttleit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/throttleit/-/throttleit-1.0.0.tgz", "integrity": "sha1-nnhYNtr0Z0MUWlmEtiaNgoUorGw=", "dev": true, "optional": true}, "through": {"version": "2.3.8", "resolved": "http://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "timers-ext": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/timers-ext/-/timers-ext-0.1.7.tgz", "integrity": "sha512-b85NUNzTSdodShTIbky6ZF02e8STtVVfD+fu4aXXShEELpozH+bCpJLYMPZbsABN2wDH7fJpqIoXxJpzbf0NqQ==", "dev": true, "requires": {"es5-ext": "0.10.46", "next-tick": "1.0.0"}}, "tiny-lr": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/tiny-lr/-/tiny-lr-0.2.0.tgz", "integrity": "sha1-8lFOwAGO9UQy76xzxGVRKYE91XA=", "dev": true, "requires": {"body-parser": "1.14.2", "debug": "2.2.0", "faye-websocket": "0.10.0", "livereload-js": "2.4.0", "parseurl": "1.3.2", "qs": "5.1.0"}, "dependencies": {"body-parser": {"version": "1.14.2", "resolved": "http://registry.npmjs.org/body-parser/-/body-parser-1.14.2.tgz", "integrity": "sha1-EBXLH+LEQ4WCWVgdtTMy+NDPUPk=", "dev": true, "requires": {"bytes": "2.2.0", "content-type": "1.0.4", "debug": "2.2.0", "depd": "1.1.2", "http-errors": "1.3.1", "iconv-lite": "0.4.13", "on-finished": "2.3.0", "qs": "5.2.0", "raw-body": "2.1.7", "type-is": "1.6.16"}, "dependencies": {"qs": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.0.tgz", "integrity": "sha1-qfMRQq9GjLcrJbMBNrokVoNJFr4=", "dev": true}}}, "bytes": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.2.0.tgz", "integrity": "sha1-/TVGSkA/b5EXwt42Cez/nK4ABYg=", "dev": true}, "debug": {"version": "2.2.0", "resolved": "http://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "requires": {"ms": "0.7.1"}}, "http-errors": {"version": "1.3.1", "resolved": "http://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz", "integrity": "sha1-GX4izevUGYWF6GlO9nhhl7ke2UI=", "dev": true, "requires": {"inherits": "2.0.3", "statuses": "1.3.1"}}, "iconv-lite": {"version": "0.4.13", "resolved": "http://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz", "integrity": "sha1-H4irpKsLFQjoMSrMOTRfNumS4vI=", "dev": true}, "ms": {"version": "0.7.1", "resolved": "http://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "qs": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.1.0.tgz", "integrity": "sha1-TZMuXH6kEcynajEtOaYGIA/VDNk=", "dev": true}, "raw-body": {"version": "2.1.7", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.7.tgz", "integrity": "sha1-rf6s4uT7MJgFgBTQjActzFl1h3Q=", "dev": true, "requires": {"bytes": "2.4.0", "iconv-lite": "0.4.13", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.4.0.tgz", "integrity": "sha1-fZcZb51br39pNeJZhVSe3SpsIzk=", "dev": true}}}}}, "tmp": {"version": "0.0.28", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.28.tgz", "integrity": "sha1-Fyc1t/YU6nrzlmT6hM8N5OUV0SA=", "dev": true, "requires": {"os-tmpdir": "1.0.2"}}, "tmpl": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.4.tgz", "integrity": "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=", "dev": true}, "to-array": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/to-array/-/to-array-0.1.4.tgz", "integrity": "sha1-F+bBH3PdTz10zaek/zI46a2b+JA=", "dev": true}, "to-fast-properties": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=", "dev": true}, "tough-cookie": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz", "integrity": "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==", "dev": true, "optional": true, "requires": {"psl": "1.1.29", "punycode": "1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true, "optional": true}}}, "tree-sync": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/tree-sync/-/tree-sync-1.2.2.tgz", "integrity": "sha1-LPdrhYn1n/7bWNtaOsfLAT0BWLc=", "dev": true, "requires": {"debug": "2.6.9", "fs-tree-diff": "0.5.9", "mkdirp": "0.5.1", "quick-temp": "0.1.8", "walk-sync": "0.2.7"}, "dependencies": {"walk-sync": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.2.7.tgz", "integrity": "sha1-tJvk7mhnZXrrc2l4tWop0Q+jmWk=", "dev": true, "requires": {"ensure-posix-path": "1.0.2", "matcher-collection": "1.0.5"}}}}, "trim-right": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "try-resolve": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/try-resolve/-/try-resolve-1.0.1.tgz", "integrity": "sha1-z95vq9ctY+V5fPqrhzq76OcA6RI=", "dev": true}, "tryor": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/tryor/-/tryor-0.1.2.tgz", "integrity": "sha1-gUXkynyv9ArN48z5Rui4u3W0Fys=", "dev": true}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "optional": true, "requires": {"safe-buffer": "5.1.2"}}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true}, "type-is": {"version": "1.6.16", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.16.tgz", "integrity": "sha512-HRkVv/5qY2G6I8iab9cI7v1bOIdhm94dVjQCPFElW9W+3GeDOSHmy2EBYe4VTApuzolPcmgFTN3ftVJRKR2J9Q==", "dev": true, "requires": {"media-typer": "0.3.0", "mime-types": "2.1.21"}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true, "optional": true}, "uc.micro": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.5.tgz", "integrity": "sha512-JoLI4g5zv5qNyT09f4YAvEZIIV1oOjqnewYg5D38dkQljIzpPT296dbIGvKro3digYI1bkb7W6EP1y4uDlmzLg==", "dev": true}, "uglify-js": {"version": "2.3.6", "resolved": "http://registry.npmjs.org/uglify-js/-/uglify-js-2.3.6.tgz", "integrity": "sha1-+gmEdwtCi3qbKoBY9GNV0U/vIRo=", "dev": true, "optional": true, "requires": {"async": "0.2.10", "optimist": "0.3.7", "source-map": "0.1.43"}, "dependencies": {"async": {"version": "0.2.10", "resolved": "http://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E=", "dev": true, "optional": true}, "optimist": {"version": "0.3.7", "resolved": "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz", "integrity": "sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=", "dev": true, "optional": true, "requires": {"wordwrap": "0.0.3"}}}}, "uglify-to-browserify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc=", "dev": true, "optional": true}, "ultron": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/ultron/-/ultron-1.0.2.tgz", "integrity": "sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po=", "dev": true}, "underscore": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.9.1.tgz", "integrity": "sha512-5/4etnCkd9c8gwgowi5/om/mYO5ajCaOgdzj/oW+0eQV9WxKBDZw5+ycmKmeaTXjInS/W0BzpGLo2xR2aBwZdg==", "dev": true}, "underscore.string": {"version": "3.3.5", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.5.tgz", "integrity": "sha512-g+dpmgn+XBneLmXXo+sGlW5xQEt4ErkS3mgeN2GFbremYeMBSJKr9Wf2KJplQVaiPY/f7FN6atosWYNm9ovrYg==", "dev": true, "requires": {"sprintf-js": "1.1.1", "util-deprecate": "1.0.2"}}, "unicode-5.2.0": {"version": "0.7.5", "resolved": "https://registry.npmjs.org/unicode-5.2.0/-/unicode-5.2.0-0.7.5.tgz", "integrity": "sha512-KVGLW1Bri30x00yv4HNM8kBxoqFXr0Sbo55735nvrlsx4PYBZol3UtoWgO492fSwmsetzPEZzy73rbU8OGXJcA==", "dev": true}, "universalify": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==", "dev": true}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true}, "uri-js": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz", "integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "dev": true, "optional": true, "requires": {"punycode": "2.1.1"}}, "user-home": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/user-home/-/user-home-1.1.1.tgz", "integrity": "sha1-K1viOjK2Onyd640PKNSFcko98ZA=", "dev": true}, "username-sync": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/username-sync/-/username-sync-1.0.1.tgz", "integrity": "sha1-HN6H7vz5S4gimE2Ti6K3l0Jtrh8=", "dev": true}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true}, "uuid": {"version": "2.0.3", "resolved": "http://registry.npmjs.org/uuid/-/uuid-2.0.3.tgz", "integrity": "sha1-Z+LoY3lyFVMN/zGOW/nc6/1Hsho=", "dev": true}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "optional": true, "requires": {"assert-plus": "1.0.0", "core-util-is": "1.0.2", "extsprintf": "1.3.0"}}, "walk-sync": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/walk-sync/-/walk-sync-0.3.3.tgz", "integrity": "sha512-jQgTHmCazUngGqvHZFlr30u2VLKEKErBMLFe+fBl5mn4rh9aI/QVRog8PT1hv2vaOu4EBwigfmpRTyZrbnpRVA==", "dev": true, "requires": {"ensure-posix-path": "1.0.2", "matcher-collection": "1.0.5"}}, "walker": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/walker/-/walker-1.0.7.tgz", "integrity": "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=", "dev": true, "requires": {"makeerror": "1.0.11"}}, "watch": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/watch/-/watch-0.10.0.tgz", "integrity": "sha1-d3mLLaD5kQ1ZXxrOWwwiWFIfIdw=", "dev": true}, "websocket-driver": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.0.tgz", "integrity": "sha1-DK+dLXVdk67gSdS90NP+LMoqJOs=", "dev": true, "requires": {"http-parser-js": "0.5.0", "websocket-extensions": "0.1.3"}}, "websocket-extensions": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.3.tgz", "integrity": "sha512-nqHUnMXmBzT0w570r2JpJxfiSD1IzoI+HGVdd3aZ0yNi3ngvQ4jv1dtHt5VGxfI2yj5yqImPhOK4vmIh2xMbGg==", "dev": true}, "which": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "dev": true, "requires": {"isexe": "2.0.0"}}, "wide-align": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.3.tgz", "integrity": "sha512-QGkOQc8XL6Bt5PwnsExKBPuMKBxnGxWWW3fU55Xt4feHozMUhdUMaBCk290qpm/wG5u/RSKzwdAC4i51YigihA==", "dev": true, "requires": {"string-width": "1.0.2"}}, "window-size": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.4.tgz", "integrity": "sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY=", "dev": true}, "winston": {"version": "2.4.4", "resolved": "https://registry.npmjs.org/winston/-/winston-2.4.4.tgz", "integrity": "sha512-NBo2Pepn4hK4V01UfcWcDlmiVTs7VTB1h7bgnB0rgP146bYhMxX0ypCz3lBOfNxCO4Zuek7yeT+y/zM1OfMw4Q==", "dev": true, "optional": true, "requires": {"async": "1.0.0", "colors": "1.0.3", "cycle": "1.0.3", "eyes": "0.1.8", "isstream": "0.1.2", "stack-trace": "0.0.10"}, "dependencies": {"async": {"version": "1.0.0", "resolved": "http://registry.npmjs.org/async/-/async-1.0.0.tgz", "integrity": "sha1-+PwEyjoTeErenhZBr5hXjPvWR6k=", "dev": true, "optional": true}, "colors": {"version": "1.0.3", "resolved": "http://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "integrity": "sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=", "dev": true, "optional": true}}}, "wordwrap": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc=", "dev": true}, "workerpool": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/workerpool/-/workerpool-2.3.3.tgz", "integrity": "sha512-L1ovlYHp6UObYqElXXpbd214GgbEKDED0d3sj7pRdFXjNkb2+un/AUcCkceHizO0IVI6SOGGncrcjozruCkRgA==", "dev": true, "requires": {"object-assign": "4.1.1"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write-file-atomic": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.4.tgz", "integrity": "sha1-+Aek8LHZ6ROuekgRLmzDrxmRtF8=", "dev": true, "requires": {"graceful-fs": "4.1.15", "imurmurhash": "0.1.4", "slide": "1.1.6"}, "dependencies": {"graceful-fs": {"version": "4.1.15", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "dev": true}}}, "ws": {"version": "1.1.1", "resolved": "http://registry.npmjs.org/ws/-/ws-1.1.1.tgz", "integrity": "sha1-CC3bbGQehdS7RR8D1S8G6r2x8Bg=", "dev": true, "requires": {"options": "0.0.6", "ultron": "1.0.2"}}, "wtf-8": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wtf-8/-/wtf-8-1.0.0.tgz", "integrity": "sha1-OS2LotDxw00e4tYw8V0O+2jhBIo=", "dev": true}, "xdg-basedir": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-2.0.0.tgz", "integrity": "sha1-7byQPMOF/ARSPZZqM1UEtVBNG9I=", "dev": true, "requires": {"os-homedir": "1.0.2"}}, "xmldom": {"version": "0.1.27", "resolved": "https://registry.npmjs.org/xmldom/-/xmldom-0.1.27.tgz", "integrity": "sha1-1QH5ezvbQDr4757MIFcxh6rawOk=", "dev": true}, "xmlhttprequest-ssl": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.5.3.tgz", "integrity": "sha1-GFqIjATspGw+QHDZn3tJ3jUomS0=", "dev": true}, "y18n": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz", "integrity": "sha1-bRX7qITAhnnA136I53WegR4H+kE=", "dev": true}, "yallist": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.0.2.tgz", "integrity": "sha1-hFK0u36Dx8GI2AQcGoN8dz1ti7k=", "dev": true}, "yam": {"version": "0.0.18", "resolved": "https://registry.npmjs.org/yam/-/yam-0.0.18.tgz", "integrity": "sha1-5cq3cfD8gMpZmBTLnCacuL/wDiw=", "dev": true, "requires": {"findup": "0.1.5", "fs-extra": "0.16.5", "lodash.merge": "3.3.2"}, "dependencies": {"fs-extra": {"version": "0.16.5", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.16.5.tgz", "integrity": "sha1-GtZh+myGyWCM0bSe/G/Og0k5p1A=", "dev": true, "requires": {"graceful-fs": "3.0.11", "jsonfile": "2.4.0", "rimraf": "2.6.2"}}, "graceful-fs": {"version": "3.0.11", "resolved": "http://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.11.tgz", "integrity": "sha1-dhPHeKGv6mLyXGMKCG1/Osu92Bg=", "dev": true, "requires": {"natives": "1.1.6"}}}}, "yargs": {"version": "3.27.0", "resolved": "http://registry.npmjs.org/yargs/-/yargs-3.27.0.tgz", "integrity": "sha1-ISBUaTFuk5Ex1Z8toMbX+YIh6kA=", "dev": true, "requires": {"camelcase": "1.2.1", "cliui": "2.1.0", "decamelize": "1.2.0", "os-locale": "1.4.0", "window-size": "0.1.4", "y18n": "3.2.1"}}, "yauzl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "integrity": "sha1-lSj0QtqxsihOWLQ3m7GU4i4MQAU=", "dev": true, "optional": true, "requires": {"fd-slicer": "1.0.1"}}, "yeast": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/yeast/-/yeast-0.1.2.tgz", "integrity": "sha1-AI4G2AlDIMNy28L47XagymyKxBk=", "dev": true}}}