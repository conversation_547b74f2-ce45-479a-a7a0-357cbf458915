/*
Build script for the MyFoot UI
*/
var _, appCSSFiles, appJSFiles,  concat, cssmin, debug, gulp, gulpif, gutil, ignorePaths, initJSFiles, isProduction, mocha, myfootCSSFiles, myfootJSFiles, params, shell, spawn, uglify;

gulp = require('gulp');

_ = require('lodash');

cssmin = require('gulp-cssmin');

concat = require('gulp-concat');

debug = require('gulp-debug');

gutil = require('gulp-util');

mocha = require('gulp-mocha');

shell = require('gulp-shell');

spawn = require('gulp-spawn');

uglify = require('gulp-uglify');

gulpif = require('gulp-if');

// Flip this bit to run uglify & other expensive ops
isProduction = false;

params = {
  rootDir: './public'
};

appCSSFiles = [
  'public/vendors/bootstrap/dist/css/bootstrap.css',
  'public/css/oldlibs/font-awesome.css',
  'public/vendors/bootstrap-daterangepicker/daterangepicker-bs3.css',
  // 'public/css/oldlibs/jquery-ui.min.css'
  // 'public/css/oldlibs/jquery-ui-1.8.16.custom.css'
  'public/vendors/bootstrap-tour/build/css/bootstrap-tour.css',
  'public/vendors/chosen/chosen.css',
  'public/sparefoot/css/theme.css',
  'public/css/mysparefoot.css',
  'public/vendors/leaflet/dist/leaflet.css',
  'public/css/login.css',
  'public/vendors/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css',
];

appJSFiles = [
  'public/vendors/bluebird/js/browser/bluebird.min.js',
  'public/vendors/jquery-migrate/jquery-migrate.min.js',
  'public/vendors/modernizr/modernizr.js',
  'public/vendors/jquery-cookie/cookie.min.js',
  // jQuery UI
  'public/vendors/jquery-ui/ui/core.js',
  'public/vendors/jquery-ui/ui/widget.js',
  'public/vendors/jquery-ui/ui/mouse.js',
  'public/vendors/jquery-ui/ui/datepicker.js',
  'public/vendors/jquery-ui/ui/effect.js',
  'public/vendors/bootstrap/dist/js/bootstrap.min.js',
  'public/js/oldlibs/bootstrap-switch.js',
  'public/vendors/chosen/chosen.jquery.min.js',
  // 'public/js/oldlibs/countdown.js'
  'public/vendors/DateJS/build/date.js',
  // 'public/vendors/bootstrap-daterangepicker/daterangepicker.js'
  'public/js/oldlibs/daterangepicker.js',
  'public/vendors/easing/easing-min.js',
  'public/vendors/es5-shim/es5-shim.js',
  'public/vendors/jquery-expander/jquery.expander.js',
  'public/js/oldlibs/format-date.js', // used by daterangepicker
  'public/vendors/jquery-hashbhange/jquery.ba-hashchange.js',
  'public/vendors/quicksearch/dist/jquery.quicksearch.js',
  'public/vendors/jquery-serialize-object/jquery.serialize-object.js',
  'public/vendors/stupidtable/stupidtable.js',
  'public/js/oldlibs/tablesorter.min.js',
  'public/js/oldlibs/timepicker.js', // used by daterangepicker
  'public/js/application.js',
  'public/js/analytics/segmenttracking.js',
  'public/vendors/bootstrap-tour/build/js/bootstrap-tour.js',
  'public/vendors/jquery-tablesort/jquery.tablesort.min.js',
  'public/js/oldlibs/mousehold.js', // used only in the bid tool
  'public/vendors/moment/moment.js',
  'public/vendors/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js',
  'public/vendors/leaflet/dist/leaflet.js',
  'public/vendors/wicket/wicket.js',
  'public/vendors/wicket/wicket-leaflet.js'
];

ignorePaths = ['!node_modules/**', '!public/vendors/**'];

initJSFiles = ['public/vendors/jquery/dist/jquery.min.js', 'public/vendors/lodash/dist/lodash.min.js', 'public/vendors/underscore-string/dist/underscore.string.min.js', 'public/sparefoot/sparefoot.js'];

myfootJSFiles = ['public/vendors/semantic-ui/dist/semantic.js', 'public/new-ui/js/myfoot.js'];

myfootCSSFiles = [
  'public/vendors/chartist/dist/chartist.min.css',
  'public/new-ui/css/myfoot.css',
  'public/new-ui/css/myfoot-new.css',
  'public/new-ui/css/features.css',
  // jQuery UI should probably be moved up above myfoot.css
  'public/css/oldlibs/jquery-ui.min.css',
  'public/css/oldlibs/jquery-ui-1.8.16.custom.css'
];

function copySemanticConfig() {
  return gulp.src('./public/new-ui/semantic.json').pipe(gulp.dest('./public/vendors/semantic-ui'));
}

function copySemantic() {
  return gulp.src('./public/new-ui/css/semantic-overrides/**/*.*').pipe(gulp.dest('./public/vendors/semantic-ui/src/site'));
}

gulp.task('build-semantic', gulp.parallel(copySemanticConfig, copySemantic));


gulp.task('test', shell.task(["multi='spec=- mocha-bamboo-reporter=-' ./node_modules/mocha/bin/mocha tests-mocha --recursive -R mocha-multi"]));

function concatAppJs() {
  return gulp.src(appJSFiles, {allowEmpty: true}).pipe(concat('app.js')).pipe(gulpif(isProduction, uglify())).pipe(gulp.dest('./public/dist/'));
}

function concatAppCss() {
  return gulp.src(appCSSFiles).pipe(concat('app.css')).pipe(gulpif(isProduction, cssmin())).pipe(gulp.dest('./public/dist/'));
}

function concatCss() {
  return gulp.src(myfootCSSFiles).pipe(concat('myfoot.css')).pipe(gulpif(isProduction, cssmin())).pipe(gulp.dest(params.rootDir + '/new-ui/dist/'));
}

function concatInitJs() {
  return gulp.src(initJSFiles).pipe(concat('init.js')).pipe(gulpif(isProduction, uglify())).pipe(gulp.dest('./public/dist/'));
}

function concatJs() {
  return gulp.src(myfootJSFiles).pipe(concat('myfoot.js')).pipe(gulpif(isProduction, uglify())).pipe(gulp.dest('./public/new-ui/dist/'));
}

function build(cb) {
  return gulp.series(
      concatAppJs,
      gulp.parallel(concatAppCss, concatCss, concatInitJs, concatJs)
  )(cb);
}

function buildProduction(cb) {
  isProduction = true;
  return gulp.series(build)(cb);
}

gulp.task('default', gulp.series(build));

gulp.task('stage', gulp.series(buildProduction));

function watch() {
  // Watch CSS files and rebuild when they change
  gulp.watch(myfootCSSFiles, concatCss);
  
  // Watch JS files and rebuild when they change  
  gulp.watch(myfootJSFiles, concatJs);
  
  // Watch app CSS files
  gulp.watch(appCSSFiles, concatAppCss);
  
  // Watch app JS files
  gulp.watch(appJSFiles, concatAppJs);
  
  // Watch init JS files
  gulp.watch(initJSFiles, concatInitJs);
}

// Add the watch task
gulp.task('watch', watch);

