# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public: false       # Allows optimizing the container by removing unused services; this also means
                            # fetching services directly from the container via $container->get() won't work.
                            # The best practice is to be explicit about your dependencies anyway.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    # as the project have too many classes, we need to exclude some of them
    # it will show Error: Maximum execution time of 30 seconds exceeded
    # Sparefoot\MyFootService\:
    #     resource: '../src/*'
    #     exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php,Health}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    Sparefoot\MyFootService\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    Rollbar\RollbarLogger:
        class: Rollbar\RollbarLogger
        factory: ['\Rollbar\Rollbar', logger]

    Predis\Client:
        arguments:
            - '%env(REDIS_SCHEME)%://%env(REDIS_HOST)%:%env(REDIS_PORT)%'

    PDO:
        class: \PDO
        arguments:
            - 'mysql:host=%env(MYSQL_SPAREFOOT_HOST)%;dbname=%env(MYSQL_SPAREFOOT_DB)%;port=%env(MYSQL_SPAREFOOT_PORT)%'
            - '%env(MYSQL_SPAREFOOT_USER)%'
            - '%env(MYSQL_SPAREFOOT_PASS)%'

    # Enables "JsonSerializable" interface support
    json_normalizer:
        class: Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer
        public: false
        tags: [serializer.normalizer]

    # Custom error handler
    custom_error:
        class: Sparefoot\MyFootService\EventListener\ExceptionListener
        arguments:
            - '@twig'
            - '@router'
            - '@http_kernel'
            - '@logger'
            - '@security.token_storage'
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }

    Sparefoot\MyFootService\Twig\TwigExtension:
        tags: ['twig.extension']
    
    Sparefoot\MyFootService\Service\:
        resource: '../src/Service/*'
        autowire: true
        autoconfigure: true

    # explicitly configure the custom user provider
    Sparefoot\MyFootService\Security\CustomUserProvider:
        public: true
        arguments:
            - '@request_stack'
            - '@logger'

    Sparefoot\MyFootService\Security\CustomAuthenticator:
        public: true

    # Token validation listener for continuous token validation
    Sparefoot\MyFootService\EventListener\TokenValidationListener:
        tags:
            - { name: kernel.event_subscriber }

    # Subscriber
    # Sparefoot\MyFootService\EventSubscriber\MaintenanceModeSubscriber:
    #     tags:
    #         - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }
    # Sparefoot\MyFootService\EventSubscriber\InventoryAccessSubscriber:
    #     tags:
    #         - { name: kernel.event_subscriber }
    # Sparefoot\MyFootService\EventSubscriber\MaintenanceModeSubscriber:
    #     arguments:
    #         $twig: '@twig'
    #         $exemptPaths: ['/tools/feature-flags', '/admin/login']
    #     tags:
    #         - { name: kernel.event_subscriber }
            
    Sparefoot\MyFootService\EventSubscriber\ControllerInitSubscriber:
        tags:
            - { name: kernel.event_subscriber }

    Sparefoot\MyFootService\Security\CustomLogoutListener:
        tags:
            - { name: kernel.event_subscriber }

