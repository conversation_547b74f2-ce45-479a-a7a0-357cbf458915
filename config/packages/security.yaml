security:
    enable_authenticator_manager: true
    
    providers:
        custom_provider:
            id: <PERSON><PERSON>foot\MyFootService\Security\CustomUserProvider

    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        Sparefoot\MyFootService\Security\User:
            algorithm: auto

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|img|fonts|js)/
            security: false
        main:
            lazy: true
            provider: custom_provider
            custom_authenticator: Sparefoot\MyFootService\Security\CustomAuthenticator
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 1209600 # 2 weeks in seconds (longer than OAuth tokens)
                path: /
                domain: ~ # Will be set per environment
                secure: auto # Force HTTPS in production
                httponly: true # Prevent XSS attacks
                samesite: strict # CSRF protection
                always_remember_me: false
                remember_me_parameter: '_remember_me'
                user_providers: [custom_provider]
            logout:
               path: login_logout
               target: login_index
    role_hierarchy:
       ROLE_GOD: [ROLE_FACILITYEDITOR, ROLE_SEARCHANALYST, R<PERSON><PERSON>_USER, R<PERSON><PERSON>_ADMIN]
    access_control:
       - { path: ^/public, roles: PUBLIC_ACCESS }
       - { path: ^/login, roles: PUBLIC_ACCESS }
       - { path: ^/logout, roles: PUBLIC_ACCESS }
       - { path: ^/ping, roles: PUBLIC_ACCESS }
       - { path: ^/signup-start, roles: PUBLIC_ACCESS }
       - { path: ^/signup-end, roles: [ROLE_ADMIN] }
       - { path: ^/inventory, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/user, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/api/units, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/api, roles: [ROLE_ADMIN] }
       - { path: ^/payment, roles: [ROLE_ADMIN] }
       - { path: ^/error, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/search, roles: [ROLE_SEARCHANALYST] }
       - { path: ^/dashboard, roles: [ROLE_FACILITYEDITOR] }
       - { path: ^/features, roles: [ROLE_ADMIN] }
       - { path: ^/, roles: ROLE_GOD } # Fallback
