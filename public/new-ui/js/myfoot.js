"use strict";

(function () {
  var <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TEM<PERSON>ATES;

  CACHE = {};

  TEMPLATES = {
    accountsItem: "<a class=\"result item {{ status.toLowerCase() }}\" data-value=\"{{ account_id }}\">\n    {{ name }}\n    <div class=\"row-2\">SFID:\#{{ sf_account_id }} - {{ bid_type }} - {{ integration_names }} ({{ num_facilities }})</div>\n    <div class=\"row-3 {{ status == 'Live' ? 'green' : 'red' }}\">\#{{ account_id }} {{ status }}</div>\n</a>",
    facDropdownItem: "<a class=\"item facility\" data-value=\"{{ id }}\">{{ title }} {{ company_code }} <span data-active=\"{{active}}\"></span></a>",
    oldBrowser: "<div class=\"alert alert-danger\">\n<span class=\"glyphicon glyphicon-exclamation-sign\"></span>\nInternet Explorer 8 is not supported. Your changes are NOT saving. <a target=\"_blank\" href=\"http://windows.microsoft.com/en-us/internet-explorer/download-ie\">Click here to update your browser</a>\n</div>"
  };

  Banners = {
    init: function init() {
      return $(document).ready(function () {
        var $randomBanner, $sitelinkSideAd;
        $randomBanner = $('#random-banner');
        $sitelinkSideAd = $('#sitelink-side-ad');
        if (!$('div', $randomBanner).length) {
          return false;
        }
        $randomBanner.slideDown('slow');
        $randomBanner.on('click', 'a[href*="marketing.sparefoot"]', function (e) {
          e.preventDefault();
          return Banner.submitSitelinkLead();
        });
        return $sitelinkSideAd.on('click', 'a[href*="marketing.sparefoot"]', function (e) {
          e.preventDefault();
          return Banner.submitSitelinkLead();
        });
      });
    },
    submitSitelinkLead: function submitSitelinkLead() {
      var next = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function () {};

      var $link, options;
      $link = $('#sitelink-side-ad a[href*="marketing.sparefoot"]');
      options = {
        url: '/sitelink/sales-lead',
        type: 'POST',
        dataType: 'json',
        data: {
          first_name: USER.firstName,
          last_name: USER.lastName,
          company: ACCOUNT.name,
          phone: USER.phone,
          email: USER.email,
          user_id: USER.id,
          description: "USER\n\n" + JSON.stringify(USER, null, '\t') + "\n\n\nACCOUNT\n\n" + JSON.stringify(ACCOUNT, null, '\t')
        }
      };
      return $.ajax(options).fail(function () {
        if (CONFIG.env !== 'live') {
          console.error('Failed to create salesforce lead', arguments);
        }
        return window.location = $link.attr('href');
      }).done(function () {
        return window.location = $link.attr('href');
      });
    }
  };

  Banners.init();

  MyFoot = {
    activeAccountId: null,
    activeFacId: null,
    pagesShowPicker: ['dashboard', 'customers', 'features', 'reviews', 'inventory', 'bidding', 'listings'],
    init: function init() {
      /*
      // old code from Zend
      return $(document).ready(function () {
        var ref;
        MyFoot.activeFacId = (ref = SF.tools.parseQueryString()['fid']) != null ? ref : SF.cookies.get('active_facility_id');
        MyFoot.initFacilityDropdown();
        MyFoot.initUser();
        MyFoot.initSidebar();
        MyFoot.checkCompatibility();
        // user logout click
        $('body').on('click', '#header-logout', function () {
          return SF.cookies.deleteAll();
        }).on('click', '#mobile-sidebar-button, #mobile-header .js-toggle-menu', function () {
          return $('#site-sidebar').sidebar('toggle');
        });
        $('.ui.checkbox').checkbox();
        $('.table.sortable').tablesort();
        return $('.popup-text').popup({
          preserve: true
        });
      });
      */
      return $(document).ready(function () {
        var ref;
        MyFoot.activeFacId = (ref = SF.tools.parseQueryString()['fid']) != null ? ref : SF.cookies.get('active_facility_id');
        // # TO-DO NEED TO CHECK
        MyFoot.initFacilityDropdown();
        MyFoot.initUser();
        MyFoot.initSidebar();
        MyFoot.checkCompatibility();
        // // user logout click
        $('body').on('click', '#header-logout', function () {
          return SF.cookies.deleteAll();
        }).on('click', '#mobile-sidebar-button, #mobile-header .js-toggle-menu', function () {
          return $('#site-sidebar').sidebar('toggle');
        });
        $('.ui.checkbox').checkbox();
        
        // Initialize table sorting if tablesort is available
        if ($.fn.tablesort) {
          $('.table.sortable').tablesort();
        } else {
          console.warn('113 myfoot.js tablesort plugin is not available');
        }
        
        return $('.popup-text').popup({
          preserve: true
        });
      });
    },
    initFacilityDropdown: function initFacilityDropdown() {
      var $dropdown, options, showPicker;
      $dropdown = $('#site-fac-dropdown');
      if (!$dropdown.length) {
        return false;
      }
      // determine if we should show picker or not
      showPicker = false;
      _.each(this.pagesShowPicker, function (path) {
        if (location.pathname.substr(1).indexOf(path) === 0) {
          showPicker = true;
        }
        if (location.pathname === '/features/type') {
          return showPicker = false;
        }
        if (location.pathname === '/features/demandoptimizer' || location.pathname === '/features/bid-optimizer') {
          return showPicker = false;
        }
      });
      if (showPicker) {
        $('.js-facility-header').removeClass('hidden');
        $dropdown.show().removeClass('hidden');
      } else {
        $('.js-account-header').removeClass('hidden');
        $('.add-facility-btn').removeClass('hidden');
        return;
      }
      $dropdown.dropdown({
        fullTextSearch: true,
        onChange: function onChange(facId, text, $choice) {
          return location.href = window.location.href.split('?')[0] + "?fid=" + facId;
        }
      }).on('mousewheel', function (e) {
        return e.stopPropagation();
      });
      options = {
        url: '/api/accounts/' + ACCOUNT.accountId + '/facilities',
        type: 'GET',
        dataType: 'json',
        data: {
          fields: 'title,company_code,active'
        }
      };
      return $.ajax(options).fail(function () {
        return console.error('MyFoot.initFacilityDropdown - failed to get list of facilities', arguments);
      }).done(function (response) {
        var $list, activeFacText, ref;
        $list = $('.items', $dropdown);
        response.facilities = _.sortBy(response.data, function (i) {
          return i.title + i.company_code; // resort by title
        });
        if ((ref = response.facilities) != null ? ref.length : void 0) {
          activeFacText = null;
          if (!MyFoot.activeFacId && response.facilities.length) {
            MyFoot.activeFacId = response.facilities[0]._id;
            activeFacText = response.facilities[0].title;
            if (response.facilities[0].company_code !== null) {
              activeFacText += " " + response.facilities[0].company_code;
            }
            SF.cookies.set('active_facility_id', MyFoot.activeFacId, null);
          }
          _.each(response.facilities, function (fac) {
            var $row;
            $row = $(_.template(TEMPLATES.facDropdownItem)(fac));
            $row.appendTo($list);
            if (parseInt(MyFoot.activeFacId) === parseInt(fac.id)) {
              activeFacText = fac.title;
              if (fac.company_code !== null) {
                activeFacText += " " + fac.company_code;
              }
            }
            return $('*[data-active="false"]').addClass('ui').addClass('label').html('Inactive');
          });
          if (activeFacText) {
            return $dropdown.dropdown('set text', activeFacText);
          }
        }
      });
    },
    initUser: function initUser() {
      var $user;
      $user = $('#user');
      console.log('173 init in myfoot.js initUser()');
      if (!$user.length) {
        console.log('176 (!$user.length) == true not foud #user initUser()');
        return false;
      }
      $('.ui.dropdown', $user).dropdown({
        on: 'click'
      });
      return $('.js-account-settings').dropdown({
        on: 'click'
      });
    },
    initSidebar: function initSidebar() {
      var $search, accountId, ref;
      // Start Rotating the Marketing Widget in 60s
      // setTimeout (=> @rotateMarketing()), 60000

      // Get the Account ID from the Query Param or Cookie
      accountId = (ref = SF.tools.parseQueryString()['account_id']) != null ? ref : SF.cookies.get('active_account_id');
      $search = $('#accounts-search');
      if (!$search.length) {
        return false;
      }
      $search.on('blur', '.prompt', function () {
        // blur will hide this
        $('.prompt', $search).hide();
        $('.results', $search).hide();
        return $('.selected', $search).show();
      }).on('keyup', '.prompt', function (e) {
        // scroll/up down with keyboard
        var $active, $results, newY;
        switch (e.keyCode) {
          case $.ui.keyCode.ENTER:
            // enter
            return $('.results .result.active', $search).trigger('click');
          case $.ui.keyCode.UP:
          case $.ui.keyCode.DOWN:
            // up/down scroll if needed
            $active = $('.result.active', $search);
            $results = $('.results', $search);
            newY = $active.position().top - $active.outerHeight() - 20;
            return $results.animate({
              scrollTop: '+=' + newY
            }, 50);
          default:
            $results = $('.results', $search);
            return $results.animate({
              scrollTop: 0
            });
        }
      }).on('click', '.input .selected', function () {
        // click on box to change account
        $('.prompt', $search).removeClass('hidden').show().val('').trigger('focus');
        return $('.selected', $search).hide();
      }).on('click', '.results .result', function () {
        // click on account will redirect
        accountId = $(this).attr('data-value');
        return location.href = '/?account_id=' + accountId;
      }).search({
        source: [],
        searchFields: ['name', 'sf_account_id', 'account_id'],
        searchFullText: false,
        maxResults: 30,
        minCharacters: 0,
        type: 'account',
        templates: {
          account: function account(r) {
            var html;
            html = '';
            if (r.results !== 'undefined') {
              _.each(r.results, function (account) {
                return html += _.template(TEMPLATES.accountsItem)(account);
              });
              return html;
            }
            return false;
          }
        }
      });
      $.ajax('/api/accounts').fail(function () {
        return console.error('MyFoot.initSidebar - failed to get list of accounts', arguments);
      }).done(function (response) {
        var $row;
        if (response.data) {
          CACHE.accounts = response.data;
          $search.data('searchModule').setting('source', response.data);
          if (typeof ACCOUNT !== "undefined" && ACCOUNT !== null ? ACCOUNT.accountId : void 0) {
            ACCOUNT.account_id = ACCOUNT.accountId;
            ACCOUNT.bid_type = ACCOUNT.bidType;
            ACCOUNT.integration_names = ACCOUNT.integrationString;
            ACCOUNT.num_facilities = ACCOUNT.numFacilities;
            ACCOUNT.sf_account_id = ACCOUNT.sfAccountId;
            $row = $(_.template(TEMPLATES.accountsItem, ACCOUNT));
            return $('.selected', $search).html($row.html()).show();
          }
        }
      });
      return $('#site-sidebar').sidebar({
        dimPage: false,
        closable: false,
        transition: 'overlay',
        mobileTransition: 'push'
      });
    },
    rotateMarketing: function rotateMarketing() {
      var _this = this;

      $('#site-sidebar .marketing-rotator').shape('flip up');
      return setTimeout(function () {
        return _this.rotateMarketing();
      }, 60000);
    },
    checkCompatibility: function checkCompatibility() {
      var $alert, m;
      // We don't support anything less than IE 9
      m = navigator.userAgent.match(/MSIE ([0-9\.]+)/);
      if (m) {
        if (m[1] >= 9) {
          return;
        }
        $alert = $(Site.TEMPLATES.oldBrowser);
        return $alert.prependTo($('.master-layout'));
      }
    }
  };

  MyFoot.init();

  // window observer
  $(window).on('resize', _.debounce(function () {
    var $sidebar, width;
    width = $(this).width();
    $sidebar = $('#site-sidebar');
    $sidebar.sidebar({
      dimPage: false,
      closable: false,
      transition: 'overlay',
      mobileTransition: 'push'
    });
    if (width < 768) {
      return $sidebar.sidebar('hide').removeClass('overlay').addClass('push');
    } else {
      return $sidebar.sidebar('show');
    }
  }, 150)).trigger('resize');

  // IE10 only code
  if (navigator.userAgent.indexOf('MSIE 10.0') !== -1) {
    $('#site-fac-dropdown').on('click', function (e) {
      e.stopPropagation();
      $('.menu', this).show();
      return $('body').one('click', function () {
        return $('#site-fac-dropdown .menu', this).hide();
      });
    }).on('click', '.menu a', function (e) {
      var facId;
      e.stopPropagation();
      facId = $(this).attr('data-value');
      return location.href = window.location.href.split('?')[0] + "?fid=" + facId;
    });
  }
}).call(undefined);