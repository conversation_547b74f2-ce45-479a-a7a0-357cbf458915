$(function(){

    // Edit User Contact Information
    $('.js-edit-contact-info-view-mode').on('click', function() {
        ga('send', 'event', 'button', 'click', 'Users: Contact Info Edit');
        var $parent = $(this).closest('.js-edit-contact-info');
        $parent.find('.js-edit-contact-info-edit-mode').toggleClass('hidden');
        $parent.find('.js-edit-contact-info-view-mode').toggleClass('hidden');
    });

    $('.js-edit-contact-info-edit-mode .confirm').on('click', function() {
        ga('send', 'event', 'button', 'click', 'Users: Contact Info Save');

        var $btn = $(this);
        $btn.addClass('loading');
        var $parent = $btn.closest('.js-edit-contact-info');
        var $firstName = $parent.find('.js-first-name');
        var $lastName = $parent.find('.js-last-name');
        var $email = $parent.find('.js-email');

        // Grab all form values
        var formValues = $parent.find('.js-edit-contact-info-edit-mode').form('get values');

        $.ajax({
            type: "PUT",
            url: "/api/users/" + formValues.user_id,
            dataType: 'json',
            data: JSON.stringify(formValues),
            success: function (response) {
                $btn.removeClass('loading');

                $firstName.text(formValues.first_name);
                $lastName.text(formValues.last_name);
                $email.text(formValues.email);

                $parent.find('.js-edit-contact-info-edit-mode').toggleClass('hidden');
                $parent.find('.js-edit-contact-info-view-mode').toggleClass('hidden');
            },
            error: function (xhr) {
                $btn.removeClass('loading');

                var errorDetail = '';
                if (xhr.responseJSON && xhr.responseJSON.errors && xhr.responseJSON.errors[0]) {
                    errorDetail = xhr.responseJSON.errors[0].detail;
                }

                console.error('Error Saving:', xhr);
                showUserError(errorDetail);
            }
        });
    });

    $('.js-edit-contact-info-edit-mode .deny').on('click', function() {
        var $parent = $(this).closest('.js-edit-contact-info');
        var $editView = $parent.find('.js-edit-contact-info-edit-mode');

        // Restore Changes
        var $firstName = $parent.find('.js-first-name');
        var $lastName = $parent.find('.js-last-name');
        var $email = $parent.find('.js-email');

        $editView.find('input[name="first_name"]').val($firstName.text());
        $editView.find('input[name="last_name"]').val($lastName.text());
        $editView.find('input[name="email"]').val($email.text());

        $editView.toggleClass('hidden');
        $parent.find('.js-edit-contact-info-view-mode').toggleClass('hidden');
    });

    //------>
    try {
        $(".chosen").data("placeholder","Click to make selections...").chosen({
            width: "99%"
        });
    } catch (error) {
        console.error('users.js Error initializing chosen:', error);
    }
    
    //click on checkboxes to save email prefs
    $('.inquiries').change(function() {
        var uid = $(this).parent().parent('td').parent('tr').attr('id');
        if (uid !== 'new_user') {
            var value = $(this).attr('checked') ? 1 : 0;
            getsEmails(uid, 'inquiries', value);
        }
    });
    $('.reservations').change(function() {
        var uid = $(this).parent().parent('td').parent('tr').attr('id');
        if (uid !== 'new_user') {
            var value = $(this).attr('checked') ? 1 : 0;
            getsEmails(uid, 'reservations', value);
        }
    });
    $('.isadmin').change(function() {
        var uid = $(this).parent().parent('td').parent('tr').attr('id');
        if (uid !== 'new_user') {
            var value = $(this).attr('checked') ? 1 : 0;
            isAdmin(uid, value);
        }
    });
    $('.statements').change(function() {
        var uid = $(this).parent().parent('td').parent('tr').attr('id');
        if (uid !== 'new_user') {
            var value = $(this).attr('checked') ? 1 : 0;
            getsEmails(uid, 'statements', value);
        }
    });
    $('.access').change(function() {
        var uid = $(this).parent().parent('td').parent('tr').attr('id');
        if (uid !== 'new_user') {
            var value = $(this).attr('checked') ? 1 : 0;
            accessMyFoot(uid, value);
        }
    });
    $('#add_user_reveal,#cancel_user_reveal').click(function() {
        $('#new_user').toggleClass('hidden');
        $('#new_user_dialog').toggleClass('hidden');
        $('#add_user_reveal').toggleClass('hidden');
    });
    $('.numfacilities').click(function() {
        var uid = $(this).parent('td').parent('tr').attr('id');
        doExpand(uid);
    });
    $('.hidefacpicker').click(function() {
        var uid = $(this).parent('td').parent('tr').attr('id');
        resetExpand(uid.replace('fac-select-row-', ''));
    });
    $('.user-delete').click(function() {
        var uid = $(this).closest('tr').attr('id');
        var email = $(this).closest('tr').data('email');

        var $deleteModal = $('#user-delete-modal')
        $deleteModal.data('user_id',uid);
        $deleteModal.data('user_email',email);
        $('#user-delete-modal .delete-email').html(email);
        $deleteModal.modal('show');
    });
    $('#confirm-delete-user').click(function() {
        var $deleteModal = $('#user-delete-modal')
        var uid = $deleteModal.data('user_id');
        $.ajax({
            type: "DELETE",
            url: "/api/users/" + uid,
            success: function(msg) {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError){
                showUserError("An error occured, please try later or contact customer support.");
            }
        });
    });
    try {
        //make + and - icons toggle row and hover cursor
    $(".min-max-swap").live('click', function() {
        var whichFacSelect = $(this).attr("uid");

        if ($(this).attr("class") == "min-max-swap") {
            this.src = this.src.replace("expand","minimize");
            $("#fac_select_"+whichFacSelect).removeClass('hidden');
        } else {
            this.src = this.src.replace("minimize","expand");
            $("#fac_select_"+whichFacSelect).addClass('hidden');
        }

        $(this).toggleClass("on");
    }).hover(function() {
            $(this).css('cursor','pointer');
        }, function() {
            $(this).css('cursor','auto');
        });
    } catch (error) {
        console.error('users.js Error initializing (".min-max-swap").live:', error);
    }
    
    $passwordModal = $('#password-modal');
    $passwordModal.submit(function(event) {
        event.preventDefault();
        accessMyFoot($('#password-modal-user-id').val(), 1);
        $('#password-modal')[0].reset();
    });

    $passwordModal.on('hidden.bs.modal', function(){
        var uid = $('#password-modal-user-id').val();
        $('#password-modal')[0].reset();
        $('#gets_statements_'+uid).removeAttr("checked");
        //$('#myfoot_access_'+uid).removeAttr("checked"); //removed. let the logic handle it below
        $('#is_admin_'+uid).removeAttr("checked");
    });

    //make sure modal is empty on load
    $passwordModal[0].reset();
});

function resetExpand(uid) {
    $("#fac-select-row-" + uid).hide();
    $("#expansion_img_"+uid).replaceWith('<img src="'+CONFIG.cdnUrl+'/images/expand.png" class="min-max-swap" uid="'+uid+'" id="expansion_img_'+uid+'" style="cursor: pointer;"/>');
    $('.numfacilities').show();
    //reset facility selector
    refreshFacPicker(uid);
}

function doExpand(uid) {
    $('.numfacilities').hide();
    $("#fac-select-row-" + uid).show();
    $("#expansion_img_"+uid).replaceWith('<img src="'+CONFIG.cdnUrl+'/images/minimize.png" class="min-max-swap on" uid="'+uid+'" id="expansion_img_'+uid+'" style="cursor: pointer;"/>');
}

function isAdmin(uid, value) {
    $.ajax({
        type: "POST",
        url: "/user/isAdmin",
        data: { uid: uid, isAdmin: value },
        success: function(msg) {
            if (msg.match(/^\s*Error/)) {
                showUserError(msg);

                //set checkbox back on error
                if (value == 1) {
                    $("#is_admin_"+uid).removeAttr("checked");
                } else {
                    $("#is_admin_"+uid).attr('checked', true);
                }
            } else {
                if (value == 1) {
                    //admin implies myfoot access, gets statements, and gets emails by default
                    $("#gets_statements_"+uid).attr('checked', true);
                    $("#gets_emails_"+uid).attr('checked', true);
                    $("#geopage_only_"+uid).removeAttr('checked');
                    //udpate num facilities to all in UI
                    $("#num_facs_"+uid).replaceWith('<span id=num_facs_'+uid+'>All</span>');
                    $("#myfoot_access_"+uid).attr('checked', true);
                    accessMyFoot(uid, 1);
                } else {
                    //msg is returned as number of facilities user can access
                    $("#num_facs_"+uid).replaceWith('<span id=num_facs_'+uid+'>'+msg+'</span>');
                }

                //refresh facility selector
                refreshFacPicker(uid);
            }
            //$("#dynamic_msg").show();
        },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to make user an admin.  Try again later.");
        }
    });
}

function geopageOnly(uid) {
    var geopageOnly = $("#geopage_only_"+uid).is(':checked') ? 1 : 0;

    $.ajax({
        type: "POST",
        url: "/user/geopageOnly",
        data: "uid=" + uid + "&geopageOnly=" + geopageOnly,
        success: function(msg){
            if (msg.match(/^\s*Error/)) {
                showUserError(msg);

                //set checkbox back on error
                if (geopageOnly == 1) {
                    $("#geopage_only_"+uid).removeAttr("checked");
                } else {
                    $("#geopage_only_"+uid).attr('checked', true);
                }
            } else {
                if (geopageOnly == 1) {
                    $("#is_admin_"+uid).removeAttr('checked');
                    $("#gets_statements_"+uid).removeAttr('checked');
                    $("#myfoot_access_"+uid).attr('checked', true);
                    accessMyFoot(uid,1);
                } else {
                    $("#myfoot_access_"+uid).removeAttr('checked');
                }
            }
        },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to restrict user to GeoPages only.  Try again later.");
        }
    });
}


/**
 *
 * @param uid
 * @param type reservations|inquiries
 * @param value 0|1
 */
function getsEmails(uid, type, value) {

    $.ajax({
        type: "POST",
        url: "/user/getsEmails",
        data: { uid: uid,  type: type, value: value },
        success: function (msg) {
            if (msg.match(/^\s*Error/)) {
                showUserError(msg);

                // Set checkbox back to original value on error
                $("#gets_statements_"+uid).attr('checked', !value);
            }
        },
        error: function (xhr, ajaxOptions, thrownError) {
            showUserError("Unable to change emails.  Try again later.");
        }
    });
}

function passwordModalAppend(property) {
    $("<input type='hidden' id='modalDismissProperty' value='#" + property + "'>").appendTo("#modal-password");
}

function detoggle() {
    var val = $('#modalDismissProperty').val();
    $(val).removeAttr("checked");
    $('#modalDismissProperty').remove();
}

function accessMyFoot(uid, value) {
    //populated only if modal was displayed
    var notify = $('#password-modal-notify').is(':checked') ? 1 : 0;
    var pass = $('#modal-password').val();
    var passconfirm = $('#modal-password-confirm').val();
    var postVars = { uid: uid, accessMyfoot: value, notify: notify};
    //if the password modal is up, pass those vals along so we can validate
    if ($('#password-modal').css('display') == 'block') {
        postVars['pass'] = pass;
        postVars['passconfirm'] = passconfirm;
    }

    $.ajax({
        type: "POST",
        url: "/user/accessMyfoot",
        data: postVars,
        success: function(msg){
            if (msg.match(/^\s*Error/)) {
                if (msg.match(/\s*needpass/)) {
                    $('#password-modal-user-id').val(uid);
                    passwordModalAppend('myfoot_access_' + uid);
                    $('#password-modal').bModal();
                } else {
                    showUserError(msg);
                    //set checkbox back on error
                    if (value == 1) {
                        $("#myfoot_access_"+uid).removeAttr("checked");
                    } else {
                        $("#myfoot_access_"+uid).attr('checked', true);
                    }
                }

            } else {
                if (value == 0) {
                    //with no access, cannot do statements/disputes, or be admin
                    $("#gets_statements_"+uid).removeAttr("checked");
                    $("#is_admin_"+uid).removeAttr("checked");
                    $("#geopage_only_"+uid).removeAttr("checked");
                }
                //make sure password collection modal is closed
                $('#password-modal').bModal('hide');
            }
        },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to change MySpareFoot access.  Try again later.");
        }
    });
}

//updates facility access for user
function updateFacs(uid) {
    var multiSelFields = $("select#fac_select_"+uid).val();
    var facilityIds = "";
    var allFacs = 0;
    var numFacs = 0;

    //if one of the values is all, then just set allFacs=1 continue
    if (multiSelFields != null) {
        for(var i=0; i<multiSelFields.length; i++) {
            numFacs++;
            if (multiSelFields[i] == "all") {
                allFacs = 1;
                numFacs = "All";
                facilityIds = "";
                break;
            }
            facilityIds = facilityIds + multiSelFields[i] + ",";
        }
    }

    //remove last comma
    facilityIds = facilityIds.slice(0, -1);

    $.ajax({
        type: "POST",
        url: "/user/updateFacs",
        data: "all=" + allFacs +
            "&uid=" + uid +
            "&facIds=" + facilityIds,
        success: function(msg){
                if (msg.match(/^\s*Error/)) {
                    showUserError(msg);
                } else {
                    //check myfoot access checkbox on andy facility change
                    $("#myfoot_access_"+uid).attr('checked', true);

                    //update num facs in UI
                    $("#num_facs_"+uid).replaceWith('<span id=num_facs_'+uid+'>'+numFacs+'</span>');

                    showUserError("Facility Access Updated!");
                    resetExpand(uid);
                }
            },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to change facility access at this time.  Contact <NAME_EMAIL>");
            }
    });
}

function createNewUser() {
    var email = $("#email").val();
    var fname = $("#fname").val();
    var lname = $("#lname").val();
    var notify = $("#notify").is(':checked') ? 1 : 0;
    var pass = $("#password").val();
    var passconfirm = $("#password_confirm").val();
    var getsEmails = $("#gets_emails").is(':checked') ? 1 : 0;
    var getsStatements = $("#gets_statements").is(':checked') ? 1 : 0;
    var geopageOnly = $("#geopage_only").is(':checked') ? 1 : 0;
    var accessMyfoot = $("#myfoot_access").is(':checked') ? 1 : 0;
    var isAdmin = $("#is_admin").is(':checked') ? 1 : 0;
    var multiSelFields = $("#fac_select").val();
    var facilityIds = "";
    var allFacs = 0;
    var numFacs = 0;

    //if one of the values is all, then just set allFacs=1 continue
    if (multiSelFields != null) {
        for(var i=0; i<multiSelFields.length; i++) {
            numFacs++;
            if (multiSelFields[i] == "all") {
                allFacs = 1;
                numFacs = "All";
                facilityIds = "";
                break;
            }
            facilityIds = facilityIds + multiSelFields[i] + ",";
        }
    }

    //remove last comma
    facilityIds = facilityIds.slice(0, -1);

    $.ajax({
        type: "POST",
        url: "/user/create",
        data: {
            email: email,
            fname: fname,
            lname: lname,
            notify: notify,
            pass: pass,
            passconfirm: passconfirm,
            getsEmails: getsEmails,
            getsStatements: getsStatements,
            geopageOnly: geopageOnly,
            accessMyfoot: accessMyfoot,
            isAdmin: isAdmin,
            facilityIds: facilityIds,
            allFacs: allFacs
        },
        success: function(msg){
            if (msg.match(/^\s*Error/)) {
                showUserError(msg);
            } else {
                showUserError("New User Created!");
                //for now, refresh page
                //TODO: make this appear inline as a new row?
                window.location = "/user";
                return false;
            }
        },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to add user at this time.  Try again later.");
        }
    });
}

function refreshFacPicker(uid) {
    //reset original selections
    $.ajax({
        type: "POST",
        url: "/user/getFacAccess",
        data: "uid=" + uid,
        success: function(msg){
                if (msg.match(/^\s*Error/)) {
                    showUserError(msg);
                } else {
                    //clear facility selector
                    $("#fac_select_"+uid+" option").each(function () {
                        $(this).removeAttr("selected");
                    });

                    //reset selections
                    if (msg == "All") {
                        $("#fac_select_"+uid).prop("selectedIndex", "All");
                    } else {
                        var ids = jQuery.parseJSON(msg);
                        for(var i=0; i<ids.length; i++) {
                            $("#fac_select_"+uid+" option[value='" + ids[i] + "']").prop('selected', true);
                        }
                    }

                    //refresh facility selector
                    $("#fac_select_"+uid).trigger("liszt:updated");
                }
            },
        error: function (xhr, ajaxOptions, thrownError){
            showUserError("Unable to reset facility selector.  Make your own selections.");
            }
    });
}

