$(function(){
    var signUpForm = $('#signup-company-form');
    signUpForm.submit(function(event){

        event.preventDefault();

        signUpForm.find('.form-group').removeClass('has-error');
        var errors = 0;
        var firstError;

        signUpForm.find('.form-group').each(function() {
            if($(this).find('input, select').val() == '') {
                $(this).addClass('has-error');

                if(errors == 0){
                    firstError = $(this).find('input, select');
                    window.console.log(firstError);
                }

                errors++;
            }
        });

        if (errors > 0) {

            $('html,body').animate({scrollTop: ($(firstError).offset().top - $('.navbar').height() - 50)});

        } else {
            $.ajax({
                type: 'POST',
                dataType: 'json',
                url: "/signup-start/add-company",
                data: $('#signup-company-form').serialize(),
                beforeSend: function() {
                    $('#submit').button('loading');
                    $('.content-footer .loading').toggleClass('hide');
                },
                success: function(data) {
                    console.error('39 company.js ', data);
                    if (data.success == 0) {
                        $('#submit').button('reset');
                        $('.content-footer .loading').toggleClass('hide');
                        showUserError(data.message);
                    } else {
                        //this logic in process login will direct them to next step
                        // window.location = '/login/process-login?email=' + encodeURIComponent($('#email').val()) + '&password=' + encodeURIComponent($('#password').val()) + '&csrf_token=' + encodeURIComponent($('#login_csrf_token').val());
                        window.location = '/signup-end/terms';
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    $('#submit').button('reset');
                    $('.content-footer .loading').toggleClass('hide');
                    showUserError('Something went wrong in our system! Contact <EMAIL> for help.');
                    console.log(xhr.responseText);
                }
            });
        }
    });
});
