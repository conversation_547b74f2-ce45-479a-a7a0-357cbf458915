{"name": "sparefoot/pita", "prefer-stable": true, "minimum-stability": "dev", "authors": [], "description": "MyFoot", "license": "proprietary", "repositories": [{"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/emails_service_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/phlow_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/reservation_rules.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/authorization.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/error_logger.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/pillar_lib.git"}, {"type": "vcs", "url": "https://github.com/SpareFoot/rollbar-php"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/genesis.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/sf_service_bundle.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/salesforce_client.git"}, {"type": "vcs", "url": "https://gitlab.com/storable/sparefoot/marketplace_api_client_bundle"}], "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "require": {"php": "^8.2", "ext-json": "*", "ext-pdo": "*", "ext-zip": "*", "ext-soap": "*", "ext-gd": "*", "datadog/dd-trace": "^0.97", "datadog/php-datadogstatsd": "^1.4", "guzzlehttp/guzzle": "7.5.1", "predis/predis": ">=1.0.0", "php-http/curl-client": "^2.3.0", "rollbar/rollbar": "~1", "sparefoot/emails_service_client": "dev-php8", "sparefoot/error-logging": "dev-master", "sparefoot/phlow_client": "dev-php8", "sparefoot/pillar": "dev-master", "sparefoot/reservation_rules": "dev-master", "sparefoot/authorization": "dev-master", "sparefoot/salesforce_client": "dev-php8", "sparefoot/genesis": "dev-php8", "symfony/asset": "5.4.*", "symfony/browser-kit": "^5.4", "symfony/cache": "^5.4", "symfony/config": "^5.4", "symfony/console": "^5.4", "symfony/css-selector": "^5.4", "symfony/dotenv": "^5.4", "symfony/flex": "^2.3", "symfony/framework-bundle": "^5.4", "symfony/http-client": "^5.4", "symfony/http-foundation": "^5.4", "symfony/http-kernel": "^5.4", "symfony/messenger": "^5.4", "symfony/monolog-bundle": "^3.8", "symfony/phpunit-bridge": "^5.4", "symfony/proxy-manager-bridge": "^5.4", "symfony/security-bundle": "^5.4", "symfony/serializer": "^5.4", "symfony/property-access": "^5.4", "symfony/property-info": "^5.4", "symfony/twig-bundle": "^5.4", "symfony/yaml": "^5.4", "symfony/routing": "^5.4", "voodoophp/handlebars": "dev-master", "open-telemetry/opentelemetry-auto-symfony": "dev-main", "open-telemetry/sdk": "^1.1", "open-telemetry/exporter-otlp": "^1.1", "php-http/guzzle7-adapter": "^1.1", "zordius/lightncandy": "*"}, "require-dev": {"mockery/mockery": "^1.6", "phpunit/phpunit": "^10.4", "symfony/browser-kit": "5.4.*", "symfony/stopwatch": "5.4.*"}, "autoload": {"psr-4": {"Sparefoot\\MyFootService\\": "src/", "Sparefoot\\MyFootTests\\": "tests/"}, "classmap": ["vendor/sparefoot/genesis/src", "vendor/sparefoot/genesis/lib/centaur-php"]}, "autoload-dev": {"psr-4": {"Sparefoot\\MyFootTests\\": "tests/"}}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}, "config": {"allow-plugins": {"ocramius/package-versions": true, "php-http/discovery": true, "symfony/flex": true, "tbachert/spi": true}}}