# common command

```bash
vendor/bin/php-cs-fixer --rules=@PSR1,@PSR2,@Symfony,-yoda_style  fix src
```

```bash
docker-compose -f docker_compose/local/docker-compose-local.yml up
```

```bash

php -d xdebug.mode=coverage bin/phpunit --stderr --log-junit junit.xml --coverage-text --colors=never --coverage-html coverage --coverage-clover clover.xml --testsuite Full

```

```bash
# Install and run Gulp
./node_modules/.bin/gulp
npm run gulp watch
```

# MyFoot - SpareFoot Account & Facility Management System

[![PHP Version](https://img.shields.io/badge/PHP-8.2+-blue.svg)](https://php.net)
[![Symfony](https://img.shields.io/badge/Symfony-5.4-green.svg)](https://symfony.com)
[![Ember.js](https://img.shields.io/badge/Ember.js-2.1.2-orange.svg)](https://emberjs.com)
[![Node.js](https://img.shields.io/badge/Node.js-8.9.4-brightgreen.svg)](https://nodejs.org)

## Table of Contents
- [Quick Start](#quick-start)
- [Project Overview](#project-overview)
- [Architecture](#architecture)
- [Ember.js Application Domain](#emberjs-application-domain)
- [Development Environment](#development-environment)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Deployment](#deployment)

## Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js v8.9.4 (use `nvm use v8.9.4`)
- PHP 8.2+
- Composer

### Local Development Setup
```bash
# Clone and navigate to project
cd /opt/sparefoot/apps/myfoot

# Install dependencies
php composer.phar install
npm install

# Start Docker container
docker-compose -f docker_compose/local/docker-compose-local.yml up --force-recreate

# In separate terminal - Start Ember development server
nvm use v8.9.4
cd ember/features
rm -rf node_modules bower_components
npm install && bower install
npm run start

# In another terminal - Start Gulp for CSS processing
npm run gulp
```

Access the application at: `http://localhost:9019`
## 2025.09.22
before improve Authorization
## 2025.07.15


# MyFoot Project Overview



## Project Purpose

**MyFoot** (MySpareFoot) is a comprehensive **Facility Management System (FMS)** that serves as the primary account and facility management tool for SpareFoot's storage customers and Storable employees.

### Core Functionality
MyFoot enables storage facility owners and operators to:



- Manage storage facilities and units
- Add new storage facilities and units
- Edit existing facilities and units
- Control visibility of listings
- Manage bidding settings
- Handle customer bookings and move-ins
- View reviews
- Access reporting and insights

## Technology Stack

MyFoot is built as a **multi-layered application** combining modern and legacy technologies:

### Backend Technologies
- **PHP 8.2+** with **Symfony 5.4** framework
- **Zend Framework** (legacy components)
- **MySQL** database with reporting database
- **Redis** for session management
- **Docker** containerization
- **Composer** for PHP dependency management

### Frontend Technologies
- **Ember.js 2.1.2** for dynamic single-page application features
- **jQuery** and **Semantic UI** for traditional web interface
- **Gulp** for asset processing and CSS compilation
- **Bower** for frontend package management (Ember app)
- **npm** for Node.js dependencies

### External Integrations
- **OAuth2** authentication
- **Salesforce** via Genesis client
- **NetSuite** for payment processing
- **PITA** via WSDL
- **Datadog** for APM and monitoring
- **Rollbar** for error tracking

### Development & Testing
- **PHPUnit** for PHP unit testing
- **Selenium WebDriver** for integration testing
- **Steward** test framework
- **WebDriver.IO** for smoke testing
- **Mockery** for mocking in tests

### Infrastructure
- **AWS ECR** for container registry
- **Apache** web server
- **Memcached** for caching
- **Node.js v8.9.4** (specific version requirement)

## Authentication Workflow

MyFoot uses a sophisticated multi-layered authentication system combining **OAuth2**, **Symfony Security**, and **Remember Me** functionality.

### Authentication Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Login    │───▶│ CustomAuth       │───▶│ OAuth Service   │
│   Form          │    │ enticator        │    │ (Authorization) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Remember Me     │    │ Session &        │    │ Token Storage   │
│ Cookie          │    │ Token Binding    │    │ (Cabinet)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Authentication Flow

#### 1. **Login Process**
```php
// Login form submits to /login_check
POST /login_check
├── _username: <EMAIL>
├── _password: ********
├── _remember_me: on (optional)
└── _csrf_token: ABC123...
```

**Flow Steps:**
1. **CustomAuthenticator** validates CSRF token
2. **UserOauth::authenticate()** calls Authorization Service
3. OAuth service returns access token + refresh token
4. Tokens stored in Cabinet (server-side session storage)
5. User object created from Genesis UserAccess entity
6. **Remember Me** cookie set (if requested) - handled by Symfony
7. Session bound to OAuth token for security

#### 2. **Token Management**
```php
// Token structure
Token {
    identifier: "access_token_string",
    refresh: "refresh_token_string", 
    expires: 1695225600, // Unix timestamp
    host: "https://auth.sparefoot.com"
}
```

**Token Lifecycle:**
- **Access tokens** expire after ~14 days
- **Refresh tokens** used for automatic renewal
- **Remember Me** tokens last 2 weeks (configurable)
- **Automatic renewal** when 80% of token lifetime used (90% for Remember Me users)

#### 3. **Request Authentication**
Every authenticated request goes through:

```php
// TokenValidationListener (runs on each request)
1. Check session has oauth_token_id
2. Validate current token matches session
3. Check if token needs renewal → auto-renew if needed
4. Update session with new token info
5. Continue request OR redirect to login if invalid
```

#### 4. **Remember Me Integration**
```yaml
# security.yaml configuration
remember_me:
    secret: '%kernel.secret%'
    lifetime: 1209600  # 2 weeks
    secure: true       # HTTPS only in prod
    httponly: true     # XSS protection
    samesite: strict   # CSRF protection
```

**Remember Me Flow:**
- Checkbox checked → `RememberMeBadge` added to authentication
- Symfony handles secure cookie creation/validation
- Extended OAuth token lifetime for Remember Me users
- More aggressive token renewal (90% vs 80% threshold)

#### 5. **Logout Process**
```php
// Multi-step cleanup process
1. UserOauth::logout()        // Clear OAuth tokens
2. Clear Cabinet metadata     // Remove refresh tokens
3. Clear session data        // Remove bound token info  
4. Symfony clears Remember Me cookies automatically
5. Redirect to login page
```

### Security Features

#### **Token Security**
- **Token-Session Binding**: Each login binds OAuth token to session
- **Consistency Validation**: Continuous validation prevents token hijacking
- **Automatic Renewal**: Proactive token refresh before expiration
- **Secure Storage**: Refresh tokens stored server-side in Cabinet

#### **Remember Me Security**
- **HttpOnly cookies**: Prevent XSS attacks
- **Secure flag**: HTTPS-only transmission
- **SameSite=strict**: CSRF protection
- **Extended token lifetime**: Longer OAuth sessions for convenience

#### **Request Security**
- **CSRF Protection**: All forms include CSRF tokens
- **Token Validation**: Every request validates token consistency
- **Automatic Logout**: Invalid/expired tokens force logout
- **IP Filtering**: God users get IP allowlist entries

### Authentication Configuration

#### **Environment-Specific Settings**
```yaml
# Production
security:
    firewalls:
        main:
            remember_me:
                secure: true           # Force HTTPS
                domain: '.sparefoot.com'

# Development  
security:
    firewalls:
        main:
            remember_me:
                secure: false          # Allow HTTP
```

#### **OAuth Service Endpoints**
- **Production**: `https://auth.sparefoot.com`
- **Staging**: `https://auth.sparefoot.extrameter.com` 
- **Development**: `https://auth.sparefoot.moreyard.com`
- **Local**: `http://auth.sparefoot.localhost:8888`

### Common Authentication Issues & Solutions

#### **Token Renewal Failures**
```php
// Logs show: "Token Renewal Warning: Failed to renew token"
// Solution: Check Cabinet service and refresh token validity
UserOauth::needsRenew();  // Check if renewal needed
UserOauth::renew();       // Manual renewal attempt
```

#### **Remember Me Not Working**
```yaml
# Check security configuration
remember_me:
    lifetime: 1209600     # Must be set
    user_providers: [custom_provider]  # Must reference your provider
```

#### **Session/Token Mismatch**
```php
// TokenValidationListener detects inconsistency
// Automatic logout triggered - check logs for:
"Token validation failed - forcing logout"
```

### Developer Guidelines

#### **Testing Authentication**
```bash
# Test login flow
curl -X POST http://localhost:9019/login_check \
  -d "_username=<EMAIL>" \
  -d "_password=password" \
  -d "_remember_me=on"

# Check token validation
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:9019/api/accounts
```

#### **Custom Authentication Extensions**
```php
// Extend User class for token-aware methods
$user->isTokenExpired();        // Check token status
$user->needsTokenRenewal();     // Check renewal need
$user->renewToken();            // Manual renewal
$user->isRememberMeUser();      // Check if Remember Me active
```

#### **Debugging Authentication**
- Check logs in `var/log/` for authentication events
- Monitor Cabinet service for token storage issues
- Validate OAuth service connectivity
- Test Remember Me cookie creation/deletion

### CustomUserProvider - The Authentication Bridge

The **CustomUserProvider** is a critical component that bridges Symfony Security with the OAuth2 system and legacy Genesis framework. It handles user loading, token validation, and session management.

#### **Core Responsibilities**

```php
class CustomUserProvider implements UserProviderInterface, PasswordUpgraderInterface
{
    // 1. Load users from Genesis database
    // 2. Validate OAuth token consistency  
    // 3. Handle automatic token renewal
    // 4. Manage legacy system integration
}
```

#### **User Loading Process**

```php
// Called during authentication and on each request
public function loadUserByIdentifier(string $identifier): UserInterface
{
    // 1. Load from Genesis database
    $genesisUser = \Genesis_Service_UserAccess::loadByEmail($identifier);
    
    // 2. Validate OAuth token consistency
    $this->validateTokenConsistency($identifier);
    
    // 3. Create Symfony User object
    $user = new User($genesisUser);
    
    // 4. Initialize legacy systems
    UserAccountFacilityContext::init($user->getId());
    
    // 5. Handle token renewal if needed
    $this->handleTokenRenewal($user, $request);
    
    return $user;
}
```

#### **Token Validation & Renewal**

**Automatic Token Validation:**
```php
private function validateTokenConsistency(string $identifier): void
{
    // Check session has oauth_token_id
    $sessionTokenId = $request->getSession()->get('oauth_token_id');
    
    // Validate current token matches session
    if (!UserOauth::validateTokenConsistency($sessionTokenId, $sessionTokenExpires)) {
        // Clear invalid session data
        $request->getSession()->remove('oauth_token_id');
        $this->logger->warning('Token validation failed during user loading');
    }
}
```

**Proactive Token Renewal:**
```php
private function handleTokenRenewal(User $user, Request $request): void
{
    if ($user->needsTokenRenewal()) {
        $renewalSuccess = $user->renewToken();
        
        if ($renewalSuccess) {
            // Update session with new token info
            $token = UserOauth::getToken();
            $request->getSession()->set('oauth_token_id', $token->getIdentifier());
            $this->logger->info('OAuth token renewed during user loading');
        }
    }
}
```

#### **User Refresh Security**

```php
public function refreshUser(UserInterface $user): UserInterface
{
    // Check if user's token is expired before refreshing
    if ($user->isTokenExpired()) {
        $this->logger->info('User token expired during refresh, forcing re-authentication');
        throw new UserNotFoundException('User token expired, re-authentication required.');
    }
    
    return $this->loadUserByUsername($user->getUsername());
}
```

#### **Integration Points**

**Legacy System Compatibility:**
- **Genesis Integration**: Loads users from `Genesis_Service_UserAccess`
- **Static Variables**: Sets `ServiceUser::setUserAccess()` for legacy code
- **Context Initialization**: Initializes `UserAccountFacilityContext` for facility management

**Modern Security Integration:**
- **OAuth2 Tokens**: Validates and renews OAuth access tokens
- **Session Management**: Binds tokens to sessions for security
- **Symfony Security**: Implements `UserProviderInterface` for framework integration

#### **Service Configuration**

```yaml
# config/services.yaml
Sparefoot\MyFootService\Security\CustomUserProvider:
    public: true
    arguments:
        - '@request_stack'    # For request context
        - '@logger'          # For comprehensive logging
```

#### **Logging & Monitoring**

The CustomUserProvider provides extensive logging for debugging:

```php
// Token validation events
$this->logger->warning('Token validation failed during user loading', [
    'user' => $identifier,
    'session_token_id' => $sessionTokenId
]);

// Token renewal events  
$this->logger->info('OAuth token renewed during user loading', [
    'user' => $user->getEmail()
]);

// Error handling
$this->logger->error('Error handling token renewal', [
    'user' => $user->getEmail(),
    'error' => $e->getMessage()
]);
```

#### **Security Benefits**

1. **Token Hijacking Prevention**: Validates token-session binding on every user load
2. **Automatic Token Renewal**: Prevents unexpected logouts due to expired tokens
3. **Expired Token Detection**: Forces re-authentication when tokens are truly expired
4. **Comprehensive Logging**: Tracks all authentication events for security monitoring
5. **Graceful Error Handling**: Logs errors without breaking authentication flow

#### **Development & Testing**

**Testing User Loading:**
```php
// Test user loading with token validation
$userProvider = $container->get(CustomUserProvider::class);
$user = $userProvider->loadUserByIdentifier('<EMAIL>');

// Check token methods are available
$isExpired = $user->isTokenExpired();
$needsRenewal = $user->needsTokenRenewal();
```

**Monitoring Logs:**
```bash
# Watch authentication logs
tail -f var/log/dev.log | grep "user loading\|token"

# Check for token renewal events
grep "OAuth token renewed" var/log/*.log
```

#### **Common Issues & Solutions**

**User Not Found:**
```php
// Check Genesis database connectivity
$genesisUser = \Genesis_Service_UserAccess::loadByEmail($email);
if (!$genesisUser) {
    // User doesn't exist in Genesis system
}
```

**Token Validation Failures:**
```php
// Check OAuth service connectivity and Cabinet storage
UserOauth::validateTokenConsistency($sessionTokenId, $sessionTokenExpires);
```

**Legacy System Integration:**
```php
// Ensure static variables are set for legacy code
ServiceUser::setUserAccess($genesisUser);
UserAccountFacilityContext::init($user->getId());
```

The **CustomUserProvider** serves as the central hub for user authentication, seamlessly integrating modern OAuth2 security with legacy Genesis systems while providing robust token management and security validation.

## Architecture Overview



The project has a multi-layered architecture that combines several technologies:



### 1. Frontend Architecture



The frontend is built using a combination of:



1. **Ember.js Application**:
   - Located in the `ember/features` directory
   - Uses Ember.js 2.1.2 (an older version)
   - Follows Ember's conventional structure with models, routes, controllers, and templates
   - Handles dynamic UI components and client-side routing
   - Communicates with the backend via RESTful API endpoints



2. **Traditional Web Interface**:
   - Uses jQuery and Semantic UI for some parts of the interface
   - CSS is managed through multiple files that are concatenated using Gulp
   - The styling system includes `myfoot.css`, `myfoot-new.css`, and `features.css`



### 2. Backend Architecture



The backend is primarily built with:



1. **PHP with Zend Framework**:
   - Located in the `application` directory
   - Uses Zend Framework for MVC architecture
   - Contains controllers, models, and services
   - Handles routing, authentication, and business logic
   - Exposes API endpoints for the Ember frontend



2. **API Layer**:
   - RESTful API endpoints defined in `application/Bootstrap.php`
   - Controllers prefixed with "Api" (e.g., `ApiAccountController`, `ApiFacilityController`)
   - Serves data to the Ember frontend



### 3. Integration Points



The application integrates with several external services:



- SearchService
- Reporting
- Authorization (OAuth2)
- PITA via WSDL
- Netsuite for credit card processing
- Salesforce via Genesis
- Redis for account sessions



## Project Structure

The MyFoot codebase is organized into several key directories:

```
myfoot/
├── application/          # Legacy Zend Framework application
├── config/               # Symfony configuration files
├── docker_compose/       # Docker configuration for different environments
├── ember/                # Ember.js application
│   └── features/         # Main Ember.js application code
├── etc/                  # Server configuration files
├── public/               # Web-accessible files
│   ├── css/              # CSS files
│   ├── js/               # JavaScript files
│   ├── new-ui/           # New UI assets
│   └── vendors/          # Third-party libraries
├── src/                  # Symfony application code
│   ├── Controller/       # API controllers
│   ├── Entity/           # Data models
│   └── Service/          # Business logic services
├── templates/            # Twig templates
├── tests/                # Test suites
│   ├── e2e/              # End-to-end tests
│   ├── integration/      # Integration tests
│   └── unit/             # Unit tests
└── vendor/               # Composer dependencies
```

### Key Configuration Files
- `composer.json` - PHP dependencies
- `package.json` - Node.js dependencies
- `docker-compose-local.yml` - Local Docker configuration
- `Dockerfile` - Container definition
- `.env` - Environment variables
- `config/services.yaml` - Symfony service configuration
- `ember/features/config/environment.js` - Ember environment configuration

## Development Environment

The project requires a specific development setup:



1. **Docker Container**: The main application runs in a Docker container
2. **Ember Server**: The Ember application requires its own server
3. **Gulp**: Used for CSS processing and hot reloading



The project requires specific Node.js versions (v8.9.4) and has separate dependency management for:
- The main application (using Composer for PHP and npm for JavaScript)
- The Ember application (using npm and Bower)

### Environment Configuration

MyFoot uses environment-specific configuration through `.env` files and Docker environment variables:

#### Key Environment Variables
```bash
# Application
APP_NAME=myfoot
APP_ENV=dev|prod|test
APP_DEBUG=true|false

# Database
MYSQL_SPAREFOOT_HOST=db-dev.sparedev.com
MYSQL_SPAREFOOT_DB=sparefoot
MYSQL_SPAREFOOT_USER=username
MYSQL_SPAREFOOT_PASS=password

# External Services
PHLOW_HOSTNAME=localhost
SALESFORCE_*=various_salesforce_configs
DATADOG_*=monitoring_configs

# Caching
MEMCACHE_SERVER=memcached_myfoot:11211
```

#### Environment-Specific Domains
- **Production**: `my.sparefoot.com`, `mysparefoot.com`
- **Staging**: `my.sparefoot.extrameter.com`, `mysparefoot.extrameter.com`
- **Development**: `my.sparefoot.moreyard.com`, `mysparefoot.moreyard.com`
- **Local**: `my.sparefoot.localhost`, `localhost:9019`



## Key Features



Based on the codebase, here are some of the main features:



1. **Facility Management**:
   - Adding and editing storage facilities
   - Managing units within facilities
   - Controlling visibility of listings



2. **Customer Management**:
   - Viewing and managing customer information
   - Handling bookings and move-ins
   - Processing contactless and online move-ins



3. **Booking System**:
   - Processing new bookings
   - Managing move-ins
   - Handling booking denials and reminders



4. **Review Management**:
   - Viewing and responding to customer reviews



5. **Reporting and Insights**:
   - Dashboard with key metrics
   - Detailed reports on performance
   - Visualization of data using Chartist



6. **Bidding System**:
   - Managing bid settings
   - Optimizing bidding strategy



## Technical Details



1. **Ember Application Structure**:
   - Models define data structures (facilities, units, bookings, etc.)
   - Routes handle navigation and data loading
   - Controllers manage user interactions
   - Templates render the UI



2. **API Structure**:
   - RESTful endpoints for various resources
   - Authentication using OAuth
   - JSON data format



3. **Testing**:
   - Unit tests using PHPUnit
   - API integration tests
   - Frontend integration tests using Steward and Selenium
   - Smoke tests using Webdriver.IO



## Development Workflow



The project follows a specific workflow:
1. Local development using Docker
2. Manual deployment to development environment
3. Testing across multiple layers
4. Deployment to staging and production



The README mentions that the project is "like an onion" with multiple technology layers, each with its own dependencies and configuration. This makes it a complex but comprehensive system for managing storage facilities and customer interactions.



## Ember.js Application Domain Structure

The MyFoot project includes a sophisticated **Ember.js 2.1.2** single-page application that handles the dynamic, interactive features of the facility management system. This Ember application is embedded within the main PHP application and provides a modern user experience for complex workflows.

### Ember Application Overview

The Ember.js application is located in the `ember/features/` directory and serves as the primary interface for:
- **Facility and unit management** - Adding, editing, and organizing storage facilities
- **Customer management** - Viewing tenants, reservations, and customer data
- **Booking processing** - Handling move-ins, denials, and booking workflows
- **Review management** - Displaying and responding to customer reviews
- **Reporting dashboard** - Interactive charts and analytics
- **Bidding optimization** - Managing bid settings and strategy

### Domain Configuration

The Ember.js application is configured to run within a specific domain structure across different environments:

1. **Main Application Domain**:
   - In production: `my.sparefoot.com` or `mysparefoot.com`
   - In staging: `my.sparefoot.extrameter.com` or `mysparefoot.extrameter.com`
   - In development: `my.sparefoot.moreyard.com` or `mysparefoot.moreyard.com`
   - Locally: `my.sparefoot.localhost` or `localhost:9019`

2. **Services Base URL**:
   - The application uses a global variable `App.servicesBaseUrl` which is set based on the environment:
     - Production: `sparefoot.com`
     - Staging: `sparefoot.extrameter.com`
     - Development: `sparefoot.moreyard.com`
     - Local: `sparefoot.localhost`

3. **Ember Application Root**:
   - The Ember application is mounted at the DOM element with ID `#featuresApp`
   - It uses the `history` location type for routing (HTML5 History API)

## API Endpoints Called by the Ember.js Application

The Ember.js application makes various API calls to both internal and external services. Here are the main categories and examples:

### 1. Internal API Endpoints (within the same domain)

The main application adapter sets a namespace of `api` for all API calls, so most internal endpoints start with `/api/`:

```javascript
// From application.js adapter
export default DS.RESTAdapter.extend({
    coalesceFindRequests: true,
    namespace: 'api',
    // ...
});
```

**Facility-related endpoints**:
- `GET /api/facilities/:id` - Get facility details
- `GET /api/accounts/:account_id/facilities` - Get all facilities for an account
- `GET /api/facilities/:facility_id/reviews` - Get reviews for a facility

**Unit-related endpoints**:
- `GET /api/units/:id` - Get unit details
- `GET /api/facilities/:facility_id/units` - Get all units for a facility
- `POST /api/facilities/:facility_id/units` - Create a new unit
- `DELETE /api/units/:id` - Delete a unit

**Booking-related endpoints**:
- `GET /api/facilities/:facility_id/bookings` - Get bookings for a facility
- `PUT /api/bookings/:id` - Update a booking (e.g., mark as moved in)
- `GET /api/bookings/:confirmation_code/move-in` - Process a move-in
- `GET /api/bookings/:confirmation_code/deny` - Deny a booking
- `GET /api/bookings/:confirmation_code/remind-me-later` - Set a reminder for a booking

**Account-related endpoints**:
- `GET /api/accounts/:account_id` - Get account details
- `GET /api/accounts/:account_id/specials` - Get specials for an account

**Customer-related endpoints**:
- `GET /api/customers/:customer_filter` - Get customers filtered by status (e.g., tenants, reservations)

### 2. External Service API Endpoints

The application also makes calls to external microservices:

**Reporting Service**:
```javascript
// From account-overview.js adapter
export default DS.RESTAdapter.extend({
    host: '//reporting.' + App.servicesBaseUrl,
    // ...
});
```

Example endpoints:
- `GET //reporting.sparefoot.com/accounts/:id/overview` - Get account overview data
- `GET //reporting.sparefoot.com/accounts/:id/inventory` - Get inventory data
- `GET //reporting.sparefoot.com/accounts/:id/bookings` - Get bookings data
- `GET //reporting.sparefoot.com/facilities/:facility_id/bids?type=city` - Get bidding data

**Other External Services**:
- Authorization Service: `https://auth.sparefoot.com` (or equivalent in other environments)
- Location Service: `https://locationservice.sparefoot.com`
- Booking Service: `https://bookingservice.sparefoot.com`
- Search Service: `https://search.sparefoot.com`
- Client API Service: `https://client-api-service.sparefoot.com`
- Bid Optimizer Service: `https://bid-optimizer.sparefoot.com`

## Ember.js Routes and URL Structure

The Ember application defines these main routes in its router:

```javascript
Router.map(function() {
    this.route('listings');
    this.route('overview');
    this.route('contactlessMoveIns', {
        path: 'move-ins/contactless'
    });
    this.route('onlineMoveIns', {
        path: 'move-ins/online'
    });
    this.route('customers', {
        path: 'customers/:customer_filter'
    });
    this.route('bookingsMoveIn', {
        path: 'bookings/:confirmation_code/move-in'
    });
    this.route('bookingsDeny', {
        path: 'bookings/:confirmation_code/deny'
    });
    this.route('bookingsRemindMeLater', {
        path: 'bookings/:confirmation_code/remind-me-later'
    });
    this.route('reviews', function() {
        this.route('show', {
            path: ':id'
        });
    });
});
```

This translates to these URL paths in the application:
- `/listings` - View and manage facility listings and units
- `/overview` - Account overview dashboard
- `/move-ins/contactless` - Manage contactless move-ins
- `/move-ins/online` - Manage online move-ins
- `/customers/:customer_filter` - View customers (filtered by status)
- `/bookings/:confirmation_code/move-in` - Process a move-in for a booking
- `/bookings/:confirmation_code/deny` - Deny a booking
- `/bookings/:confirmation_code/remind-me-later` - Set a reminder for a booking
- `/reviews` - View all reviews
- `/reviews/:id` - View a specific review

## Integration with Parent Application

The Ember.js application is embedded within the main PHP application. The PHP application:

1. Sets up the environment and configuration
2. Provides global variables that the Ember app uses:
   ```javascript
   App.authBearerToken = '<?= $this->authBearerToken ?>';
   App.servicesBaseUrl = '<?= $this->servicesBaseUrl ?>' + (location.port ? ':'+location.port : '');
   ```
3. Renders the container element (`#featuresApp`) where the Ember app is mounted

This architecture allows the Ember application to handle specific dynamic features while the PHP application manages the overall structure, authentication, and server-side processing.

## Summary

The MyFoot project uses Ember.js for its frontend application, which is embedded within a PHP/Zend Framework backend. The Ember app communicates with both internal API endpoints (within the same domain) and external microservices to provide a comprehensive facility management system. The application is designed to work across multiple environments (local, development, staging, production) with appropriate domain configurations for each.
