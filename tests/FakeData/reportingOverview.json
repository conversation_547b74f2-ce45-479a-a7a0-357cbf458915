{"account_id": "311", "data": [{"facility_id": "64684", "title": "Facility B", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "70737", "title": "Morgan Manhattan Storage", "company_code": "321", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "2.50", "integration_type": "Manual", "active": true}, {"facility_id": "104616", "title": "TESTY", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "105024", "title": "Test Facility 5", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "105028", "title": "Tyler's Store Hause", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": false}, {"facility_id": "105386", "title": "Test Facility 8472", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "105583", "title": "tester", "company_code": "emailTester", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "105799", "title": "example", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "75.00", "integration_type": "Manual", "active": true}, {"facility_id": "106304", "title": "Southside Mini Storage", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": false}, {"facility_id": "106305", "title": "Storage Warehouse", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "106318", "title": "test aaa", "company_code": "123", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "109878", "title": "Pier 58 Storage", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "153443", "title": "Fac 369", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "154492", "title": "Test Facility w45636734567456", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "155534", "title": "DEMO FACILITY -NOT REAL", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "158782", "title": "Fake Storage Facility", "company_code": "", "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": false}, {"facility_id": "199381", "title": "Happy Storage", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "199823", "title": "Fake OGF", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": false}, {"facility_id": "199879", "title": "Gotham Storage", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.00", "integration_type": "Manual", "active": true}, {"facility_id": "212580", "title": "Test - Kara's Storage Mart", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.20", "integration_type": "Manual", "active": false}, {"facility_id": "236089", "title": "Orange Cyber Storage", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.20", "integration_type": "Manual", "active": false}, {"facility_id": "236090", "title": "Orange Storage", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.20", "integration_type": "Manual", "active": false}, {"facility_id": "241801", "title": "Geo Test", "company_code": null, "bid_type": "RESIDUAL", "bid_amount": "0.25", "min_bid": "0.20", "integration_type": "Manual", "active": false}]}