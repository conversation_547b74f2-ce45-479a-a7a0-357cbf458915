networks:
  default:
    external:
      name: localnet

services:
  myfoot:
    ports:
      - 9019:80
    expose:
      - 80
#    image: 850077434821.dkr.ecr.us-east-1.amazonaws.com/sparefoot/myfoot:stage
    build:
      context: ../../
      args:
        - COMPOSER_GITLAB_TOKEN=YOUR_GITLAB_TOKEN_HERE
    restart: always
    env_file:
        - .env
    environment:
      APP_NAME: myfoot
      SF_ENV: local
      APP_LOG_FILE: php://stdout
      MEMCACHE_SERVER: memcached_myfoot:11211
      MYSQL_SPAREFOOT_HOST: "db-dev.sparedev.com"
      MYSQL_SPAREFOOT_DB: "sparefoot"
      MYSQL_SPAREFOOT_USER: "YOUR_DATABASE_USER_HERE"
      MYSQL_SPAREFOOT_PASS: "YOUR_DATABASE_PASSWORD_HERE"
      MYSQL_SPAREFOOT_PORT: "3306"
      MYSQL_REPORTING_DSN: "mysql:host=db-dev.sparedev.com;port=3306;dbname=sparefoot"
      MYSQL_REPORTING_USER: "YOUR_DATABASE_USER_HERE"
      MYSQL_REPORTING_PASS: "YOUR_DATABASE_PASSWORD_HERE"
      MYSQL_REPORTING_PORT: "3306"
      PHLOW_HOSTNAME: localhost
      #     SALESFORCE environment vars can be accessed in sparefoot/salesforce_client if needed - FacilitiesTest uses them.
      SOLR_HOST: stg-solr.sparefoot.com
      SOLR_PORT: 8282
      SOLR_SCHEME: http
      URL_AUTHORIZATION_SERVICE: 'https://auth.sparefoot.extrameter.com'
      URL_LOCATION_SERVICE: 'https://locationservice.sparefoot.extrameter.com'
      URL_BOOKING_SERVICE: 'https://bookingservice.sparefoot.extrameter.com'
      URL_SEARCH_SERVICE: 'https://search.sparefoot.extrameter.com'
      URL_CLIENT_API_SERVICE: 'https://client-api-service.sparefoot.extrameter.com'
      URL_BID_OPTIMIZER_SERVICE: 'https://bid-optimizer.sparefoot.moreyard.com'
      URL_PHIDO: 'https://phido.sparefoot.extrameter.com'
      SERVICES_BASE_URL: 'sparefoot.localhost'
      VERSION: 1
      DISABLE_OPCODE_CACHE: 1
      TZ: 'America/Chicago'
      XDEBUG_CONFIG: "client_port=9090 client_host=***********"
      XDEBUG_SESSION: "ffff"
      XDEBUG_MODE: debug
      PHP_IDE_CONFIG: serverName=localhost
      BRANCH_NAME : 'lt-sf-11992-symfony5-php8-dev'
      OTEL_EXPORTER_OTLP_ENDPOINT : 'your-otel-collector:4317'
    volumes:
      - ../..:/var/www/service
    stdin_open: true
    tty: true
    links:
      - memcached
    networks:
      default:
        aliases:
          - myfoot
  myfoot-chrome:
    image: robcherry/docker-chromedriver
    networks:
      default:
        aliases:
        - myfoot
    environment:
      CHROMEDRIVER_WHITELISTED_IPS: ""
      CHROMEDRIVER_PORT: "9515"
    ports:
      - 9515:9515
    cap_add:
      - "SYS_ADMIN"

  memcached:
    expose:
    - 11211
    image: memcached
    stdin_open: true
    tty: true
    networks:
      default:
        aliases:
        - memcached_myfoot
