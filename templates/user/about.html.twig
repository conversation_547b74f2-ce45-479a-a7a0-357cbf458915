<h1>Access Level</h1>

<p>Your MySpareFoot access level: {{ view.loggedUser.getMyfootRole()|title }}</p>
<p>Only administrators can edit payment information, and can control user permissions.<p>
<h4>Administrators for your account:</h4>
{{ view.loggedUser.getAccount().getAdminEmails()|replace(',', ', ') }}</p>

{% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) %}
<a class="ui primary button" href="/user">Edit Users</a>
{% endif %}