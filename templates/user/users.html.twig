{% extends 'layout.html.twig' %}
{% block title %}{{ view.title }}{% endblock %}
{% block content %}
{{ include('settings/header.html.twig', {
    'loggedUser': view.loggedUser,
    'active': 'user'
}) }}

<h3>User Management</h3>

	<p>Administrators have access to all facilities, can add facilities, and can add or edit users.</p>

    <button id="add_user_reveal" class="ui secondary button"><i class="plus icon"></i> Add New User</button>

    <table class="ui striped sortable cell-headers table">
        <thead>
            <tr>
                <th class="four wide">User Information</th>
                <th class="option">Administrator <a href="#" data-rel="tooltip" title="Administrators have access to all facilities, can add facilities, and can add or edit users."><i class="fa fa-info-circle"></i></a></th>
                <th class="option">Facilities</th>
                <th class="option">Reservation emails <a href="#" data-rel="tooltip" title="Users with this permission will receive email notifications when associated facilities get new customers from the SpareFoot AdNetwork and calls from their SpareFoot GeoPages."><i class="fa fa-info-circle"></i></a></th>
                <th class="option">Inquiry emails <a href="#" data-rel="tooltip" title="Users with this permission will receive email notifications when associated facilities get inquiries from the SpareFoot AdNetwork."><i class="fa fa-info-circle"></i></a></th>
                <th class="option">MySpareFoot access <a href="#" data-rel="tooltip" title="Users with this permission with be able to log-in to MySpareFoot and make updates to their units and facility details."><i class="fa fa-info-circle"></i></a></th>
                <th class="option">Statements &amp; reconciliations <a href="#" data-rel="tooltip" title="Users with this permission have access to the statements tab where they can view monthly statements and reconcile the move-ins."><i class="fa fa-info-circle"></i></a></th>
                <th class="option">Invoices & receipts <a href="#" data-rel="tooltip" title="Users with this permission will receive invoices and/or receipts for charges. These permissions can be edited in your payment settings."><i class="fa fa-info-circle"></i></a></th>
                {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'getLoggedUser').isMyfootAdmin() and call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::DELETE_USER')) %}
                {# Column header for trashcan column #}
                <th style="width:16px"></th>
                {% endif %}
            </tr>
            <tr class="dark expanded-row hidden" id="new_user">
                <td>
                    <div class="ui input">
                    <input id="email" name="email" size="30" type="text" class="input text" placeholder="Email address" value="[<EMAIL>]" onfocus="this.value = (this.value=='[<EMAIL>]')? '' : this.value;">
                    </div>
                </td>
                <td>
                    <div class="ui checkbox">
                    <input type="checkbox" name="is_admin" id="is_admin"  onClick="$('input#gets_emails').attr('checked', true);$('input#myfoot_access').attr('checked', true);$('input#gets_statements').attr('checked', true);" />
                    </div>
                </td>
                <td></td>
                <td>
                    <div class="ui checkbox">
                    <input type="checkbox" name="gets_emails" id="gets_emails" />
                    </div>
                </td>
                <td><div class="ui checkbox"><input type="checkbox" class="inquiries"></div></td>
                <td><div class="ui checkbox"><input type="checkbox" name="myfoot_access" id="myfoot_access" onClick="if (!$(this).is(':checked')) {$('input#is_admin').removeAttr('checked');$('input#gets_statements').removeAttr('checked');$('input#geopage_only').removeAttr('checked');}"/></div></td>
                <td><div class="ui checkbox"><input type="checkbox" name="gets_statements" id="gets_statements" onClick="if ($(this).is(':checked')) {$('input#myfoot_access').attr('checked', true);}" /></div></td>
				<td></td>
            </tr>
      </thead>
      <tbody>
        <tr class="expanded-content dark hidden" id="new_user_dialog">
        <td colspan="3">
            <label for="fac_select">Facilities associated with this email address:</label>
            <select class="chosen" multiple style="width: 350px; display: none;" id="fac_select">
                <option value="all">--ALL FACILITIES--</option>
                {% for fac in view.facilities %}
                    <option value="{{ fac.getId() }}">{{ fac.getTitle() }}{{ fac.getCompanyCode() }}{{ fac.getActive() ? '' : ' (HIDDEN)' }}</option>
                {% endfor %}
            </select>
            <div class="spacer"></div>

            <div class="ui checkbox">
                <input type="checkbox" checked="checked" id="notify" name="notify"/>
                <label for="notify">Notify New User via Email</label>
            </div>

            <div class="form-actions clear">
                <a href="#" class="ui basic button" id="cancel_user_reveal">Cancel</a>
                <input id="create_new" class="ui primary button" name="create_new" value="Create" onclick="createNewUser();" type="button"/>
            </div>
        </td>
        <td colspan="2">
            <div class="ui input">
              <input size="30" type="text" value="" placeholder="First Name" id="fname" name="fname" class="input text" tabindex="1"/>
            </div>

            <div class="spacer"></div>
            <div class="ui input">
              <input size="30" type="password" value="" placeholder="Password" id="password" name="password" class="input text" tabindex="3"/>
            </div>
        </td>
        <td colspan="2">
            <div class="ui input">
            <input size="30" type="text" value="" placeholder="Last Name" id="lname" name="lname" class="input text" tabindex="2"/>
            </div>
            <div class="spacer"></div>
            <div class="ui input">
            <input size="30" type="password" value="" placeholder="Confirm Password" id="password_confirm" name="password_confirm" class="input text" tabindex="4"/>
            </div>
        </td>
        <td></td>
      </tr>

      {% for user in view.users %}

      <tr id="{{ user.id }}"
            class="users-list-item"
            data-email="{{ user.email }}">
        <td data-sort-value="{{ user.email|e('html_attr') }}"
            class="col-email js-edit-contact-info">
            <div class="js-edit-contact-info-view-mode editable-field">
                <div>
                    <span class="js-first-name">{{ user.first_name }}</span> <span class="js-last-name">{{ user.last_name }}</span>
                </div>
                <span class="js-email">{{ user.email }}</span>
                <div class="editable-notifier"><i class="edit icon"></i></div>
            </div>
            <div class="js-edit-contact-info-edit-mode ui form hidden">
                <div class="field">
                    <input name="first_name" placeholder="First Name" value="{{ user.first_name }}" />
                </div>
                <div class="field">
                    <input name="last_name" placeholder="Last Name"  value="{{ user.last_name }}" />
                </div>
                <div class="field">
                    <input name="email" placeholder="Email"  value="{{ user.email }}" />
                </div>
                <div class="inline fields right aligned">
                    <button class="ui compact primary button confirm">Save</button>
                    <button class="ui compact basic button deny">Cancel</button>
                    <input type="hidden" name="user_id" value="{{ user.id }}" />
                </div>
            </div>
        </td>
        <td>
          <div class="ui checkbox">
            <input type="checkbox" class="isadmin" id="is_admin_{{ user.id }}" {{ user.myfootRole is same as(constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) ? 'checked="checked"' : '' }} />
          </div>
        </td>
        <td>
            <span id="num_facs_{{ user.id }}">{{ user.numFacilities }}</span>
            <a href="#" class="numfacilities">Edit</a>
        </td>
        <td>
          <div class="ui checkbox">
            <input type="checkbox" class="reservations" id="gets_emails_{{ user.id }}" {{ user.getsEmails ? 'checked="checked"' : '' }} />
          </div>
        </td>
        <td>
          <div class="ui checkbox">
              <input type="checkbox" class="inquiries" {{ user.getsInquiries ? 'checked="checked"' : '' }} />
          </div>
        </td>
        <td>
            <div class="ui checkbox">
                <input type="checkbox" class="access" id="myfoot_access_{{ user.id }}" {{ user.email is same as(view.user.getEmail()) ? 'disabled="disabled"' : '' }} {{ user.myfootRole ? 'checked="checked"' : '' }} />
            </div>
        </td>
        <td>
            <div class="ui checkbox">
                <input type="checkbox" class='statements' id="gets_statements_{{ user.id }}" {{ user.getsStatements ? 'checked="checked"' : '' }} />
            </div>
        </td>
	  	<td>
			<div class="ui checkbox">
				<a href="/payment">Edit payment settings</a>
			</div>
		</td>
        {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'getLoggedUser').isMyfootAdmin() and call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::DELETE_USER')) %}
            {# Only allow delete for admins #}
        <td>
            <a href="#" title="Delete User"><i class="user-delete trash outline icon"></i><a/>
        </td>
        {% endif %}

      </tr>
      <tr class="expanded-content" style="display:none" id="fac-select-row-{{ user.id }}">
        <td colspan="8">
          <label for="fac_select_{{ user.id }}">Facilities that above permissions apply to:</label>
            <select class="chosen" multiple style="width:500px;" id="fac_select_{{ user.id }}">
            <option value="all" {{ user.allFacs ? 'selected' : '' }}>--ALL FACILITIES--</option>
                {% for fac in view.facilities %}
                    <option value="{{ fac.getId() }}" {{ not user.allFacs and fac.getId() in user.managableFacs ? 'selected' : '' }}>{{ fac.getTitle() }}{% if fac.getCompanyCode() %} ({{ fac.getCompanyCode() }}){% endif %}{{ fac.getActive() ? '' : ' (HIDDEN)' }}</option>
                {% endfor %}
            </select>

          <div class="spacer">Click box to make changes.</div>

          <input id="user_submit_{{ user.id }}" class="ui primary button" name="commit" value="Save" onclick="updateFacs({{ user.id }})" type="button"/>
          <a class="ui basic button hidefacpicker" href="#" >Cancel</a>
        </td>
      </tr>
      {% endfor %}
      </tbody>
   </table>
   <div style="clear:both"></div>


    {% if view.totalPages > 1 %}
        <ul class="pagination">
            <li{{ view.currentPage == 1 ? ' class="disabled"' : '' }}><a href="{{ view.currentPage == 1 ? '#' : '/user/users/p/' ~ (view.currentPage - 1) }}">&laquo;</a></li>
        {% for i in 1..view.totalPages %}
            {% if i != view.currentPage %}
                <li><a href="/user/users/p/{{ i }}">{{ i }}</a></li>
            {% else %}
                <li class="active"><a href="#">{{ i }}</a></li>
            {% endif %}
        {% endfor %}
            <li{{ view.currentPage == view.totalPages ? ' class="disabled"' : '' }}><a href="{{ view.currentPage == view.totalPages ? '#' : '/user/users/p/' ~ (view.currentPage + 1) }}">&raquo;</a></li>
        </ul>
    {% endif %}

<form id="password-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                <h4 class="modal-title">Create password</h4>
            </div>
            <div class="modal-body form-horizontal">
                <p>This user needs a password to be able to log in to MySpareFoot.</p>

                <div class="form-group">
                    <label class="col-md-3 control-label" for="modal-password">Password</label>
                    <div class="col-md-9">
                        <input type="password" name="password" id="modal-password" class="form-control" />
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label" for="modal-password-confirm">Confirm Password</label>
                    <div class="col-md-9">
                        <input type="password" name="password_confirm" id="modal-password-confirm" class="form-control" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-9 col-md-offset-3">
                        <div class="checkbox">
                            <label for="password-modal-notify">
                                <input type="checkbox" checked="checked" id="password-modal-notify" /> Notify user via email
                            </label>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <a class="ui basic button" data-dismiss="modal" onclick="detoggle();">Cancel</a>
                <input type="submit" class="ui primary button" value="Save" />
                <input type="hidden" value="" id="password-modal-user-id" name="user_id" />
            </div>
        </div>
    </div>
</form>

<div id="user-delete-modal" class="ui modal small delete">
    <i class="close icon"></i>
    <div class="header">
        Remove <strong class="delete-email"></strong> from MySpareFoot
    </div>
    <div class="content">

        <i class="archive icon huge"></i>
        <div class="description">
            <p>Are you sure you want to remove <strong class="delete-email"></strong> from MySpareFoot?</p>
        </div>
    </div>
    <div class="actions">
        <a id="confirm-delete-user" class="ui button red confirm">Delete User</a>
        <a class="ui button cancel-button basic close cancel">Cancel</a>
    </div>
</div>
<span class="hidden" id="usersOverviewPageTest"></span>
{% endblock %}