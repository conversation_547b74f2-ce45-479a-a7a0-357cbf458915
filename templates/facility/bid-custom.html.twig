{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'details', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

{% set isBidTypePercent = (view.facility.getAccount().getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_PERCENT')) %}
<script type="text/javascript">
var smallUnitsBidPercent = {{ constant('\\Genesis_Entity_Transaction::BID_SMALL_PERCENT') }};
var largeUnitsBidPercent = {{ constant('\\Genesis_Entity_Transaction::BID_LARGE_PERCENT') }};
var updateBidRequest = new XMLHttpRequest();
var updateBidTimeout;
var facilityMinBid = "{{ view.facility.getMinBid()|number_format(2, '.', '') }}";
var facilityMaxBid = "{{ view.facility.getMaxBid()|number_format(2, '.', '') }}";
var unitAvgs = {{ view.facility.getAvgUnitPrices()|json_encode|raw }};
var currentFacilityId = {{ view.facility.getId() }};
var bidShiftAmount = {{ isBidTypePercent ? 0.05 : 5 }};
var isBidTypePercent = {{ isBidTypePercent ? 'true' : 'false' }};
</script>

<div class="row header-row">
    <div class="col-md-12">
        <h2>Update Your Bid Modifier</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div id="bid_amount">
            <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />

            <label for="bid_amount"><strong>Update My Bid</strong></label>
            <div style="z-index:10;">
                {% if view.canChangeBid %}
                    <div class="bid-tool">
                        <input id="bid_amount_value" type="number" min="{{ view.facility.getMinBid() }}" max="{{ view.facility.getMaxBid() }}" value="{{ view.facility.getEffectiveBidAmount() }}" />
                        <div id="bid-toggle"><span id="bid-up"></span><span id="bid-down"></span></div>
                    </div>
                {% else %}
                    <div id="bid-amount-display">{{ view.facility.getEffectiveBidAmount() }}</div>
                {% endif %}
            </div>
            <div class="clear"></div>
            {% if isBidTypePercent %}
                <small>For example the amount on a $50 <br>
                    unit will be {{ view.facility.getEffectiveBidAmount()|number_format(2) }} x $50 = ${{ (view.facility.getEffectiveBidAmount()*50)|number_format(2) }}</small>
            {% else %}<!-- TODO REMOVE AFTER CPA TIER IS GONE, AND VARS -->
                <p><strong>Small Units</strong> <small class="minor">&lt; 50 sqft</small><br />
                    <span id="smallUnitsBid">${{ (view.facility.getBidFlat() * constant('\\Genesis_Entity_Transaction::BID_SMALL_PERCENT'))|number_format(2) }}</span></p>
                <p><strong>Standard</strong><br />
                    <span id="standardUnitsBid">${{ view.facility.getBidFlat()|number_format(2) }}</span></p>
                <p><strong>Large Units</strong> <small class="minor">&gt;= 100 sqft</small><br />
                    <span id="largeUnitsBid">${{ (view.facility.getBidFlat() * constant('\\Genesis_Entity_Transaction::BID_LARGE_PERCENT'))|number_format(2) }}</span></p>
            {% endif %}
        </div>

        <div id="submit-button-container">
            {% if view.canChangeBid %}
                <input id="submit_button" class="ui primary button update-bid-btn" name="commit" type="submit" value="Update My Bid" onclick="return saveBid();" /><img src="/images/loading.gif" class="hide" />
            {% else %}
                Only system administrators are able to change your bid and improve your search ranking. Please contact {{ view.adminEmails }} to make this change. Thanks!
            {% endif %}
        </div>
    </div>
    <div class="col-md-7">
        <div class="panel panel-default">
            <div class="panel-body">
                <table id="rankingsTable" class="table bid-opps-table">
                   <tbody class="table-body">
                       <tr class="header-row">
                           <th></th>
                           <th>Search Term</th>
                           <th>Facility Rank</th>
                       </tr>
                       <tr>
                           <th>City</th>
                           <td>{{ view.facility.getLocation().getCity() }}, {{ view.facility.getLocation().getState() }}</td>
                           <td class="ranking" data-opptype="city">-</td>
                       </tr>
                       <tr>
                           <th>Zip Code</th>
                           <td>{{ view.facility.getLocation().getZip() }}</td>
                           <td class="ranking" data-opptype="zip">-</td>
                       </tr>
                       <tr>
                           <th>Custom Zip Code</th>
                           <td>
                               <div class="ui action input">
                                   <input type="text" name="custom_zip" id="custom_zip" size="10" maxlength="5" />
                                   <button type="button" id="custom_zip_button" class="ui secondary button" onclick="refreshBid();return false;">View</button>
                               </div>
                           </td><td class="ranking" data-opptype="customZip">-</td>
                       </tr>
                       <tr>
                           <th>
                               Custom City
                               <br />
                               <i><span class="subtext">example: Austin, TX</span></i>
                           </th>
                           <td>
                               <div class="ui action input">
                                   <input type="text" name="custom_city" id="custom_city" size="10" />
                                   <button type="button" id="custom_city_button" class="ui secondary button" onclick="refreshBid();return false;">View</button>
                               </div>
                           </td><td class="ranking" data-opptype="customCity">-</td>
                       </tr>
                   </tbody>
                </table>
            </div>
        </div>

        {% if view.algorithms %}
            <div>
                <br/>
                <select id="algorithm" name="algorithm">
                    {% for class, name in view.algorithms %}
                        <option value="{{ class }}"{% if view.algorithm == class or (not view.algorithm and class == constant('\\Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH')) %} selected="selected"{% endif %}>{{ class == constant('\\Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH') ? '[DEFAULT] ' : '' }}{{ name }}</option>
                    {% endfor %}
                </select>
            </div>
        {% endif %}
    </div>
</div>

<div id="why-change-bid" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
            <button class="close" data-dismiss="modal">×</button>
                <h4 class="modal-title">Why change your bid?</h4>
            </div>
            <div class="modal-body">
                <img src="/images/bookings_by_rank_chart.gif" width="361" height="289" style="float:right; margin-left:1.667em;" />
                <p>The number of reservations SpareFoot can generate for your facility strongly depends on your rank in our search results. For example, a facility that ranks first will typically get more than four times as many bookings than a facility ranked seventh.</p>
                <p>SpareFoot uses a complex algorithm to determine rank, based on relevance factors such as distance from where the user is searching (closer is better) and unit prices (lower is better). We also consider quality factors such as how often consumers actually reserve a unit after viewing your listing, and the rate at which they move in.</p>
                <p>The amount you are willing to pay for tenants is also a factor— the higher you bid, the better your facility will rank in search results. But note that when your facility’s distance or quality factors are lower or less relevant than local competition, it will generally be more expensive to bid to a higher rank.</p>
            </div>
        </div>
    </div>
</div>
