<div class="setup-content-container">
    <div class="setup-content">

        <div class="content-row">
            <img src="/images/ess-logo-large.png" style="float:right"/>
            <h2>Easy Storage Solutions Integration Setup</h2>
            <p>Automatically import your facilities and units into SpareFoot.</p>
            <p>SpareFoot will sync with Easy Storage Solutions once per hour to make sure we are showing the most current list available units for your properties.</p>
        </div>

        <div class="input-row">
            <p class="description">Enter your unique Easy Storage Solutions identifier in the form below and click "sync".</p>
            <br />

            <div class="form-horizontal">

                <div class="form-group">
                    <label for="essidentifier" class="col-md-2 control-label">Easy Storage Solutions Identifier</label>
                    <div class="col-md-10">
                        <input id="essidentifier" name="essidentifier" type="text" class="form-control" />
                    </div>
                </div>

                <div class="form-actions">
                    <div class="col-md-offset-2">
                        <input id="sync" class="btn btn-large btn-success" name="sync" type="submit" value="Sync" data-loading-text="Syncing" data-complete-text="Sync Complete" />&nbsp;&nbsp;<div class="loading hide" />
                        <p class="loading hide"><br />We're connecting to the Easy Storage Solutions servers now. This could take a minute.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-footer">
            <div class="row">
                <div class="col-md-6">
                    <a id="back_btn" class="btn btn-default btn-lg" href="{{ path('features', {'action': 'type'}) }}">Back</a>
                </div>

                <div class="col-md-6">
                    <form method="post" action="{{ path('features', {'action': 'addsummary'}) }}">
                        <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value=""/>

                        <div class="form-group text-right">
                            <img src="/images/loading.gif" id="nextLoader" class="hide" />
                            <input id="next" class="btn btn-lg btn-primary submit" name="next" type="submit" value="Next" disabled="disabled">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
