{% set unitInfo = {} %}

{% if view.inventory %}
    {% for unit in view.inventory %}
        {% set unitInfo = unitInfo|merge({
            (unit.id): {
                "facilityId": view.facility.getId(),
                "type": unit.type_num,
                "uw": unit.unit_w,
                "ul": unit.unit_l,
                "uh": unit.unit_h,
                "dw": unit.door_w,
                "dh": unit.door_h,
                "climate": unit.climate,
                "humidity": unit.humidity,
                "alarm": unit.alarm,
                "power": unit.power,
                "outdoorAccess": unit.outdoorAccess,
                "driveUp": unit.driveUp,
                "stacked": unit.stacked,
                "premium": unit.premium,
                "heated": unit.heated,
                "aircooled": unit.aircooled,
                "ada": unit.ada,
                "unitlights": unit.unitlights,
                "twentyfourhouraccess": unit.twentyfourhouraccess,
                "shelvesinunit": unit.shelvesinunit,
                "basement": unit.basement,
                "parkingwarehouse": unit.parkingwarehouse,
                "pullthru": unit.pullthru,
                "vehicle": unit.vehicle,
                "doorType": unit.doorType,
                "floor": unit.rawFloor,
                "covered": unit.covered,
                "desc": unit.desc,
                "special": unit.special,
                "qty": unit.qty,
                "deposit": unit.deposit,
                "regPrice": unit.list_price,
                "sfPrice": unit.sparefoot_price
            }
        }) %}
    {% endfor %}
{% endif %}

{% if view.groupedUnits %}
    {% for gUnit in view.groupedUnits %}
        {% set unitInfo = unitInfo|merge({
            (gUnit.groupIdsStr): {
                "facilityId": view.facility.getId(),
                "type": gUnit.type_num,
                "uw": gUnit.unit_w,
                "ul": gUnit.unit_l,
                "uh": gUnit.unit_h,
                "dw": gUnit.door_w,
                "dh": gUnit.door_h,
                "climate": gUnit.climate,
                "humidity": gUnit.humidity,
                "alarm": gUnit.alarm,
                "power": gUnit.power,
                "outdoorAccess": gUnit.outdoorAccess,
                "driveUp": gUnit.driveUp,
                "stacked": gUnit.stacked,
                "premium": gUnit.premium,
                "heated": gUnit.heated,
                "aircooled": gUnit.aircooled,
                "ada": gUnit.ada,
                "unitlights": gUnit.unitlights,
                "twentyfourhouraccess": gUnit.twentyfourhouraccess,
                "shelvesinunit": gUnit.shelvesinunit,
                "basement": gUnit.basement,
                "parkingwarehouse": gUnit.parkingwarehouse,
                "pullthru": gUnit.pullthru,
                "vehicle": gUnit.vehicle,
                "doorType": gUnit.doorType,
                "floor": gUnit.rawFloor,
                "covered": gUnit.covered,
                "desc": gUnit.desc,
                "special": gUnit.special,
                "qty": gUnit.qty,
                "deposit": gUnit.deposit,
                "regPrice": gUnit.list_price,
                "sfPrice": gUnit.sparefoot_price,
                "slyn": gUnit.sitelinkunit
            }
        }) %}
    {% endfor %}
{% endif %}
<script type="text/javascript">
let sourceType = {{ view.sourceType }};
let promoSync = {{ view.promoSync }};
let facilityId = {{ view.facility.getId() }};
let unit = {{ unitInfo|json_encode|raw }};
let integratedFields = {{ view.integratedFields|json_encode|raw }};
</script>

{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'inventory', 'loggedUser': view.loggedUser, 'facility': view.facility, 'isBidOptimizerActive': view.isBidOptimizerActive}) }}

{% if not view.facility.getApproved() %}
<div id="unapproved-units-msg" class="ui warning message">
    This facility currently has unapproved units. These units are pending approval by a SpareFoot account manager.
    If the units remain unapproved for over 72 hours, please e-mail
    <a href="mailto:<EMAIL>"><EMAIL></a> or call ************.
</div>
{% endif %}

<div class="ui secondary menu">
    {% if view.inventory %}
{# TODO: Remove these buttons
        <div class="btn-group pull-right">
            <a class="ui secondary button" id="group_edit" onclick="$('div#i_units').hide();$('#g_units').show();$('a#group_edit').addClass('active');$('#individual_edit').removeClass('active');$('input[type=checkbox]').attr('checked', false);">All Units</a>
            <a class="ui secondary button active" id="individual_edit" onclick="$('div#i_units').show();$('#g_units').hide();$('#individual_edit').addClass('active');$('#group_edit').removeClass('active');$('input[type=checkbox]').attr('checked', false);">Units in Group</a>
        </div> #}
    {% endif %}
    <div class="ui buttons">
        <a class="ui secondary button" onclick="multi_modal({{ view.facility.getId() }});">
            <i class="pencil icon"></i> Edit Selections
        </a>
        <a class="ui secondary button" href="{{ url('features', {'action': 'unitexport'}) }}?fid={{ view.facilityId }}">
            <i class="file icon"></i> Export Spreadsheet
        </a>
    </div>
</div>
    {% if view.inventory %}
        <div id="i_units">
            <div class="table-responsive">
                <table id="units-table" class="ui sortable cell-headers striped table">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <div class="ui checkbox">
                                    <input type="checkbox" id="checkall_i"/>
                                </div>
                            </th>
                            <th class="no-sort">Available</th>
                            <th></th>
                            <th><a href="#" rel="tooltip" title="Unit name from your management software.">Name</a></th>
                            <th>Size</th>
                            <th><a href="#" rel="tooltip" title="How sparefoot has classified this unit.">Type</a></th>
                            {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SITELINK') %}
                                <th>
                                    <a href="#" rel="tooltip"
                                       title="The unit type classification from your management software."
                                    >SiteLink Type</a>
                                </th>
                            {% elseif view.sourceType == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') %}
                                <th>
                                    <a href="#" rel="tooltip"
                                       title="The unit type classification from your management software."
                                    >Unit Type Code</a>
                                </th>
                            {% endif %}
                            <th>Amenities</th>
                            <th>Promo</th>
                            <th>List Price</th>
                            <th>
                                <a href="#" rel="tooltip"
                                   title="Price you will allow SpareFoot to sell your unit for."
                                >SpareFoot Price</a>
                            </th>
                            <th>
                                <a href="#" rel="tooltip"
                                   title="Is this unit available to rent? (unoccupied, not reserved, etc)"
                                >Rentable?</a>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if view.inventory %}
                            {% for unit in view.inventory %}
                            <tr id="{{ unit.id }}" data-unitindex="{{ unit.id }}" class="{{ unit.hidden ? 'disabled-row' : 'enabled-row' }}">
                                <td class="center on-off-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" id="{{ unit.id }}" name="unit"/>
                                    </div>
                                </td>
                                <td class="center availability-checkbox">
                                    {% if view.facility.getApproved() and not unit.approved %}
                                        <p></p>
                                    {% else %}
                                        <div class="ui checkbox">
                                            <input type="checkbox" name="listing" id="tg_{{ unit.id }}"
                                                value="{{ unit.id }}" {{ unit.hidden ? '' : ' checked="checked"' }} />
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if view.facility.getApproved() and not unit.approved %}
                                        <p></p>
                                    {% else %}
                                        <a onclick="unit_modal({{ view.facility.getId() }},{{ unit.id }});">Edit</a>
                                    {% endif %}
                                </td>
                                {# <td>{{ unit.approved ? '<span style="color:green;"><b>&#x2713; Yes</b></span>' : '' }}</td> #}
                                <td>{{ unit.unitName }}</td>
                                <td data-sort-value="{{ unit.unit_w * unit.unit_l }}">{{ unit.dimensions|replace({' ': '&nbsp;'})|raw }}</td>
                                <td>{{ unit.type }}</td>
                                {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SITELINK') 
                                   or view.sourceType == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') %}
                                    <td>{{ unit.classType }}</td>
                                {% endif %}
                                <td>{{ unit.amenities }}</td>
                                <td>{{ unit.special }}</td>
                                <td>${{ unit.list_price|number_format(2) }}</td>
                                <td><div id="{{ unit.id }}" name="sparefootprice">
                                {% if unit.sparefoot_price != "" %}
                                ${{ unit.sparefoot_price|number_format(2) }}
                                {% endif %}
                                </div></td>
                                <td>{{ unit.published ? '<span style="color:green;"><b>&#x2713; Yes</b></span>' : '' }}</td>
                            </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}

    {% if view.groupedUnits %}
        <div id="g_units" {% if view.inventory %}class="hidden"{% endif %}>
            <div class="table-responsive">
                <table id="units-table" class="ui sortable cell-headers striped table">
                    <thead>
                        <tr>
                            <th class="no-sort">
                                <div class="ui checkbox">
                                    <input type="checkbox" id="checkall_g"/>
                                </div>
                            </th>
                            <th class="no-sort"></th>
                            <th class="no-sort" style="width:170px;"></th>
                            <th class="no-sort">Available</th>
                            <th width="300px"><a href="#" rel="tooltip" title="Unit names from your management software.">Units in Group</a></th>
                            <th>Size</th>
                            {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') %}<th>Unit Type Code</th>{% endif %}
                            <th><a href="#" rel="tooltip" title="How SpareFoot has classified this unit.">Type</a></th>
                            {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SITELINK') %}<th><a href="#" rel="tooltip" title="Unit types as designated by your management software.">SiteLink Type</a></th>{% endif %}
                            <th>Amenities</th>
                            <th><a href="#" rel="tooltip" title="Price you will allow SpareFoot to sell your unit for.">SpareFoot Price</a></th>
                            <th>List Price</th>
                            <th>Promo</th>
                            {% if call_static('\\Genesis_Service_Feature', 'isActive', 'myfoot.unit_list_show_reservation_window', {'account_id': view.facility.getAccountId(), 'listing_avail_id': view.facility.getId()}) %}
                                <th>Reservation Window</th>
                            {% endif %}
                            <th>Quantity</th>
                            <th style="width: 95px;" {{ view.sourceType == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') ? ' class="hide"' : '' }}><a href="#" rel="tooltip" title="The number of units in this group that are available for rent."># Rentable</a></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for gUnit in view.groupedUnits %}
                            {% set selectedRow = false %}
                            <tr id="{{ gUnit.groupIdsStr }}"
                                data-unitindex="{{ gUnit.groupIdsStr }}"
                                class="{{ gUnit.hidden ? 'disabled-row' : 'enabled-row' }}">

                                <td class="center on-off-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" id="{{ gUnit.groupIdsStr }}" name="unit"/>
                                    </div>
                                </td>
                                <td>
                                    <a onclick="unit_modal({{ view.facility.getId() }},'{{ gUnit.groupIdsStr }}');">Edit</a>
                                </td>
                                <td>
                                    {% if selectedRow %}
                                        Selected
                                    {% else %}
                                        <a href="{{ url('features_uid', {'unitIds': gUnit.groupIdsStr}, true) }}">Detailed View</a>
                                    {% endif %}
                                </td>
                                <td class="center availability-checkbox">
                                    <div class="ui checkbox">
                                        <input type="checkbox" name="listing"
                                            id="tg_{{ gUnit.groupIdsStr }}"
                                            value="{{ gUnit.groupIdsStr }}" {{ gUnit.hidden ? '' : ' checked="checked"' }} />
                                    </div>
                                </td>
                                <td>
                                    {% set unitNames = [] %}
                                    {% for groupUnit in gUnit.groupUnits %}
                                        {% set unitName %}
                                            <span{% if gUnit.quantity %} class="{{ groupUnit.getPublish() ? 'unit-rentable' : 'unit-unrentable' }}"{% endif %}>{{ groupUnit.getUnitName() }}</span>
                                        {% endset %}
                                        {% set unitNames = unitNames|merge([unitName]) %}
                                    {% endfor %}
                                    {{ unitNames|join(', ')|raw }}
                                </td>
                                <td data-sort-value="{{ gUnit.unit_w * gUnit.unit_l }}">{{ gUnit.dimensions|replace({' ': '&nbsp;'})|raw }}</td>
                                {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') %}<td>{{ gUnit.classType }}</td>{% endif %}
                                <td>{{ gUnit.type }}</td>
                                {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_SITELINK') %}<td>{{ gUnit.classType }}</td>{% endif %}
                                <td>{{ gUnit.amenities }}</td>
                                <td><div id="{{ gUnit.groupIdsStr }}" name="sparefootprice">
                                {% if gUnit.sparefoot_price != "" %}
                                ${{ gUnit.sparefoot_price|number_format(2) }}
                                {% endif %}
                                </div></td>
                                <td>${{ gUnit.list_price|number_format(2) }}</td>
                                <td>{{ gUnit.special }}</td>
                                {% if call_static('\\Genesis_Service_Feature', 'isActive', 'myfoot.unit_list_show_reservation_window', {'account_id': view.facility.getAccountId(), 'listing_avail_id': view.facility.getId()}) %}
                                    <td>{{ gUnit.reservation_days }} days</td>
                                {% endif %}
                                <td>{{ gUnit.quantity }}</td>
                                <td{{ gUnit.source_id == constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') ? ' class="hide"' : '' }}>{{ gUnit.numRentable }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}

    {% if not view.inventory and not view.groupedUnits and view.unpublishedUnits %}
        <br /><p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    {% elseif not view.inventory and not view.groupedUnits %}
        <br /><div id="loading" align="center"><img src="/images/loading.gif" /></div><br/><p>We are currently syncing your units for this facility with your management software.<br/>Check back shortly.</p>
    {% endif %}
{{ include('facility/hide-facility-reason-modal.html.twig') }}
{{ include('facility/inventory-modals.html.twig', {'facility': view.facility, 'covidModal': view.covidModal ?? false}) }}
