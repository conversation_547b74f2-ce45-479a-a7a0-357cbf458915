<div class="setup-content-container">
    <div class="setup-content">

        <div class="content-row">
            <img src="/images/storEDGE.png" style="float:right"/>
            <h2>storEDGE Integration Setup</h2>
            <p>Automatically import your facilities and units into SpareFoot.</p>
            <p>SpareFoot will sync with storEDGE once per hour to make sure we are showing the most current list available units for your properties.</p>
        </div>

        <div class="input-row">
            <p class="description">Enter your corporation code in the form below and click "sync".</p>
            <br />

            <div class="form-horizontal">
                
                <div class="form-group">
                    <label for="corpcode" class="col-md-2 control-label">Corporation Code</label>
                    <div class="col-md-10">
                        <input id="corpcode" name="corpcode" type="text" class="form-control" />
                    </div>
                </div>
                
                <div class="form-actions">
                    <div class="col-md-offset-2">
                        <input id="sync" class="btn btn-large btn-success" name="sync" type="submit" value="Sync" data-loading-text="Syncing" data-complete-text="Sync Complete" />&nbsp;&nbsp;<img src="/images/loaders/large.gif" class="loading hide" />
                        <p class="loading hide"><br />We're connecting to the storEDGE servers now. This could take a minute.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-footer">
            <form method="post" action="{{ path('features', {'action': 'addsummary'}) }}" class="pull-right">
                <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value=""/>
                <input id="next" class="btn btn-lg btn-primary" name="next" type="submit" value="Next" disabled onClick="nextLoader();"/>
            </form>
            <a id="back_btn" class="btn btn-default btn-lg" href="{{ path('features', {'action': 'type'}) }}">Back</a>
        </div>
    </div>
</div>
