{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'amenities', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<h3>Amenities</h3>

<form method="post" action="{{ path('features', {'action': 'amenities'}) }}?fid={{ view.facilityId }}" class="form-horizontal">

    {% if view.alert %}
        <p class="alert{{ view.alertClass ? ' ' ~ view.alertClass : '' }}">
            {{ view.alert }}
        </p>
    {% endif %}
    <script>
        var integratedFields = {{ (depositIsDisabled is defined and depositIsDisabled) ? "['security_deposit_required']"|raw : "[]"|raw }}
    </script>
    <h4>Moving Options</h4>

    <div class="form-group{{ ('truck_rental' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Truck rental</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="truck-rental-yes" name="truck_rental" value="1"{{ (view.facility.getTruckRental() == '1') ? ' checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="truck-rental-no" name="truck_rental" value="0"{{ (view.facility.getTruckRental() == '0') ? ' checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('free_truck_rental' in view.erroredFields) ? ' has-error' : '' }}" id="free-truck">
        <label class="col-md-2 control-label">Free use of truck</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="free-truck-yes" name="free_truck_rental" value="1" onclick="$('#free-truck-stipulations').show();" {{ (view.facility.getFreeTruckRental() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="free-truck-no" name="free_truck_rental" value="0" onclick="$('#free-truck-stipulations').hide();" {{ (view.facility.getFreeTruckRental() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment" id="free-truck-stipulations" style="display:{{ view.facility.getFreeTruckRental() ? 'block' : 'none' }}">

        <div class="form-group{{ ('truck_distance_limit' in view.erroredFields) ? ' has-error' : '' }}" id="show-distance-limit">
            <label class="col-md-2 col-md-offset-1 control-label">Truck distance limit</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-distance-limit-yes" name="truck_distance_limit" onclick="$('#show-max-mileage').show();" value="1" {{ (view.facility.getTruckDistanceLimit() or 'truck_max_mileage' in view.erroredFields) ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-distance-limit-no" name="truck_distance_limit" onclick="$('#show-max-mileage').hide();$('#free-truck-max-mileage').val('');" value="0" {{ (view.facility.getTruckDistanceLimit() == '0' and 'truck_max_mileage' not in view.erroredFields) ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('truck_max_mileage' in view.erroredFields) ? ' has-error' : '' }}" id="show-max-mileage" style="display:{{ (view.facility.getTruckDistanceLimit() > 0 or 'truck_max_mileage' in view.erroredFields) ? 'block' : 'none' }}">
            <label class="col-md-2 col-md-offset-2 control-label">Max mileage</label>
            <div class="col-md-8">
                <div class="input-group">
                    <input class="form-control" type="text" id="free-truck-max-mileage" name="truck_max_mileage" value="{{ view.facility.getTruckDistanceLimit() }}">
                    <span class="input-group-addon">mi</span>
                </div>
            </div>
        </div>

        <div class="form-group{{ ('truck_insurance_required' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Truck insurance required</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="truck-insurance-required-yes" name="truck_insurance_required" onclick="$('#show-free-truck-insurance-amount').show();" value="1" {{ (view.facility.getTruckInsurance() or 'truck_insurance_amount' in view.erroredFields) ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="truck-insurance-required-no" name="truck_insurance_required" onclick="$('#show-free-truck-insurance-amount').hide();$('#free-truck-insurance-amount').val('');" value="0" {{ (view.facility.getTruckInsurance() == '0' and 'truck_insurance_amount' not in view.erroredFields) ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('truck_insurance_amount' in view.erroredFields) ? ' has-error' : '' }}" id="show-free-truck-insurance-amount" style="display:{{ (view.facility.getTruckInsurance() > 0 or 'truck_insurance_amount' in view.erroredFields) ? 'block' : 'none' }}">
            <label class="col-md-2 col-md-offset-2 control-label">Truck insurance amount</label>
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-addon">$</span>
                    <input class="form-control" type="text" id="free-truck-insurance-amount" name="truck_insurance_amount" value="{{ view.facility.getTruckInsurance() }}">
                </div>
            </div>
        </div>

        <div class="form-group{{ ('truck_fuel_refill' in view.erroredFields) ? ' has-error' : '' }}" id="show_distance_limit">
            <label class="col-md-2 col-md-offset-1 control-label">Tenant refuels</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-fuel-refill-yes" name="truck_fuel_refill" value="1" {{ (view.facility.getTruckRefuelPolicy() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="free-truck-fuel-refill-no" name="truck_fuel_refill" value="0" {{ (view.facility.getTruckRefuelPolicy() == '0') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

    </div>


    <div class="form-group{{ ('truck_access' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Loading dock</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="truck-access-yes" name="truck_access" value="1" onclick="document.getElementById('show-truck-access-size').style.display='block';" {{ (view.facility.getTruckAccess() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="truck-access-no" name="truck_access" value="0" onclick="document.getElementById('show-truck-access-size').style.display='none';$('#truck-access-size').val('');" {{ (view.facility.getTruckAccess() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment" id="show-truck-access-size" style="display:{{ view.facility.getTruckAccess() ? 'block' : 'none' }}">
        <div class="form-group{{ ('truck_access_size' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">What size truck can fit?</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input class="form-control" type="text" id="truck-access-size" name="truck_access_size" value="{{ view.facility.getTruckAccessSize() }}">
                    <span class="input-group-addon">ft</span>
                </div>
            </div>
        </div>
    </div>

    {% if constant('\\Genesis_Entity_Feature::MYFOOT_ALLOW_18WHEELER_DROPOFF') is defined %}
        <div class="form-group{{ ('allow_18wheeler_dropoff' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Is your facility parking lot or driveway large enough for an 18-wheel truck?</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="allow-18wheeler-dropoff-yes" name="allow_18wheeler_dropoff" value="1" {{ (view.facility.getAllow18WheelerDropoff() == '1') ? 'checked="checked"' : '' }} onclick="$('#has-18wheeler-alleys').removeClass('hidden'); " />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="allow-18wheeler-dropoff-no" name="allow_18wheeler_dropoff" value="0" {{ (view.facility.getAllow18WheelerDropoff() == '0') ? 'checked="checked"' : '' }} onclick="$('#has-18wheeler-alleys').addClass('hidden'); $('#has-18wheeler-alleys-false').prop('checked', true );" />No
                </label>
            </div>
        </div>

        <div id="has-18wheeler-alleys" class="ui segment {{ (view.facility.getAllow18WheelerDropoff() == '1') ? '' : 'hidden' }}">
            <div class="form-group{{ ('has_18wheeler_alleys' in view.erroredFields) ? ' has-error' : '' }}">
                <label class="col-md-2 col-md-offset-1 control-label">Are your unit drive aisles wide enough for an 18-wheel truck?</label>

                <div class="col-md-9">
                    <label class="control-label radio-inline">
                        <input type="radio" id="has-18wheeler-alleys-true" name="has_18wheeler_alleys" value="1" {{ (view.facility.getHas18WheelerAlleys() == '1') ? 'checked="checked"' : '' }} />Yes
                    </label>
                    <label class="control-label radio-inline">
                        <input type="radio" id="has-18wheeler-alleys-false" name="has_18wheeler_alleys" value="0" {{ (view.facility.getHas18WheelerAlleys() == '0') ? 'checked="checked"' : '' }} />No
                    </label>
                </div>
            </div>
        </div>
    {% endif %}


    <div class="form-group{{ ('handcarts' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Handcarts or dollies</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="handcarts-yes" name="handcarts" value="1" {{ (view.facility.getHandcarts() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="handcarts-no" name="handcarts" value="0" {{ (view.facility.getHandcarts() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('elevator' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Elevator</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="elevator-yes" name="elevator" value="1" {{ (view.facility.getElevator() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="elevator-no" name="elevator" value="0" {{ (view.facility.getElevator() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('sell_moving_supplies' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Moving supplies for sale</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="sell-moving-supplies-yes" name="sell_moving_supplies" value="1" {{ (view.facility.getSellsMovingSupplies() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="sell-moving-supplies-no" name="sell_moving_supplies" value="0" {{ (view.facility.getSellsMovingSupplies() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Security Options</h4>

    <div class="form-group{{ ('surveillance' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Video cameras onsite</label>
        <div class="col-md-10">
            <label class="radio-inline">
                <input type="radio" id="surveillance-yes" name="surveillance" value="1" {{ (view.facility.getSurveillance() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label control-label radio-inline">
                <input type="radio" id="surveillance-no" name="surveillance" value="0" {{ (view.facility.getSurveillance() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('egate_access' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Electronic gate access</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="egate-access-yes" name="egate_access" value="1" {{ (view.facility.getEGateAccess() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="egate-access-no" name="egate_access" value="0" {{ (view.facility.getEGateAccess() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('fenced_lighted' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Fenced &#43; lighted</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="fenced-lighted-yes" name="fenced_lighted" value="1" {{ (view.facility.getFencedLighted() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="fenced-lighted-no" name="fenced_lighted" value="0" {{ (view.facility.getFencedLighted() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('resident_manager' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Resident manager</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="resident-manager-yes" name="resident_manager" value="1" {{ (view.facility.getResidentManager() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="resident-manager-no" name="resident_manager" value="0" {{ (view.facility.getResidentManager() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Access</h4>

    <div class="form-group{{ ('kiosk' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">24-hour kiosk</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="kiosk-yes" name="kiosk" value="1" {{ (view.facility.getKiosk() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="kiosk-no" name="kiosk" value="0" {{ (view.facility.getKiosk() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('bilingual_manager_available' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Bilingual manager</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="bilingual-manager-available-yes" name="bilingual_manager_available" value="1" onclick="document.getElementById('show_bilingual_language').style.display='block';" {{ view.facility.getBilingualManager() ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="bilingual-manager-available-no" name="bilingual_manager_available" value="0" onclick="document.getElementById('show_bilingual_language').style.display='none';$('#show_bilingual_language').val('');" {{ (view.facility.getBilingualManager() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show_bilingual_language" style="display:{{ view.facility.getBilingualManager() ? 'block' : 'none' }}">
        <div class="form-group">
            <label class="col-md-2 col-md-offset-1 control-label">Bilingual language</label>
            <div class="col-md-9">
                <input type="text" id="bilingual-language" name="bilingual_language" class="form-control" value="{{ view.facility.getBilingualManager() ? view.facility.getBilingualManager() : '' }}" />
            </div>
        </div>
    </div>

    <div class="form-group{{ ('accept_tenant_mail' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Mail or packages accepted</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="accept-tenant-mail-yes" name="accept_tenant_mail" value="1" {{ (view.facility.getAcceptTenantMail() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="accept-tenant-mail-no" name="accept_tenant_mail" value="0" {{ (view.facility.getAcceptTenantMail() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <hr />

    <h4>Payment Options</h4>

    <div class="form-group{{ ('payment_accept_cash' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Cash</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-cash-yes" name="payment_accept_cash" value="1" {{ (view.paymentAcceptCash == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-cash-no" name="payment_accept_cash" value="0" {{ (view.paymentAcceptCash == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('payment_accept_check' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Check</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-check-yes" name="payment_accept_check" value="1" {{ (view.paymentAcceptCheck == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="payment-accept-check-no" name="payment_accept_check" value="0" {{ (view.paymentAcceptCheck == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('payment_accept_credit' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Credit cards</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input onclick="$('#accepted_cards').show();" type="radio" id="payment-accept-credit-yes" name="payment_accept_credit" value="1" {{ (view.paymentAcceptCredit == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input onclick="$('#accepted_cards').hide(); $('[name=\'payment_accepted_cards[]\']').prop('checked', false);" id="payment-accept-credit-no" type="radio" name="payment_accept_credit" value="0" {{ (view.paymentAcceptCredit == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="accepted_cards" style="display:{{ view.paymentAcceptCredit ? 'block' : 'none' }}">

        <div class="form-group{{ ('payment_accepted_cards' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-3 col-md-offset-1 control-label">Cards accepted</label>
            <div class="col-md-8">
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-visa" name="payment_accepted_cards[]" value="Visa"
                        {{ (view.paymentAcceptedCards is iterable and 'Visa' in view.paymentAcceptedCards) ? 'checked="checked"' : '' }}
                    />Visa
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-mastercard" name="payment_accepted_cards[]" value="Mastercard"
                        {{ (view.paymentAcceptedCards is iterable and 'Mastercard' in view.paymentAcceptedCards) ? 'checked="checked"' : '' }}
                    />Mastercard
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-amex" name="payment_accepted_cards[]" value="AMEX"
                        {{ (view.paymentAcceptedCards is iterable and 'AMEX' in view.paymentAcceptedCards) ? 'checked="checked"' : '' }}
                    />AMEX
                </label>
                <label class="control-label checkbox-inline">
                    <input type="checkbox" id="payment-discover" name="payment_accepted_cards[]" value="Discover"
                        {{ (view.paymentAcceptedCards is iterable and 'Discover' in view.paymentAcceptedCards) ? 'checked="checked"' : '' }}
                    />Discover
                </label>
            </div>
        </div>

    </div>

    <hr />

    <h4>Billing</h4>

    <div class="form-group{{ ('email_invoicing' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Email invoicing available</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="email-invoicing-yes" name="email_invoicing" value="1" {{ (view.facility.getEmailInvoicingAvailable() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="email-invoicing-no" name="email_invoicing" value="0" {{ (view.facility.getEmailInvoicingAvailable() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('auto_payments' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Automatic payments available</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="auto-payments-yes" name="auto_payments" value="1" {{ (view.facility.getAutoPayAvailable() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="auto-payments-no" name="auto_payments" value="0" {{ (view.facility.getAutoPayAvailable() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('charge_date' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Charge date</label>
            <div class="col-md-10">
                <select name="charge_date" id="charge-date" class="form-control">
                    <option value=""></option>
                    <option value="{{ constant('\\Genesis_Entity_FacilityData::CHARGE_ON_FIRST_OF_MONTH') }}" {{ (view.facility.getChargeDate() == constant('\\Genesis_Entity_FacilityData::CHARGE_ON_FIRST_OF_MONTH')) ? 'selected="selected"' : '' }}>First of month</option>
                    <option value="{{ constant('\\Genesis_Entity_FacilityData::CHARGE_ON_MOVE_IN_ANNIVERSARY') }}" {{ (view.facility.getChargeDate() == constant('\\Genesis_Entity_FacilityData::CHARGE_ON_MOVE_IN_ANNIVERSARY')) ? 'selected="selected"' : '' }}>Monthly anniversary date</option>
                    <option value="{{ constant('\\Genesis_Entity_FacilityData::CHARGE_OTHER_TIME') }}" {{ (view.facility.getChargeDate() == constant('\\Genesis_Entity_FacilityData::CHARGE_OTHER_TIME')) ? 'selected="selected"' : '' }}>Other</option>
                </select>
            </div>
    </div>

    <div class="form-group{{ ('security_deposit_required' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Security deposit required</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="security-deposit-required-yes" name="security_deposit_required" value="1" onclick="$('#security-deposit-options').show();" {{ view.facility.getSecurityDeposit() ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="security-deposit-required-no" name="security_deposit_required" value="0" onclick="$('#security-deposit-options').hide();$('#deposit_price').val('');" {{ (view.facility.getSecurityDeposit() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="security-deposit-options" style="display:{{ view.facility.getSecurityDeposit() ? 'block' : 'none' }};">
        <div class="form-group{{ ('security_deposit_refundable' in view.erroredFields) ? ' has-error' : '' }}" >
            <label class="col-md-2 col-md-offset-1 control-label">Security deposit refundable</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="security-deposit-refundable-yes" name="security_deposit_refundable" value="1" {{ (view.facility.getSecurityDepositRefundable() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="security-deposit-refundable-no" name="security_deposit_refundable" value="0" {{ (view.facility.getSecurityDepositRefundable() == '0') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('deposit_price' in view.erroredFields) ? ' has-error' : '' }}" id="show_security_deposit_options">
           <label class="col-md-2 col-md-offset-1 control-label">Deposit amount</label>

            <div class="col-md-9 {{ ('security_deposit_type' in view.erroredFields) ? 'has-error' : '' }}">
                <div class="btn-group btn-group-justified">
                    <a role="button" id="security-deposit-percent-button" class="ui basic teal button" data-deposit-type="percent">% of first month's rent</a>
                    <a role="button" id="security-deposit-dollar-button" class="ui basic teal button" data-deposit-type="dollar">Flat dollar amount</a>
                </div>
                <input type="hidden" name="security_deposit_type" value="{{ view.facility.getSecurityDepositType() }}" />
            </div>
        </div>

        <div class="form-group{{ ('deposit_price' in view.erroredFields) ? ' has-error' : '' }}">

            <div class="col-md-9 col-md-offset-3 security-deposit-types security-deposit-percent">
                <div class="input-group {{ ('security_deposit_percent' in view.erroredFields) ? 'has-error' : '' }}">
                    <input type="text" id="security-deposit-percent" name="security_deposit_percent" class="form-control" value="{{ view.facility.getSecurityDepositPercent() ? view.facility.getSecurityDepositPercent() : '' }}" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
            <div class="col-md-9 col-md-offset-3 security-deposit-types security-deposit-dollar">
                <div class="input-group {{ ('security_deposit_dollar' in view.erroredFields) ? 'has-error' : '' }}">
                    <span class="input-group-addon">$</span>
                    <input type="text" id="security-deposit-dollar" name="security_deposit_dollar" class="form-control" value="{{ view.facility.getSecurityDepositDollar() ? view.facility.getSecurityDepositDollar() : '' }}" />
                </div>
            </div>

        </div>

    </div>

    <hr />

    <h4>Discounts</h4>

    <div class="form-group{{ ('military_discount_available' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Military discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="military-discount-available-yes" name="military_discount_available" value="1" onclick="document.getElementById('show-military-discount').style.display='block';" {{ view.facility.getMilitaryDiscount() ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="military-discount-available-no" name="military_discount_available" value="0" onclick="document.getElementById('show-military-discount').style.display='none';$('#military_discount_amount').val('');" {{ (view.facility.getMilitaryDiscount() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-military-discount" style="display:{{ view.facility.getMilitaryDiscount() ? 'block' : 'none' }};">
        <div class="form-group{{ ('military_discount_amount' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Military discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="military_discount_amount" class="form-control" value="{{ view.facility.getMilitaryDiscount() ? view.facility.getMilitaryDiscount() : view.facility.getDefaultMilitaryDiscount() }}" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
        <div class="form-group{{ ('military_discount_reserves' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Applies to reserves</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-reserves-yes" name="military_discount_reserves" value="1" {{ view.facility.getMilitaryDiscountAppliesToReserves() ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-reserves-no" name="military_discount_reserves" value="0" {{ (view.facility.getMilitaryDiscountAppliesToReserves() == '0') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>
        <div class="form-group{{ ('military_discount_veterans' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Applies to veterans</label>
            <div class="col-md-9">
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-veterans-yes" name="military_discount_veterans" value="1" {{ view.facility.getMilitaryDiscountAppliesToVeterans() ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="military-discount-veterans-no" name="military_discount_veterans" value="0" {{ (view.facility.getMilitaryDiscountAppliesToVeterans() == '0') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>
    </div>

    <!--
    <div class="ui segment " id="show-military-discount" style="display:{{ view.facility.getMilitaryDiscount() ? 'block' : 'none' }};">
        <div class="form-group{{ ('military_discount_amount' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Military discount amount</label>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" name="military_discount_percent_amount" class="form-control" value="{{ view.facility.getMilitaryDiscount() ? view.facility.getMilitaryDiscount() : '' }}" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
            <div class="col-md-1">
                OR
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-addon">$</span>
                    <input type="text" name="military_discount_dollar_amount" class="form-control" value="{{ view.facility.getMilitaryDiscount() ? view.facility.getMilitaryDiscount() : '' }}" />
                </div>
            </div>
        </div>
    </div>
    -->

    <div class="form-group{{ ('senior_discount_available' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Senior discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="senior-discount-available-yes" name="senior_discount_available" value="1" onclick="document.getElementById('show-senior-discount').style.display='block';" {{ view.facility.getSeniorDiscount() ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="senior-discount-available-no" name="senior_discount_available" value="0" onclick="document.getElementById('show-senior-discount').style.display='none';$('#senior_discount_amount').val('');" {{ (view.facility.getSeniorDiscount() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-senior-discount" style="display:{{ view.facility.getSeniorDiscount() ? 'block' : 'none' }};">
        <div class="form-group{{ ('senior_discount_amount' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Senior discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="senior_discount_amount" class="form-control" value="{{ view.facility.getSeniorDiscount() ? view.facility.getSeniorDiscount() : view.facility.getDefaultSeniorDiscount() }}" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group{{ ('student_discount_available' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Student discount</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="student-discount-available-yes" name="student_discount_available" value="1" onclick="document.getElementById('show-student-discount').style.display='block';" {{ view.facility.getStudentDiscount() ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="student-discount-available-no" name="student_discount_available" value="0" onclick="document.getElementById('show-student-discount').style.display='none';$('#student_discount_amount').val('');" {{ (view.facility.getStudentDiscount() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="ui segment " id="show-student-discount" style="display:{{ view.facility.getStudentDiscount() ? 'block' : 'none' }};">
        <div class="form-group{{ ('student_discount_amount' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 col-md-offset-1 control-label">Student discount amount</label>
            <div class="col-md-9">
                <div class="input-group">
                    <input type="text" name="student_discount_amount" class="form-control" value="{{ view.facility.getStudentDiscount() ? view.facility.getStudentDiscount() : view.facility.getDefaultStudentDiscount() }}" />
                    <span class="input-group-addon">%</span>
                </div>
            </div>
        </div>
    </div>

    <hr />

    <h4>Insurance</h4>

    <div class="form-group{{ ('insurance_required' in view.erroredFields) ? ' has-error' : '' }}" id="show_insurance_options">
        <label class="col-md-2 control-label">Insurance required</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="insurance-options-yes" name="insurance_required" value="1" {{ (view.facility.getInsuranceRequired() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="insurance-options-no" name="insurance_required" value="0" {{ (view.facility.getInsuranceRequired() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

	<div class="form-group{{ ('insurance_available' in view.erroredFields) ? ' has-error' : '' }}" id="show-available-insurance-options">
		<label class="col-md-2 control-label">Insurance available</label>
		<div class="col-md-9">
			<label class="control-label radio-inline">
				<input type="radio" id="insurance-available-yes" name="insurance_available" value="1" {{ (view.facility.getInsuranceAvailable() == 1) ? 'checked="checked"' : '' }} />Yes
			</label>
			<label class="control-label radio-inline">
				<input type="radio" id="insurance-available-no" name="insurance_available" value="0" {{ (view.facility.getInsuranceAvailable() is not null and view.facility.getInsuranceAvailable() == 0) ? 'checked="checked"' : '' }} />No
			</label>
		</div>
	</div>

	{% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.protection_plan_facility_amenities') %}

		<div class="form-group{{ ('protection_plan_required' in view.erroredFields) ? ' has-error' : '' }}">
			<label class="col-md-2 control-label">Protection Plan required</label>
			<div class="col-md-10">
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-required-yes" name="protection_plan_required" value="1" {{ (view.facility.getProtectionPlanRequired() == '1') ? 'checked="checked"' : '' }} />Yes
				</label>
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-required-no" name="protection_plan_required" value="0" {{ (view.facility.getProtectionPlanRequired() == '0') ? 'checked="checked"' : '' }} />No
				</label>
			</div>
		</div>

		<div class="form-group{{ ('protection_plan_available' in view.erroredFields) ? ' has-error' : '' }}">
			<label class="col-md-2 control-label">Protection Plan available</label>
			<div class="col-md-9">
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-available-yes" name="protection_plan_available" value="1" {{ (view.facility.getProtectionPlanAvailable() == 1) ? 'checked="checked"' : '' }} />Yes
				</label>
				<label class="control-label radio-inline">
					<input type="radio" id="protection-plan-available-no" name="protection_plan_available" value="0" {{ (view.facility.getProtectionPlanAvailable() is not null and view.facility.getProtectionPlanAvailable() == 0) ? 'checked="checked"' : '' }} />No
				</label>
			</div>
		</div>

	{% endif %}

	<div class="form-group{{ ('homeowners_insurance_accepted' in view.erroredFields) ? ' has-error' : '' }}">
		<label class="col-md-2 control-label">Homeowners or renters insurance accepted</label>
		<div class="col-md-9">
			<label class="control-label radio-inline">
				<input type="radio" id="homeowners-insurance-accepted-yes" name="homeowners_insurance_accepted" value="1" {{ (view.facility.getHomeownersInsuranceAccepted() == '1') ? 'checked="checked"' : '' }} />Yes
			</label>
			<label class="control-label radio-inline">
				<input type="radio" id="homeowners-insurance-accepted-no" name="homeowners_insurance_accepted" value="0" {{ (view.facility.getHomeownersInsuranceAccepted() == '0') ? 'checked="checked"' : '' }} />No
			</label>
		</div>
	</div>

	<hr>
    <h4>Other Amenities</h4>

    <div class="form-group{{ ('band_practice_allowed' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Band practice allowed</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="band-practice-allowed-yes" name="band_practice_allowed" value="1" {{ (view.facility.getBandPracticeAllowed() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="band-practice-allowed-no" name="band_practice_allowed" value="0" {{ (view.facility.getBandPracticeAllowed() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <div class="form-group{{ ('remote_paperwork' in view.erroredFields) ? ' has-error' : '' }}">
        <label class="col-md-2 control-label">Paperwork can be done remotely</label>
        <div class="col-md-10">
            <label class="control-label radio-inline">
                <input type="radio" id="remote-paperwork-yes" name="remote_paperwork" value="1" {{ (view.facility.getRemotePaperwork() == '1') ? 'checked="checked"' : '' }} />Yes
            </label>
            <label class="control-label radio-inline">
                <input type="radio" id="remote-paperwork-no" name="remote_paperwork" value="0" {{ (view.facility.getRemotePaperwork() == '0') ? 'checked="checked"' : '' }} />No
            </label>
        </div>
    </div>

    <hr>

    <h4>Vehicle Storage</h4>
    <script>
        /**
         * Semantic ui css is present, but not JS to make its accordian available.
         * @param state
         * @param event
         */
        function toggleVehicle(event) {
            event.preventDefault();
            if (event.currentTarget.text === 'Hide vehicle options') {
                event.currentTarget.text = 'Show vehicle options';
                document.getElementById('vehicle-amenities').style.display='none';
            } else {
                event.currentTarget.text = 'Hide vehicle options';
                document.getElementById('vehicle-amenities').style.display='block';
            }
        }
    </script>
    <div class="col-md-10">
        <a id="vehicle-hide" onclick="toggleVehicle(event);">Hide vehicle options</a>
    </div>

    <hr>
    <hr>

    <div id="vehicle-amenities" class="ui segment">

        <div class="form-group{{ ('vehicle_require_title' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Requires title</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-requires-title-yes" name="vehicle_require_title" value="1"{{ (view.vehicleRequireTitle == '1') ? ' checked' : '' }}>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-requires-title-no" name="vehicle_require_title" value="0"{{ (view.vehicleRequireTitle != '1') ? ' checked' : '' }}>No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('vehicle_require_registration' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Requires registration</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-registration-yes" name="vehicle_require_registration" value="1"{{ (view.vehicleRequireRegistration == '1') ? ' checked' : '' }}>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-registration-no" name="vehicle_require_registration" value="0"{{ (view.vehicleRequireRegistration != '1') ? ' checked' : '' }}>No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('vehicle_require_insurance' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Requires insurance</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-insurance-yes" name="vehicle_require_insurance" value="1"{{ (view.vehicleRequireInsurance == '1') ? ' checked' : '' }}>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-insurance-no" name="vehicle_require_insurance" value="0"{{ (view.vehicleRequireInsurance != '1') ? ' checked' : '' }}>No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('vehicle_require_running' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Must be drivable</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-running-yes" name="vehicle_require_running" value="1"{{ (view.vehicleRequireRunning == '1') ? ' checked' : '' }}>Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vehicle-require-running-no" name="vehicle_require_running" value="0"{{ (view.vehicleRequireRunning != '1') ? ' checked' : '' }}>No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('maintenance_allowed' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Maintenance allowed</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="maintenance-allowed-yes" name="maintenance_allowed" value="1" {{ (view.facility.getMaintenanceAllowed() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="maintenance-allowed-no" name="maintenance_allowed" value="0" {{ (view.facility.getMaintenanceAllowed() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::VEHICLE_MINIMUM_STAY')) %}
        <div class="form-group{{ ('minimum_stay' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Minimum stay required</label>
            <div class="col-md-10">
                <select name="minimum_stay" id="minimum-stay" class="form-control">
                    <option value="0">No minimum stay required</option>
                    <option value="1">1 month</option>
                    {% set minStay = view.facility.getMinimumStayRequired() %}
                    {% for x in 2..12 %}
                        <option value="{{ x }}" {{ (minStay == x) ? 'selected="selected"' : '' }}>
                            {{ x }} months
                        </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        {% endif %}

        <h5>Premium Services</h5>
        <h6>*Additional fees may apply</h6>

        <div class="form-group{{ ('wash_station' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Wash Station available</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="wash-station-yes" name="wash_station" value="1" {{ (view.facility.getWashStationAvailable() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="wash-station-no" name="wash_station" value="0" {{ (view.facility.getWashStationAvailable() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('dump_station' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Dump Station available</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="dump-station-yes" name="dump_station" value="1" {{ (view.facility.getDumpStationAvailable() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="dump-station-no" name="dump_station" value="0" {{ (view.facility.getDumpStationAvailable() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('general_maintenance' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">General Maintenance</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="general-maintenance-yes" name="general_maintenance" value="1" {{ (view.facility.getGeneralMaintenance() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="general-maintenance-no" name="general_maintenance" value="0" {{ (view.facility.getGeneralMaintenance() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('propane' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Propane</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="propane-yes" name="propane" value="1" {{ (view.facility.getPropane() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="propane-no" name="propane" value="0" {{ (view.facility.getPropane() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('diesel_and_gas' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Diesel & Gas</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="diesel-and-gas-yes" name="diesel_and_gas" value="1" {{ (view.facility.getFuel() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="diesel-and-gas-no" name="diesel_and_gas" value="0" {{ (view.facility.getFuel() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('does_state_inspections' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Does State Inspections</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="does-state-inspections-yes" name="does_state_inspections" value="1" {{ (view.facility.getDoesStateInspections() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="does-state-inspections-no" name="does_state_inspections" value="0" {{ (view.facility.getDoesStateInspections() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('auto_cleaning_and_detailing' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Auto Cleaning and/or Detailing</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="auto-cleaning-and-detailing-yes" name="auto_cleaning_and_detailing" value="1" {{ (view.facility.getDetailing() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="auto-cleaning-and-detailing-no" name="auto_cleaning_and_detailing" value="0" {{ (view.facility.getDetailing() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('air_pump' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Has Air Pump</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="air-pump-yes" name="air_pump" value="1" {{ (view.facility.getAirPump() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="air-pump-no" name="air_pump" value="0" {{ (view.facility.getAirPump() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('vacuum_station' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Has Vacuum Station</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="vacuum-station-yes" name="vacuum_station" value="1" {{ (view.facility.getVacuum() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="vacuum-station-no" name="vacuum_station" value="0" {{ (view.facility.getVacuum() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('ice_machine' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Has Ice Machine</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="ice-machine-yes" name="ice_machine" value="1" {{ (view.facility.getIce() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="ice-machine-no" name="ice_machine" value="0" {{ (view.facility.getIce() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>

        <div class="form-group{{ ('water_hose_spigot' in view.erroredFields) ? ' has-error' : '' }}">
            <label class="col-md-2 control-label">Water (Hose and/or Spigot)</label>
            <div class="col-md-10">
                <label class="control-label radio-inline">
                    <input type="radio" id="water-hose-spigot-yes" name="water_hose_spigot" value="1" {{ (view.facility.getWater() == '1') ? 'checked="checked"' : '' }} />Yes
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="water-hose-spigot-no" name="water_hose_spigot" value="0" {{ (view.facility.getWater() != '1') ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>
    </div>

    <hr>

    <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />
    <div class="form-actions">
        <div class="right">
            <input class="ui primary button" name="commit" type="submit" value="Save Changes" />
            <input type="hidden" name="csrf_token" value="{{ view.csrf_token }}">
        </div>
    </div>
</form>