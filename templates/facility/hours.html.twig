{% macro isAndInArray(needle, array) %}
    {% if array is iterable and needle in array %}checked="checked"{% endif %}
{% endmacro %}
{% import _self as macros %}

{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'hours', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

{% set appointmentOnlyOfficeHours = view.facility.getAppointmentOnlyOfficeHours() %}
{% set daysOfTheWeek = constant('\\Genesis_Entity_FacilityHours::DAYS_OF_THE_WEEK') %}

<form id="hours-form" class="ui form form-horizontal" method="post" action="{{ url('features', {'action': 'hours'}) }}?fid={{ view.facilityId }}">
    {% if view.alert %}
        <p class="alert{{ view.alertClass ? ' ' ~ view.alertClass : '' }}">
            {{ view.alert }}
        </p>
    {% endif %}

    <img id="loading-spinner" src="/images/loading.gif" width="15" height="15" style="display:none" />

    <h4>Observed Holidays</h4>
    <div class="ui grid holidays-section">
        <div class="two wide column">
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="newYearsDay" name="observed[newYearsDay]" type="checkbox"{{ 'newYearsDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>New Year's Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="mlkDay" name="observed[mlkDay]" type="checkbox"{{ 'mlkDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Martin Luther King, Jr. Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="presidentsDay" name="observed[presidentsDay]" type="checkbox"{{ 'presidentsDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Presidents' Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="memorialDay" name="observed[memorialDay]" type="checkbox"{{ 'memorialDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Memorial Day</label>
            </div>
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="independenceDay" name="observed[independenceDay]" type="checkbox"{{ 'independenceDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Independence Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="laborDay" name="observed[laborDay]" type="checkbox"{{ 'laborDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Labor Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="columbusDay" name="observed[columbusDay]" type="checkbox"{{ 'columbusDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Columbus Day</label>
            </div>
            <div class="ui checkbox bootstrap-aligned">
                <input class="holiday" id="easterDay" name="observed[easter]" type="checkbox"{{ 'easter' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Easter</label>
            </div>
        </div>
        <div class="four wide column">
            <div class="ui checkbox">
                <input class="holiday" id="veteransDay" name="observed[veteransDay]" type="checkbox"{{ 'veteransDay' in view.observedHolidays ? ' checked="checked"' : '' }}/>
                <label>Veterans Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="thanksgivingDay" name="observed[thanksgivingDay]" type="checkbox"{{ 'thanksgivingDay' in view.observedHolidays ? ' checked="checked"' : '' }}>
                <label>Thanksgiving Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="christmasDay" name="observed[christmasDay]" type="checkbox"{{ 'christmasDay' in view.observedHolidays ? ' checked="checked"' : '' }}>
                <label>Christmas Day</label>
            </div>
            <div class="ui checkbox">
                <input class="holiday" id="christmasEve" name="observed[christmasEve]" type="checkbox"{{ 'christmasEve' in view.observedHolidays ? ' checked="checked"' : '' }}>
                <label>Christmas Eve</label>
            </div>
        </div>
        <div class="two wide column">
            <a class="ui secondary button holidays-select-all" onclick="$('.holiday').attr('checked',true);">Select All</a>
            <a class="ui secondary button holidays-unselect-all" onclick="$('.holiday').attr('checked', false);">Unselect All</a>
        </div>
    </div>

    <hr />

    <h4>Custom Closures</h4>
    <div class="custom-closures-section">
        <div class="form-group">
            <label class="col-md-2 control-label">Closed On</label>
            <p class="col-md-3">
            <span class="input-group">
              <input class="form-control" type="text" value="" name="custom_closure_start" placeholder="Starting">
              <span class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </span>
            </span>
            </p>
            <p class="col-md-3">
            <span class="input-group">
              <input class="form-control" type="text" value="" name="custom_closure_end" placeholder="Ending">
              <span class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </span>
            </span>
            </p>
            <div class="col-md-2">
            <p><button type="button" class="ui secondary button" data-js="add-closure" disabled="">Add</button></p>
            </div>
            <input type="hidden" id="custom_closure" name="custom_closure" value="">
        </div>

        <div class="form-group">
            <label class="col-md-2 control-label">Upcoming Closures</label>
            <div class="col-md-4">
            <ol data-js="upcoming-closures" class="upcoming-closures list-unstyled">
                <li data-js="closure-template" class="hidden">
                    <a class="pull-right" data-id="">
                        <i class="fa fa-times-circle"></i>
                    </a>
                </li>
                {% for closure in view.upcomingClosures %}
                <li>
                    <a class="pull-right" data-id="{{ closure.closureId }}">
                        <i class="fa fa-times-circle"></i>
                    </a>
                    {{ closure.dateString }}
                </li>
                {% endfor %}
            </ol>
            <p data-js="no-closures" class="help-block{{ view.upcomingClosures|length > 0 ? ' hidden' : '' }}">No upcoming closures.</p>
            </div>
        </div>
    </div>

    <hr />

    <h4>Office Hours</h4>
    <div class="ui grid office-hours-section">
        <div class="fourteen wide column">
            {% for i, dayOfTheWeek in daysOfTheWeek %}
                {% set shortName = dayOfTheWeek[:3] %}
                {% set lowerName = shortName|lower %}
                {% set startGetter = 'get' ~ shortName ~ 'Start' %}
                {% set endGetter = 'get' ~ shortName ~ 'End' %}
                {% set hideOfficeHours = not view.officeHours[startGetter]() or not view.officeHours[endGetter]() %}
                <div class="form-group">
                    <label class="col-md-2 control-label">{{ dayOfTheWeek }}</label>
                    <div class="col-md-2">
                        <input id="hrs_o_{{ lowerName }}_s" name="hrs_o_{{ lowerName }}_s" class="form-control{{ hideOfficeHours ? ' gray' : '' }}" type="text" value="{{ view.officeHours.humanReadable(view.officeHours[startGetter]()) }}" {{ hideOfficeHours ? 'disabled' : '' }}/>
                    </div>
                    <div class="col-md-2">
                        <input id="hrs_o_{{ lowerName }}_e" name="hrs_o_{{ lowerName }}_e" class="form-control{{ hideOfficeHours ? ' gray' : '' }}" type="text" value="{{ view.officeHours.humanReadable(view.officeHours[endGetter]()) }}" {{ hideOfficeHours ? 'disabled' : '' }}/>
                    </div>
                    <div class="col-md-1">
                        <div id="check_o_{{ lowerName }}_s" class="ui checkbox bootstrap-aligned">
                            <input name="closed_{{ lowerName }}" type="checkbox" {{ hideOfficeHours ? 'checked="checked"' : '' }} />
                            <label>Closed</label>
                        </div>
                    </div>
                    <div class="col-md-2 {{ call_static('\\Genesis_Service_Feature', 'isActive', constant('\\Genesis_Entity_Feature::MYFOOT_APPOINTMENT_ONLY')) ? '' : 'hide' }}">
                        <div class="ui checkbox appointment-only-{{ lowerName }}-checkbox">
                            <input id="appointment_only_{{ lowerName }}" type="checkbox" name="appointment_only_office_hours_{{ lowerName }}" {{ appointmentOnlyOfficeHours[lowerName] ? 'checked="checked"' : '' }} value="1" onclick ="toggleAppointmentOnlyHours(this,new Array('hrs_o_{{ lowerName }}_s', 'hrs_o_{{ lowerName }}_e'),'check_o_{{ lowerName }}_s')"/>
                            <label for="appointment_only_{{ lowerName }}">Appointment Only</label>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <div class="two wide column">
            <a class="ui secondary button apply-to-all-office-button" onclick="applyToAll('o')">Apply to All</a>
        </div>
    </div>

    <hr />

    <h4>Access Hours</h4>
    <div class="ui grid access-hours-section">
        <div class="row">
            <div class="sixteen wide column">
                                <div class="form-group{{ 'twentyfour_hr_access' in view.erroredFields ? ' has-error' : '' }}">
                    <label class="col-md-2 control-label">24-hour access</label>
                    <div class="col-md-10">
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="1" id="twentyfour_hr_access_yes" {{ view.facility.getTwentyFourHourAccess() == '1' and view.facility.getTwentyFourHourAccessRestricted() == '0' ? 'checked="checked"' : '' }} />Yes, for all units and customers
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="2" id="twentyfour_hr_access_yes_with_restrictions" {{ view.facility.getTwentyFourHourAccess() == '1' and view.facility.getTwentyFourHourAccessRestricted() == '1' ? 'checked="checked"' : '' }} />Yes, but with restrictions
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="twentyfour_hr_access" value="0" id="twentyfour_hr_access_no" {{ view.facility.getTwentyFourHourAccess() == '0' ? 'checked="checked"' : '' }} />No
                            </label>
                        </div>
                    </div>
                </div>

                <div class="ui segment" id="show-24-hour-access-type" style="display:{{ view.facility.getTwentyFourHourAccessRestricted() ? 'block' : 'none' }};"
                    <div class="form-group">
                        <label class="col-md-2 col-md-offset-1 control-label">Limitations to 24-hour access</label>
                        <div class="col-md-9">
                        <div class="ui form">
                        <div class="grouped fields">
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                           value="{{ constant('\\Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}
                                    />
                                    <label>Specific units (must be applied to individual units in the <a href="{{ url('features', {'action': 'inventory'}) }}?fid={{ view.facilityId }}">Units section</a>)
                                    </label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                           value="{{ constant('\\Genesis_Entity_FacilityData::BACKGROUND_CHECK_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::BACKGROUND_CHECK_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}
                                    />
                                    <label>Background check</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                           value="{{ constant('\\Genesis_Entity_FacilityData::BUSINESS_ACCESS_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::BUSINESS_ACCESS_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}
                                    />
                                    <label>Business accounts</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                           value="{{ constant('\\Genesis_Entity_FacilityData::MANAGER_APPROVAL_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::MANAGER_APPROVAL_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}
                                    />
                                    <label>Manager approval</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                        id="twentyfour_hour_access_fee_option"
                                        value="{{ constant('\\Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}
                                    />
                                    <label>Additional fees</label>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned" id="24hr-fee-input"
                                    {% set hasAdditionalFees = constant('\\Genesis_Entity_FacilityData::ADDITIONAL_FEES_24_HOUR_ACCESS') in view.facility.getTwentyFourHourAccessSupplemental() %}
                                    {{ not hasAdditionalFees ? 'style="display: none;"' : '' }}
                                >
                                    <div class="form-group">
                                        <label class="col-md-2 control-label">Additional fee amount</label>
                                        <div class="col-md-10">
                                            <div class="input-group">
                                                <span class="input-group-addon">$</span>
                                                <input type="text" name="twentyfour_hr_access_fee" id="twentyfour_hr_access_fee" class="form-control" value="{{ view.facility.getTwentyFourHourAccessFee() ? view.facility.getTwentyFourHourAccessFee() : '' }}" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="field">
                                <div class="ui checkbox bootstrap-aligned">
                                    <input type="checkbox" name="twentyfour_hr_access_type[]"
                                           value="{{ constant('\\Genesis_Entity_FacilityData::APPOINTMENT_ONLY_24_HOUR_ACCESS') }}"
                                        {{ macros.isAndInArray(constant('\\Genesis_Entity_FacilityData::APPOINTMENT_ONLY_24_HOUR_ACCESS'), view.facility.getTwentyFourHourAccessSupplemental()) }}/>
                                    <label>By appointment only</label>
                                </div>
                            </div>
                        </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="fourteen wide column">
                {% for i, dayOfTheWeek in daysOfTheWeek %}
                    {% set shortName = dayOfTheWeek[:3] %}
                    {% set lowerName = shortName|lower %}
                    {% set startGetter = 'get' ~ shortName ~ 'Start' %}
                    {% set endGetter = 'get' ~ shortName ~ 'End' %}
                    {% set isClosed = not view.accessHours[startGetter]() or not view.accessHours[endGetter]() %}
                    {% set hideAccessHours = view.facility.getTwentyFourHourAccess() == '1' and view.facility.getTwentyFourHourAccessRestricted() != '1' %}
                    <div class="form-group js-access-hours">
                        <label class="col-md-2 control-label">{{ dayOfTheWeek }}</label>
                        <div class="col-md-2">
                            <input id="hrs_a_{{ lowerName }}_s" name="hrs_a_{{ lowerName }}_s" class="form-control js-access-hours-start{{ hideAccessHours or isClosed ? ' gray' : '' }}" type="text" value="{{ view.accessHours.humanReadable(view.accessHours[startGetter]()) }}" {{ hideAccessHours or isClosed ? 'disabled' : '' }}/>
                        </div>
                        <div class="col-md-2">
                            <input id="hrs_a_{{ lowerName }}_e" name="hrs_a_{{ lowerName }}_e" class="form-control js-access-hours-end{{ hideAccessHours or isClosed ? ' gray' : '' }}" type="text" value="{{ view.accessHours.humanReadable(view.accessHours[endGetter]()) }}" {{ hideAccessHours or isClosed ? 'disabled' : '' }}/>
                        </div>
                        <div class="col-md-1">
                            <div id="check_a_{{ lowerName }}_s" class="ui checkbox bootstrap-aligned">
                                <input  type="checkbox" class="js-access-closed" {{ isClosed ? 'checked="checked"' : '' }} />
                                <label>Closed</label>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="two wide column">
                <a class="ui secondary button apply-to-all-access-button" onclick="applyToAll('a')">Apply to All</a>
            </div>
        </div>
    </div>

    <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />
    <div class="form-actions">
        <div class="right">
            <input class="ui primary button" name="commit" type="submit" value="Save Changes" />
        </div>
    </div>
</form>
