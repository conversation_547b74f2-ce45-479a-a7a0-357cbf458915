{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'photos', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<h3>Upload Photos</h3>
<p>(Maximum size: 5 MB)</p>

<form id="photos-form" method="post" enctype="multipart/form-data"
    action="{{ url('features', {'action': 'photos'}) }}?fid={{ view.facilityId }}"
    class="ui form segment">

    <p>Choose images:</p>
    <div id="image-uploaders">
        <input type="file" name="image[]" />
    </div><br />
    <p><a id="add-image" href="#">Add another image</a></p>
    <div class="right">
        <button type="submit" id="photos-form-submit" class="ui primary button" data-loading-text="Uploading">Upload</button>&nbsp;&nbsp;<img src="/images/loaders/large.gif" class="is-hidden" id="photos-form-submit-loading" />
    </div>
    <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />
</form>

<div class="row">
    <div class="col-md-9">
        <div class="col-md-5">
        {# @var image Genesis_Entity_FacilityImage #}
        {% for key, image in view.facility.getImages() %}
        {% if image.getPictureNum() == 1 %}

            <div class="thumbnail">
                <img src="{{ view.facility.getFirstImage().getCdnMedium() }}" /><br />
                <div class="caption">
                    <a href="javascript:confirmRedirect('Are you sure you want to delete this image?','{{ url('features', {'action': 'deleteimage'}) }}?fid={{ view.facilityId }}&number={{ view.facility.getFirstImage().getPictureNum() }}');" class="ui button icon">
                        <i class="trash outline icon"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-7">

        {% else %}
            <div style="margin-bottom:1em; margin-right:1em; float:left;" class="thumbnail">
                <img src="{{ image.getCdnSmall() }}" />
                <br />
                <div class="caption">
                    <a href="javascript:confirmRedirect('Are you sure you want to delete this image?','{{ url('features', {'action': 'deleteimage'}) }}?fid={{ view.facilityId }}&number={{ image.getPictureNum() }}');" class="ui icon button">
                        <i class="trash outline icon"></i>
                    </a>

                    <br />
                    <br />
                    <a href="{{ url('features', {'action': 'defaultimage'}) }}?fid={{ view.facilityId }}&number={{ image.getPictureNum() }}" class="ui secondary button">Set as Default</a>

                </div>
            </div>
        {% endif %}
        {% endfor %}
        <div class="clear"></div>
        </div>
    </div>
    <div class="col-md-3" >
        <p>NOTE: SpareFoot prohibits photos that display your phone number or other contact information, and reserves the right to edit or remove such photos. If you would like us to edit a photo for you, email <a href="mailto:<EMAIL>"><EMAIL></a> with your facility name, address, and the photo attached.</p>
    </div>
</div>
