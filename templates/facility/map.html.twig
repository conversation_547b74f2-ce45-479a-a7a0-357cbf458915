<style type="text/css">
#map_canvas, #panorama img {
    border: none !important;
    max-width: none !important;
}
</style>

{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'map', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<script type="text/javascript">
var latitude = {{ view.latitude }};
var longitude = {{ view.longitude }};

{% if view.pov %}
var myPov = { heading: {{ view.pov.getYaw() }}, pitch: {{ view.pov.getPitch() }}, zoom:{{ view.pov.getZoom() }} };
{% else %}
var myPov = {heading:0, pitch:0, zoom: 1.5};
{% endif %}

</script>

<div class="ui segment basic">
    <h2>Fine tune your street view image</h2>
    <p>We use your facility address to show a street view.  Sometimes it's not perfect so we built a tool that allows you to tune in your street view and orient the camera so that it points at your facility.  Just follow the steps below and your street view picture will be updated on SpareFoot automatically.</p>

    {% if view.alert %}
        <div class="ui message {{ view.alertClass ? ' ' ~ view.alertClass : '' }}">
            {{ view.alert }}
        </div>
    {% endif %}

    <h6>Step 1</h6>
    <h4>Click your exact location on the map</h4><br />
    <div id="map_canvas" style="width:99.9%; height:300px; border:1px solid #ccc;"></div><br /><br />

    <h6>Step 2</h6>
    <h4>Orient the view to point at your facility</h4><br />
    <div id="panorama" style="width:99.9%; height:300px; border:1px solid #ccc; margin-bottom: 1.5em;"></div>

    <form class="ui right form" method="post" action="{{ path('features', {'action': 'map'}) }}?fid={{ view.facilityId }}">
        <input id="lat" name="lat" type="hidden" />
        <input id="lng" name="lng" type="hidden" />
        <input id="yaw" name="yaw" type="hidden" />
        <input id="pitch" name="pitch" type="hidden" />
        <input id="zoom" name="zoom" type="hidden" />
        <input name="facility_id" type="hidden" value="{{ view.facility.getId() }}" />
        <button onclick="return updateStreetView();" class="ui primary button">Save Changes</button>
    </form>

</div>

<script type="text/javascript" src="https://maps.google.com/maps/api/js?sensor=false"></script>
