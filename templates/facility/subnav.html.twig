{% set action = view.actionName %}
{% if not view.facility or not (view.facility.getId() is defined) %}
    {# Error: facility object must be passed to partial view #}
    <div class="alert alert-danger">
        Error: facility object must be passed to partial view<br/>{{ action }} {{ view.request.getControllerName() }}
    </div>
{% else %}
{% set facilityId = view.facility.getId() %}
{% set isFSS = view.facility.getType() == constant('\\Genesis_Entity_Facility::TYPE_VALET') %}

<div class="subnav">
    {% if view.facility.getCorporation().getSourceId() == constant('\\Genesis_Entity_Source::ID_MANUAL') or isFSS %}
        {% if view.facility %}
            {% if view.facility.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                {% if action == 'bid' or action == 'bulkbid' %}
                    <ul class="nav nav-tabs">
                        <li{{ action in ['bid','bidsetup','bid-custom'] ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'bid'}) }}?fid={{ facilityId }}" id="facility-bid" data-segment-category="facility subnav" data-segment-label="bidding">Facility Bidding</a></li>
                        <li{{ action == 'bulkbid' ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'bulkbid'}) }}?fid={{ facilityId }}" id="bulk-bidding" data-segment-category="facility subnav" data-segment-label="bulk bidding">Bulk Bidding</a></li>
                    </ul>
                {% endif %}
            {% endif %}
        {% elseif 'listings' != action %}
            <a class="ui button" href="{{ path('features', {'action': 'units'}) }}?fid={{ facilityId }}" data-segment-category="facility subnav" data-segment-label="back button"><i class="arrow left icon"></i> Back</a>
        {% endif %}
    {% else %}
        <ul class="nav nav-tabs">
            <li{{ action in ['inventory','units', 'groupedinventory'] ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'units'}) }}?fid={{ facilityId }}" id="facility-units">Units</a></li>
            <li{{ 'details' == action ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'details'}) }}?fid={{ facilityId }}" id="facility-details" data-segment-category="facility subnav" data-segment-label="details">Details</a></li>
            <li{{ 'amenities' == action ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'amenities'}) }}?fid={{ facilityId }}" id="facility-amenities" data-segment-category="facility subnav" data-segment-label="amenities">Amenities</a></li>
            <li{{ 'hours' == action ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'hours'}) }}?fid={{ facilityId }}" id="facility-hours" data-segment-category="facility subnav" data-segment-label="hours">Hours</a></li>
            <li{{ 'photos' == action ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'photos'}) }}?fid={{ facilityId }}" id="facility-media" data-segment-category="facility subnav" data-segment-label="photos">Photos</a></li>
            {% if view.facility and view.facility.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                {% if view.isBidOptimizerActive %}
                    <li><a href="{{ path('features', {'action': 'demandoptimizer'}) }}?fid={{ facilityId }}" id="bid-optimizer" data-segment-category="facility subnav" data-segment-label="bidding">Bid Optimizer</a></li>
                {% endif %}
                <li{{ action in ['bid','bidsetup','bid-custom'] ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'bid'}) }}?fid={{ facilityId }}" id="facility-bid" data-segment-category="facility subnav" data-segment-label="bidding">Facility Bidding</a></li>
                <li{{ 'bulkbid' == action ? ' class="active"' : '' }}><a href="{{ path('features', {'action': 'bulkbid'}) }}?fid={{ facilityId }}" id="bulk-bidding" data-segment-category="facility subnav" data-segment-label="bulk bidding"> Bulk Bidding</a></li>
             {% endif %}
            {% if action == 'list' %}{# hidden option for gods #}
                <li class="active"><a href="{{ path('features', {'action': 'list'}) }}">List</a></li>
            {% endif %}
        </ul>
    {% endif %}

    <div class="clear"></div>
</div>
{% endif %}
