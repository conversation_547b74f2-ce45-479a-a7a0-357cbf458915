{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'bulkbid', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<div class="bulk-bidding-page">
    <div class="bid-menu full-width-row light-grey" style="margin-top: -14px; background-color: #FAFAFA" >
        <div class="row">
            <div class="col-md-10">
                <h3> Bulk Bidding</h3>
                <p>The Bulk Bidding tool allows you to quickly and easily change bids across multiple facilities at once. To get started, download your bid opportunity file to understand where your facilities are currently ranking in the search results, and what adjustments are needed for your bid modifier to achieve your desired rank by zip code or city.<a href="https://support.sparefoot.com/hc/en-us/articles/360053658393-Bulk-Bidding" target=”_blank”> Learn more > </a></span></p>
            </div>
        </div>
    </div>
    <!-- Added bidoptimizer active check -->
    {% if not view.isBidOptimizerActive %}
    <div class="bid-menu bid-menu full-width-row">
        <div class="row">
            <div class="col-md-4">
                <h4>Step 1.</h4>
                <p class="bulk-bid-step-spacing">Either export your bid opportunity file below <span class="bold">OR</span> create your own .csv with your facility IDs</p>
            </div>
            <div class="col-md-4">
                <h4>Step 2.</h4>
                <p class="bulk-bid-step-spacing">Fill out your file with your desired bid rates. <a href="https://support.sparefoot.com/hc/en-us/articles/360053658393-Bulk-Bidding" target=”_blank” class="white-space: nowrap">Learn more ></a></p>
            </div>
            <div class="col-md-4">
                <h4>Step 3.</h4>
                <p class="bulk-bid-step-spacing">Upload your file below to update your bid modifier in bulk</p>
            </div>
        </div>
    </div>
    <div class="full-width-row light-grey" style="padding-bottom: 0px;">
        <div class="row">
            <div class="col-md-5">
                <h2>Upload Bulk Bids</h2>
                <form id="bulkbid-form" method="post" enctype="multipart/form-data"
                      action="{{ url('features', {'action': 'bulkbid'}) }}?fid={{ view.facilityId }}"
                      class="ui form segment">
                    <div id="csv-uploader">
                        <input type="file" name="bids" accept="text/csv"/>
                    </div><br/>
                    <div class="export-button-wrapper">
                        <button type="submit" id="bulkbid-form-submit" class="ui secondary button download-opp-btn">Upload bulk bids</button>
                    </div>
                </form>
                <ul class="remove-bullet-padding">
                    <li class="italics"> File type must be .csv</li>
                    <li class="italics"> Upload a file name 'bids.csv' with 'facility_id' as the header for the first column, and 'bid' in the second column. See example.</li>
                </ul>
            </div>
            <div class="col-md-7 centered">
                <img class="bulk-bid-example-image" src="/images/bulk_bid_preview.png"/>
            </div>
        </div>
    </div>
   
    <div class="full-width-row">
        <div class="row header-row">
            <div class="col-md-10">
                <h2>Export Data</h2>
                <p>Download detailed bidding opportunities for your facilities by zip code or city.
                    Get access to even more data about your listing performance, average market prices on SpareFoot and how your facility compares to help you make informed bidding decisions.</p>
            </div>
        </div>
        <div class="export-row">
            <div class="export-zip-opps">
                <div class="export-button-wrapper">
                    <button id="download-zip-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('zip');" data-segment-category="export data" data-segment-label="export by zip">Export by Zip</button>
                </div>
                <div class="messaging">
                    <div class="progress zip">
                        <div id="zip_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <span class="download-link"></span>
                    <span class="error-message"></span>
                </div>
                <div class="spacer"></div>
            </div>
        </div>
        <div class="export-row">
            <div class="export-city-opps">
                <div class="export-button-wrapper">
                    <button id="download-city-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('city');" data-segment-category="export data" data-segment-label="export by zip">Export by City</button>
                </div>
                <div class="messaging">
                    <div class="progress city">
                        <div id="city_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <span class="download-link"></span>
                    <span class="error-message"></span>
                </div>
                <div class="spacer"></div>
            </div>
        </div>
    </div>
    <div>
        <hr>
    </div>
    <div class="full-width-row">
        <h4>Please note the following:</h4>
        <ul class="remove-bullet-padding">
            <li class="italics">If a bid provided in bid.csv is lower than your minimum bid, we will adjust to use the minimum bid.</li>
            <li class="italics">If a bid provided in bids.csv is higher than 10.00, we will adjust to use 10.00</li>
            <li class="italics">Bids may only be adjusted in increments of .05. If a bid provided in bids.csv has more than two decimal places of precision, we will round up to the next increment of .05.</li>
        </ul>
    </div>
    {% endif %}
</div>