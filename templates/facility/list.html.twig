{% set facility = call_static('\\Genesis_Service_Facility', 'loadById', view.facilityId) %}

<div class="toolbar">
    {{ include('daterange.html.twig', {
        'action': url('account_overview'),
        'trueDateRange': view.trueDateRange,
        'trueBeginDate': view.trueBeginDate,
        'trueEndDate': view.trueEndDate,
        'limit': view.limit,
        'page': view.page,
        'showExport': false
    }) }}

    <a href="{{ url('features', {'action': 'export'}) }}?true_date_range={{ view.trueDateRange|url_encode }}" class="ui secondary button pull-right" style="margin-right:0.5em;"><i class="file icon"></i> Export Spreadsheet</a>

    {# now they can add a facility even if they don't have a manual integration yet (one will be created) #}
    {% if view.loggedUser.isMyfootGod() or view.loggedUser.isMyfootAdmin() %}
        <a id="add_facility" class="ui primary button" href="{{ url('features', {'action': 'type'}) }}"><i class="fa fa-plus"></i> Add Facility</a>
    {% endif %}
</div>
<br class="clear" />
<form class="ui form form-search" action="{{ url('account_overview') }}" method="get">
    {# <span class="fa fa-search"></span> #}

    <div class="ui fluid action input">
      <input type="search" name="search_term" id="search_term" placeholder="Search Facilities" value="{{ view.searchTerm }}" />
      <button class="ui icon button">
        <i class="search icon"></i>
      </button>
    </div>


    {% if view.searchTerm %}
    <span class="clear-search">
        <a href="{{ url('account_overview') }}?clear_search_term=1">
            <img src="/images/search-clear.gif" />
        </a>
    </span>
    {% endif %}
</form>

{% if view.facilities|length == 0 %}
<h5>No facilities found. <a href="{{ url('account_overview') }}?clear_search_term=1">Clear Search.</a></h5>
{% else %}

<table id="facilities" class="ui table table-striped data-grid">
    <thead>
        <tr>
            <th>Facility</th>
            <th><a data-rel="tooltip" title="The method of which this facility data was input into SpareFoot.">Source</a></th>
            {% if view.loggedUser.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                <th><a data-rel="tooltip" title="Your bid affects where you rank in AdNetwork search results. Increasing your bid raises your ranking, which generally results in more reservations.">Bid</a></th>
            {% endif %}
            <th>Reservations</th>
            {% if view.loggedUser.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                <th><a data-rel="tooltip" title="Average cost per reservation is the total cost of all reservations at a facility divided by the number of reservations at that facility. If you received reservations at different bid amounts, the average cost per reservation is the average of those bid amounts.">Avg. Cost Per Reservation</a></th>
            {% endif %}
            <th>Inquiries</th>
        </tr>
    </thead>
    <tbody>
        {% for facility in view.facilities %}
            <tr id="{{ facility.id }}" class="{{ facility.hidden ? 'disabled-row' : 'enabled-row' }}">
                <td>
                    <strong><a href="{{ url('features', {'action': 'units'}) }}?fid={{ facility.id }}" id="facility-page-{{ facility.id }}">{{ facility.title }}</a></strong>
                    {% if facility.hidden %}
                    <span class="ui label">Inactive</span>
                    {% endif %}
                </td>
                <td>
                    {{ facility.source_name }}
                    {% if facility.source_id == constant('\\Genesis_Entity_Source::ID_MANUAL') and not facility.self_reported_source_id %}
                        (<a href="{{ url('features', {'action': 'details'}) }}?fid={{ facility.id }}#facility_software">Edit</a>)
                    {% endif %}
                </td>
                {% if view.loggedUser.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                <td>{{ facility.bid_string }}</td>
                {% endif %}
                <td>{{ facility.num_reservations }}</td>
                {% if view.loggedUser.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                <td>${{ facility.cost_per_reservation|number_format(2) }}</td>
                {% endif %}
                <td>{{ facility.consumer_contacts }}</td>
            </tr>
        {% endfor %}
    </tbody>
</table>

{# 
Quick Paginator adapted from:
http://code.tutsplus.com/tutorials/how-to-paginate-data-with-php--net-2928
#}
{# {% macro createLinks(links, list_class, page, limit, total) %}
    {% if limit == 'all' %}
        {% set html = '' %}
    {% else %}
        {% set last = (total / limit)|round(0, 'ceil') %}
        {% set start = ((page - links) > 0) ? (page - links) : 1 %}
        {% set end = ((page + links) < last) ? (page + links) : last %}

        {% set html = '<div class="' ~ list_class ~ '">' %}
        {% set class = (page == 1) ? "disabled" : "" %}
        {% set url = (page == 1) ? '' : ('?limit=' ~ limit ~ '&page=' ~ (page - 1)) %}
        {% set html = html ~ '<a class="' ~ class ~ ' item icon" href="' ~ url ~ '"><i class="icon arrow left"></i></a>' %}

        {% if start > 1 %}
            {% set html = html ~ '<a class="item" href="?limit=' ~ limit ~ '&page=1">1</a>' %}
            {% set html = html ~ '<div class="disabled item"><span>...</span></div>' %}
        {% endif %}

        {% for i in start..end %}
            {% set class = (page == i) ? "active" : "" %}
            {% set html = html ~ '<a class="' ~ class ~ ' item" href="?limit=' ~ limit ~ '&page=' ~ i ~ '">' ~ i ~ '</a>' %}
        {% endfor %}

        {% if end < last %}
            {% set html = html ~ '<div class="disabled item"><span>...</span></div>' %}
            {% set html = html ~ '<a class="item" href="?limit=' ~ limit ~ '&page=' ~ last ~ '">' ~ last ~ '</a>' %}
        {% endif %}

        {% set class = (page == last) ? "disabled" : "" %}
        {% set url = (page == last) ? '' : ('?limit=' ~ limit ~ '&page=' ~ (page + 1)) %}
        {% set html = html ~ '<a class="' ~ class ~ ' item icon" href="' ~ url ~ '"><i class="icon arrow right"></i></a>' %}
        {% set html = html ~ '</div>' %}
    {% endif %}
    {{ html|raw }}
{% endmacro %}
{% import _self as macros %}

{% if not view.searchTerm and view.totalItems > view.limit %}
    {{ macros.createLinks(3, 'ui pagination menu', view.page, view.limit, view.totalItems) }}
{% endif %} #}

{% endif %}
