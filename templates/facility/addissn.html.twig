<div class="setup-content-container">

<div class="setup-content">

<div class="content-row">
    <h2>Insomniac Integration Setup</h2>
    <p>Automatically import your facilities and units into SpareFoot.</p>
    <p>SpareFoot will sync with Insomniac once per hour to make sure we are showing the most current list available units for your properties.</p>
</div>

<div class="input-row string">
    <h4>Step 1</h4>
    <p class="description">Obtain your Account Id from Opentech <a href="#" target="_blank">Click here for instructions</a>.</p>
</div>

<div class="input-row string">
    <h4>Step 2</h4>
    <p class="description">Enter the Account Id you received from Opentech below and click "sync".</p>
    <br />

    <div class="form-horizontal">

        <div class="form-group">
            <label for="username" class="col-md-2 control-label">Account Id</label>
            <div class="col-md-10">
                <input id="accountId" name="accountId" type="text" class="form-control" />
            </div>
        </div>

    </div>
    <div class="form-horizontal">
        <div class="sync-action">
                <input type="checkbox" style="display: none;" id="syncInactive" name="syncInactive" value="1"/>
            <input id="sync" class="btn btn-large btn-success pull-right" name="sync" type="submit" value="Sync" data-loading-text="Syncing" data-complete-text="Sync Complete" /><img src="/images/loaders/large.gif" class="loading hide pull-right" />&nbsp;&nbsp;
                <span class="loading-message alert alert-warning hide"><br />We're connecting to the Opentech servers now. This could take a minute.</span>

        </div>
    </div>
</div>



<div class="content-footer">
    <form method="post" action="{{ path('features', {'action': 'addsummary'}) }}" class="pull-right">
        <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value=""/>
        <input id="next" class="btn btn-lg btn-primary" name="next" type="submit" value="Next" disabled onClick="nextLoader();"/>
    </form>
    <a id="back_btn" class="btn btn-default btn-lg" href="{{ path('features', {'action': 'type'}) }}">Back</a>
</div>

</div>
</div>