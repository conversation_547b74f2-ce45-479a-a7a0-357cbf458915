{% set maxOpportunities = constant('\\Sparefoot\\MyFootService\\Clients\\ClientApiClient::MAX_BID_OPPORTUNITIES_SHOWN') %}
{% set isBidTypePercent = (view.facility.getAccount().getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_PERCENT')) %}

<script type="text/javascript">
    var smallUnitsBidPercent = {{ constant('\\Genesis_Entity_Transaction::BID_SMALL_PERCENT') }};
    var largeUnitsBidPercent = {{ constant('\\Genesis_Entity_Transaction::BID_LARGE_PERCENT') }};
    var updateBidRequest = new XMLHttpRequest();
    var updateBidTimeout;
    var facilityMinBid = "{{ view.facility.getMinBid()|number_format(2, '.', '') }}";
    var facilityMaxBid = "{{ view.facility.getMaxBid()|number_format(2, '.', '') }}";
    var unitAvgs = {{ view.facility.getAvgUnitPrices()|json_encode|raw }};
    var currentFacilityId = {{ view.facility.getId() }};
    var bidShiftAmount = {{ isBidTypePercent ? 0.05 : 5 }};
    var isBidTypePercent = {{ isBidTypePercent ? 'true' : 'false' }};
    var currentTempBid = {{ view.currentBid }};
    var currentSavedBid = {{ view.currentBid }};
    var initialEffectiveBid = {{ view.currentBid }};
    var myfootRole = "{{ view.loggedUser.getMyfootRole() }}";
    var canChangeBid = {{ view.canChangeBid ? 'true' : 'false' }};
    var isBidOptimizerActive = {{ view.isBidOptimizerActive ? 'true' : 'false' }};
</script>

{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'details', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<div class="bidding-page">
        {% if view.alert %}
            <p class="ui success message{{ view.alertClass ? ' ' ~ view.alertClass : '' }}">{{ view.alert }}</p>
        {% endif %}
        <div class="bid-menu full-width-row">
            <div class="row">
                <div class="col-md-12">
                    <h2 class="page-header adjust-bid-modifier">
                        Adjust Your Bid Modifier to Get More Leads
                        <a href="{{ call_static('\\Genesis_Util_Url', 'facilityUrl', [view.facility]) }}" target="_blank" class="pull-right ui basic secondary button" data-segment-category="adjust bid modifier" data-segment-label="view on sparefoot">View on SpareFoot.com <i class="external icon"></i></a>
                    </h2>
                </div>
            </div>

            <p>Your bid modifier determines what you pay for each SpareFoot customer who moves in. The price per move-in for your facility is your facility’s bid modifier times the list price of the unit reserved, if your account is manual. For accounts integrated with auto-reconciliation, this is the price of the unit when the tenant moves in. For example, if your bid modifier is 2.2 and the unit price is $50, you’d pay SpareFoot a one-time fee of $110 (2.2 x 50).<br /><br />
                Additionally, your bid factors into where your facility ranks on our site. The number of leads SpareFoot can generate for a facility strongly depends on where it ranks in our search results. We know on average facilities that rank first and second get 45% of all clicks! Adjust your bid modifier below to achieve your desired rank by zip code or city in our search results to reach your occupancy goals. <a id="show-why-change-bid" href="https://support.sparefoot.com/hc/en-us/articles/************-All-About-Bidding" data-segment-category="adjust bid modifier" data-segment-label="learn more">Learn more &raquo;</a></p>
             <!-- Added bidoptimizer active check -->        
            {% if not view.isBidOptimizerActive %}
            <h2 class="page-header">Your Current Rankings</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="panel">
                        <div class="panel-body">
                            {% if not view.facility.getActive() %}
                                <p>You currently do not appear in the search results for your city or zip because this facility is not accepting reservations.
                                    You can make this facility available for bookings on the SpareFoot network from the
                                    <a href="{{ url('features', {'action': 'details'}) }}?fid={{ view.facility.getId() }}" data-segment-category="current rankings" data-segment-label="view features page">Features page.</a></p>
                            {% else %}
                                <table class="current-rankings-table table">
                                    <tr>
                                        <td>
                                            <b>by Zip Code:</b> {{ view.facility.getLocation().getZip() }}
                                        </td>
                                        <td class="ranking">
                                            {% if not view.zipBidOppsErrorMessage %}
                                                {{ view.facilityZipBidOpps.rank }} of {{ view.facilityZipBidOpps.num_results }}
                                            {% else %}
                                                <p>{{ view.zipBidOppsErrorMessage }}</p>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ view.sparefootSearchZipUrl }}" target="_blank" class="analytics-enabled" data-segment-category="current rankings" data-segment-label="current search results - zip">
                                                View Current Search Results &raquo;
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <b>by City:</b> {{ view.facility.getLocation().getCity() }}, {{ view.facility.getLocation().getState() }}
                                        </td>
                                        <td class="ranking">
                                            {% if not view.cityBidOppsErrorMessage %}
                                                {{ view.facilityCityBidOpps.rank }} of {{ view.facilityCityBidOpps.num_results }}
                                            {% else %}
                                                <p>{{ view.cityBidOppsErrorMessage }}</p>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ view.sparefootSearchCityUrl }}" target="_blank" data-segment-category="current rankings" data-segment-label="current search results - city">View Current Search Results &raquo;</a>
                                        </td>
                                    </tr>
                                </table>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="panel">
                        <div class="panel-body">
                            <table class="current-bid-table table">
                                <tr>
                                    <td>
                                        <b>Your Current Bid:</b> <span class="current-bid">{{ view.currentBid }}</span>
                                        <span class="bid-delta">
                                        {% if view.bidDelta != 0 %}
                                            (+{{ view.bidDelta|number_format(2, '.', '') }})
                                        {% endif %}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Minimum Bid:</b> {{ view.minBid }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <p class="ui success message alert hide">Changes saved. Please allow 15 minutes for the changes to take effect.</p>
        </div>

        <div class="full-width-row">
            <div class="row header-row">
                <div class="col-md-12">
                    <h2>Update Your Bid Modifier</h2>
                    {% if view.facility %}
                        <p> Have multiple facilities? Access <a href="{{ url('features', {'action': 'bulkbid'}) }}?fid={{ view.facilityId }}">Bulk Bidding > </a></p>
                    {% endif %}
                    <p> Use the arrows to update your bid modifier by increments of .05 or enter in the bid amount directly into the box.
                        Keep in mind you can only select one bid which will affect both zip code and city rankings.</p>
                    <p class="subtext">Your Current Bid: <span class="current-bid">{{ view.currentBid }}</span></p>
                </div>
            </div>
             
            <div class="row" style="margin-bottom:20px !important;"></div>         
            <div class="row">
                <div class="col-md-3">
                    <form action="javascript:;" onsubmit="return saveBid(this)" data-segment-category="update bid modifier" data-segment-label="save bid form">
                        <div id="bid_amount">
                            <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />
                            <input type="hidden" name="update_bid_csrf_token" value="{{ view.update_bid_csrf_token }}">

                            <label for="bid_amount"><strong>Update My Bid</strong></label>
                            <div style="z-index:10;">
                                {% if view.canChangeBid %}
                                    <div class="bid-tool">
                                        <input id="bid_amount_value" name="current_temp_bid" type="number" step=".01" min="{{ view.facility.getMinBid() }}" max="{{ view.facility.getMaxBid() }}" value="{{ view.currentBid }}" data-segment-category="update bid modifier" data-segment-label="bid value input" />
                                        <div id="bid-toggle">
                                            <span id="bid-up" class="analytics-enabled" data-segment-category="update bid modifier" data-segment-label="bid up"></span>
                                            <span id="bid-down" class="analytics-enabled" data-segment-category="update bid modifier" data-segment-label="bid down"></span>
                                        </div>
                                    </div>
                                {% else %}
                                    <div id="bid-amount-display">{{ view.facility.getEffectiveBidAmount() }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div id="submit-button-container">
                            {% if view.canChangeBid %}
                                <button id="submit_button" class="ui primary button update-bid-btn" type="submit" data-segment-category="update bid modifier" data-segment-label="update my bid">Update My Bid</button>
                                <img src="/images/loading.gif" class="hide" />
                            {% else %}
                                <hr />
                                Only system administrators are able to change your bid and improve your search ranking. Please contact {{ view.adminEmails }} to make this change. Thanks!
                            {% endif %}
                        </div>
                    </form>
                </div>
                <div class="col-md-7">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <form action="javascript:;" onsubmit="return refreshBid(this)" id="update-temp-bid-form" data-segment-category="update bid modifier" data-segment-label="refresh temp bid form">
                            <input type="hidden" name="rank_bid_csrf_token" value="{{ view.rank_bid_csrf_token }}">
                                <table id="rankingsTable" class="table bid-opps-table">
                                    <tbody class="table-body">
                                        <tr class="header-row">
                                            <th></th>
                                            <th>Search Term</th>
                                            <th>Facility Rank</th>
                                        </tr>
                                        <tr>
                                            <th>Zip Code</th>
                                            <td>{{ view.facility.getLocation().getZip() }}</td>
                                            <td class="ranking" data-opptype="zip">-</td>
                                        </tr>
                                        <tr>
                                            <th>City</th>
                                            <td>{{ view.facility.getLocation().getCity() }}, {{ view.facility.getLocation().getState() }}</td>
                                            <td class="ranking" data-opptype="city">-</td>
                                        </tr>
                                        <tr>
                                            <th>Custom Zip Code</th>
                                            <td>
                                                <div class="ui action input">
                                                    <input type="text" name="custom_zip" id="custom_zip" size="10" maxlength="5" />
                                                    <button type="submit" id="custom_zip_button" class="ui secondary button" data-segment-category="update bid modifier" data-segment-label="view - custom zip">View</button>
                                                </div>
                                            </td>
                                            <td class="ranking" data-opptype="customZip">-</td>
                                        </tr>
                                        <tr>
                                            <th>
                                                Custom City
                                                <br />
                                                <span class="subtext"><i>example: Austin, TX</i></span>
                                            </th>
                                            <td>
                                                <div class="ui action input">
                                                    <input type="text" name="custom_city" id="custom_city" size="10" />
                                                    <button type="submit" id="custom_city_button" class="ui secondary button" data-segment-category="update bid modifier" data-segment-label="view - custom city">View</button>
                                                </div>
                                            </td>
                                            <td class="ranking" data-opptype="customCity">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </form>
                        </div>
                    </div>

                    {% if view.algorithms %}
                        <div>
                            <br/>
                            <select id="algorithm" name="algorithm">
                                {% for class, name in view.algorithms %}
                                    <option value="{{ class }}"{% if view.algorithm == class or (not view.algorithm and class == constant('\\Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH')) %} selected="selected"{% endif %}>{{ class == constant('\\Genesis_Entity_Search_Container::DEFAULT_STORAGE_SEARCH') ? '[DEFAULT] ' : '' }}{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
                           
        <div class="bidding-opportunities full-width-row">
            <div class="row">
                <div class="col-md-12" id="bid-by-zip">
                    <h2 class="page-header bidding-header">Bidding Opportunities</h2>
                    <p> This chart shows your bid modifier options and the rank that those bids would yield in searches by zip code or city.
                        A dash on the chart indicates that there is no bidding opportunity for that ranking position.</p>
                    {% if not view.facility.getActive() %}
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <p>You currently do not appear in the search results for your city or zip because this facility is not accepting reservations.
                                    You can make this facility available for bookings on the SpareFoot network from the
                                    <a href="{{ url('features', {'action': 'details'}) }}?fid={{ view.facility.getId() }}">Features page.</a></p>
                            </div>
                        </div>
                    {% endif %}
                    <table class="table bidding-table">
                        <tr>
                            <th class="bidding-cell" width="15%">Search Rank</th>
                            {% for i in 1..maxOpportunities %}
                                <th colspan="3" class="bidding-cell">{{ i }}</th>
                            {% endfor %}
                        </tr>

                        <tr>
                            <th class="bidding-cell" width="15%">Bid By Zip Code</th>
                            {% for i in 1..maxOpportunities %}
                                {% set hasBidOpp = not view.zipBidOppsErrorMessage and view.facilityZipBidOpps.bid_opportunities[i] and (view.facilityZipBidOpps.bid_opportunities[i]|number_format(0, '', '') <= 10) and (view.facilityZipBidOpps.bid_opportunities[i]|round(2) > 0) %}
                                <td colspan="3" class="bidding-cell">{{ hasBidOpp ? view.facilityZipBidOpps.bid_opportunities[i]|number_format(2) : '-' }}</td>
                            {% endfor %}
                        </tr>

                        <tr>
                            <th class="bidding-cell" width="15%">Bid By City</th>
                            {% for i in 1..maxOpportunities %}
                                {% set hasBidOpp = not view.cityBidOppsErrorMessage and view.facilityCityBidOpps.bid_opportunities[i] and (view.facilityCityBidOpps.bid_opportunities[i]|number_format(0, '', '') <= 10) and (view.facilityCityBidOpps.bid_opportunities[i]|round(2) > 0) %}
                                <td colspan="3" class="bidding-cell">{{ hasBidOpp ? view.facilityCityBidOpps.bid_opportunities[i]|number_format(2) : '-' }}</td>
                            {% endfor %}
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="full-width-row">
            <div class="row header-row">
                <div class="col-md-12">
                    <h2>Export Data</h2>
                    <p>Download detailed bidding opportunities for your facilities by zip code or city.
                        Get access to even more data about your listing performance, average market prices on SpareFoot and how your facility compares to help you make informed bidding decisions.</p>
                </div>
            </div>

            <div class="export-row">
                <div class="export-zip-opps">
                    <div class="export-button-wrapper">
                        <button id="download-zip-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('zip');" data-segment-category="export data" data-segment-label="export by zip">Export by Zip</button>
                    </div>
                    <div class="messaging">
                        <div class="progress zip">
                            <div id="zip_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="download-link"></span>
                        <span class="error-message"></span>
                    </div>
                    <div class="spacer"></div>
                </div>
            </div>
            <div class="export-row">
                <div class="export-city-opps">
                    <div class="export-button-wrapper">
                        <button id="download-city-opp-btn" class="ui secondary button download-opp-btn" onclick="return downloadBidOpportunities('city');" data-segment-category="export data" data-segment-label="export by zip">Export by City</button>
                    </div>
                    <div class="messaging">
                        <div class="progress city">
                            <div id="city_progress" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="download-link"></span>
                        <span class="error-message"></span>
                    </div>
                    <div class="spacer"></div>
                </div>
            </div>
        </div>
        {% endif %} 
</div>
