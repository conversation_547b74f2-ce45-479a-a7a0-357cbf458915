<div class="setup-content-container">
    <div class="setup-content">
        <div class="content-row">
            <img src="/images/centershift.png" style="float:right"/>
            <h2>Centershift 4.0 Integration Setup</h2>
            <p>Automatically import your facilities and units into SpareFoot.</p>
            <p>SpareFoot will sync with Centershift once per hour to make sure we are showing the most current list available units for your properties.</p>
        </div>

        <div class="input-row string">
            <h4>Step 1</h4>
            <p class="description">Setup a username for SpareFoot in your Centershift account. <a href="http://facilities.sparefoot.com/Centershift4Integration.pdf" target="_blank">Click here for instructions</a>.</p>
        </div>

        <div class="input-row string">
            <h4>Step 2</h4>
            <p class="description">Enter the username and password you just created in the form below and click "sync".</p>
            <br />

            <div class="form-horizontal">

                <div class="form-group">
                    <label for="username" class="col-md-2 control-label">Username</label>
                    <div class="col-md-10">
                        <input id="username" name="username" type="text" class="form-control" />
                    </div>
                </div>

                <div class="form-group">
                    <label for="pin" class="col-md-2 control-label">PIN</label>
                    <div class="col-md-10">
                        <input id="pin" name="pin" type="text" class="form-control" />
                    </div>
                </div>

            </div>

        </div>

        <div class="input-row string">
            <h4>Step 3</h4>
            <p class="description">Which Centershift data center do you use?</p>
            <select id="datacenter" class="form-control">
                <option value="slc" selected>Salt Lake City</option>
                <option value="den">CS10 (Formerly Denver)</option>
                <option value="slc">I don't know</option>
            </select>
        </div>

        <div class="input-row string">
            <h4>Step 4</h4>
            <p class="description">Which Centershift API channel would you like to use?</p>
            <input type="text" name="channel" id="channel" value="3" class="form-control" />
        </div>

        <div class="input-row string">
            <h4>Step 5</h4>
            <p class="description">Agree to the Centershift Terms of Use</p>
            <br />

            <div class="form-horizontal">
                <div class="form-group">
                    <label for="termsofuse" class="col-md-2 control-label">Terms of Use</label>
                    <div class="col-md-10">
                        <textarea id="termsofuse" rows="10" readonly="readonly" class="form-control">Data Access Agreement

Recitals

    A.    Centershift provides web-based rental management and point-of-sale software (called "StoreSM") and services related thereto (collectively referred to as the "Service").

    B.    Centershift and ____ (the "Client") have entered into an agreement relating to the license of StoreSM by the Client and providing the Service to the Client.

    C.    Centershift offers to its clients, integration and data service tools via XML Web Services that allow the client or third parties on behalf of the client, to query and/or update real-time data for use in external systems.

    D.    The Client has authorized the User to access the data relating to the Client at the level indicated below and the User desires to access such data.

    E.    Centershift is willing to allow the User access to the data on the terms and conditions set forth below.

Agreement

    NOW, THEREFORE, based on the stated premises, which are incorporated herein by reference, and for and in consideration of the mutual covenants and agreements hereinafter set forth and the mutual benefit to the parties to be derived herefrom, it is hereby agreed as follows:

    1.    Level of Use. The User shall have access ("Access Services") to data relating to the Client at the level indicated below, which level has been approved by the Client:



    Type IV. Bi-directional access to any data relating to the Client offered through Centershift web services, including sensitive account and/or payment information.  Also gives permission to create new data such as accounts, reservations (with payment/deposits), take payments on existing accounts, etc.



    2.    Available Data. Depending on the level of access for which the User shall be authorized, Centershift will provide Access Services to the following types of data relating to the Client:

-    Account details, including names, address and phone number information, and additional potentially sensitive details for the purpose of account collection.

-    Payment information, including amounts due, late fees or other fees, credit card or other payment details for payment on account, etc.

-    Rental information regarding units rented, price, status, etc.

-    Generic facility information, including hours of operation, location, unit information (sizes, occupancy rates, street/rack rates, actual rates, etc.), and promotions.

-    Rental histories.

-    Transaction histories.

-    Account notes, either from Centershift to the User, or from the User to be recorded in StoreSM.

-    Other data to be determined in the future.

3.    Terms of Use. The User agrees to utilize the Access Services in conformity with the terms and conditions of use that Centershift shall develop from time to time and make applicable to all users of the Access Services.

4.    Centershift Proprietary Rights.  The Service is protected by copyright laws and international copyright treaties, patent laws and other intellectual property laws and treaties.  Centershift, the Centershift logo, Store, the Store logo, Centershift.net, Centershift.com, and other trade names and logos used in connection with the Service, are trademarks and/or service marks of Centershift, Inc. or one of its affiliates.  The User acknowledges and agrees that, except as otherwise provided herein, this Agreement and use of the Access Service does not grant any right, title or interest in and to any patents, copyrights, trade secrets, trademarks, service marks, or other property rights or rights of ownership in the Service, or any part thereof in whatever form, and acknowledges that the Service contains proprietary and confidential information of Centershift, which is intellectual property belonging solely to Centershift and is protected by law.  All rights not expressly granted by Centershift hereunder are reserved by Centershift.

5.    Confidential Information.  The User acknowledges that it may have access to certain confidential information of Centershift and/or the Client concerning their respective business, plans, customers, technologies, products, services and other information held in confidence by such party ("Confidential Information").  Confidential Information includes all information, in tangible or intangible form, that is marked or designated as confidential or that, under the circumstances of its disclosure, should be considered Confidential Information, including, without limitation, any information disclosed by Centershift about the technologies, methodologies, equipment, software or processes used by Centershift in connection with the Service, and/or any confidential information disclosed by the Client, including customer data, business rules or methodologies ("Customer Provided Content").

6.    Nondisclosure of Confidential Information.  The User agrees to keep confidential the Confidential Information of Centershift and the Client.  The User agrees that it will use the Confidential Information solely for the purposes of this Agreement and will not use the Confidential Information for its own account or benefit or for the account or benefit of any third party, except as expressly permitted by or required to achieve the purposes of this Agreement, nor to disclose to any third party (except as required by law or to that party's attorneys, accountants or other advisors as reasonably necessary) any of the Confidential Information and will take reasonable precautions to protect the confidentiality of such information, which precautions shall be at least as stringent as such party takes to protect its own Confidential Information. Notwithstanding the foregoing, the User shall not have any obligation with respect to information which (a) is publicly known by the User at the time of disclosure or becomes publicly know through no fault of the User, (b) is in the User's possession prior to disclosure by the disclosing party, as evidenced by written records, (c) is hereafter received from a third party as a matter of right and without breach of any nondisclosure restrictions, (d) is furnished to any third party by the disclosing party without similar nondisclosure restrictions on such third party, (e) is approved for release in writing by the disclosing party, or (f) is disclosed pursuant to a law or a governmental or judicial order, provided, however that, prior to any such disclosure, the User immediately notifies the disclosing party so that the disclosing party may seek a protective order or take other protective action.

7.    Unauthorized Access and Improper Uses.  The User shall not utilize any portion of the Access Service or any of the applications, databases, computer systems or other resources that relate to this Agreement to gain unauthorized access to any other computer programs, databases, computer system, Confidential Information or other property of Centershift, any customer of Centershift or any other third party or for any improper purpose including, without limitation, to interfere with or disrupt another computer system or its use, to alter or delete any data or computer programs, to propagate viruses, Trojan horses, time bombs or other harmful computer code, to engage in any unlawful or immoral act, or to assist or encourage any other person in doing any of the foregoing.  In addition, the User shall not modify, reverse engineer, reverse compile, or disassemble Centershift's computer software or any other proprietary technology provided by Centershift.  The User shall not download and/or save a copy of any of the screens appearing in the Access Service for any purpose, except as otherwise provided in this Agreement. A breach of this Section shall give Centershift the right to terminate the User's access to the Access Service and/or this Agreement immediately, notwithstanding anything to the contrary and without obligation of any kind to the User, in addition to all remedies available to Centershift in law or in equity.

8. Security of Client Customer and Payment Data. The User acknowledges and agrees to use reasonable and prudent security measures to protect the clients customer data, including customers' contact information, social security number, drivers license, etc. If the User is involved in processing payments for the Client in any way, the User acknowledges and agrees to use Payment Application Best Practices (PABP) in conformance with Payment Application Data Security Standards (PA-DSS) that can be found at <a href="http://www.pcisecuritystandards.org">www.pcisecuritystandards.org</a> -to protect the clients customer payment information including but not limited to credit card number, CVV2 number, debit card number with PIN and checking account number. The User also acknowledges and agrees to mitigate compromises, prevent storage of sensitive cardholder data (i.e. full magnetic stripe data, CVV2 or PIN data) and support compliance with PCI Data Security Standard (PCI-DSS). The User further acknowledges and agrees that Centershift and its affiliates are not responsible or liable, directly or indirectly, for any damage or loss caused or alleged to be caused by or in connection to processing payments by the User.

9.     Equitable Remedies. The rights which are the subject matter of this Agreement, as set forth hereinabove, are of a special, unique, extraordinary and intellectual character which gives them a peculiar value, the loss of which cannot be reasonably or adequately compensated in damages in an action at law and which would cause Centershift great irreparable injury and damage. Accordingly, Centershift shall be entitled to injunctive relief, specific performance and other equitable relief to preserve its rights and interest in and to such rights as are set forth herein. This provision shall not, however, be construed as a waiver of any rights Centershift may have for damages or otherwise arising from any breach of this Agreement.

10.    Survival of Proprietary Rights and Confidential Obligations.  The provisions of Sections 4 through 8 inclusive shall survive termination of this Agreement.

11.    Termination. Upon the occurrence of a breach of a material provision of this Agreement, the nonbreaching party shall have the right to terminate this Agreement by giving 30 days written notice of termination to the breaching party specifying the breach for which termination is noticed and the date (not less than 30 days distant) on which termination shall be effective; provided, however, that if the breaching party shall cure the specified default(s) within 20 days of said notice, such notice shall be of no force or effect. In addition, this Agreement shall terminate upon the termination of the Service Agreement between Centershift and the Client.

    11.    Expenses.  Each party hereto will pay all costs and expenses incident to its negotiation and preparation of this Agreement and to its performance and compliance with all agreements and conditions contained herein on its part to be performed or complied with, including the fees, expenses and disbursements of its counsel and accountants.

    12.    Survival of Warranties.  Unless otherwise set forth in this Agreement, the warranties, representations and covenants of the parties contained in or made pursuant to this Agreement shall survive the execution and delivery of this Agreement and the Closing.

    13.    Notices.  All notices, demands, requests, or other communications required or authorized hereunder shall be deemed given sufficiently if in writing and if personally delivered; if sent by facsimile transmission, confirmed with a written copy thereof sent by overnight express delivery; if sent by registered mail or certified mail, return receipt requested and postage prepaid; or if sent by overnight express delivery, to the addresses or facsimile numbers set forth on the signature page hereto, or such other addresses and facsimile numbers as shall be furnished in writing by any party in the manner for giving notices hereunder, and any such notice, demand, request, or other communication shall be deemed to have been given as of the date so delivered or sent by facsimile transmission, three days after the date so mailed, or one day after the date so sent by overnight delivery.

    14.    Attorneys' Fees.  In the event that any party institutes any action or suit to enforce this Agreement or to secure relief from any default hereunder or breach hereof, the breaching party or parties shall reimburse the nonbreaching party or parties for all costs, including reasonable attorneys' fees, incurred in connection therewith and in enforcing or collecting any judgment rendered therein.

    15.    Entire Agreement.  This Agreement, together with the documents to be delivered pursuant hereto, represent the entire agreement between the parties relating to the subject matter hereof.  There are no other courses of dealing, understanding, agreements, representations, or warranties, written or oral, except as set forth herein.

    16.    Successors and Assigns.  The terms and conditions of this Agreement shall inure to the benefit of and be binding upon the respective successors and assigns of the parties. Nothing in this Agreement, express or implied, is intended to confer upon any party other than the parties hereto or their respective successors and assigns any rights, remedies, obligations, or liabilities under or by reason of this Agreement, except as expressly provided in this Agreement.

    17.    Governing Law.  This Agreement shall be governed by, enforced, and construed under and in accordance with the laws of the United States of America, and, with respect to other matters of state law, the laws of the state of Utah.

    18.    Amendment or Waiver.  Every right and remedy provided herein shall be cumulative with every other right and remedy, whether conferred herein, at law, or in equity, and may be enforced concurrently herewith, and no waiver by any party of the performance of any obligation by the other shall be construed as a waiver of the same or any other default then, theretofore, or thereafter occurring or existing.  This Agreement may be amended only by a writing signed by all parties hereto, with respect to any of the terms contained herein, and any term or condition of this Agreement may be waived or the time for performance thereof may be extended by a writing signed by the party or parties for whose benefit the provision is intended.

    19.    Severability.  If one or more provisions of this Agreement are held to be unenforceable under applicable law, the parties agree to renegotiate such provision in good faith. In the event that the parties cannot reach a mutually agreeable and enforceable replacement for such provision, then (a) such provision shall be excluded from this Agreement, (b) the balance of the Agreement shall be interpreted as if such provision were so excluded and (c) the balance of the Agreement shall be enforceable in accordance with its terms.

    20.    Counterparts.  This Agreement may be executed in multiple counterparts, each of which shall be deemed an original and all of which taken together shall be but a single instrument.

Consent by Client

Recitals

    A.    The Client has entered into a Service Agreement with Centershift, Inc., relating to the license by the Client of certain software and provision by Centershift of certain services related thereto.

    B.    Centershift offers to its clients, integration and data service tools via XML Web Services that allow the client or third parties on behalf of the client, to query and/or update real-time data for use in external systems.

    C.    The Client desires to authorize the User(s) set forth below to access the data relating to the Client at the level indicated below.

    NOW, THEREFORE, based on the stated premises, the Client hereby agrees as follows:

    1.    Level of Use. The Client hereby authorizes SpareFoot, Inc.  (the "User") to have access ("Access Services") to data relating to the Client at the level indicated below:

    &middot;    Type IV. Bi-directional access to any data relating to the Client offered through Centershift web services, including sensitive account and/or payment information.  Also gives permission to create new data such as accounts, reservations (with payment/deposits), take payments on existing accounts, etc.

    2.    The authorization for access to data by the User(s) identified above shall apply to the data respecting the facilities described below.

    &middot;    a.  All of the facilities owned or operated by the Client that utilize Centershift StoreSM.

    3.    Release.  The Client, acting on behalf of itself and its future, present, and former affiliates, fully and irrevocably acquits and forever discharges and releases Centershift, together with all of the future, present, and former agents, employees, successors, assigns, and representatives of Centershift, each of whom might be liable or who might be claimed to be liable for any claims, demands, debts, contracts, actions, or causes of action of any kind or nature whatsoever, arising from, out of, or in any way connected with the access by the User to the Client's data or the use by the User of such data. The Client further agrees to indemnify and hold harmless Centershift from and against any and all loss, cost and expense incurred by Centershift relating thereto.

    By providing a digital signature and date to this document, Client agrees to all terms and conditions set forth in this agreement.</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-10 col-md-offset-2 checkbox">
                        <label><input type="checkbox" name="agreement" id="agreement" /> I agree to the Centershift Terms of Use</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-row">
            <h4>Step 6</h4>
            <p class="description">By typing your full name in this box, you are digitally signing this agreement.</p>
            <br />
            <div class="form-horizontal">
                <div class="form-group">
                    <label for="signature" class="col-md-2 control-label">Your Full Name</label>
                    <div class="col-md-10 controls">
                        <input id="signature" name="signature" size="30" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-actions">
                    <div class="col-md-offset-2">
                    	<input type="checkbox" style="display: none;" id="syncInactive" name="syncInactive" value="1"/>
                        <input id="sync" class="btn btn-large btn-success" name="sync" type="submit" value="Sync" data-loading-text="Syncing" data-complete-text="Sync Complete" />&nbsp;&nbsp;<img src="/images/loaders/large.gif" class="loading hide" />
                        <p class="loading hide"><br />We're connecting to the Centershift servers now. This could take a minute.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-footer">
            <form method="post" action="{{ path('features', {'action': 'addsummary'}) }}" class="pull-right">
                <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value=""/>
                <input type="hidden" id="csrf_token" name="csrf_token" value="{{ view.cs4_csrf_token }}" />
                <input id="next" class="btn btn-lg btn-primary" name="next" type="submit" value="Next" disabled onClick="nextLoader();"/>
            </form>
            <a id="back_btn" class="btn btn-default btn-lg" href="{{ path('features', {'action': 'type'}) }}">Back</a>
        </div>

    </div>
</div>