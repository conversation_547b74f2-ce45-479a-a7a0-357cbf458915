{{ include('facility/header.html.twig', {'facility': view.facility}) }}

<div class="ui positive message bid-optimizer-hidden" id="bid-optimizer-success-message">
    <i class="green close icon" id="bid-optimizer-success-close-icon"></i>
    <div class="bid-optimizer-success-header">
        <i class="green check icon"></i>
        <p>Your bidding changes were successfully updated.</p>
    </div>
</div>

<script>
    const bidSummaryData = {{ view.bidSummaryData|json_encode|raw }};
</script>

<div class="bid-optimizer-page">
    <div class="page-header header-spaced">
        <h1 class="mysf-select">Bidding Strategies</h1>
        <div class="bid-optimizer-option">
            <div class="input-group">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-search"></span>
                </span>
                <input type="text" class="form-control" id="search-input" placeholder="Search by facility name of ID">
            </div>
            <div class="ui dropdown bulk-drop-menu" id="bulk-drop-menu">
                <div class="text">BULK BIDDING</div>
                <i class="dropdown icon"></i>
                <div class="menu">
                    <div class="item">Export Bid Opportunities</div>
                    <div class="item">Upload Bulk Bids</div>
                </div>
            </div>
            <button
                type="button"
                class="btn btn-primary"
                id="manage-bids-btn"
                data-mode="view">
                MANAGE BIDS
            </button>
            <button
                type="button"
                class="btn btn-default bid-optimizer-hidden"
                id="manage-cancel-bids-btn">
                CANCEL
            </button>
        </div>
    </div>

    <div class="page-header header-spaced bulk-bid-head bid-optimizer-hidden">
        <div class="bid-optimizer-bulk-outer">
            <div class="bid-optimizer-bulk-selection">
                <h3 class="header">
                    Bulk Edit Max Bid
                </h3>
                <div class="selected-facilities-container">
                    <p class="selected-facilities-text">
                        Selected Facilities:
                    </p>
                </div>
                <div class="input-form-group">
                    <label class="label">MAX BID</label>
                    <div class="bulk-max-bid-input-container">
                        <input
                            type="number"
                            class="max-bid-input bulk-max-bid-input"
                            value=""
                            step="0.05"
                            min="0.05"
                            max="10" />
                            <span class="error-message error bid-optimizer-hidden" id="bulk-bid-error">0.05 increments required</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-container">
        {% if view.bidSummaryData|length > 0 %}
            <table class="table sortable">
                <thead>
                    <tr class="header-row">
                        <th class="theader first-col with-chevron">Facility Name</th>
                        <th class="theader" id="max-bid-header">
                            <div class="header-content">
                                <span>Max Bid</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        The maximum value you’re willing to bid.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Enhanced Bid</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        The best bid value calculated within the max bid range to achieve the highest possible rank
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>City & Zip Rank</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        Your facility’s current rank based on the optimized bid value
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>City Bid for Rank 1</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        City Bid for Rank 1 is the amount the facility’s max bid needs to be set at to to achieve a city search rank of 1.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>ZIP Bid for Rank 1</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        ZIP Bid for Rank 1 is the amount the facility’s max bid needs to be set at to to achieve a ZIP search rank of 1.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Length of Stay</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        The average length of unit being occupied at the facility.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Occupancy</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        Facility’s current occupancy rate (across all units).
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Submit Rate</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        Percentage of visitors viewing your facility on Sparefoot to actual reservations (Last 90 days).
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Move-in Rate</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        Reservation to move in percentage (Last 90 days).
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Min Bid</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        The minimum bid value defined for your facility.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Facility ID</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        This is the ID of the storage facility. It can be used to update your max bid when using the bulk bidding options.
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="theader">
                            <div class="header-content">
                                <span>Bid Opportunities</span>
                                <div class="tooltip-wrapper">
                                    <img src="/images/information.png" class="tooltip-icon" width="16" />
                                    <div class="tooltip-box">
                                        View of bids required to achieve desired ranks at city & zip level.
                                    </div>
                                </div>
                            </div>
                        </th>
                    </tr>
                </thead>
            </thead>
            <tbody>
                {% for data in view.bidSummaryData %}
                    <tr class="body-row">
                        <td class="tcell first-col">
                            <a class="facility-url" href="{{ url('dashboard') }}?fid={{ data.facilityId }}">
                                {{ data.name }}
                            </a>
                            <br>
                            {{ data.city }}, {{ data.state }} - {{ data.zip }}
                        </td>
                        <td class="tcell max-bid-cell" data-facility-id="{{ data.facilityId }}">
                            <span class="max-bid-static">{{ data.maxBid|number_format(2) }}</span>
                            <div class="max-bid-input-container bid-optimizer-hidden">
                                <input
                                    type="number"
                                    class="max-bid-input"
                                    value="{{ data.maxBid|number_format(2) }}"
                                    step="0.05"
                                    min="0.05"
                                    max="10" />
                                <span class="error-message error bid-optimizer-hidden" id="error">
                                    Must be greater than or equal to 0.05
                                </span>
                            </div>
                        </td>

                        <td class="tcell">
                            {{ data.optimizedBid|number_format(2) }}
                            <br>
                            <span class="optimized-bid">{{ data.optimizedBidDelta|number_format(2) }}</span>
                        </td>
                        <td class="tcell">
                            {{ data.zipRank }} - Zip
                            <br>
                            {{ data.cityRank }} - City
                        </td>
                        <td class="tcell">
                            {{ data.cityBids[0].bid|number_format(2) }}
                            <br>
                            {% if data.cityRank1BidDelta > 0 %}
                                <span class="plus-bid">+{{ data.cityRank1BidDelta|number_format(2) }}</span>
                            {% else %}
                                <span class="minus-bid">{{ data.cityRank1BidDelta|number_format(2) }}</span>
                            {% endif %}
                        </td>
                        <td class="tcell">
                            {{ data.zipBids[0].bid|number_format(2) }}
                            <br>
                            {% if data.zipRank1BidDelta > 0 %}
                                <span class="plus-bid">+{{ data.zipRank1BidDelta|number_format(2) }}</span>
                            {% else %}
                                <span class="minus-bid">{{ data.zipRank1BidDelta|number_format(2) }}</span>
                            {% endif %}
                        </td>
                        <td class="tcell">{{ (data.lengthOfStay ?? 0) > 0 ? ((data.lengthOfStay ?? 0) / 30)|number_format(1) : '' }}</td>
                        <td class="tcell">{{ data.facilityPhysicalOccupancy|number_format(2) }}%</td>
                        <td class="tcell">{{ (data.submitRate|number_format(2) * 100) }}%</td>
                        <td class="tcell">{{ (data.moveInRate|number_format(2) * 100) }}%</td>
                        <td class="tcell">{{ data.minBid|number_format(2) }}</td>
                        <td class="tcell row-facility-id">{{ data.facilityId }}</td>
                              
                        <td class="tcell">
                            <button class="btn view-btn" data-facility-id="{{ data.facilityId }}" data-facility-name="{{ data.name }}" data-max-bid="{{ data.max_bid }}">VIEW</button>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% elseif view.errorMessage|length > 0 %}
            <div class="ui error message" id="bid-alert-message" style="margin:10px;">
                <i class="info circle icon large red"></i>
                <span id="view-bid-alert-text" id="view-bid-alert-text">There was a problem loading your facilities.</span>

            </div>
        {% endif %}
    </div>
</div>

<div id="manage-cancel-bid-modal" class="ui small modal">
    <div class="header">
        <button id="cancel-close-icon" type="button" class="close" data-dismiss="modal">&times;</button>
        <div class="cancel-changes">Cancel Changes?</div>
    </div>
    <div class="content">
        <p>Are you sure you want to cancel the changes you have made to your bidding strategy?</p>
    </div>
    <div class="actions">
        <button class="btn btn-default" id="cancel-changes">CANCEL CHANGES</button>
        <button class="btn btn-primary" id="review-cancel-changes">REVIEW CHANGES</button>
    </div>
</div>

<div id="manage-apply-bid-modal" class="ui small modal">
    <div class="header">
        <button id="apply-close-icon" type="button" class="close" data-dismiss="modal" onClick="closeApplyModal()">&times;</button>
        <div class="apply-changes">Confirm Changes</div>
    </div>
    <div class="content">
        <p>Are you sure you want to update your bidding strategies?</p>
        <div class="ui message info">
            <i class="primaryColor large info circle icon"></i>
            <span id="info-alert-text" class="alert-text">Bidding strategy updates are calculated every 15-20 minutes. Ensure to check your changes after 15-20 minutes to ensure you are meeting your desired results.</span>
        </div>
        <div class="ui message error bid-optimizer-hidden" id="apply-alert-message">
            <img class="bulk-bid-info-icon" id="apply-alert-icon" src="/images/error-icon.png" />
            <span id="apply-alert-text" class="alert-text">Something went wrong</span>
        </div>
    </div>
    <div class="actions">
        <button class="btn btn-default" id="cancel-apply-changes" onClick="closeApplyModal()">CANCEL</button>
        <button class="btn btn-primary" id="review-apply-changes" onClick="submitApplyBids(event, '{{ view.facilityId }}')">
            <img class="loading-spinner bid-optimizer-hidden" id="apply-loading-spinner" src="/images/loading.gif" width="15" height="15" />
            <span id="apply-text">SAVE BID STRATEGY</span>
        </button>
    </div>
</div>

<div id="export-bid-modal" class="ui small modal">
    <div class="header">
        <i class="close icon"></i>
        <div>Export Bid Opportunities</div>
    </div>
    <div class="content">
        <p>Bid opportunity exports allow you to quickly and easily change bids across multiple facilities at once. <a href="https://support.sparefoot.com/hc/en-us/articles/360053658393-Bulk-Bidding-OneTime-Pricing" target="_blank">Learn More</a></p>
        <h3>Step 1</h3>
        <p>Either export your bid opportunity file below OR create your own .csv with your facility IDs.</p>
        <h3>Step 2</h3>
        <p>Fill out your file with your desired bid rates. <a href="https://support.sparefoot.com/hc/en-us/articles/360053658393-Bulk-Bidding-OneTime-Pricing" target="_blank">Learn More</a></p>
        <h3>Step 3</h3>
        <p>Upload your file via the bulk bidding dropdown on the bidding page to update your bid modifier in bulk</p>

        <div id="error-bibs-export-banner" class="ui error message" style="display: none;">
            <p>There was a problem loading your facilities.</p>
        </div>

        <button onclick="exportBidsFile('zip')" class="modal-export-buttons">
            Export by Zip Code <img id="loading-zip" src="/images/loaders/spinner.gif" style="display:none; width: 16px; height: 16px;" />
        </button>

        <button onclick="exportBidsFile('city')" class="modal-export-buttons">
            Export by City <img id="loading-city" src="/images/loaders/spinner.gif" style="display:none; width: 16px; height: 16px;" />
        </button>

    </div>
    <div class="actions">
        <button class="ui grey basic button">Close</button>
    </div>
</div>


<div id="viewModal" class="ui modal">
    <div class="header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <div id="facilityName" class="facility-name">Facility Name</div>
    </div>
    <div class="content">
        <p class="title">Use the bid amount to underneath a desired rank position to rank in or near that rank position for a given search.</p>
        <div class="table-body">
            <div class="table-container">
                <table class="table sortable">
                    <thead>
                        <tr class="header-row">
                            <th class="theader first-col with-chevron">Search</th>
                            <th class="theader">Bid for Rank 1</th>
                            <th class="theader">Bid for Rank 2</th>
                            <th class="theader">Bid for Rank 3</th>
                            <th class="theader">Bid for Rank 4</th>
                            <th class="theader">Bid for Rank 5</th>
                            <th class="theader">Bid for Rank 6</th>
                            <th class="theader">Bid for Rank 7</th>
                            <th class="theader">Bid for Rank 8</th>
                            <th class="theader">Bid for Rank 9</th>
                            <th class="theader">Bid for Rank 10</th>
                            <th class="theader">Bid for Rank 11</th>
                            <th class="theader">Bid for Rank 12</th>
                            <th class="theader">Bid for Rank 13</th>
                            <th class="theader">Bid for Rank 14</th>
                            <th class="theader">Bid for Rank 15</th>
                            <th class="theader">Bid for Rank 16</th>
                            <th class="theader">Bid for Rank 17</th>
                            <th class="theader">Bid for Rank 18</th>
                            <th class="theader">Bid for Rank 19</th>
                            <th class="theader">Bid for Rank 20</th>
                            <th class="theader">Bid for Rank 21</th>
                            <th class="theader">Bid for Rank 22</th>
                            <th class="theader">Bid for Rank 23</th>
                            <th class="theader">Bid for Rank 24</th>
                            <th class="theader">Bid for Rank 25</th>
                            <th class="theader">Bid for Rank 26</th>
                            <th class="theader">Bid for Rank 27</th>
                            <th class="theader">Bid for Rank 28</th>
                            <th class="theader">Bid for Rank 29</th>
                            <th class="theader">Bid for Rank 30</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="actions">
        <div class="ui button">CLOSE</div>
    </div>
</div>

<div id="upload-bid-modal" class="modal" role="dialog" style="align-content:center;">
    <div class="modal-dialog modal-dialog-centered">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" style="height:60px">
                <button type="button" class="close" data-dismiss="modal" onClick="closeModal()">&times;</button>
                <h2 class="modal-title pull-left">Upload Bulk Bids</h2>
            </div>
            <div class="modal-body">
                <p class="bid-optimizer-modal-p-content">Upload a file name 'bids.csv' with 'facility_id' as the header for the first column, and 'max_bid' in the second column</p>
                <div class="bid-optimizer-bulkbid_upload" id="drop-area" ondragstart="return false;" ondrop="return false;">
                    <form id="bulkbid-form" method="post" enctype="multipart/form-data"
                        action="{{ url('features', {'action': 'bidoptimizer-bidupdate'}) }}?fid={{ view.facilityId }}"
                        class="ui form segment">
                        <label for="file-upload" class="bid-optimizer-custom-file-upload btn" id="bid-optimizer-custom-file-upload">
                            <i class="fa fa-cloud-upload"></i> DRAG / SELECT FILE TO USE
                        </label>
                        <input id="file-upload" type="file" name="bids" accept="text/csv" onchange="updateFileName(this);" onClick="resetVal(this)">
                        <span id="file-support-text" class="file-support-text"> File type must be .csv </span>
                        <span id="preview-area" class="preview-area"></span>
                </div>
                <div class="ui message error" id="alert-message">
                    <img class="bulk-bid-info-icon" src="/images/error-icon.png" />
                    <span id="alert-text" class="alert-text">Something went wrong</span>
                </div>
                <hr/>
                <div class="bid-optimizer-bulk_bid_example" id="bulk-bid-example">
                    <div class="ui horizontal list" id="bulk-bid-example-header">
                        <div class="item">
                            <span>CSV FILE TEMPLATE</span>
                        </div>
                        <div class="item">
                            <button id="download-template" class="btn">
                                <i class="fa fa-cloud-download"></i> DOWNLOAD CSV TEMPLATE
                            </button>
                        </div>
                    </div>
                    <img class="bulk-bid-example-image_bidopt" src="/images/bids-file-example.png" />
                    <p class="text-left text-small">&nbsp;Note: This image is for illustrative purposes only & doesn't reflect your actual data.</p>
                </div> 
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" onClick="closeModal()" id="close-button">CLOSE</button>
                <button type="button" id="bulkbid-form-submit" class="btn btn-primary download-opp-btn" onClick="submitBid(event)">
                    <img class="loading-spinner" id="loading-spinner" src="/images/loading.gif" width="15" height="15" style="display:none" />
                    <span id="upload-text">Upload</span>
                </button>
            </div>

            </form>
        </div>

    </div>
</div>

