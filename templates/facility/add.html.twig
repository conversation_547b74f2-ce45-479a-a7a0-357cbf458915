<form id="add-facility-form" method="post" action="{{ path('features', {'action': 'add_facility'}) }}">
    <div class="setup-content-container">
        <div class="setup-content">
            <div class="content-row">
                <h1>Add a Facility</h1>
                <p>Add a new facility here, then add units to the facility. Those units will appear on SpareFoot.com. You can always hide your facility or its units later on. (Note: If you use SiteLink, Centershift, or QuikStor, please click back and choose the appropriate option on the prior page.)</p>
            </div>
            <div class="content-row">
                <div class="form-horizontal">

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="name">Facility Name</label>
                        <div class="col-md-10">
                            <input size="50" type="text" id="name" name="name" tabindex="1" class="form-control" value="{{ view.facName }}"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="phone">Phone</label>
                        <div class="col-md-10">
                            <input size="12" type="tel" value="{{ view.facPhone }}" id="phone" name="phone" tabindex="5" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="address">Address</label>
                        <div class="col-md-10">
                            <input size="30" type="text" value="{{ view.facAddress }}" id="address" name="address" tabindex="6" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="city">City</label>
                        <div class="col-md-10">
                            <input size="15" type="text" value="{{ view.facCity }}" id="city" name="city" tabindex="7" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="state">State</label>
                        <div class="col-md-10">
                            {% set states = [
                                'AK', 'AL', 'AR', 'AZ', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL',
                                'GA', 'HI', 'IA', 'ID', 'IL', 'IN', 'KS', 'KY', 'LA', 'MA',
                                'MD', 'ME', 'MI', 'MN', 'MO', 'MS', 'MT', 'NC', 'ND', 'NE',
                                'NH', 'NJ', 'NM', 'NV', 'NY', 'OH', 'OK', 'OR', 'PA', 'RI',
                                'SC', 'SD', 'TN', 'TX', 'UT', 'VA', 'VT', 'WA', 'WI', 'WV', 'WY'
                            ] %}
                            <select name="state" id="state" tabindex="8" class="form-control">
                                <option value=""></option>
                                {% for state in states %}
                                    <option value="{{ state }}"{{ view.facState == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label" for="zip">Zip</label>
                        <div class="col-md-10">
                            <input size="15" type="text" value="{{ view.facZip }}" id="zip" name="zip" tabindex="9" class="form-control" />
                        </div>
                    </div>

                    <hr />

                    <h3>Facility Contacts</h3>
                    <p>You can edit your users list later.</p>
                    <p><strong>Who can access this facility in MySpareFoot?</strong><br/>{{ view.accessemails }}</p>

                    {% if not view.update %}
                    <div class="form-group">
                        <label class="col-md-2 control-label" for="reservation-emails">Reservation Email(s)</label>
                        <div class="col-md-10">
                            <input type="text" name="reservation_emails" id="reservation-emails" value="" class="form-control" />
                            <p class="help-block">Where to send reservation confirmation for new tenants. (Comma separate for multiple)<br/>
                            These people already receive emails for all facilities: {{ view.reservationemails }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <div class="form-actions">
                        <div class="right">
                            <input type="hidden" name="csrf_token" value="{{ view.csrf_token }}">
                            <input type="hidden" name="update" id="update" value="{{ view.update }}" />
                            <a id="cancel" href="{{ path('features', {'action': 'type'}) }}" class="ui basic large button" />Cancel</a>&nbsp;&nbsp;<img src="/images/loaders/small.gif" class="hide" />
                            <input id="add-facility-button" class="ui primary large button" name="commit" type="submit" value="{{ view.update ? 'Update Facility' : 'Add Facility' }}" data-loading-text="Saving" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
