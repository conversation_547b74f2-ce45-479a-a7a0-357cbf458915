<div id="unit-modal" class="modal" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <form id="unit-form" class="form-horizontal">
                    <p class="alert alert-danger hide"></p>

                    <div class="form-group" id="trtype">
                        <label for="unit-type" class="col-md-3 control-label">Space Type</label>
                        <div class="col-md-9">
                            <select id="unit-type" name="unit_type" class="form-control">
                              <option value="14">Self-Storage Unit</option>
                              <option value="4">Parking Space</option>
                              <option value="10">Office Space / Warehouse</option>
                              <option value="15">Wine Storage</option>
                              <option value="13">Locker</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group" id="trdimensions">
                        <label class="col-md-3 control-label">Dimensions</label>

                        <p class="col-md-3">
                            <span class="input-group">
                                <input type="number" min="0" class="form-control" name="unit_width" id="unit-width" placeholder="Width" /><span class="input-group-addon">ft</span>
                            </span>
                        </p>

                        <p class="col-md-3">
                            <span class="input-group">
                                <input type="number"  min="0"class="form-control" name="unit_length" id="unit-length" placeholder="Length" /><span class="input-group-addon">ft</span>
                            </span>
                        </p>


                        <p class="col-md-3">
                            <span class="input-group">
                                <input type="number" class="form-control" name="unit_height" id="unit-height" placeholder="Height" /><span class="input-group-addon">ft</span>
                            </span>
                        </p>

                    </div>

                    <div id="trpricing">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="price-regular">Regular Price</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-addon">$</span><input type="text" size="4" name="price_regular" id="price-regular" class="form-control" />
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 control-label" for="price-sf">SpareFoot Price</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-addon">$</span><input type="text" size="4" name="price_sf" id="price-sf" class="form-control" />
                                </div>
                                <p class="help-block">NOTE: SpareFoot prices are not temporary promotions and should be available for at least six months. If the web rate is less than or equal to 75% of the regular price, billing is based on the regular price, otherwise we charge based on the web rate.</p>
                            </div>
                        </div>

                    </div>


                    <div class="form-group" id="trpromotion">
                        <label class="col-md-3 control-label" for="promotion">Promotion</label>
                        <div class="col-md-9">
                            <input type="text" size="40" name="promotion" id="promotion" class="form-control" />
                            <p class="help-block" id="promotion-help-block">The unit promotion will be shown in place of the facility promotion for this unit on the AdNetwork. Please be clear and specific.</p>
                        </div>
                    </div>


                    <div class="form-group" id="tramenities">
                        <label class="col-md-3 control-label">Amenities</label>
                        <div class="col-md-9">
                            <div class="checkbox">
                                <label for="power"><input type="checkbox" name="power" id="power" />Power Outlet</label>
                            </div>
                            <div class="checkbox">
                                <label for="alarm"><input type="checkbox" name="alarm" id="alarm" />Alarm</label>
                            </div>
                            <div class="checkbox">
                                <label for="unitlights"><input type="checkbox" name="unitlights" id="unitlights" />Light in Unit</label>
                            </div>
                            <div class="checkbox">
                                <label for="shelvesinunit"><input type="checkbox" name="shelvesinunit" id="shelvesinunit" />Shelves in Unit</label>
                            </div>
                        </div>
                    </div>


                    <div class="form-group" id="trheatingandcooling">
                        <label class="col-md-3 control-label">Heating &amp; Cooling</label>
                        <div class="col-md-9">
                            <div class="checkbox">
                                <label for="climate"><input type="checkbox" name="climate" id="climate" />Climate Controlled</label>
                            </div>
                            <div class="checkbox">
                                <label for="humidity"><input type="checkbox" name="humidity" id="humidity" />Humidity Controlled</label>
                            </div>
                            <div id="aircooled-and-heating">
                                <div class="checkbox">
                                    <label for="aircooled"><input type="checkbox" name="aircooled" id="aircooled" />Air Cooled</label>
                                </div>
                                <div class="checkbox">
                                    <label for="heated"><input type="checkbox" name="heated" id="heated" />Heated</label>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div id="traccess">
                        <div class="form-group">
                            <label class="col-md-3 control-label">Access</label>
                            <div class="col-md-9">

                                {# Only display the 24hr-access unit-level attribute if the facility-level
                                   24hr-access attribute is true, and the restrictions include specific-units.
                                   Otherwise leave it in the form for JS to use, but hide it in the UI #}
                                <div id="js-twentyfourhouraccess-container"
                                     style="display:{{ view.facility.getShowUnitLevelTwentyFourHourAccessOption() ? 'block' : 'none' }}">
                                    <div class="checkbox">
                                        <label for="twentyfourhouraccess"><input type="checkbox" name="twentyfourhouraccess" id="twentyfourhouraccess" />24 Hour Access</label>
                                    </div>
                                </div>

                                <div class="js-traccess-extras">
                                    <div class="checkbox">
                                        <label for="stacked" id="stacked-span"><input type="checkbox" name="stacked" id="stacked" />Stacked Space</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="basement"><input type="checkbox" name="basement" id="basement" />Underground Level</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="parkingwarehouse"><input type="checkbox" name="parkingwarehouse" id="parkingwarehouse" />Parking Warehouse</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="pullthru"><input type="checkbox" name="pullthru" id="pullthru" />Pull-Thru</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="js-traccess-extras">
                            <div class="form-group">
                                <div class="col-md-9 col-md-offset-3">
                                    Can you drive up to the space?
                                    <div class="radio">
                                        <label for="driveup-yes" class="radio-inline">
                                            <input type="radio" name="driveup" id="driveup-yes" value="true" /> Yes</label>
                                        <label for="driveup-no" class="radio-inline">
                                            <input type="radio" name="driveup" id="driveup-no" value="false" checked="checked" /> No
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-md-9 col-md-offset-3">
                                    How do tenants get to this unit?
                                    <div class="radio">
                                        <label for="inside"><input type="radio" name="outdoor_access" id="inside" value="false" />&nbsp;From an indoor hallway</label>
                                    </div>
                                    <div class="radio">
                                        <label for="outside"><input type="radio" name="outdoor_access" id="outside" value="true" checked="checked" />&nbsp;From outside </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-md-9 col-md-offset-3">
                                    Available for vehicle storage?
                                    <div class="radio">
                                        <label for="vehicle-yes"><input type="radio" name="vehicle" id="vehicle-yes" value="true" />Yes, for storage or vehicles</label>
                                    </div>
                                    <div class="radio">
                                        <label for="vehicle-only"><input type="radio" name="vehicle" id="vehicle-only" value="only" />Yes, for vehicles only</label>
                                    </div>
                                    <div class="radio">
                                        <label for="vehicle-no"><input type="radio" name="vehicle" id="vehicle-no" value="false" checked="checked" />No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="form-group" id="trdoor">
                        <label class="col-md-3 control-label">Door Type</label>
                        <div class="col-md-9">
                            <div class="radio">
                                <label for="door-rollup"><input type="radio" name="door_type" id="door-rollup" value="ROLL_UP" checked="checked" />&nbsp;Roll-up Door</label>
                            </div>
                            <div class="radio">
                                <label for="door-swing"><input type="radio" name="door_type" id="door-swing" value="SWING"/>&nbsp;Swing Door</label>
                            </div>
                            <div class="radio">
                                <label for="door-none"><input type="radio" name="door_type" id="door-none" value="NONE"/>&nbsp;None</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id="trlottype">
                        <label class="col-md-3 control-label">Lot Type</label>
                        <div class="col-md-9">
                            <div class="radio">
                                <label for="lot-gravel"><input type="radio" name="lot_type" id="lot-gravel" value="gravel"/>&nbsp;Gravel</label>
                            </div>

                            <div class="radio">
                                <label for="lot-grass"><input type="radio" name="lot_type" id="lot-grass" value="grass"/>&nbsp;Grass</label>
                            </div>

                            <div class="radio">
                                <label for="lot-asphalt"><input type="radio" name="lot_type" id="lot-asphalt" value="asphalt"/>&nbsp;Asphalt</label>
                            </div>

                            <div class="radio">
                                <label for="lot-other"><input type="radio" name="lot_type" id="lot-other" value="other"/>&nbsp;Other</label>
                            </div>
                        </div>
                    </div>

                    <div id="trlocation">
                        <div class="form-group" >
                            <label class="col-md-3 control-label" for="floor">Floor</label>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-addon">#</span>
                                    <input type="text" name="floor" id="floor" class="form-control" />
                                </div>
                            </div>
                        </div>

                        <div class="form-group" >
                            <label class="col-md-3 control-label">Location</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <label for="location-covered" class="radio-inline"><input type="radio" name="covered" id="location-covered" value="true" checked="checked" />&nbsp;Covered</label>
                                </div>
                                <div class="input-group">
                                    <label for="location-uncovered" class="radio-inline"><input type="radio" name="covered" id="location-uncovered" value="false" />&nbsp;Uncovered</label>
                                </div>
                                <div class="checkbox">
                                    <label for="premium"><input type="checkbox" name="premium" id="premium" />Premium Location</label>
                                </div>
                                <div class="checkbox">
                                    <label for="ada"><input type="checkbox" name="ada" id="ada" />ADA Accessible</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p id="trcs4tags" class="help-block hide">All unit variables (unit type, climate, floor, etc.) are configured in the Centershift STORE application.  <a href="http://facilities.sparefoot.com/Centershift4Integration.pdf" target="_blank">This PDF explains how to make changes</a>.</p>
                    <p id="trssmtags" class="help-block hide">All unit variables (unit type, climate, floor, etc.) are configured in SSM.</p>
                </form>
            </div>
            <div class="modal-footer">
                <img src="/images/loaders/small.gif" class="hide" />&nbsp;
                <a id="unit-modal-cancel" class="ui basic button">Cancel</a>
                <a id="unit-modal-save" class="ui button primary" data-loading-text="Saving">Save</a>
            </div>
        </div>
    </div>
</div>
<div id="multi-modal" class="modal" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <form id="multi-unit-form">
                    <p class="alert alert-danger hide"></p>
                    <table class="table table-striped">
                        <tr id="trtype">
                            <th><input type="checkbox" name="change_type" id="multi-change-type" onclick="toggleFieldGroup('multi-change-type','multi-unit-type');" /></th>
                            <th>Unit Type</th>
                            <td>
                                <select id="multi-unit-type" name="unit_type" class="form-control">
                                  <option value="14" selected>Self-Storage Unit</option>
                                  <option value="4">Parking Space</option>
                                  <option value="10">Office Space / Warehouse</option>
                                  <option value="15">Wine Storage</option>
                                  <option value="13">Locker</option>
                                </select>
                            </td>
                        </tr>
                        <tr id="trpricing">
                            <th><input type="checkbox" name="change_pricing" id="multi-change-pricing" onclick="toggleFieldGroup('multi-change-pricing','multi-price-');" /></th>
                            <th>Pricing</th>
                            <td class="form-horizontal">
                                <div>
                                    <label class="control-label" for="multi-price-regular">Regular Price</label>
                                    <div class="controls">
                                        <div class="input-group">
                                            <span class="input-group-addon">$</span><input type="text" size="4" name="price_regular" id="multi-price-regular" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="control-label" for="multi-price-sf">SpareFoot Price</label>
                                    <div class="controls">
                                        <div class="input-group">
                                            <span class="input-group-addon">$</span><input type="text" size="4" name="price_sf" id="multi-price-sf" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="form-group">
                                    <label class="control-label" for="multi-price-deposit">Deposit</label>
                                    <div class="controls">
                                        <div class="input-prepend">
                                            <span class="add-on">$</span><input type="text" size="4" name="deposit" id="multi-price-deposit" class="input-small" />
                                        </div>
                                    </div>
                                </div>-->
                                <p class="help-block">NOTE: SpareFoot prices are not temporary promotions and should be available for at least six months. If the web rate is less than or equal to 75% of the regular price, billing is based on the regular price, otherwise we charge based on the web rate.</p>
                                <input type="hidden" name="qty" id="multi-price-qty" value="1" />
                            </td>
                        </tr>
                        <tr id="trpromotion">
                            <th><input type="checkbox" name="change_promotion" id="multi-change-promotion" onclick="toggleFieldGroup('multi-change-promotion','multi-promotion');"/></th>
                            <th><label for="multi-promotion">Promotion</label></th>
                            <td>
                                <input type="text" size="40" name="promotion" id="multi-promotion" class="form-control" />
                                <p class="help-block" id="promotion-help-block">The unit promotion will be shown in place of the facility promotion for this unit on the AdNetwork. Please be clear and specific.</p>
                            </td>
                        </tr>
                        <tr id="trdimensions">
                            <th><input type="checkbox" name="change_dimensions" id="multi-change-dimensions" onclick="toggleFieldGroup('multi-change-dimensions','multi-dimensions-');"/></th>
                            <th>Dimensions</th>
                            <td class="form-horizontal">

                                <div class="form-group">


                                    <div class="col-sm-4">
                                        <p class="input-group">
                                            <input type="number" min="0" class="form-control" name="unit_width" id="multi-dimensions-unit-width" placeholder="Width" /><span class="input-group-addon">ft</span>
                                        </p>
                                    </div>
                                    <div class="col-sm-4">
                                        <p class="input-group">
                                            <input type="number" min="0" class="form-control" name="unit_length" id="multi-dimensions-unit-length" placeholder="Length" /><span class="input-group-addon">ft</span>
                                        </p>
                                    </div>
                                    <div class="col-sm-4">
                                        <p class="input-group">
                                            <input type="number" class="form-control" name="unit_height" id="multi-dimensions-unit-height" placeholder="Height" /><span class="input-group-addon">ft</span>
                                        </p>
                                    </div>

                                </div>

                                <!--<div class="form-group">
                                    <label class="control-label" for="multi-dimensions-door-width">Door Width</label>
                                    <div class="controls">
                                        <div class="input-append">
                                            <input type="text" class="input-mini" name="door_width" id="multi-dimensions-door-width" /><span class="add-on">ft</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="multi-dimensions-door-height">Door Height</label>
                                    <div class="controls">
                                        <div class="input-append">
                                            <input type="text" class="input-mini" name="door_height" id="multi-dimensions-door-height" /><span class="add-on">ft</span>
                                        </div>
                                    </div>
                                </div>-->
                            </td>

                        </tr>
                        <tr id="tramenities">
                            <th><input type="checkbox" name="change_amenities" id="multi-change-amenities" onclick="toggleFieldGroup('multi-change-amenities','multi-amenities-');"/></th>
                            <th>Amenities</th>
                            <td>
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label for="multi-amenities-power"><input type="checkbox" name="power" id="multi-amenities-power" />Power Outlet</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="multi-amenities-alarm"><input type="checkbox" name="alarm" id="multi-amenities-alarm" />Alarm</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="multi-amenities-climate"><input type="checkbox" name="climate" id="multi-amenities-climate" />Climate Controlled</label>
                                    </div>
                                    <div class="checkbox">
                                        <label for="multi-amenities-humidity"><input type="checkbox" name="humidity" id="multi-amenities-humidity" />Humidity Controlled</label>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr id="traccess">
                            <th><input type="checkbox" name="change_access" id="multi-change-access" onclick="toggleFieldGroup('multi-change-access','multi-access-');"/></th>
                            <th>Access</th>
                            <td class="form-group">
                                <div>
                                    <p>Can you drive up to the space?</p>
                                    <div class="radio">
                                        <label for="multi-access-driveup-yes"><input type="radio" name="driveup" id="multi-access-driveup-yes" value="true" /> Yes</label></div>
                                    <div class="radio">
                                        <label for="multi-access-driveup-no"><input type="radio" name="driveup" id="multi-access-driveup-no" value="" checked="checked" /> No</label>
                                    </div>
                                </div>
                                <div>
                                    <p>How do tenants get to this unit?</p>
                                    <div class="radio">
                                        <label for="multi-access-unit-inside"><input type="radio" name="outdoor_access" id="multi-access-unit-inside" value="" checked="checked" /> From an indoor hallway</label>
                                    </div>
                                    <div class="radio">
                                        <label for="multi-access-unit-outside"><input type="radio" name="outdoor_access" id="multi-access-unit-outside" value="true" /> From outside</label>
                                    </div>
                                </div>
                                <div>
                                    <p>Available for vehicle storage?</p>
                                    <div class="radio">
                                        <label for="multi-access-vehicle-yes"><input type="radio" name="vehicle" id="multi-access-vehicle-yes" value="true" /> Yes</label>
                                    </div>
                                    <div class="radio">
                                        <label for="multi-access-vehicle-no"><input type="radio" name="vehicle" id="multi-access-vehicle-no" value="" checked="checked" /> No</label>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr id="trdoor">
                            <th><input type="checkbox" name="change_door" id="multi-change-door" onclick="toggleFieldGroup('multi-change-door','multi-door-');"/></th>
                            <th>Door Type</th>
                            <td class="form-group">
                                <div class="radio">
                                <label for="multi-door-rollup"><input type="radio" name="door_type" id="multi-door-rollup" value="ROLL_UP" checked="checked" /> Roll-up Door</label>
                                </div>
                                <div class="radio">
                                    <label for="multi-door-swing"><input type="radio" name="door_type" id="multi-door-swing" value="SWING"/> Swing Door</label>
                                </div>
                                <div class="radio">
                                    <label for="multi-door-none"><input type="radio" name="door_type" id="multi-door-none" value="NONE"/> None</label>
                                </div>
                            </td>
                        </tr>
                        <tr id="trlocation">
                            <th><input type="checkbox" name="change_location" id="multi-change-location" onclick="toggleFieldGroup('multi-change-location','multi-location-');"/></th>
                            <th>Location</th>
                            <td class="form-group">
                                <div>
                                    <label for="multi-location-floor">Floor #</label>
                                    <input type="text" size="4" name="floor" id="multi-location-floor" class="form-control" />
                                </div>
                                <div class="radio">
                                    <label for="multi-location-covered"><input type="radio" name="covered" id="multi-location-covered" value="true" checked="checked" /> Covered</label>
                                </div>
                                <div class="radio">
                                    <label for="multi-location-uncovered"><input type="radio" name="covered" id="multi-location-uncovered" value="" /> Uncovered</label>
                                </div>
                            </td>
                        </tr>
                        <!--<tr>
                            <th><input type="checkbox" name="change_description" id="multi-change-description" onclick="toggleFieldGroup('multi-change-description','multi-description');"/></th>
                            <th><label for="multi-description">Description</label></th>
                            <td>
                                <input type="text" size="40" name="description" id="multi-description" />
                                <p class="help-block">This is an internal field only&mdash;it does not appear for consumers.</p>
                            </td>
                        </tr>-->
                    </table>
                </form>
            </div>
            <div class="modal-footer">
                <img src="/images/loaders/small.gif" class="hide" />&nbsp;
                <a id="multi-modal-cancel" class="ui basic button">Cancel</a>
                <a id="multi-modal-save" class="ui primary button" data-loading-text="Saving">Save</a>
            </div>
        </div>
    </div>
</div>

{{ include('inventory/single-unit-confirm-modal.html.twig', {'covidModal': view.covidModal ?? false}) }}
{{ include('inventory/all-unit-confirm-modal.html.twig') }}
{{ include('inventory/reactivate-facility-modal.html.twig') }}
