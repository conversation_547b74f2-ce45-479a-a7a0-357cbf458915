<script type="text/javascript">
    var sourceType = '{{ view.sourceType }}';
    var facilityId = {{ view.facility.getId() }};
    var promoSync = {{ view.promoSync }};
{% if view.inventory %}
    {% set unitInfo = {} %}
    {% for unit in view.inventory %}
        {% set unitInfo = unitInfo|merge({
            (unit.id): {
                "facilityId": view.facility.getId(),
                "type": unit.type_num,
                "uw": unit.unit_w,
                "ul": unit.unit_l,
                "uh": unit.unit_h,
                "dw": unit.door_w,
                "dh": unit.door_h,
                "climate": unit.climate,
                "humidity": unit.humidity,
                "alarm": unit.alarm,
                "power": unit.power,
                "outdoorAccess": unit.outdoorAccess,
                "driveUp": unit.driveUp,
                "stacked": unit.stacked,
                "premium": unit.premium,
                "heated": unit.heated,
                "aircooled": unit.aircooled,
                "ada": unit.ada,
                "unitlights": unit.unitlights,
                "twentyfourhouraccess": unit.twentyfourhouraccess,
                "shelvesinunit": unit.shelvesinunit,
                "basement": unit.basement,
                "parkingwarehouse": unit.parkingwarehouse,
                "pullthru": unit.pullthru,
                "vehicle": unit.vehicle,
                "doorType": unit.doorType,
                "floor": unit.rawFloor,
                "covered": unit.covered,
                "desc": unit.desc,
                "special": unit.special,
                "qty": unit.qty,
                "deposit": unit.deposit,
                "regPrice": unit.list_price,
                "sfPrice": unit.sparefoot_price
            }
        }) %}
    {% endfor %}

    var integratedFields = {{ view.integratedFields|json_encode|raw }};
    //print jsons of unit data, used to populate unit edit dialog
    var unit = {{ unitInfo|json_encode|raw }};

{% else %}
    var unit = [];
{% endif %}
</script>

{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'inventory', 'loggedUser': view.loggedUser, 'facility': view.facility, 'isBidOptimizerActive': view.isBidOptimizerActive}) }}

<div class="ui menu secondary">
{% if view.sourceType == constant('\\Genesis_Entity_Source::ID_MANUAL') %}
    <a id="add_unit_modal_dialog" href="#" class="ui secondary button" onclick="unit_modal({{ view.facility.getId() }});">
        <i class="plus icon"></i> Add Unit
    </a>
{% endif %}
    <a href="{{ url('features', {'action': 'unitexport'}) }}?fid={{ view.facilityId }}" class="ui secondary button">
        <i class="icon file"></i> Export Spreadsheet
    </a>
</div>

{% if view.inventory %}
    <form>
        <div class="table-responsive">
            <table id="units-table" class="ui table striped sortable cell-headers">
                <thead>
                    <tr>
                        <th class="center availability-checkbox no-sort">
                            Available
                        </th>
                        <th class="no-sort"></th>
                        <th>Size</th>
                        <th>Type</th>
                        {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_EXTRA_SPACE') %}
                             <th>ExtraSpace Unit Type</th>
                        {% endif %}

                        <th>Amenities</th>
                        <th>Floor</th>
                        <th><a href="#" rel="tooltip" title="Price you will allow SpareFoot to sell your unit for.">SpareFoot Price</a></th>
                        <th>List Price</th>
                        <th>Unit Promo</th>
                        {% if view.sourceType != constant('\\Genesis_Entity_Source::ID_MANUAL') %}
                            <th>Quantity</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for unit in view.inventory %}
                        {# {{ dump(unit) }} #}
                        <tr id="{{ unit.id }}" data-unitindex="{{ unit.id }}" class="{{ unit.hidden ? 'disabled-row' : 'enabled-row' }}">
                            <td class="center availability-checkbox">
                                {% if view.facility.getApproved() and not unit.approved %}
                                    <p></p>
                                {% else %}
                                    <div class="ui checkbox">
                                        <input type="checkbox" name="listing" id="tg_{{ unit.id }}"
                                            value="{{ unit.id }}" {{ unit.hidden ? '' : 'checked="checked"' }} />
                                    </div>
                                {% endif %}
                             </td>
                            <td class="center">
                                {% if (view.facility.getApproved() and not unit.approved) or 
                                      (view.facility.getCorporation().getSourceId() == constant('\\Genesis_Entity_Source::ID_QUIKSTOR') or
                                       view.facility.getCorporation().getSourceId() == constant('\\Genesis_Entity_Source::ID_EXTRA_SPACE')) %}
                                    <p>&nbsp;</p>
                                {% else %}
                                    <a onclick="unit_modal({{ view.facility.getId() }},{{ unit.id }});">Edit</a>
                                {% endif %}
                            </td>
                            <td data-sort-value="{{ unit.unit_w * unit.unit_l }}">
                                {{ unit.dimensions|replace({' ': '&nbsp;'})|raw }}
                            </td>
                            <td>{{ unit.type }}</td>
                            {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_EXTRA_SPACE') %}
                            <td>{{ unit.unitType }}</td>
                            {% endif %}
                            <td>{{ unit.amenities }}</td>
                            <td>{{ unit.floor }}</td>
                            <td><div id="{{ unit.id }}" name="sparefootprice">{{ (unit.sparefoot_price is defined and unit.sparefoot_price > 0) ? '$' ~ unit.sparefoot_price|number_format(2) : '' }}</div></td>
                            <td>${{ unit.list_price|number_format(2) }}</td>
                            <td>{{ unit.special ? unit.special : '' }}</td>
                            {% if view.sourceType != constant('\\Genesis_Entity_Source::ID_MANUAL') %}
                            <td>{{ unit.quantity }}</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>

            </table>
            {{ include('facility/add-more-units.html.twig', {'units': view.inventory|length, 'facility': view.facility}) }}
        </div>
    </form>
{% elseif view.unpublishedUnits %}
          <p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
{% else %}
      {% if view.sourceType == constant('\\Genesis_Entity_Source::ID_CENTERSHIFT') %}
                <br /><div id="loading" align="center"><img src="/images/loading.gif" /><p>We are currently syncing your units for this facility with Centershift.<br/>Check back shortly.</p>
      {% elseif view.sourceType == constant('\\Genesis_Entity_Source::ID_SITELINK') %}
                <br /><div id="loading" align="center"><img src="/images/loading.gif" /><p>We are currently syncing your units for this facility with SiteLink.<br/>Check back shortly.</p>
      {% elseif view.sourceType == constant('\\Genesis_Entity_Source::ID_MANUAL') %}
                <br />
                <div class="jumbo">
                    <h2>Next up: List your inventory.</h2>
                    <p>Let's add your first unit.</p>
                    <a id="add-first-unit" onclick="unit_modal({{ view.facility.getId() }});" class="huge ui blue button">Add Unit</a>
                </div>
      {% else %}
            <br />
            <div class="ui warning message">
                <p>No units found</p>
            </div>
      {% endif %}
{% endif %}

{{ include('facility/hide-facility-reason-modal.html.twig') }}
{{ include('facility/inventory-modals.html.twig', {'facility': view.facility, 'covidModal': view.covidModal ?? false}) }}
