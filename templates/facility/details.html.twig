{{ include('facility/header.html.twig', {'facility': view.facility}) }}
{{ include('facility/subnav.html.twig', {'selected': 'details', 'loggedUser': view.loggedUser, 'facility': view.facility}) }}

<script>
    try {
        var facilityId = '{{ view.facility.getId() }}';
        var FACILITY = {{ view.facility.toArray()|json_encode|raw }};
        if(FACILITY) FACILITY.reservationWindow = {{ view.facilityResRules|json_encode|raw }};
    } catch(err) { if(CONFIG.env != 'live') console.error('failed to parse facility data', err); }

</script>


<br>
<form id="facility-details-form" method="post" action="{{ url('features', {'action': 'details'}) }}?fid={{ view.facilityId }}" class="form-horizontal">

    {% if view.alert %}
        <p class="alert{{ view.alertClass ? ' ' ~ view.alertClass : '' }}">
            {{ view.alert }}
        </p>
    {% endif %}
    <script>
        var integratedFields = {{ view.integratedFields|json_encode|raw }}
    </script>

    {% set states = [
        'AK', 'AL', 'AR', 'AZ', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL',
        'GA', 'HI', 'IA', 'ID', 'IL', 'IN', 'KS', 'KY', 'LA', 'MA',
        'MD', 'ME', 'MI', 'MN', 'MO', 'MS', 'MT', 'NC', 'ND', 'NE',
        'NH', 'NJ', 'NM', 'NV', 'NY', 'OH', 'OK', 'OR', 'PA', 'RI',
        'SC', 'SD', 'TN', 'TX', 'UT', 'VA', 'VT', 'WA', 'WI', 'WV', 'WY'
    ] %}

    {% set i = 1 %}
    <div class="form-group{{ 'facility_name' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_name">Facility Name</label>
        <div class="col-lg-10">
            <input size="50" type="text" id="facility_name" name="facility_name" tabindex="{{ i }}" class="form-control" value="{{ view.facility.getTitle() }}"/>
        </div>
    </div>

    {% set i = i + 1 %}
    <div class="form-group{{ 'facility_code' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_code">Company Code</label>
        <div class="col-lg-10">
            <input size="12" type="text" id="facility_code" name="facility_code" tabindex="{{ i }}" class="form-control" value="{{ view.facility.getCompanyCode() }}"/>
            <p class="help-block">If you use a code to reference this facility, you can enter it here and we will surface it on your statements to help you with accounting.</p>
        </div>
    </div>

    {% set i = i + 1 %}
    <div class="form-group{{ 'facility_url' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_url">Facility URL</label>
        <div class="col-lg-10">
            <input size="50" type="text" value="{{ view.facility.getUrl() }}" id="facility_url" name="facility_url" tabindex="{{ i }}" class="form-control"/>
        </div>
    </div>

    {% set i = i + 1 %}
    <div class="form-group{{ 'facility_admin_fee' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_admin_fee">Admin Fee</label>
        <div class="col-lg-7">
            <div class="input-group">
                <span class="input-group-addon">$</span><input type="text" value="{{ view.facility.getAdminFee() }}" id="facility_admin_fee" name="facility_admin_fee" tabindex="{{ i }}" class="form-control"/>
            </div>
            <p class="help-block">How much you charge a customer on their initial move-in (not including rent fees).</p>
        </div>
    </div>

    {% if view.facility.canEditReservationWindow() %}
        <div class="form-group res-window-group">
        <div class="col-lg-2 form-label">
            <label class="control-label">Reservation Window</label>
            <a class="res-window-more-info btn btn-xs btn-link" href="#">Learn More</a>
        </div>
        <div class="col-lg-10 {{ view.sources == false ? 'integrated' : 'manual' }}">
            <div class="facility-res-win-widget"></div>

            {# unit level exceptions #}
            {% set units = [] %}
            {% for unit in view.units %}
                {% set resDays = unit.getReservationWindowRules()|length > 0 ? unit.getReservationDays() : null %}
                {% set units = units|merge([{
                    'id': unit.getId(),
                    'name': (unit.stringDimensions(false)|replace({'&nbsp;': ' '}) ~ ' ' ~ unit.stringType()),
                    'amenities': unit.stringAmenities(),
                    'numAvail': unit.getGroupedNumAvailable(),
                    'qty': unit.getQuantity(),
                    'resDays': resDays,
                    'unitGroups': unit.getGroupedUnitNames(),
                    'length': unit.getLength(),
                    'width': unit.getWidth()
                }]) %}
            {% endfor %}
            <script>
                var canGroupUnits = {{ view.facility.canGroupUnits() ? 'true' : 'false' }};
                var units = {{ units|length ? (units|json_encode) : '[]' }};
            </script>
        </div>
    </div>
    {% else %}
        {% set resWindow = view.facility.getSuppAttrHash().integratedReservationWindow %}
        <div class="form-group">
            <label class="col-lg-2 control-label">Reservation Window</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="{{ resWindow[1] ~ ' Days' }}" readonly="readonly" class="form-control disabled" />
                <p class="help-block-disabled">Number of days you will hold a unit for a reservation. To update this field, please edit within your management software.</p>
            </div>
        </div>
    {% endif %}

    {% if call_static('\\Genesis_Service_Feature', 'isActive', ['tenant_connect_sms']) %}

        <div class="form-group{{ 'facility_phone' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="facility_phone">Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="{{ view.facility.getPhone() ? view.facility.stringPhone() : '' }}" id="facility_phone" name="facility_phone" tabindex="{{ tabindex + 1 }}" class="form-control" />
                {% set tabindex = tabindex + 1 %}
            </div>
        </div>

        {#
        <div class="form-group{{ 'facility_tenant_connect_override_phone' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="facility_tenant_connect_override_phone">Direct Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="{{ view.facility.getTenantConnectOverridePhone() ? view.facility.stringTenantConnectOverridePhone() : '' }}" id="facility_tenant_connect_override_phone" name="facility_tenant_connect_override_phone" tabindex="{{ tabindex + 1 }}" class="form-control" />
                {% set tabindex = tabindex + 1 %}
            </div>
        </div>
         #}

        <div class="form-group{{ 'facility_tenant_connect_sms_number' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="facility_tenant_connect_sms_number">Cell Phone Number</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="{{ view.facility.getTenantConnectSMSNumber() ? view.facility.stringTenantConnectSMSNumber() : '' }}" id="facility_tenant_connect_sms_number" name="facility_tenant_connect_sms_number" tabindex="{{ tabindex + 1 }}" class="form-control" />
                {% set tabindex = tabindex + 1 %}
                <p class="help-block">We'll send a text message when you get a new reservation! This message will include the customer's phone #, so you can follow up with them immediately. </p>
            </div>
        </div>
        {% if call_static('\\Genesis_Service_Feature', 'isActive', [constant('\\Sparefoot\\MyFootService\\Models\\Features::TENANT_CONNECT_TOGGLE')]) %}
        <div class="form-group">
            <div class="col-lg-10 col-lg-offset-2">
                <label>Enable Tenant Connect</label>
                <p class="help-block">We will give you a phone call when a booking occurs and you will have the option to immediately be connected with them via phone.</p>

                <div>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-tenant-connect-yes" name="facility_tenant_connect" value="1" {{ view.facility.getTenantConnect() != '0' ? 'checked="checked"' : '' }} />Yes
                    </label>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-tenant-connect-no" name="facility_tenant_connect" value="0" {{ view.facility.getTenantConnect() == '0' ? 'checked="checked"' : '' }} />No
                    </label>
                </div>
            </div>
        </div>
            {% endif %}
    {% else %}
        <div class="form-group{{ 'facility_phone' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="facility_phone">Phone</label>
            <div class="col-lg-10">
                <input size="12" type="text" value="{{ view.facility.getPhone() ? view.facility.stringPhone() : '' }}" id="facility_phone" name="facility_phone" tabindex="{{ tabindex + 1 }}" class="form-control" />
                {% set tabindex = tabindex + 1 %}
            </div>
        </div>
    {% endif %}

    <div class="form-group{{ 'facility_address1' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_address1">Address</label>
        <div class="col-lg-10">
            <input size="30" type="text" value="{{ view.facility.getLocation().getAddress1() }}" id="facility_address1" name="facility_address1" tabindex="{{ tabindex + 1 }}" class="form-control" />
            {% set tabindex = tabindex + 1 %}
        </div>
    </div>

    <div class="form-group{{ 'facility_city' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_city">City</label>
        <div class="col-lg-10">
            <input size="15" type="text" value="{{ view.facility.getLocation().getCity() }}" id="facility_city" name="facility_city" tabindex="{{ tabindex + 1 }}" class="form-control" />
            {% set tabindex = tabindex + 1 %}
        </div>
    </div>

    <div class="form-group{{ 'facility_state' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_state">State</label>
        <div class="col-lg-10">
            <select name="facility_state" id="facility_state" tabindex="{{ tabindex + 1 }}" class="form-control">
                {% set tabindex = tabindex + 1 %}
                <option value="">-- Select State --</option>
                {% for state in states %}
                    <option value="{{ state }}"{{ view.facility.getLocation().getState() == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                {% endfor %}
            </select>
        </div>
    </div>

    <div class="form-group{{ 'facility_zip' in view.erroredFields ? ' has-error' : '' }}">
        <label class="col-lg-2 control-label" for="facility_zip">Zip</label>
        <div class="col-lg-10">
            <input size="15" type="text" value="{{ view.facility.getLocation().getZip() }}" id="facility_zip" name="facility_zip" tabindex="{{ tabindex + 1 }}" class="form-control" />
            {% set tabindex = tabindex + 1 %}
        </div>
    </div>

    <div class="form-group{{ 'has_alt_address' in view.erroredFields ? ' has-error' : '' }}">
        <div class="col-lg-10 col-lg-offset-2">
            <label>SpareFoot will occasionally send things to you in the mail. Can you receive mail at the above address?</label>
            <div>
              <label class="control-label radio-inline">
                  <input type="radio" id="has-alt-address-yes" name="has_alt_address" value="0" tabindex="{{ tabindex + 1 }}" {{ view.facility.getAltAddress() == '0' ? 'checked="checked"' : '' }} />Yes
                  {% set tabindex = tabindex + 1 %}
              </label>
              <label class="control-label radio-inline">
                  <input type="radio" id="has-alt-address-no" name="has_alt_address" value="1" tabindex="{{ tabindex }}"{{ view.facility.getAltAddress() and view.facility.getAltAddress() != '0' ? ' checked="checked"' : '' }} />No
              </label>
            </div>
        </div>
    </div>

    <div class="ui segment col-lg-offset-2" id="has-alt-address-options" style="display:{{ view.facility.getAltAddress() and view.facility.getAltAddress() != '0' ? 'block' : 'none' }};">

        {% set altAddress = view.facility.getAltAddress() %}

        <p>Alternate address for receiving packages</p>

        <div class="form-group{{ 'alt_facility_address' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="alt_facility_address1">Address</label>
            <div class="col-lg-10">
                <input size="30" type="text" value="{{ altAddress.address is defined ? altAddress.address : '' }}" id="facility_address1" name="alt_facility_address1" class="form-control" />
            </div>
        </div>

        <div class="form-group{{ 'alt_facility_city' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="alt_facility_city">City</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="{{ altAddress.city is defined ? altAddress.city : '' }}" id="facility_city" name="alt_facility_city" class="form-control" />
            </div>
        </div>

        <div class="form-group{{ 'alt_facility_state' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="alt_facility_state">State</label>
            <div class="col-lg-10">
                <select name="alt_facility_state" id="alt_facility_state" tabindex="{{ tabindex + 1 }}" class="form-control">
                    {% set tabindex = tabindex + 1 %}
                    <option value="">-- Select State --</option>
                    {% for state in states %}
                        <option value="{{ state }}"{{ altAddress.state == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group{{ 'alt_facility_zip' in view.erroredFields ? ' has-error' : '' }}">
            <label class="col-lg-2 control-label" for="alt_facility_zip">Zip</label>
            <div class="col-lg-10">
                <input size="15" type="text" value="{{ altAddress.zip is defined ? altAddress.zip : '' }}" id="facility_zip" name="alt_facility_zip" class="form-control" />
            </div>
        </div>

    </div>

    <div class="form-group{{ 'onsite_office_at_facility' in view.erroredFields ? ' has-error' : '' }}">
        <div class="col-lg-10 col-lg-offset-2">
            <label>Is there an on-site office at the above address?</label>
            <div>
                <label class="control-label radio-inline">
                    <input type="radio" id="onsite-office-at-facility-yes" name="onsite_office_at_facility" value="1" tabindex="{{ tabindex + 1 }}" {{ view.facility.getOnsiteOfficeAtFacility() == '1' ? 'checked="checked"' : '' }} />Yes
                    {% set tabindex = tabindex + 1 %}
                </label>
                <label class="control-label radio-inline">
                    <input type="radio" id="onsite-office-at-facility-no" name="onsite_office_at_facility" value="0" tabindex="{{ tabindex }}" {{ view.facility.getOnsiteOfficeAtFacility() == '0' ? 'checked="checked"' : '' }} />No
                </label>
            </div>
        </div>
    </div>

    {% if view.source.getId() != constant('\\Genesis_Entity_Source::ID_MANUAL') %}
    <div class="form-group{{ 'facility_promotions' in view.erroredFields ? ' has-error' : '' }}">

        <label class="col-lg-2 control-label" for="facility_promotions">Special Offer</label>
        <div class="col-lg-10">

            {% if view.corporation.getPullPromos() %}
                <input type="text" value="{{ view.facility.getSpecials() }}" disabled="disabled" size="50" maxlength="100" class="form-control" />
                <input type="hidden" name="facility_promotions" value="{{ view.facility.getSpecials() }}"/>
            {% else %}
                <input type="text" name="facility_promotions" id="facility_promotions" value="{{ view.facility.getSpecials() }}" tabindex="{{ tabindex + 1 }}" size="50" maxlength="100" class="form-control" />
                {% set tabindex = tabindex + 1 %}
                <span class="help-inline">(<span class="countdown"></span>max. 100 characters)</span>
                <p class="help-block">Example: "First Month Free!". The facility-wide promotion will show up by default, but will be overwritten for any units with unit-level promotions.</p>
            {% endif %}

        </div>
    </div>
    {% endif %}

    <div class="form-group{{ 'facility_description' in view.erroredFields ? ' has-error' : '' }}">

        <label class="col-lg-2 control-label" for="facility_description">Facility Description</label>
        <div class="col-lg-10">
            <textarea cols="57" rows="5" name="facility_description" id="facility_description" tabindex="{{ tabindex + 1 }}"  class="form-control">{{ view.facility.getDescription() }}</textarea>
            {% set tabindex = tabindex + 1 %}
        </div>
    </div>

    <div class="form-group{{ 'facility_software' in view.erroredFields ? ' has-error' : '' }}">
        <a name="facility_software"></a>
        <label class="col-lg-2 control-label" for="facility_software">{{ view.sources === false ? 'This Facility Uses' : 'What software do you use to manage your facility?' }}</label>
        <div class="col-lg-10">
            {% if view.sources === false %}{# just show the integration name, no options #}
                <label class="control-label"><strong>{% if view.source.getId() == constant('\\Genesis_Entity_Source::ID_OPENTECH') %}{{ view.facility.getOpentechFacility().getPropertyManagementSoftware() }} (Opentech ISSN){% else %}{{ view.source.getSource() }}{% endif %}</strong></label>
                <input type="hidden" name="facility_software" value="{{ view.source.getId() }}" />
            {% else %}
            <select name="facility_software" id="facility_software" tabindex="{{ tabindex + 1 }}" class="form-control">
                {% set tabindex = tabindex + 1 %}
                <option value=""></option>
                {% for sourceEntity in view.sources %}
                    <option value="{{ sourceEntity.getId() }}"{{ sourceEntity.getId() == view.facility.getSelfReportedSourceId() ? ' selected="selected"' : '' }}>{{ sourceEntity.getSource() }}</option>
                {% endfor %}
            </select>
            {% endif %}
        </div>
    </div>

    {% if view.facility.getSourceId() != constant('\\Genesis_Entity_Source::ID_EXTRA_SPACE') %}
        <div class="form-group{{ 'facility_active' in view.erroredFields ? ' has-error' : '' }}">
            <div class="col-lg-10 col-lg-offset-2">
                <label>Make this facility available for bookings on the SpareFoot network?</label>
                <div>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-active-yes" name="facility_active" value="1" tabindex="{{ tabindex }}" {{ view.facility.getActive() != '0' ? 'checked="checked"' : '' }} />Yes
                    </label>
                    <label class="control-label radio-inline" style="vertical-align: top;">
                        <input type="radio" id="facility-active-no" name="facility_active" value="0" tabindex="{{ tabindex + 1 }}" {{ view.facility.getActive() == '0' ? 'checked="checked"' : '' }} />No
                        {% set tabindex = tabindex + 1 %}
                    </label>
                    {% set facilityReactivationDate = view.facility.getAutomaticReactivationDate() %}
                    {% if facilityReactivationDate and call_static('\\strtotime', [facilityReactivationDate]) > call_static('\\strtotime', ['today']) %}
                        {% set reactivationDate = call_static('\\strtotime', [facilityReactivationDate]) %}
                        {% set hasReactivationDate = true %}
                    {% else %}
                        {% set reactivationDate = call_static('\\strtotime', ['+1 month']) %}
                        {% set hasReactivationDate = false %}
                    {% endif %}

                    <script>
                        var nativeReactivationDate = "{{ view.facility.getAutomaticReactivationDate() }}";
                        var hasReactivationDate = {{ hasReactivationDate ? 'true' : 'false' }};
                        var defaultReactivationDate =       new Date({{ reactivationDate * 1000 }});
                        var firstAllowedReactivationDate =  new Date({{ call_static('\\strtotime', ['tomorrow']) * 1000 }});
                        var lastAllowedReactivationDate =   new Date({{ call_static('\\strtotime', ['+1 year']) * 1000 }});
                    </script>
                    <label id="reactivation-message" for="automatic_reactivation_date" {{ (view.facility.getActive() == '1' or not hasReactivationDate) ? 'style="display:none"' : '' }} class="control-label radio-inline{{ 'automatic_reactivation_date' in view.erroredFields ? ' has-error' : '' }}">
                        This facility is scheduled to reactivate on
                        <span id="automatic-reactivation-date-input" style="display:none">
                            <input id="automatic-reactivation-date-datepicker" type="text" name="automatic_reactivation_date" value="{{ hasReactivationDate ? call_static('\\date', ['M j, Y', reactivationDate]) : '' }}" class="form-control datepicker-field"/>
                        </span>
                        <span id="automatic-reactivation-date-label" style="font-weight: bold">
                            {{ call_static('\\date', ['M j, Y', reactivationDate]) }}
                        </span>
                        <a id="change-automatic-reactivation-date">
                        Change
                        </a>
                    </label>
                    <label id="option-automatic-reactivation-date-label" for="option-automatic-reactivation-date" {{ (view.facility.getActive() == '1' or hasReactivationDate) ? 'style="display:none"' : '' }} class="control-label radio-inline{{ 'automatic_reactivation_date' in view.erroredFields ? ' has-error' : '' }}">
                        <a id="option-automatic-reactivation-date">
                            Set an automatic reactivation date for this facility?
                        </a>
                    </label>
                </div>
            </div>
        </div>
    {% endif %}

    <div class="form-actions">
        <div class="right">
            <input type="hidden" id="facility_id" name="facility_id" value="{{ view.facility.getId() }}" />
            <input type="hidden" id="facility_address_change" name="facility_address_change" value="0" />
            <input id="facility-details-save" class="ui primary button" name="commit" type="submit" value="Save Changes" tabindex="{{ tabindex + 1 }}"/>
            {% set tabindex = tabindex + 1 %}
        </div>
    </div>
</form>

{{ include('facility/hide-facility-reason-modal.html.twig') }}
