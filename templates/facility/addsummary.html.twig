<div class="setup-content-container">

    <div class="setup-content">

        <div class="content-row">
            {% if view.facility %}
                <h2>New facility added</h2>
            {% else %}
                <h2>New facilities synced</h2>
            {% endif %}
            <p>For each new facility, please select a payment method. You can use one of your existing payment methods by selecting it from the drop down box, or enter a new payment method (new credit card, ACH, etc.) by selecting the "Add a New Payment Type" option.</p>
        </div>

        <div class="content-row smaller">

            <div class="alert alert-danger hide">Please choose a payment type for each new facility.</div>

            <table class="table">
                <tr class="header">
                    <th>Facility</th>
                    <th>Payment Method</th>

                </tr>

            {% for fac in view.facilities %}
                <tr id="{{ fac.getId() }}">
                    <td>{{ fac.getTitle() }}</td>
                    <td>
                        <select id="fac_{{ fac.getId() }}" name="beId" class="form-control" onChange="changeFacBe({{ fac.getId() }}, this.value)">
                        {% if not fac.getBillableEntityId() %}<option value="choose" selected="selected">Choose a payment type</option>{% endif %}
                        {% for be in view.payment_methods %}
                            <option value="{{ be['id'] }}" {% if fac.getBillableEntityId() == be['id'] %}selected="selected"{% endif %}>{{ be['displayStr'] }}</option>
                        {% endfor %}
                            <option value="new">Add a new payment type</option>
                        </select>
                    </td>
                </tr>
            {% endfor %}

            </table>

        </div>

        <div class="content-footer">
            <form id="target" method="post" action="{{ path('features', {'action': 'addsummary'}) }}" class="pull-right">
                <img src="/images/loaders/small.gif" class="hide" />&nbsp;&nbsp;
                <input type="hidden" id="syncd_fac_ids" name="syncd_fac_ids" value="{{ view.facIds|join(',') }}"/>
                <input class="ui primary large button" type="submit" data-loading-text="Saving" value="Finish"/>
            </form>
            <a href="{{ path('features', {'action': 'type'}) }}" class="ui basic large button">Back</a>
        </div>

    </div>

</div>
