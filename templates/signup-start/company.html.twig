{% extends 'signup-layout.html.twig' %}

{% block content %}
<form id="signup-company-form" action="/signup-start/add-company" method="post">
	<input type="hidden" id="login_csrf_token" name="login_csrf_token" value="{{ view.login_csrf_token }}">
	<input type="hidden" name="csrf_token" value="{{ view.csrf_token }}">
    <div class="content-row">
        <h2 id="companyinformation">Company Information</h2>
    </div>
    <div class="input-row string">
    	<div class="form-horizontal">
    		<fieldset>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="company-name">Company Name </label>
                    <div class="col-md-10">
                        <input type="text" name="company_name" class="form-control" required pattern=".{3,}" title="3 characters minimum" id="company-name" value="{{ view.companyName }}"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="address">Business Address </label>
                    <div class="col-md-10">
    				    <input type="text" name="address" class="form-control" required pattern=".{4,}" title="4 characters minimum" id="address" value="{{ view.address }}"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="city">City </label>
                    <div class="col-md-10">
    				    <input type="text" name="city" class="form-control" required pattern=".{3,}" title="3 characters minimum" id="city" value="{{ view.city }}"/>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="state">State </label>
                    <div class="col-md-10">
        				<select name="state" id="state" class="form-control" required title="2 letter state code">
        				    <option value=""></option>
        				    {% set states = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'] %}
        				    {% for state in states %}
        				        <option value="{{ state }}"{{ view.state == state ? ' selected="selected"' : '' }}>{{ state }}</option>
        				    {% endfor %}
                        </select>
                    </div>
    			</div>
    			<div class="form-group">
                    <label class="col-md-2 control-label" for="zip">Zip Code </label>
                    <div class="col-md-10">
    				    <input type="text" name="zip" class="form-control" required pattern=".{5,10}" title="5 characters minimum" id="zip" value="{{ view.zip }}" />
                    </div>
    			</div>
    		</fieldset>
    	</div>
    </div>

    <div class="content-row">
        <h2>User Information</h2>
    </div>
    <div class="input-row string">
    	<div class="form-horizontal">
    		<fieldset>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="first">First Name </label>
                    <div class="col-md-10">
    				    <input type="text" name="first_name" class="form-control" required pattern=".{2,}" title="2 characters minimum" id="first-name" value="{{ view.first }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="last">Last Name </label>
                    <div class="col-md-10">
    				    <input type="text" name="last_name" class="form-control" required pattern=".{2,}" title="2 characters minimum" id="last-name" value="{{ view.last }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="phone">Phone </label>
                    <div class="col-md-10">
    				    <input type="tel" name="phone" class="form-control" required pattern=".{10,15}" title="10 characters minimum" id="phone" value="{{ view.phone }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="email">Your Email </label>
                    <div class="col-md-10">
    				    <input type="email" name="email" class="form-control" required pattern=".{4,}" title="4 characters minimum" id="email" value="{{ view.email }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pass">Password </label>
                    <div class="col-md-10">
    				    <input type="password" name="password" class="form-control" required pattern=".{6,}" title="6 characters minimum" id="password" value=""/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="pass2">Re-Enter Password </label>
                    <div class="col-md-10">
    				    <input type="password" name="password_confirm" class="form-control" required pattern=".{6,}" title="6 characters minimum" id="password-confirm" value=""/>
                    </div>
                </div>
    		</fieldset>
    	</div>
    </div>
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <input id="submit" class="btn btn-primary btn-lg" name="commit" type="submit" value="Next" data-loading-text="Saving" />
        </div>
        <a href="{{ view.backlink }}" class="btn btn-default btn-lg" id="back">Back</a>
    </div>
</form>
{% endblock %}