{% extends 'signup-layout.html.twig' %}

{% block content %}
<div class="content-row">
    <h2>Signup Code</h2>
</div>
<form id="signup-code-form" action="/signup-start/validate-code" method="post">
    <div class="input-row string">
        <div class="form-horizontal">
            <fieldset>
                <div class="form-group">
                    <label class="col-lg-2 control-label" for="signup-code">Signup Code </label>
                    <div class="col-lg-10">
                        <input type="hidden" name="csrf_token" value="{{ view.csrf_token }}">
                        <input type="text" name="signup_code" class="form-control" id="signup-code" required pattern=".{4,}" title="4 characters minimum" value="{{ view.signupCode }}"/>
                        <p class="help-block">A signup code is required to create an account on SpareFoot.<br /> To obtain one, call {{ constant('\\Sparefoot\\MyFootService\\Service\\Constants::SUPPORT_PHONE_NUMBER') }} and press {{ constant('\\Sparefoot\\MyFootService\\Service\\Constants::SUPPORT_PHONE_NUMBER_EXT') }}.</p>
                    </div>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="content-footer">
        <input type="hidden" name="user_id" class="input-small" id="u" value="{{ view.userid }}"/>
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <input id="submit" name="submit" type="submit" value="Next" data-loading-text="Saving" class="btn btn-primary btn-lg" />
        </div>
        <div class="clear"></div>
    </div>
</form>
{% endblock %}
