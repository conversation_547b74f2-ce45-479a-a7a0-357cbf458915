{% if view.loggedUser is defined and view.loggedUser.isMyfootGod() %}
{% set env = '' %}
{% if call_static('\\Genesis_Config_Server', 'isProduction') %}
    {% set env = '' %}
{% elseif call_static('\\Genesis_Config_Server', 'isStaging') %}
    {% set env = 'staging' %}
{% elseif call_static('\\Genesis_Config_Server', 'isDev') %}
    {% set env = 'dev' %}
{% else %}
    {% set env = 'local' %}
{% endif %}

<div class="ui search dropdown">
    <i class="dropdown icon"></i>
    <div class="default text"></div>
    <div class="menu">
    </div>
</div>

    <div id="preheader" class="preheader preheader-{{ env }}"><span>{{ env|title }}</span>

        <p id="accounts-header">
            {{ view.loggedUser.getAccount().getName() }} &mdash; {{ view.loggedUser.getAccount().getIntegrationsString() }} ({{ view.loggedUser.getAccount().getNumFacilities() }})
            {% if view.loggedUser.getAccount().getStatus() and view.loggedUser.getAccount().getStatus() != "Live" %} &mdash; {{ view.loggedUser.getAccount().getStatus()|upper }}{% endif %}
            Account #{{ view.loggedUser.getAccount().getId() }}
            {% set corps = view.loggedUser.getAccount().getCorporations() %}
            {% if corps != false %}
                {% for corp in corps %}
                    Corp #{{ corp.getId() }}
                    {% break %}
                {% endfor %}
            {% endif %}&nbsp;&nbsp;<i class="fa fa-chevron-right"></i>
            {% if view.session.facilityId %}Facility #{{ view.session.facilityId }}{% endif %}

        </p>

    </div>
            
    <style type="text/css">
        body {
            padding-top: 110px;
        }
    </style>
{% endif %}
