{# Dashboard Index Template - Migrated from application/views/scripts/dashboard/index.phtml #}

{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
<script type="text/javascript">
    App.context = {
        facility_id: {{ view.facility ? view.facility.getId() : 'null' }}
    };
</script>

{% if view.isShowInactiveFacilityMessage %}
        <div id="inactive-facility-message" class="ui warning message" style="overflow:auto">
            <div class="header">
                This facility is currently inactive and will not receive reservations on the SpareFoot network.
                <button id="activate-facility" class="ui button compact primary pull-right" />Activate Facility</button>
            </div>
        </div>
{% endif %}

<div class="ui stackable two column grid">
    <div class="column">
        <div class="ui segment dashboard-widget welcome-widget <?=$this->showWelcomeWidget ? '' : 'hidden'?>">
            <div class="head">
                <h2 class="widget-title">Welcome to SpareFoot!</h2>
                <p>
                    Adding photos, requesting reviews, and adding additional unit types will help drive customers to your facility listing and improve conversion rates.
                </p>
            </div>
            <div class="body">
                <a href="{{ path('features_photos', {'fid': view.facility.getId()}) }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="photo icon"></i>
                        <h3>Add Photos</h3>
                    </div>
                </a>
                <a href="{{ path('reviews_request', {'fid': view.facility.getId()}) }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="star icon"></i>
                        <h3>Request Reviews</h3>
                    </div>
                </a>
                <a href="{{ path('features_units', {'fid': view.facility.getId()}) }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="building outline icon"></i>
                        <h3>Add Unit Types</h3>
                    </div>
                </a>
            </div>
        </div>
        {% if call_static('Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.dashboard-widget.submitRate') %}
        <div class="ui segment dashboard-widget submit-rate-widget"></div>
        {% endif %}

        {% if call_static('Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.dashboard-widget.moveInRate') %}
        <div class="ui segment dashboard-widget move-in-rate-chart {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}
    </div>
    <div class="column">
        {% if call_static('Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.dashboard-widget.inventory') %}
        <div class="ui segment dashboard-widget inventory-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}

        {% if call_static('Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.dashboard-widget.currentBidRanking')
        and view.account.getBidType() != constant('Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
        <div class="ui segment dashboard-widget current-bid-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}

        {% if call_static('Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', 'myfoot.dashboard-widget.customerReviews') %}
        <div class="ui segment dashboard-widget customer-reviews-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}
    </div>
</div>

<div class="bottom-more-row">
    <a href="{{ path('reports_index') }}">View More Reports &gt;</a>
</div>

<div class="clear"></div>

{% endblock %}
