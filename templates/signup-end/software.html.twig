{% extends 'signup-layout.html.twig' %}

{% block content %}
<script type="text/javascript">
    var myFootLink = '{{ view.myFootLink }}';
</script>

<div class="content-row">
    <h2>Select the storage management software you use.</h2>
    <p>We'll use this to customize your MySpareFoot experience. Select all that apply.</p>
</div>

<form id="signup-integration-form" action="/signup-end/record-software" method="post">    
    <div class="input-row">
    
        <div class="alert alert-danger hide"></div>
    
        <div class="row">
        
            <div class="col-md-4">

                <div class="form-group">
                    <div class="checkbox">
                        <label for="sl">
                            <input type="checkbox" id="sl" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_SITELINK') }}" />{{ call_static('Genesis_Service_Source', 'loadById', [constant('\\Genesis_Entity_Source::ID_SITELINK')]).buildLogoTag()|raw }}<br />Web edition
                        </label>
                    </div>
                </div>
                
            </div>
            <div class="col-md-4">
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="cs4">
                            <input type="checkbox" id="cs4" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_CENTERSHIFT4') }}" />{{ call_static('Genesis_Service_Source', 'loadById', [constant('\\Genesis_Entity_Source::ID_CENTERSHIFT4')]).buildLogoTag()|raw }} 4.0
                        </label>
                    </div>
                </div>
                
            </div>
            <div class="col-md-4">
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="qs">
                            <input type="checkbox" id="qs" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_QUIKSTOR') }}" />{{ call_static('Genesis_Service_Source', 'loadById', [constant('\\Genesis_Entity_Source::ID_QUIKSTOR')]).buildLogoTag()|raw }}
                        </label>
                    </div>
                </div>
            
                <div class="form-group">
                    <div class="checkbox">
                        <label for="ssm">
                            <input type="checkbox" id="ssm" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') }}" />{{ call_static('Genesis_Service_Source', 'loadById', [constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER')]).buildLogoTag()|raw }}
                        </label>
                    </div>
                </div>
            </div>

        </div>
        
        <div class="row" id="manual-software-options">
            <div class="col-md-4">
                
        	   <div class="form-group">
                    <div class="checkbox">
                        <label for="othersoft">
                            <input type="checkbox" id="othersoft" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_OTHER') }}" />Other software
                        </label>
                    </div>
                </div>
        
                <div class="form-group">
                    <div class="checkbox">
                        <label for="nosoftware">
                            <input type="checkbox" id="nosoftware" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_MANUAL') }}" />I don't use software
                        </label>
                    </div>
                </div>
                
        	</div>		
        </div>
    </div>
    
    <div class="input-row hide" id="man-first-facility">
        <div class="form-horizontal">
        
            <h4>Let's set up your first facility.</h4>
        
            <div class="checkbox">
                <label for="address-same">
                    <input type="checkbox" name="address_same" id="address-same" />
                    My facility address is the same as my company address.
                </label>
            </div><br />
            
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Facility Name</label>
                <div class="col-lg-10">
                    <input id="facility-name" name="facility_name" type="text" class="form-control" value="{{ view.facilityName }}"/>
                </div>
            </div>
    
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Address</label>
                <div class="col-lg-10">
                    <input id="address" name="address" type="text" class="form-control" value="{{ view.address }}"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="city" class="col-lg-2 control-label">City</label>
                <div class="col-lg-10">
                    <input id="city" name="city" type="text" class="form-control" value="{{ view.city }}"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="state" class="col-lg-2 control-label">State</label>
                <div class="col-lg-10">
                    <select id="state" name="state" class="form-control">
                        <option value=""></option>
                        {% set states = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'] %}
                        {% for state in states %}
                            <option value="{{ state }}">{{ state }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Zip Code</label>
                <div class="col-lg-10">
                    <input id="zip" name="zip" type="text" class="form-control" value="{{ view.zip }}"/>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Phone Number</label>
                <div class="col-lg-10">
                    <input id="phone" name="phone" type="text" class="form-control" value="{{ view.phone }}"/>
                </div>
            </div>
        </div>
    </div>

    
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <input class="btn btn-lg btn-primary" type="submit" id="submit" data-loading-text="Saving" value="Finish" />    
        </div>
        <a href="/signup-end/billing" class="btn btn-default btn-lg" id="back">Back</a>
    </div>

</form>
{% endblock %}