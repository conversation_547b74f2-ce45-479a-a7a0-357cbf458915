{% extends 'signup-layout.html.twig' %}

{% block content %}
{% if view.error %}
    <p class="ui message negative">{{ view.error }}</p>
{% endif %}
{% if view.completed == "true" %}
<div class="content-row">
    <h2>Billing Information <img src="/images/lock.png"></h2>
    <p>We already have your payment information.  If you'd like to change it, you can do so at any time by visiting the Payment Setup tab in your MySpareFoot account.</p>
    <br/>
</div>

<div class="content-footer">
    <a href="{{ view.backlink }}" class="btn btn-default btn-lg">Back</a>
    <a href="/" id="submit" class="btn btn-primary btn-lg pull-right">Finish</a>
    <div class="clear"></div>
</div>

{% else %}

<script type="text/javascript">
var myfootlink = '{{ view.myFootLink }}';
{% if view.billableEntityId > 0 %}
ccType = 'existing';
{% else %}
var ccType = '';
{% endif %}
var numFacilities = {{ view.user.getManageableFacilityIds()|length }};
</script>

<div class="content-row">
    <h2>Billing Information <img src="/images/lock.png"></h2>
    <p>Please provide your credit card information on file using the form below. We keep your information safe using the latest technology in online encryption.</p>
    <p>Need to setup different payment methods for different facilities? You can add multiple payment methods after you've completed the signup.</p>
</div>

<form id="billing-form" action="/payment/update-customer" method="post">

    <input type="hidden" name="billable_entity_id" id="billable-entity-id" value="{{ view.billableEntityId }}" />
    <input type="hidden" name="account_id" id="account-id" value="{{ view.accountId }}" />
    <input type="hidden" name="payment_type" id="payment-type" value="Credit Card" />
    <input type="hidden" name="csrf_token" value="{{ view.csrf_token }}">

    <div class="input-row">
    
        <div class="form-horizontal">
            <div class="form-group">
                <label for="payment-type-nickname" class="col-lg-2 control-label">Payment Type Nickname</label>
                <div class="col-lg-10">
                    <input id="payment-type-nickname" name="payment_type_nickname" type="text" class="form-control" value="{{ view.paymentTypeNickname }}" required pattern=".{4,}" title="4 characters minimum" />
                    <p class="help-block">What do you want to call this payment type? <br /> This nickname will appear as your company name on all invoices and receipts. Our clients typically use the name of their facility/facilities and the payment method. Examples: "ABC Storage - Business Amex" or "AA Storage Properties - Company Visa"</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-row">
        <h2>Credit Card Information</h2>
    </div>
    
    <div class="input-row string">
    
        <div class="form-horizontal">
            <div class="form-group">
                <label for="credit-card-number" class="col-lg-2 control-label">Credit Card Number</label>
                <div class="col-lg-10">
                    <input id="credit-card-number" name="credit_card_number" type="text" class="form-control" value="{{ view.creditCardNumber }}" maxlength="16" required pattern=".{10,16}" title="please enter a valid credit card number"/>
                    <p class="help-block" id="credit-card-image"></p>
                    <p class="help-block">We accept Visa, Mastercard, Discover, and American Express and automatically detect your credit card type.</p>
                </div>
            </div>
        
            <div class="form-group">
                <label for="credit-card-name" class="col-lg-2 control-label">Name on Card</label>
                <div class="col-lg-10">
                    <input id="credit-card-name" name="credit_card_name" type="text" class="form-control" value="{{ view.creditCardName }}" required pattern=".{4,}" title="4 characters minimum" />
                </div>
            </div>
        
        
            <div class="form-group">
                <label class="col-lg-2 control-label">Expiration Date</label>
                <div class="col-lg-5">
                
                    {% set month = view.creditCardExpirationMonth ? view.creditCardExpirationMonth : "now"|date("m") %}
                
                    <select id="credit-card-expiration-month" name="credit_card_expiration_month" class="form-control" required pattern=".{2,2}" title="2 digit month">
                        {% set months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'] %}
                        {% for monthValue in months %}
                            <option value="{{ monthValue }}"{{ month == monthValue ? ' selected="selected"' : '' }}>{{ monthValue }}</option>
                        {% endfor %}
                    </select>
                    <br />
                </div>
                <div class="col-lg-5">
                    <select id="credit-card-expiration-year" name="credit_card_expiration_year" class="form-control" required pattern=".{4,4}" title="4 digit year">
                    
                        {% for i in range("now"|date("Y"), "now"|date("Y") + 9) %}
                            <option value="{{ i }}"{{ view.creditCardExpirationYear == i ? ' selected="selected"' : '' }}>{{ i }}</option>
                        {% endfor %}
                        
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-row">
        <h2>Billing Address</h2>
    </div>
    
    <div class="input-row">
        <div class="form-horizontal">

            <div class="checkbox">
                <label for="billing-address-same">
                    <input type="checkbox" name="billing_address_same" id="billing-address-same" />
                    My billing address is the same as my company address.
                </label>
            </div><br />
    
            <div class="form-group">
                <label for="address" class="col-lg-2 control-label">Address</label>
                <div class="col-lg-10">
                    <input id="address" name="address" type="text" class="form-control" value="{{ view.address }}" required pattern=".{3,}" title="3 characters minimum"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="city" class="col-lg-2 control-label">City</label>
                <div class="col-lg-10">
                    <input id="city" name="city" type="text" class="form-control" value="{{ view.city }}" required pattern=".{3,}" title="3 characters minimum"/>
                </div>
            </div>
        
            <div class="form-group">
                <label for="state" class="col-lg-2 control-label">State</label>
                <div class="col-lg-10">
                    <select id="state" name="state" class="form-control" required title="2 letter state code">
                        <option value=""></option>
                        {% set states = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'] %}
                        {% for state in states %}
                            <option value="{{ state }}"{{ view.state == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="zip" class="col-lg-2 control-label">Zip Code</label>
                <div class="col-lg-10">
                    <input id="zip" name="zip" type="text" class="form-control" value="{{ view.zip }}" required pattern=".{5,10}" title="5 digit zip code minimum"/>
                </div>
            </div>
        </div>
    </div>
    
    <div class="input-row">
        <div class="form-horizontal">
            <div class="form-group">
                <label for="emails" class="col-lg-2 control-label">Email Address(es)</label>
                <div class="col-lg-10">
                    <input id="emails" name="emails" type="text" class="form-control" value="{{ view.emails }}" required pattern=".{4,}" title="4 characters minimum"/>
                    <span class="help-inline">(comma separate for multiple)</span><br/>
                    <p class="help-block" id="cc_section">The email address/addresses entered here will receive an email copy of your receipt each time your credit card is charged</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="content-footer">
        <div class="pull-right">
            <img src="/images/loaders/large.gif" class="loading hide" alt="loading" />&nbsp;&nbsp;
            <a id="do_this_later" class="btn btn-default btn-lg" href="{{ url('features_add_first') }}">Do This Later</a>&nbsp;
            <input id="submit" type="submit" value="Next" class="btn btn-lg btn-primary" data-loading-text="Saving" />
            <p class="loading-text text-right hide"><br />We're securely saving your credit card.<br />This could take a minute.</p>
        </div>
        <a href="{{ view.backlink }}" class="btn btn-default btn-lg" id="back">Back</a>
    </div>
</form>
{% endif %}
{% endblock %}