{{ include('sitetop.html.twig') }}

<body data-path="{{ app.request.requestUri }}">

<div id="page-content-wrapper">
    <div id="minimal-header" class="ui fixed main menu header-bar">
        <a class="logo" href="/"><h1>MySpareFoot</h1></a>
    </div>
    <div class="master-layout">
        {% block content %}{% endblock %}
        <div id="featuresApp"></div>

        <footer id="page-footer">
            <div class="ui center aligned basic segment">
            <p>&copy; {{ "now"|date("Y") }} SpareFoot.  Got questions? Check out our <a href="https://support.sparefoot.com/hc/en-us" target="_blank">Help Center</a>.</p>
            </div>
        </footer>
    </div>
</div><!--/#page-content-wrapper-->


<script type="text/javascript">
    //Global variables used within the App
    App.authBearerToken = '{{ view.authBearerToken }}';
    App.servicesBaseUrl = '{{ view.servicesBaseUrl }}' + (location.port ? ':'+location.port : '');
</script>

<script src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/dist/init.js') }}"></script>

<script type="text/javascript">
    var CONFIG = { featureFlags: {} };
    // Feature Flags
    CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::UNIT_DELETE') }}'] = {{ call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::UNIT_DELETE')) ? 'true' : 'false' }};

    SF.tools.setReadOnly(CONFIG, {
        appUrl: '//{{ app.request.host }}',
        cdnUrl: '//{{ app.request.host }}',
        env: location.hostname.match('sparefoot.com') ? 'live' : location.hostname.match('localhost') ? 'dev' : 'staging'
    });

    {% set userFields = ['email', 'firstName', 'id', 'lastName', 'phone', 'pictureExt', 'username'] %}
    {% set user = view.loggedUser.toArray(userFields) %}
    var USER = {};
    SF.tools.setReadOnly(USER, {{ user|json_encode|raw }});

    {% set accountFields = ['accountId', 'bidType', 'cpa', 'infoString', 'integrationsString', 'locationId', 'name', 'numFacilities', 'phone', 'sfAccountId', 'status'] %}
    {% set account = view.loggedUser.getAccount().toArray(accountFields) %}
    var ACCOUNT = {};
    SF.tools.setReadOnly(ACCOUNT, {{ account|json_encode|raw }});
    App.ACCOUNT = ACCOUNT;
    App.FACILITY = {{ view.facility ? call_static('\\Sparefoot\\MyFootService\\Service\\Facility', 'toJson', view.facility)|raw : '{}' }};

    {% if view.authBookingToken %}
    App.AUTH_BOOKING_TOKEN = {{ view.authBookingToken }};
    {% endif %}
</script>

<script type="text/javascript">
        $(document).ready(() => {
            $('#contact-us-button').click(() => {
                $('#contact-us-modal').modal('show')
            })
        })
    </script>

{{ include('sitebottom.html.twig', {
    'userId': view.loggedUser.getId(),
    'loggedUser': view.loggedUser,
    'scripts': view.scripts
}) }}
