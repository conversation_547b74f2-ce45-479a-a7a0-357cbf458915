{{ include('sitetop.html.twig', {'head': '<link href="/css/signup.css" rel="stylesheet"/>'}) }}

<body style="min-width: 0;" class="signup">

<nav class="navbar navbar-fixed-top">
    {% if call_static('\\Genesis_Config_Server', 'isStaging') %}
        <div class="preheader preheader-staging"><span>Staging</span><div class="clear"></div></div>
    {% elseif call_static('\\Genesis_Config_Server', 'isDev') %}
        <div class="preheader preheader-dev"><span>Dev</span><div class="clear"></div></div>
    {% elseif call_static('\\Genesis_Config_Server', 'isLocal') %}
        <div class="preheader preheader-local"><span>Local</span><div class="clear"></div></div>
    {% endif %}

    <div class="ruler-header">
        <div class="navbar-header">
            <a href="/"><span class="brand"><img src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/images/mysparefootx2.png') }}" alt="MySpareFoot" class="mysparefootx2" /></span></a>
        </div>

        <div class="collapse navbar-collapse">
            <div class="nav pull-right">
                <span class="navbar-text">Need assistance? We're here to help. <strong>{{ constant('\\Sparefoot\\MyFootService\\Service\\Constants::SUPPORT_PHONE_NUMBER') }}</strong></span>
            </div>
        </div>
    </div>
</nav>

<div class="container">
    <div class="row">
        <div>
            <div class="panel panel-default">
                <div class="panel-body setup-content-panel-body">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
</div>

<div id="message-modal" class="modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">×</button>
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <a href="#" id="message-modal-ok" data-dismiss="modal" class="btn btn-primary">OK</a>
            </div>
        </div>
    </div>
</div>
<script src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/dist/init.js') }}"></script>
<script type="text/javascript">
    var CONFIG = {
        appUrl: '//{{ app.request.host }}',
        cdnUrl: '//{{ app.request.host }}'
    };
</script>

{{ include('sitebottom.html.twig', {
    'userId': view.loggedUser is defined ? view.loggedUser.getId() : 0,
    'loggedUser': view.loggedUser is defined ? view.loggedUser : null,
    'scripts': view.scripts
}) }}
