{# Account Overview Template #}

{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
<div class="ui container">
    <div class="ui header">
        <h1>Account Overview</h1>
        <div class="sub header">{{ view.account.getName() }}</div>
    </div>

    <div class="ui stackable two column grid">
        <div class="column">
            <div class="ui segment">
                <h3 class="ui header">Account Information</h3>
                <div class="ui list">
                    <div class="item">
                        <div class="content">
                            <div class="header">Account ID</div>
                            {{ view.account.getId() }}
                        </div>
                    </div>
                    <div class="item">
                        <div class="content">
                            <div class="header">Account Name</div>
                            {{ view.account.getName() }}
                        </div>
                    </div>
                    <div class="item">
                        <div class="content">
                            <div class="header">Status</div>
                            <div class="ui label {{ view.stats.accountStatus == constant('\\Genesis_Entity_Account::STATUS_LIVE') ? 'green' : 'yellow' }}">
                                {{ view.stats.accountStatus }}
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="content">
                            <div class="header">Bid Type</div>
                            {{ view.stats.bidType }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="column">
            <div class="ui segment">
                <h3 class="ui header">Statistics</h3>
                <div class="ui statistic">
                    <div class="value">{{ view.stats.totalFacilities }}</div>
                    <div class="label">Total Facilities</div>
                </div>
            </div>
        </div>
    </div>

    {% if view.facilities is defined and view.facilities|length > 0 %}
    <div class="ui segment">
        <h3 class="ui header">Facilities</h3>
        <div class="ui divided list">
            {% for facility in view.facilities %}
            <div class="item">
                <div class="content">
                    <div class="header">{{ facility.getName() }}</div>
                    <div class="description">
                        ID: {{ facility.getId() }} | 
                        Status: {{ facility.getActive() ? 'Active' : 'Inactive' }}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
