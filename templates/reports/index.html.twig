{% extends 'layout.html.twig' %}

{% block content %}
<div class="page-header">
    <h1>Reports</h1>
</div>

<h2>Performance</h2>
<ul>
    <li><a href="{{ url({'action': 'reservations'}, 'widget') }}">Booking Widget Reservations</a></li>
    <li><a href="{{ url({'action': 'analytics'}, 'widget') }}">Booking Widget Analytics</a></li>

{% if view.loggedUser.getAccount() and view.loggedUser.getAccount().getHostedSite() %}
    <li><a href="{{ url({'action': 'reservations'}, 'sites') }}">GeoPages Reservations</a></li>
    <li><a href="{{ url({'action': 'calls'}, 'sites') }}">GeoPages Calls</a></li>
{% endif %}

{% if view.loggedUser.hasTenantConnect() %}
    <li><a href="{{ url({'action': 'tenant-connect'}, 'reports') }}">Tenant Connect Calls</a></li>
{% endif %}
</ul>


{% if view.otherReports is not empty %}
    {% set categories = view.otherReports|keys|sort %}
    {% for category in categories %}
        <h2>{{ category }}</h2>
        <ul>
            {% for reportClassName, reportName in view.otherReports[category] %}
                <li><a href="{{ url({'action': 'custom'}, 'insights') }}?report={{ reportClassName }}">{{ reportName }}</a></li>
            {% endfor %}
        </ul>
    {% endfor %}
{% else %}
    <div class="ui error message">
        <p>No more reports available at the moment. Please check back later!</p>
    </div>
{% endif %}

{% endblock %}
