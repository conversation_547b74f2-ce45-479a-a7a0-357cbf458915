{% extends 'layout.html.twig' %}

{% block content %}
<div class="page-header">
    <h6>Report</h6>
    <h2>Tenant Connect Calls</h2>
</div>

<div class="ui secondary menu">
    {{ include('daterange.html.twig', {
        'action': '/reports/tenant-connect/',
        'trueDateRange': view.trueDateRange,
        'trueBeginDate': view.trueBeginDate,
        'trueEndDate': view.trueEndDate,
        'showExport': false,
    }) }}

    <a href="/reports/tenant-connect?export=true&true_date_range={{ view.trueDateRange|url_encode }}" class="ui button secondary pull-right" style="margin-right:0.5em;">
        <i class="file icon"></i> Export Spreadsheet</a>
</div>
<br />
<table class="ui table striped sortable cell-headers">
    <thead>
        <tr>
            <th></th>
            <th>Call Attempts</th>
            <th>Facility Answered</th>
            <th>Facility Responded to Call Prompts</th>
            <th>Connected to Tenant</th>
        </tr>
    </thead>
    <tbody>
    {% for facilityId, facData in view.callData %}
        {% set responseRate = (facData.facilityRespondedCalls / facData.totalCalls * 100)|round(2) %}
        <tr id="{{ facilityId }}">
            <td data-sort-value="{{ responseRate }}">
                <a class="report-tenant-connect-link" href="/reports/tenant-connect-detail/fid/{{ facilityId }}/true_date_range/{{ view.trueDateRange }}">
                    <strong>{{ facData.facilityTitle }}</strong>
                </a><br />
                <span class="minor">{{ responseRate }}% Response Rate</span>
            </td>
            <td>
                {{ facData.totalCalls }}
            </td>
            <td>
                {{ facData.answeredCalls }}
            </td>
            <td>
                {{ facData.facilityRespondedCalls }}
            </td>
            <td>
                {{ facData.connectedCalls }}
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>
{% endblock %}