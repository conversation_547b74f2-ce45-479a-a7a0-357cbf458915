{% extends 'layout.html.twig' %}

{% block content %}
<h6>Tenant Connect Calls</h6>
<h3>{{ view.facility.getTitle() }}</h3><br />

<div class="toolbar">
    {{ include('daterange.html.twig', {
        'action': '/reports/tenant-connect-detail/fid/' ~ view.facility.getId(),
        'trueDateRange': view.trueDateRange,
        'trueBeginDate': view.trueBeginDate,
        'trueEndDate': view.trueEndDate,
        'showExport': false,
    }) }}
    <a href="/reports/tenant-connect-detail/fid/{{ view.facility.getId() }}?export=true&true_date_range={{ view.trueDateRange|url_encode }}" class="btn btn-default pull-right" style="margin-right:0.5em;"><i class="fa fa-file"></i> Export Spreadsheet</a>
</div>
<br />
<table class="table table-striped data-grid">
    <thead>
        <tr>
            <th>Call</th>
            <th>Customer</th>
            <th>Call Status</th>
            <th>Listen to Call</th>
        </tr>
    </thead>
    <tbody>
    {% set i = 0 %}
    {% for tcCall in view.callData %}
        {% set i = i + 1 %}
     	<tr id="{{ tcCall.getId() }}">
     		<td>
                <span class="minor">{{ tcCall.stringDate() }}</span><br />
                <strong>{{ tcCall.getBooking().stringPhone() }}</strong>
     		</td>
     		<td>
                 {{ tcCall.getBooking().getFirstName()|trim|title }} {{ tcCall.getBooking().getLastName()|trim|title }}<br />
     		     {% if tcCall.getBooking().getUser().getEmail() == '<EMAIL>' %}
     		         No email
     		     {% else %}
         		     <a href="mailto:{{ tcCall.getBooking().getUser().getEmail() }}">{{ tcCall.getBooking().getUser().getEmail() }}</a>
     		     {% endif %}
     		</td>
     		<td>{{ tcCall.stringStatus() }}</td>
     		<td>
     			{% if tcCall.getRecordingUrl() %}
     				<a class="ui button" href="{{ tcCall.getRecordingUrl() }}" target="_blank"><i class="fa fa-play"></i></a>
     			{% else %}
     				No audio
     			{% endif %}
     		</td>
    	</tr>
    {% endfor %}
    </tbody>
</table>

{% if i == 0 %}
    <p>No call data available for the time period.</p>
{% endif %}
{% endblock %}