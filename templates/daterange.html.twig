<div id="reportrange" class="ui secondary button {{ (view.pullRight is defined and view.pullRight == false) ? '' : 'pull-right' }}">
    <i class="fa fa-calendar"></i>
    <span>{{ trueDateRange }}</span> <b class="caret"></b>
</div>
<form method="get" action="{{ action }}" id="true_date_range_form" name="true_date_range_form" class="hidden">
    <input type="hidden" id="true_date_range" name="true_date_range" value="{{ trueDateRange }}" />
    <input type="hidden" name="limit" value="{{ view.limit|default(null) }}" />
    <input type="hidden" name="page" value="{{ view.page|default(null) }}" />
    {% if view.facility is defined %}
    <input type="hidden" name="fid" value="{{ view.facility.getID() }}" />
    {% endif %}
</form>

{% if showExport %}
    <form class="pull-right" method="get" action="{{ action }}" id="true_date_range_export_form" name="true_date_range_export_form" style="margin-right:1em;">
        <input type="hidden" name="export_to_excel" value="1" />
        <input type="hidden" name="true_date_range" value="{{ trueDateRange }}" />
        <button type="submit" class="ui secondary button"><i class="icon file"></i> Export Spreadsheet</button>
    </form>
{% endif %}