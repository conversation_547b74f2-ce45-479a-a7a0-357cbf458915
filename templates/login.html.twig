{{ include('sitetop.html.twig', {'darkTheme': true}) }}
    <body data-path="/login">
        <div id="upper-ruler"></div>
        {% block content %}{% endblock %}

        <script src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/dist/init.js') }}"></script>
        <script type="text/javascript">
            var CONFIG = {
                appUrl: '//{{ app.request.host }}',
                cdnUrl: '//{{ app.request.host }}'
            };
        </script>

{{ include('sitebottom.html.twig', {'userId': 0, 'scripts': view.scripts}) }}

