{% if unitIds|length == 0 %}
    <h3 id="unit-header">Add Unit</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
{% elseif view.facility.canGroupUnits() %}
    <h3 id="unit-header">Edit Units</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
    <h4>{{ view.unit.classType }}</h4>

    <p>
        {% for i in 0..(view.units|length - 1) %}
            <a class="edit-single-unit" href="#{{ view.units[i].unitIds[0] }}">{{ view.units[i].unitName }}</a>{% if i < (view.units|length - 1) and i > 0 %},{% endif %}
        {% endfor %}
    </p>
{% else %}
    <h3>Edit Unit</h3>
    <p>Set the appropriate attributes and remember to save your changes.</p>
{% endif %}
<br />