{# Build array of unit IDs from units #}
{% set unitIds = [] %}
{% for singleUnit in view.units %}
    {% for unitId in singleUnit.unitIds %}
        {% set unitIds = unitIds|merge([unitId]) %}
    {% endfor %}
{% endfor %}

<h3>Edit Units</h3>
<p>You're editing multiple units at a time. Check the boxes next to the attributes you want to change for all the units you've selected.</p><br />

<form class="form-horizontal group2_multi" id="unit-form">
    <p class="alert alert-danger hide"></p>

    <table class="table multiple-units-table">
        <tr>
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_unit_type" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="unit-type">Space Type</label>
                    <div class="col-md-10">
                        <select id="unit-type" name="unit_type" class="form-control">
                          <option></option>
                          <option value="14">Self-Storage Unit</option>
                          <option value="4">Parking Space</option>
                          <option value="10">Office Space / Warehouse</option>
                          <option value="15">Wine Storage</option>
                          <option value="13">Locker</option>
                        </select>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="unit-dimensions-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_unit_dimensions" id="change-unit-dimensions" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Height</label>
                    <div class="col-md-10 input-group">
                        <input type="text" class="form-control" name="height" id="height" value="" /><span class="input-group-addon">ft</span>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="amenities-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_amenities" id="change-amenities" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Amenities</label>
                    <div class="col-md-10">
                        <label for="power" class="checkbox"><input type="checkbox" name="power" id="power" />Power Outlet</label>
                        <label for="alarm" class="checkbox"><input type="checkbox" name="alarm" id="alarm" />Alarm</label>
                        <label for="unit-lights" class="checkbox"><input type="checkbox" name="unit_lights" id="unit-lights" />Light in Unit</label>
                        <label for="shelves-in-unit" class="checkbox"><input type="checkbox" name="shelves_in_unit" id="shelves-in-unit" />Shelves in Unit</label>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="heating-cooling-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_heating_cooling" id="change-heating-cooling" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Heating &amp; Cooling</label>
                    <div class="col-md-10">
                        <label for="climate-controlled" class="checkbox"><input type="checkbox" name="climate_controlled" id="climate-controlled" />Climate Controlled</label>
                        <label for="humidity-controlled" class="checkbox"><input type="checkbox" name="humidity_controlled" id="humidity-controlled" />Humidity Controlled</label>
                        <label for="air-cooled" class="checkbox"><input type="checkbox" name="air_cooled" id="air-cooled" />Air Cooled</label>
                        <label for="heated" class="checkbox"><input type="checkbox" name="heated" id="heated" />Heated</label>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="access-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_access" id="change-access" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Access</label>
                    <div class="col-md-10">

                        <label for="twenty-four-hour-access" class="checkbox"><input type="checkbox" name="twenty_four_hour_access" id="twenty-four-hour-access" />24 Hour Access</label>
                        <label for="stacked" class="checkbox" id="stacked-span"><input type="checkbox" name="stacked" id="stacked" />Stacked Space</label>
                        <label for="basement" class="checkbox"><input type="checkbox" name="basement" id="basement" />Underground Level</label>
                        <label for="parking-warehouse" class="checkbox"><input type="checkbox" name="parking_warehouse" id="parking-warehouse" />Parking Warehouse</label>
                        <label for="pull-thru" class="checkbox"><input type="checkbox" name="pull_thru" id="pull-thru" />Pull-Thru</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-10 col-md-offset-2">
                        Is the space accessible from the outside?
                        <div>
                            <label for="outside" class="radio-inline"><input type="radio" name="outdoor_access" id="outside" value="1" checked="checked" />&nbsp;Yes</label>&nbsp;
                            <label for="inside" class="radio-inline"><input type="radio" name="outdoor_access" id="inside" value="0" />&nbsp;No</label>
                        </div>
                    </div>
                </div>

                {% include 'inventory/unit/fields/drive_up.html.twig' with {'unit': null, 'disabled': false} %}

                <div class="form-group">
                    <div class="col-md-10 col-md-offset-2">
                        Available for vehicle storage?<br />
                        <label for="vehicle-yes" class="radio"><input type="radio" name="vehicle" id="vehicle-yes" value="1" />Yes, for storage or vehicles</label>
                        <label for="vehicle-only" class="radio"><input type="radio" name="vehicle" id="vehicle-only" value="only" />Yes, for vehicles only</label>
                        <label for="vehicle-no" class="radio"><input type="radio" name="vehicle" id="vehicle-no" value="0" checked="checked" />No</label>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="door-type-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_door_type" id="change-door-type" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Door Type</label>
                    <div class="col-md-10">
                        <label for="door-rollup" class="radio"><input type="radio" name="door_type" id="door-rollup" value="ROLL_UP" />Roll-up Door</label>
                        <label for="door-swing" class="radio"><input type="radio" name="door_type" id="door-swing" value="SWING" />Swing Door</label>
                        <label for="door-none" class="radio"><input type="radio" name="door_type" id="door-none" value="NONE" />None</label>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="floor-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_floor" id="change-floor" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="floor">Floor</label>
                    <div class="col-md-10 input-group">
                        <span class="input-group-addon">#</span>
                        <input type="text" name="floor" id="floor" class="form-control" value="" />
                    </div>
                </div>
            </td>
        </tr>
        <tr id="location-row">
            <td>
                <input type="checkbox" class="multiple-units-checkbox" name="change_location" id="change-location" />
            </td>
            <td class="disabled-cell">
                <div class="form-group">
                    <label class="col-md-2 control-label">Location</label>
                    <div class="col-md-10">
                        <label for="location-covered" class="radio-inline"><input type="radio" name="covered" id="location-covered" value="1" />Covered</label>
                        <label for="location-uncovered" class="radio-inline"><input type="radio" name="covered" id="location-uncovered" value="0" />Uncovered</label>
                        <label for="premium" class="checkbox"><input type="checkbox" name="premium" id="premium" />Premium Location</label>
                        <label for="ada" class="checkbox"><input type="checkbox" name="ada" id="ada" /><abbr title="Americans with Disabilities Act">ADA</abbr> Accessible</label>
                    </div>
                </div>
            </td>
        </tr>
    </table>



    <div class="form-actions">
        <!--<div class="btn-group pull-right">
            <a class="btn btn-default btn-lg disabled previous-unit"><i class="fa fa-chevron-left"></i></a>
            <a class="btn btn-default btn-lg disabled next-unit"><i class="fa fa-chevron-right"></i></a>
        </div>-->
        {% for unitId in unitIds %}
            <input type="hidden" name="unit_ids[]" value="{{ unitId }}" />
        {% endfor %}
        <div class="col-md-offset-2">
            <a href="/inventory/units" class="ui basic button" id="unit-cancel">Cancel</a>&nbsp;
            <input type="submit" class="ui primary button disabled" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="unit-save" disabled="disabled" />
            <img src="/images/loaders/small.gif" class="loading hide" />
        </div>
    </div>

</form>
