{# Set up variables #}
{% set unitIds = view.unitIds %}
{% set unit = view.units[0] %}

{# Include header partial #}
{% include 'inventory/unit/header-partial.html.twig' with {'unit': unit, 'units': view.units} %}

<form class="form-horizontal group3_single" id="unit-form">
    <p class="alert alert-danger hide"></p>

    <input type="hidden" name="change_unit_type" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="unit-type">Space Type</label>
        <div class="col-md-10 faux-control-label">
            {% if unit.typeNum == constant('\\Genesis_Entity_StorageSpace::TYPE_PARKING_SPACE') %}
                Parking Space
            {% elseif unit.typeNum == constant('\\Genesis_Entity_StorageSpace::TYPE_WORKSPACE') %}
                Office Space / Warehouse
            {% elseif unit.typeNum == constant('\\Genesis_Entity_StorageSpace::TYPE_WINE') %}
                Wine Storage
            {% elseif unit.typeNum == constant('\\Genesis_Entity_StorageSpace::TYPE_LOCKER') %}
                Locker
            {% elseif unit.typeNum == constant('\\Genesis_Entity_StorageSpace::TYPE_OUTDOOR') %}
                Outdoor Space
            {% else %}
                Self-Storage Unit
            {% endif %}
        </div>
    </div>

    <input type="hidden" name="change_unit_dimensions" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Dimensions</label>
        <div class="col-md-10">
            <div class="row">
                <div class="col-sm-4">
                    <div class="faux-control-label">{{ unit.width }} ft wide</div>
                </div>
                <div class="col-sm-4">
                    <div class="faux-control-label">{{ unit.length }} ft long</div>
                </div>
                <div class="col-sm-4">
                    <p class="input-group">
                        <input type="text" class="form-control" name="height" id="height" value="{{ unit.height }}"  placeholder="Height" /><span class="input-group-addon">ft</span>
                    </p>
                </div>
            </div>
        </div>
    </div>


    <input type="hidden" name="change_standard_rate" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="standard-rate">Standard Rate</label>
        <div class="col-md-10 faux-control-label">
            ${{ unit.standardRate }}
        </div>
    </div>

    {% if unit.hasDiscountSpecial %}
        <div class="form-group">
            <label class="col-md-2 control-label">Customer Rate</label>
            <div class="col-md-10">
                ${{ unit.sparefootPrice }}
            </div>
        </div>
    {% endif %}

    <hr />

    <input type="hidden" name="change_amenities" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Amenities</label>
        <div class="col-md-10">
            <label for="power" class="checkbox"><input type="checkbox" name="power" id="power" {{ unit.power ? 'checked="checked" ' : '' }}disabled="disabled" />Power Outlet</label>
            <label for="alarm" class="checkbox"><input type="checkbox" name="alarm" id="alarm" {{ unit.alarm ? 'checked="checked" ' : '' }}disabled="disabled" />Alarm</label>
            <label for="unit-lights" class="checkbox"><input type="checkbox" name="unit_lights" id="unit-lights" {{ unit.unitLights ? 'checked="checked" ' : '' }}/>Light in Unit</label>
            <label for="shelves-in-unit" class="checkbox"><input type="checkbox" name="shelves_in_unit" id="shelves-in-unit" {{ unit.shelvesInUnit ? 'checked="checked" ' : '' }}/>Shelves in Unit</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_heating_cooling" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Heating &amp; Cooling</label>
        <div class="col-md-10">
            <label for="climate-controlled" class="checkbox"><input type="checkbox" name="climate_controlled" id="climate-controlled" {{ unit.climateControlled ? 'checked="checked" ' : '' }}disabled="disabled" />Climate Controlled</label>
            <label for="humidity-controlled" class="checkbox"><input type="checkbox" name="humidity_controlled" id="humidity-controlled" {{ unit.humidityControlled ? 'checked="checked" ' : '' }}/>Humidity Controlled</label>
            <label for="air-cooled" class="checkbox"><input type="checkbox" name="air_cooled" id="air-cooled" {{ unit.airCooled ? 'checked="checked" ' : '' }}/>Air Cooled</label>
            <label for="heated" class="checkbox"><input type="checkbox" name="heated" id="heated" {{ unit.heated ? 'checked="checked" ' : '' }}/>Heated</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_access" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Access</label>
        <div class="col-md-10">

            {% if view.facility.getTwentyFourHourAccess() and constant('\\Genesis_Entity_FacilityData::SPECIFIC_UNITS_24_HOUR_ACCESS') in view.facility.getTwentyFourHourAccessSupplemental() %}
                <label for="twenty-four-hour-access" class="checkbox"><input type="checkbox" name="twenty_four_hour_access" id="twenty-four-hour-access" {{ unit.twentyFourHourAccess ? 'checked="checked" ' : '' }}/>24 Hour Access</label>
            {% endif %}

            <label for="stacked" class="checkbox" id="stacked-span"><input type="checkbox" name="stacked" id="stacked" {{ unit.stacked ? 'checked="checked" ' : '' }}/>Stacked Space</label>

            <label for="basement" class="checkbox"><input type="checkbox" name="basement" id="basement" {{ unit.basement ? 'checked="checked" ' : '' }}/>Underground Level</label>
            <label for="parking-warehouse" class="checkbox"><input type="checkbox" name="parking_warehouse" id="parking-warehouse" {{ unit.parkingWarehouse ? 'checked="checked" ' : '' }}/>Parking Warehouse</label>
            <label for="pull-thru" class="checkbox"><input type="checkbox" name="pull_thru" id="pull-thru" {{ unit.pullThru ? 'checked="checked" ' : '' }}/>Pull-Thru</label>
        </div>
    </div>

    {% include 'inventory/unit/fields/drive_up.html.twig' with {'unit': unit, 'disabled': false} %}

    <div class="form-group">
        <div class="col-md-10 col-md-offset-2">
            How do tenants get to this unit?<br />
                <label for="inside" class="radio"><input type="radio" name="outdoor_access" id="inside" value="0" {{ (unit.outdoorAccess is not null and not unit.outdoorAccess) ? 'checked="checked" ' : '' }}/>&nbsp;From an indoor hallway</label>
                <label for="outside" class="radio"><input type="radio" name="outdoor_access" id="outside" value="1" {{ unit.outdoorAccess ? 'checked="checked" ' : '' }}/>&nbsp;From outside </label>
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-10 col-md-offset-2">
            Available for vehicle storage?<br />
            <label for="vehicle-yes" class="radio"><input type="radio" name="vehicle" id="vehicle-yes" value="yes" {{ (unit.vehicle == 'yes') ? 'checked="checked" ' : '' }}/>Yes, for storage or vehicles</label>
            <label for="vehicle-only" class="radio"><input type="radio" name="vehicle" id="vehicle-only" value="only" {{ (unit.vehicle == 'only') ? 'checked="checked" ' : '' }}/>Yes, for vehicles only</label>
            <label for="vehicle-no" class="radio"><input type="radio" name="vehicle" id="vehicle-no" value="0" {{ (unit.vehicle == false) ? 'checked="checked" ' : '' }}/>No</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_door_type" value="1" />
    <div class="form-group" id="trdoor">
        <label class="col-md-2 control-label">Door Type</label>
        <div class="col-md-10">
            <label for="door-rollup" class="radio"><input type="radio" name="door_type" id="door-rollup" value="ROLL_UP" {{ (unit.doorType == 'roll_up') ? 'checked="checked" ' : '' }}/>&nbsp;Roll-up Door</label>
            <label for="door-swing" class="radio"><input type="radio" name="door_type" id="door-swing" value="SWING" {{ (unit.doorType == 'swing') ? 'checked="checked" ' : '' }}/>&nbsp;Swing Door</label>
            <label for="door-none" class="radio"><input type="radio" name="door_type" id="door-none" value="NONE" {{ (unit.doorType == 'none') ? 'checked="checked" ' : '' }}/>&nbsp;None</label>
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_floor" value="0" />
    <div class="form-group">
        <label class="col-md-2 control-label" for="floor">Floor</label>
        <div class="col-md-10 faux-control-label">
            #{{ unit.floor }}
        </div>
    </div>

    <hr />

    <input type="hidden" name="change_covered" value="1" />
    <div class="form-group">
        <label class="col-md-2 control-label">Location</label>
        <div class="col-md-10">
            <label for="location-covered" class="radio-inline"><input type="radio" name="covered" id="location-covered" value="1" {{ unit.covered ? 'checked="checked" ' : '' }}/>Covered</label>
            <label for="location-uncovered" class="radio-inline"><input type="radio" name="covered" id="location-uncovered" value="0" {{ (unit.covered is not null and not unit.covered) ? 'checked="checked" ' : '' }}/>Uncovered</label>

            <label for="premium" class="checkbox"><input type="checkbox" name="premium" id="premium" {{ unit.premium ? 'checked="checked" ' : '' }}/>Premium Location</label>
            <label for="ada" class="checkbox"><input type="checkbox" name="ada" id="ada" {{ unit.ada ? 'checked="checked" ' : '' }}/><abbr title="Americans with Disabilities Act">ADA</abbr> Accessible</label>
        </div>
    </div>

    <div class="form-actions">
        <!--<div class="btn-group pull-right">
            <a class="btn btn-default btn-lg disabled previous-unit"><i class="fa fa-chevron-left"></i></a>
            <a class="btn btn-default btn-lg disabled next-unit"><i class="fa fa-chevron-right"></i></a>
        </div>-->
        {% for unitId in unitIds %}
            <input type="hidden" name="unit_ids[]" value="{{ unitId }}" />
        {% endfor %}
        <div class="right">
            <a href="/inventory/units" class="ui basic button" id="unit-cancel">Cancel</a>&nbsp;
            <input type="submit" class="ui primary button" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="unit-save" />
            <img src="/images/loaders/small.gif" class="loading hide" />
        </div>
    </div>

</form>
