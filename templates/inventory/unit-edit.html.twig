<form id="unit-form" class="form-horizontal consolidated-unit">
    <h3 class="form-title"></h3>
    <p class="form-title-tag"></p>

    <p class="alert alert-danger hide"></p>

    <!-- typeNum - unit type. 14:Self-Storage Unit, 4:Parking Space, 10:Office Space / Warehouse, 15:Wine Storage, 13:Locker -->
    <div class="form-group field" data-label="unitType">
        <div class="col-md-2">
            <input type="checkbox" name="changeUnitType" class="update-checkbox"/>
            <label class="control-label">Space Type</label>
        </div>
        <div class="col-md-10">
            <select name="typeNum" class="form-control" id="unit-type">
                <option value="14" selected="selected">Self-Storage Unit</option>
                <option value="4">Parking Space</option>
                <option value="10">Office Space / Warehouse</option>
                <option value="15">Wine Storage</option>
                <option value="13">Locker</option>
            </select>
        </div>
    </div>

    <!-- width, length, height -->
    <div class="form-group field" data-label="unitDimensions">
        <div class="col-md-2">
            <input type="checkbox" name="change-unit-dimensions" class="update-checkbox"/>
            <label class="control-label">Dimensions</label>
        </div>
        <div class="col-md-10">
            <div class="row">
                <div class="col-sm-4">
                    <div class="input-group">
                        <input type="text" min="0" class="form-control" name="width" placeholder="Width"/><span class="input-group-addon">ft</span>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="input-group">
                        <input type="text" min="0" class="form-control" name="length" placeholder="Length"/><span class="input-group-addon">ft</span>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="height" placeholder="Height"/><span class="input-group-addon">ft</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- standardRate -->
    <div class="form-group field" data-label="standardRate">
        <div class="col-md-2">
            <input type="checkbox" name="change-standard-rate" class="update-checkbox"/>
            <label class="control-label" for="standardRate">Standard Rate</label>
        </div>
        <div class="col-md-10">
            <div class="input-group">
                <span class="input-group-addon">$</span><input type="text" min="0" name="standardRate" id="standard-rate" class="form-control"/>
            </div>
        </div>
    </div>

    <!-- sparefootPrice - customer rate -->
    <div class="form-group field" data-label="sparefootPrice">
        <div class="col-md-2">
            <label class="control-label">Customer Rate</label>
        </div>
        <div class="col-md-10">
            $<span data-name="sparefootPrice"></span>
        </div>
    </div>

    <!-- power, alarm, unitLights, shelvesInUnit. Possible values: true, false -->
    <div class="form-group field" data-label="amenities">
        <div class="col-md-2">
            <input type="checkbox" name="change-amenities" class="update-checkbox"/>
            <label class="control-label">Amenities</label>
        </div>
        <div class="col-md-10">
            <label for="power" class="checkbox"><input type="checkbox" name="power"/>Power Outlet</label>
            <label for="alarm" class="checkbox"><input type="checkbox" name="alarm"/>Alarm</label>
            <label for="unitLights" class="checkbox"><input type="checkbox" name="unitLights"/>Light in Unit</label>
            <label for="shelvesInUnit" class="checkbox"><input type="checkbox" name="shelvesInUnit"/>Shelves in Unit</label>
        </div>
    </div>

    <!-- climateControlled, humidityControlled, airCooled, heated. Possible values: true, false -->
    <div class="form-group field" data-label="heatingCooling">
        <div class="col-md-2">
            <input type="checkbox" name="change-heating-cooling" class="update-checkbox"/>
            <label class="control-label">Heating &amp; Cooling</label>
        </div>
        <div class="col-md-10">
            <label for="climateControlled" class="checkbox"><input type="checkbox" name="climateControlled"/>Climate Controlled</label>
            <label for="humidityControlled" class="checkbox"><input type="checkbox" name="humidityControlled"/>Humidity Controlled</label>
            <label for="airCooled" class="checkbox"><input type="checkbox" name="airCooled"/>Air Cooled</label>
            <label for="heated" class="checkbox"><input type="checkbox" name="heated"/>Heated</label>
        </div>
    </div>

    <!-- twentyFourHourAccess, stacked, basement, parkingWarehouse, pullThru. Possible values: true, false -->
    <div class="form-group field" data-label="access">
        <div class="col-md-2">
            <input type="checkbox" name="change-access" class="update-checkbox"/>
            <label class="control-label">Access</label>
        </div>
        <div class="col-md-10">
            <label for="twentyFourHourAccess" class="checkbox"><input type="checkbox" name="twentyFourHourAccess"/>24 Hour Access</label>
            <label for="stacked" class="checkbox"><input type="checkbox" name="stacked"/>Stacked Space</label>
            <label for="basement" class="checkbox"><input type="checkbox" name="basement"/>Underground Level</label>
            <label for="parkingWarehouse" class="checkbox"><input type="checkbox" name="parkingWarehouse"/>Parking Warehouse</label>
            <label for="pullThru" class="checkbox"><input type="checkbox" name="pullThru"/>Pull-Thru</label>
        </div>

        <div class="col-md-10 col-md-offset-2">
            <p>Is the space accessible from the outside?<br/>
            <label class="radio-inline">
            <input type="radio" name="outdoorAccess" value="true"/>
            Yes</label>

            <label class="radio-inline">
            <input type="radio" name="outdoorAccess" value="false"/>
            No</label></p>
        </div>

        <div class="col-md-10 col-md-offset-2">
            <p>Can you drive up to the space?<br/>
            <label class="radio-inline">
            <input type="radio" name="driveUp" value="true"/>
            Yes</label>

            <label class="radio-inline">
            <input type="radio" name="driveUp" value="false"/>
            No</label></p>
        </div>
    </div>

    <!-- vehicle. Possible values: yes, true, false -->
    <div class="form-group field" data-label="vehicle">
        <div class="col-md-2">
            <input type="checkbox" name="change-vehicle" class="update-checkbox"/>
            <label class="control-label">Available for vehicle storage?</label>
        </div>
        <div class="col-md-10">
            <label for="vehicleYes" class="radio">
            <input type="radio" name="vehicle" value="yes"/>
            Yes</label>

            <label for="vehicleOnly" class="radio">
            <input type="radio" name="vehicle" value="only"/>
            Yes, for vehicles only</label>

            <label for="vehicleNo" class="radio">
            <input type="radio" name="vehicle" value="false"/>
            No</label>
        </div>
    </div>

    <!-- doorType. Possible values: rollup, swing, none -->
    <div class="form-group field" data-label="doorType">
        <div class="col-md-2">
            <input type="checkbox" name="change-door-type" class="update-checkbox"/>
            <label class="control-label">Door Type</label>
        </div>
        <div class="col-md-10">
            <label for="doorRollup" class="radio">
            <input type="radio" name="doorType" value="roll_up"/>
            Roll-up Door</label>

            <label for="doorSwing" class="radio">
            <input type="radio" name="doorType" value="swing"/>
            Swing Door</label>

            <label for="doorNone" class="radio">
            <input type="radio" name="doorType" value="none"/>
            None</label>
        </div>
    </div>

    <!-- lotType. Possible values: gravel, grass, asphalt, other -->
    <div class="form-group field" data-label="lotType">
        <div class="col-md-2">
            <input type="checkbox" name="change-lot-type" class="update-checkbox"/>
            <label class="control-label">Lot Type</label>
        </div>
        <div class="col-md-10">
            <label for="lotTypeGravel" class="radio">
            <input type="radio" name="lotType" value="gravel"/>
            Gravel</label>

            <label for="lotTypeGrass" class="radio">
            <input type="radio" name="lotType" value="grass"/>
            Grass</label>

            <label for="lotTypeAsphalt" class="radio">
            <input type="radio" name="lotType" value="asphalt"/>
            Asphalt</label>

            <label for="lotTypeOther" class="radio">
            <input type="radio" name="lotType" value="other"/>
            Other</label>
        </div>
    </div>

    <!-- floor -->
    <div class="form-group field" data-label="floor">
        <div class="col-md-2">
            <input type="checkbox" name="change-floor" class="update-checkbox"/>
            <label class="control-label" for="floor">Floor</label>
        </div>
        <div class="col-md-10">
            <div class="input-group">
                <span class="input-group-addon">#</span>
                <input type="text" name="floor" class="form-control"/>
            </div>
        </div>
    </div>

    <!-- covered. Possible values: 1, 0. premium, ada. Possible values: true, false -->
    <div class="form-group field" data-label="location">
        <div class="col-md-2">
            <input type="checkbox" name="change-location" class="update-checkbox"/>
            <label class="control-label">Location</label>
        </div>
        <div class="col-md-10">
            <label for="locationCovered" class="radio-inline">
            <input type="radio" name="covered" value="1"/>
            Covered</label>

            <label for="locationUncovered" class="radio-inline">
            <input type="radio" name="covered" value="0"/>
            Uncovered</label>

            <label for="premium" class="checkbox">
            <input type="checkbox" name="premium"/>
            Premium Location</label>

            <label for="ada" class="checkbox">
            <input type="checkbox" name="ada"/>
            <abbr title="Americans with Disabilities Act">ADA</abbr> Accessible</label>
        </div>
    </div>

    <div class="form-actions">
        <div class="right">
            <a href="/inventory/units" class="ui basic button" id="unit-cancel">Cancel</a>
            <input type="submit" class="ui primary button" data-loading-text="Saving" data-complete-text="Saved" value="Save" id="unit-save" />
            <img src="/images/loaders/small.gif" class="loading hide" />
        </div>
    </div>
</form>
