{# Build array of unit IDs from units #}
{% set unitIds = [] %}
{% for singleUnit in view.units %}
    {% for unitId in singleUnit.unitIds %}
        {% set unitIds = unitIds|merge([unitId]) %}
    {% endfor %}
{% endfor %}

{# Set up view payload #}
{% set viewPayload = {
    'units': view.units,
    'facility': view.facility,
    'unitIds': unitIds
} %}

{# Get source ID #}
{% set sourceId = view.facility.getCorporation().getSourceId() %}

{# Determine partial template name based on unit count and source ID #}
{% set partialName = '' %}
{% if view.unitGroupCount > 1 %}
    {% if sourceId == constant('\\Genesis_Entity_Source::ID_MANUAL') %}
        {% set partialName = 'inventory/unit/manual_multi.html.twig' %}
    {% elseif sourceId == constant('\\Genesis_Entity_Source::ID_SITELINK') %}
        {% set partialName = 'inventory/unit/group3_multi.html.twig' %}
    {% elseif sourceId == constant('\\Genesis_Entity_Source::ID_CENTERSHIFT') or sourceId == constant('\\Genesis_Entity_Source::ID_DOMICO') %}
        {% set partialName = 'inventory/unit/group2_multi.html.twig' %}
    {% else %}
        <h2>Error.</h2><p>You cannot edit more than one unit at a time.</p>
    {% endif %}
{% else %}
    {% if sourceId == constant('\\Genesis_Entity_Source::ID_MANUAL') %}
        {% set partialName = 'inventory/unit/manual_single.html.twig' %}
    {% elseif sourceId == constant('\\Genesis_Entity_Source::ID_SITELINK') %}
        {% set partialName = 'inventory/unit/group3_single.html.twig' %}
    {% elseif sourceId == constant('\\Genesis_Entity_Source::ID_CENTERSHIFT') or sourceId == constant('\\Genesis_Entity_Source::ID_DOMICO') %}
        {% set partialName = 'inventory/unit/group2_single.html.twig' %}
    {% else %}
        {% set partialName = 'inventory/unit/group1_single.html.twig' %}
    {% endif %}
{% endif %}

{# Render the template #}
{% if partialName %}
    {% include partialName with viewPayload %}
{% endif %}

<script type="text/javascript">
    var controller = '/features/';
</script>
