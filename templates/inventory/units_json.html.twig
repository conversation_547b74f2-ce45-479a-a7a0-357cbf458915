<script type="text/javascript">
    var sourceType = '{{ sourceType }}';
    var facilityId = '{{ facility.getId() }}';

    {% if inventory %}
        {% set unitInfo = {} %}
        {% for key, unit in inventory %}
            {% set unitInfo = unitInfo|merge({(key): {
                "unitIds": unit.unitIds,
                "active": unit.active
            }}) %}
        {% endfor %}
        
        var units = {{ json_encode(unitInfo)|raw }};
    {% else %}
        var units = [];
    {% endif %}
</script>
