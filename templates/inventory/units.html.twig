{# Inventory Index Template #}
{% extends 'layout.html.twig' %}
{% block title %}{{ view.title }}{% endblock %}
{% block content %}
{% set facility = view.facility %}
{% include 'facility/header.html.twig' with {
    'facility': facility,
    'view': view
} %}

{% include 'facility/subnav.html.twig' with {
    'selected': 'inventory',
    'loggedUser': view.loggedUser,
    'facility': facility,
    'view': view
} %}

<div id="units-actions-alert" style="margin-top:5px;display:none;">
    <div class="ui warning message">
        <p><strong>Select the checkbox next to each unit</strong> to add a discount, promo, or free item.</p>
    </div>
</div>

<div class="ui secondary menu">
    <div class="form-inline" id="selected-unit-nav">
        <div class="ui buttons pull-right">
            <a class="ui button disabled previous-unit"><i class="fa fa-chevron-left"></i></a>
            <a class="ui button disabled next-unit"><i class="fa fa-chevron-right"></i></a>
        </div>
    </div>
<div id="units-actions" class="form-inline {{ view.inventory is defined and view.inventory ? '' : 'hide' }}">

<div class="ui buttons">

<!--
$('#unit-actions .ui.dropdown').dropdown({action:'hide'});
-->
{% if view.sourceType is defined and view.sourceType == constant('\\Genesis_Entity_Source::ID_MANUAL') %}
    <div class="ui secondary button" id="add-unit"><i class="add icon"></i> Add Unit </div>
{% endif %}
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="tag icon"></i>
        Discounts
        <i class="dropdown icon"></i>
        <div class="menu">
        {% for special in view.defaultSpecials %}
            {% if special.isDiscount() %}
            <div class="item special-menu-option"><a id="special-{{ special.getId() }}">{{ special.getString() }}</a></div>
            {% endif %}
        {% endfor %}
        {% if view.customDiscounts and view.customDiscounts|length > 0 %}
            <div class="divider js-custom-discounts-start"></div>
            {% for special in view.customDiscounts %}
                <div class="item special-menu-option"><a id="special-{{ special.id }}">{{ special.name }}</a></div>
            {% endfor %}
        {% else %}
            <div class="divider js-custom-discounts-start" style="display:none;"></div>
        {% endif %}
            <div class="divider js-custom-discounts-end"></div>
            <div class="item new-custom-discount"><a href="#"><i class="fa fa-plus"></i> New Custom Discount</a></div>
            <div class="divider"></div>
            <div class="item special-delete" rootType="{{ constant('\\Genesis_Entity_Special::TYPE_DISCOUNT_DOLLAR') }}"><a href="#"><i class="fa fa-times"></i> Remove Discount</a></div>
        </div>
    </div>
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="star icon"></i>
        Promos
        <i class="dropdown icon"></i>
        <div class="menu">
        {% for special in view.defaultSpecials %}
            {% if special.isPromo() %}
            <div class="item special-menu-option"><a id="special-{{ special.getId() }}">{{ special.getString() }}</a></div>
            {% endif %}
        {% endfor %}
        {% if view.customPromos and view.customPromos|length > 0 %}
            <div class="divider js-custom-promos-start"></div>
            {% for special in view.customPromos %}
                <div class="item special-menu-option"><a id="special-{{ special.id }}">{{ special.name }}</a></div>
            {% endfor %}
        {% else %}
            <div class="divider js-custom-promos-start" style="display:none;"></div>
        {% endif %}
            <div class="divider js-custom-promos-end"></div>
            <div class="item new-custom-promo"><a href="#"><i class="fa fa-plus"></i> New Custom Promo</a></div>
            <div class="divider"></div>
            <div class="item special-delete" rootType="{{ constant('\\Genesis_Entity_Special::TYPE_PROMO_DOLLAR') }}"><a href="#"><i class="fa fa-times"></i> Remove Promo</a></div>
        </div>
    </div>
    <div class="ui secondary dropdown button toggled-actions disabled">
        <i class="gift icon"></i>
        Free Items
        <i class="dropdown icon"></i>
        <div class="menu">
            {% set freeItemTypes = [constant('\\Genesis_Entity_Special::TYPE_FREE_ITEM')] %}
            {% for special in view.defaultSpecials %}
                {% if special.getType() in freeItemTypes %}
                <div class="item special-menu-option"><a id="special-{{ special.getId() }}">{{ special.getString() }}</a></div>
                {% endif %}
            {% endfor %}
            {% if view.specials and view.specials|length > 0 %}
                <div class="divider"></div>
                {% for special in view.specials %}
                    {% if special.getType() in freeItemTypes %}
                    <div class="item special-menu-option"><a id="special-{{ special.getId() }}">{{ special.getString() }}</a></div>
                    {% endif %}
                {% endfor %}
            {% endif %}

            <div class="divider"></div>
            <div class="item special-delete" rootType="{{ constant('\\Genesis_Entity_Special::TYPE_FREE_ITEM') }}"><a href="#"><i class="fa fa-times"></i> Remove Free Item</a></div>
        </div>
    </div>
</div>

    </div>
    <div id="unit-actions" class="form-inline hide">
        <div class="btn-group">
            <a class="ui button"
                href="{{ path('features_units', {'fid': view.facilityId}) }}">
                <i class="fa fa-arrow-left"></i> </a>
        </div>
    </div>
</div>

<div id="units-table-container" style="display:none;">
<!--     <div class="form-search">
        <span class="fa fa-search"></span>
        <input class="form-control" type="search" id="search" placeholder="Search units" />
    </div> -->
    {% if view.inventory %}
    <form>
        <div class="table-responsive">
            <table id="units-table" class="ui table sortable cell-headers">
                <thead>
                    <tr>
                        <th class="center on-off-checkbox check-col no-sort">
                            <div class="ui checkbox">
                                <input type="checkbox" name="all" id="check-all" value="all" />
                            </div>
                        </th>
                        <th class="center availability-checkbox no-sort">Available</th>
                        <th>Unit</th>
                        {% if view.showGrouped %}<th># Rentable</th>{% endif %}
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    {% for key, unit in view.inventory %}
                        {% if not (view.facility.getCorporation().getSourceId() == constant('\\Genesis_Entity_Source::ID_MANUAL') and not unit.published) and unit.unitIds|length > 0 %}
                        <tr id="{{ key }}" data-unitindex="{{ key }}" class="{{ unit.active == 0 ? 'disabled-row' : 'enabled-row' }}">
                            <td class="center on-off-checkbox js-unit-checkbox" id="{{ unit.unitIds[0] }}">
                                <!-- <label for="selectbox-{{ key }}" class="on-off-checkbox-label"> -->
                                <div class="ui checkbox">
                                    <input type="checkbox" id="selectbox-{{ key }}" class="js-unit-selector" name="on-off" />
                                </div>
                                <!-- </label> -->
                            </td>
                            <td class="center availability-checkbox">
                                <div class="ui checkbox">
                                    <input type="checkbox" name="listing" id="toggle-{{ key }}"
                                    value="{{ key }}" {{ unit.active ? 'checked="checked"' : '' }} />
                                </div>
                            </td>
                            <td data-sort-value="{{ unit.width * unit.length }}">
                                <div class="js-unit-loading unit-loading loading pull-right" style="display:none;"></div>
                                <strong><span>{{ unit.dimensions|replace({' ': '&nbsp;'})|raw }} {{ unit.type }}</span></strong><br />
                                <span class="minor">{{ unit.amenities }}</span>
                                <span class="js-special-string minor units-special">{{ unit.specialString|default('') }}</span>
                            </td>
                            {% if view.showGrouped %}<td>{{ unit.numRentable }}</td>{% endif %}
                            <td data-sort-value="{{ unit.sparefootPrice ? unit.sparefootPrice : unit.standardRate }}">
                                {% if unit.sparefootPrice and unit.standardRate > unit.sparefootPrice %}
                                    <span class="js-reg-price strikethrough">${{ unit.standardRate|number_format(2) }}</span><br/>
                                    <span class="js-sale-price">${{ unit.sparefootPrice|number_format(2) }}</span>
                                {% else %}
                                    <span class="js-reg-price">${{ unit.standardRate|number_format(2) }}</span><br/>
                                    <span class="js-sale-price"></span>
                                {% endif %}
                                <span class="js-discount-string minor units-special">{{ unit.discountString|default('') }}</span>
                            </td>
                        </tr>
                        {% endif %}
                    {% endfor %}
                </tbody>
            </table>
            {% include 'facility/add-more-units.html.twig' with {'view': view, 'facility': view.facility} %}
        </div>
    </form>
    {% elseif view.unpublishedUnits %}
        <br /><p>You currently have no vacant units in inventory. If you believe this is a mistake, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    {% else %}
        <br />
        <div class="jumbo">
            <h2>Next up: List your inventory.</h2>
            <p>Let's add your first unit.</p>
            <a id="add-first-unit" class="huge ui blue button">Add Unit</a>
        </div>
    {% endif %}
</div>

<div id="units-loading">
    <img src="/images/loaders/large.gif" class="loading" />
</div>

<div id="edit-unit-content" style="display:none;">
    {% include 'inventory/unit-edit.html.twig' %}
</div>

{% include 'facility/hide-facility-reason-modal.html.twig' %}
{% include 'inventory/units_modals.html.twig' with {'view': view, 'loggedUser': view.loggedUser} %}
{% include 'inventory/units_json.html.twig' with {'view':view, 'facility': view.facility, 'inventory': view.inventory, 'sourceType': view.sourceType} %}
{% endblock %}