{{ include('sitetop.html.twig', {'head': '<link href="/css/signup.css" rel="stylesheet"/>', 'signUp': true}) }}

    <body style="min-width: 0;" class="signup">

        <nav class="navbar navbar-fixed-top">
            {% if not call_static('\\Genesis_Config_Server', 'isProduction') %}
                    <div class="preheader preheader-{{ call_static('\\Genesis_Config_Server', 'getEnvironment') }}">
                        <span>{{ call_static('\\Genesis_Config_Server', 'getEnvironmentAsString') }}</span>
                        <div class="clear"></div>
                    </div>
            {% endif %}

            <div class="ruler-header">
                <div class="navbar-header">
                     <span class="brand"><img src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/images/mysparefootx2.png') }}" alt="MySpareFoot" class="mysparefootx2" /></span>
                </div>

                <div class="collapse navbar-collapse">
                    <div class="nav pull-right">
                        <span class="navbar-text">Need assistance? We're here to help. <strong>{{ constant('\\Sparefoot\\MyFootService\\Service\\Constants::SUPPORT_PHONE_NUMBER') }}</strong></span>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="row">
                <div>
                    <div class="panel panel-default">
                        <div class="panel-heading setup-content-heading clearfix">
                            <div class="step{{ (view.action == "code" or view.action == "index") ? " selected" : "" }}">
                                <div class="circle"><span>1</span></div>
                                <span class="description">Signup Code</span>
                            </div>

                            <div class="step{{ (view.action == "company") ? " selected" : "" }}">
                                <div class="circle"><span>2</span></div>
                                <span class="description">Company Information</span>
                            </div>

                            <div class="step{{ (view.action == "terms") ? " selected" : "" }}">
                                <div class="circle"><span>3</span></div>
                                <span class="description">Terms</span>
                            </div>

                            <div class="step{{ (view.action == "billing") ? " selected" : "" }}">
                                <div class="circle"><span>4</span></div>
                                <span class="description">Billing<br/>Information</span>
                            </div>

                        </div>
                        <div class="panel-body setup-content-panel-body">
                            {% block content %}{% endblock %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
            <a href="/logout">Logout</a>
            </div>
        </div>

        <div id="message-modal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer">
                        <a href="#" id="message-modal-ok" data-dismiss="modal" class="btn btn-primary">OK</a>
                    </div>
                </div>
            </div>
        </div>

        <script src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/dist/init.js') }}"></script>
        <script type="text/javascript">
            var CONFIG = {
                appUrl: '//{{ app.request.host }}',
                cdnUrl: '//{{ app.request.host }}'
            };
        </script>

{{ include('sitebottom.html.twig', {
    'excludeQualaroo': true,
    'userId': view.loggedUser is defined and view.loggedUser is not null ? view.loggedUser.getId() : 0,
    'scripts': view.scripts
}) }}
