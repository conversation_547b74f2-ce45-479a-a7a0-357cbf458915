{% extends 'layout.html.twig' %}
{% block content %}

{{ include('settings/header.html.twig', {'loggedUser': view.loggedUser, 'active': 'sites'}) }}

<h2>Let's get your website set up.</h2>

<div class="panel panel-default pull-right">
    <div class="panel-heading">
        <h3 class="panel-title">Questions?</h3>
    </div>
    <div class="panel-body">
        <p>We can help you at any stage of the website setup process.</p>
        <p><strong>{{ constant('\\Genesis_Config_Phone::ACCOUNT_MANAGEMENT') }}</strong></p>
    </div>
</div>

<p>Now that SpareFoot's account management team has activated your website, you need to hook up your site to Google Places.</p>
<br />
<h5>If you do not have a Google Places listing for each of your facilities:</h5>
<p>Please follow <a href="/pdf/SpareFoot_Google_Places.pdf">these instructions</a>. The URLs and tracked phone numbers for your facilities are below.</p>
<br />

<h5>If you already have Google Places listings:</h5>
<p>You just need to update your listings with the URLs and tracked phone numbers below.</p>

{% if view.facilities %}
 <table class="data-grid" style="margin:2em 0;">
    <tr>
        <th>Facility</th>
        <th>URL</th>
        <th>Phone Number</th>
        <th>Options</th>
    </tr>

    {% for facility in view.facilities %}
        <tr>
            <td>{{ facility.getTitle() }}</td>
            <td><a href="{{ call_static('\\Genesis_Util_Url', 'hostedsiteUrl', facility) }}">{{ call_static('\\Genesis_Util_Url', 'hostedsiteUrl', facility) }}</a></td>
            <td>{{ facility.stringPpcPhone() }}</td>
            <td><a onclick="hostedsite_modal({{ facility.getId() }});" href="#" class="ui button">Edit</a></td>
        </tr>
    {% endfor %}
 </table>
{% endif %}

<div id="hostedsite-modal" class="modal fade">
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title">Facility Options</h4>
        </div>
        <div class="modal-body">
            <p class="alert alert-danger hide"></p>
            <form method="post" enctype="multipart/form-data" action="{{ url('features', {'action': 'photos'}) }}" class="form-horizontal">
                <div class="control-group">
                    <label for="fac_logo" class="control-label">Logo <a href="#" rel="tooltip" title="All logos will be automatically resized to fit in a 240px x 180px space."><i class="fa fa-info-circle"></i></a></label>
                    <div class="controls">
                        <input type="file" name="image" />
                        <br />
                        <input type="submit" value="Upload Image" class="ui button">
                        <input type="hidden" id="facility-id" name="facility_id" value="" />
                        <input type="hidden" id="logo" name="logo" value="1" />
                        <p class="help-block">Maximum size: 5 MB</p>
                        <div id="existing-facility-logo"></div>
                    </div>
                </div>

            </form>

            <form class="form-horizontal">

                <div class="control-group">
                    <label for="facility-video" class="control-label">YouTube Video Link</label>
                    <div class="controls">
                        <input type="text" size="40" name="facility_video" id="facility-video" class="form-control" />
                        <p class="help-block">Upload a video to YouTube and put the link here.  We will embed it on your site with the images.  Find your video on YouTube.com and click the 'embed' button to get the link.  (ex: http://www.youtube.com/embed/W-Q7RMpINVo)</p>
                    </div>
                </div>

                <div class="control-group">
                    <label for="facility-payment" class="control-label">Payment Portal Link</label>
                    <div class="controls">
                        <input type="text" size="40" name="facility_payment" id="facility-payment" class="form-control" />
                        <p class="help-block">If you have a link for tenants to make payments for this facility, let us know and we will add it to your site.</p>
                    </div>
                </div>

                <div class="control-group">
                    <label for="facility-google" class="control-label">Google Analytics Account Number</label>
                    <div class="controls">
                        <input type="text" size="40" name="facility_google" id="facility-google" class="form-control" />
                        <p class="help-block">To add Google Analytics to your hosted site, put in your Google Analytics account number (ex: UA-********-1).</p>
                    </div>
                </div>

            </form>

        </div>
        <div class="modal-footer">
            <img src="/images/loaders/small.gif" class="hide" />&nbsp;
            <a data-dismiss="modal" class="ui button">Cancel</a>
            <a id="unit-modal-save" class="btn btn-primary" data-loading-text="Saving" data-complete-text="Saved">Save</a>
        </div>
    </div>
</div>

<span class="hidden" id="geoPagesSetupPageTest"></span>

{% endblock %}
