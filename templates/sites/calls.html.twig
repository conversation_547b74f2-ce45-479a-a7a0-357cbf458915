{% extends 'layout.html.twig' %}
{% block content %}

<div class="page-header">
    <h6>Report</h6>
    <h1>GeoPages Calls</h1>
</div>

<div class="toolbar">
    {{ include('daterange.html.twig', {
        'action': '/sites/calls',
        'trueDateRange': view.trueDateRange,
        'trueBeginDate': view.trueBeginDate,
        'trueEndDate': view.trueEndDate,
        'showExport': true
    }) }}

    <form method="POST" action="/sites/calls" id="facility_choose_form" name="facility_choose_form">
        <select onchange="javascript:$('#facility_choose_form').submit();" id="fid" name="fid">
            <option value="all">All Facilities</option>
            {% for fac in view.facilities %}
                <option{{ fac.getId() == view.facility_id ? ' SELECTED' : '' }} value="{{ fac.getId() }}">{{ fac.getTitle() }}</option>
            {% endfor %}
        </select>
    </form>
</div>

<div class="ui-layout-content">
    <table class="data-grid">
        <thead>
            <tr>
                <th>Time (CST)</th>
                <th>Duration</th>
                <th>Facility</th>
                <th>Status</th>
                <th>Caller ID</th>
                <th>Recording</th>
            </tr>
        </thead>
        <tbody>
            {% if view.calls %}
                {% for call in view.calls %}
                    <tr>
                        <td>{{ call.start_time|date('M d, Y g:i A') }}</td>
                        <td>{{ (call.duration / 60)|round(0, 'floor') }}:{{ '%02d'|format(call.duration % 60) }}</td>
                        <td>{{ call.facility_title }}</td>
                        <td>{{ call.dial_status }}</td>
                        <td>{{ call.caller_name }}</td>
                        <td><a href="{{ call.recording_url }}" target="_blank">Listen</a></td>
                    </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
</div>

<span class="hidden" id="geoPagesCallsPageTest"></span>

{% endblock %}
