{% extends 'layout.html.twig' %}
{% block content %}

<div class="page-header">
    <h6>Report</h6>
    <h1>GeoPages Overview</h1>
</div>

{% if not view.facilities and
        view.loggedUser.getMyFootRole() != constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN') and
        view.loggedUser.getMyFootRole() != constant('\\Genesis_Entity_UserAccess::ROLE_GOD') %}
    <div class="setup-content-container">
        <div class="setup-content">
            You don't have access to any facilities with GeoPages.
        </div>
    </div>
{% elseif not view.facilities %}

    <h1>GeoPages</h1>
    <p>You do not have any facilities using SpareFoot GeoPages.</p>

{% else %}

    <div class="toolbar">
        {{ include('daterange.html.twig', {
            'action': '/sites',
            'trueDateRange': view.trueDateRange,
            'trueBeginDate': view.trueBeginDate,
            'trueEndDate': view.trueEndDate,
            'showExport': true
        }) }}
        
        <form method="post" action="/sites/calls" id="facility_choose_calls_form" name="facility_choose_calls_form">
            <input type="hidden" id="calls_fid" name="fid" value="" />
        </form>
        
        <form method="post" action="/sites/reservations" id="facility_choose_reservations_form" name="facility_choose_reservations_form">
            <input type="hidden" id="reservations_fid" name="fid" value="" />
        </form>
    </div>

    <div class="ui-layout-content">
    <table class="data-grid">
        <thead>
            <tr>
                <th>Facility</th>
                <th>Visits</th>
                <th>Reservations</th>
                <th>Calls</th>
                <th>URL</th>
            </tr>
        </thead>
        <tbody>
            {% for facility in view.facilities %}
                <tr>
                    <td>{{ facility.entity.getTitle() }}</td>
                    <td>{{ facility.num_visits }}</td>
                    <td><a onclick="javascript:$('#reservations_fid').val('{{ facility.entity.getId() }}');$('#facility_choose_reservations_form').submit();" style="text-decoration:underline; cursor:pointer;">{{ facility.num_reservations }}</a></td>
                    <td><a onclick="javascript:$('#calls_fid').val('{{ facility.entity.getId() }}');$('#facility_choose_calls_form').submit();" style="text-decoration:underline; cursor:pointer;">{{ facility.num_calls }}</a></td>
                    <td>{% if view.showUrls %}<a href="{{ call_static('\\Genesis_Util_Url', 'hostedsiteUrl', facility.entity) }}">{{ call_static('\\Genesis_Util_Url', 'hostedsiteUrl', facility.entity) }}</a>{% else %}<font color="red">NOT ACTIVATED:</font> Your GeoPage URL will be generated when payment information is received (<a href="/payment">click here</a>).{% endif %}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% endif %}

<span class="hidden" id="geoPagesOverviewPageTest"></span>

{% endblock %}

