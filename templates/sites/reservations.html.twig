{% extends 'layout.html.twig' %}
{% block content %}

<div class="page-header">
    <h6>Report</h6>
    <h1>GeoPages Reservations</h1>
</div>

<div class="toolbar">
    {{ include('daterange.html.twig', {
        'action': '/sites/reservations',
        'trueDateRange': view.trueDateRange,
        'trueBeginDate': view.trueBeginDate,
        'trueEndDate': view.trueEndDate,
        'showExport': true
    }) }}
    <form method="POST" action="/sites/reservations" id="facility_choose_form" name="facility_choose_form">
        <select onchange="javascript:$('#facility_choose_form').submit();" id="fid" name="fid">
            <option value="all">All Facilities</option>
            {% for fac in view.facilities %}
                <option{{ fac.getId() == view.facility_id ? ' SELECTED' : '' }} value="{{ fac.getId() }}">{{ fac.getTitle() }}</option>
            {% endfor %}
        </select>
    </form>
</div>

<div class="ui-layout-content">
    <table class="data-grid">
        <thead>
            <tr>
                <th>Facility</th>
                <th>Date Reserved</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Unit</th>
                <th>Monthly Rent</th>
                <th>Move-In Date</th>
                <th>Unit Size</th>
            </tr>
        </thead>
        <tbody>
            {% if view.reservations %}
                {% for reservation in view.reservations %}
                    <tr>
                        <td>{{ reservation.title }}</td>
                        <td>{{ reservation.timestamp|date("m/d/Y") }}</td>
                        <td>{{ reservation.last_name }}</td>
                        <td>{{ reservation.first_name }}</td>
                        <td>{{ (reservation.email == '<EMAIL>' or reservation.email is empty) ? 'No e-mail address provided' : reservation.email }}</td>
                        <td>{{ reservation.phone }}</td>
                        <td>{{ reservation.unit_number }}</td>
                        <td>${{ reservation.monthly_rent|number_format(2) }}</td>
                        <td>{{ reservation.move_in|date("m/d/Y") }}</td>
                        <td>{{ reservation.size_w }} x {{ reservation.size_d }}</td>
                    </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
</div>

<span class="hidden" id="geoPagesReservationsPageTest"></span>

{% endblock %}
