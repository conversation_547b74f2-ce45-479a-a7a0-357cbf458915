{% extends 'layout.html.twig' %}

{% block content %}
<a id="back-button" class="ui button"><i class="icon arrow left"></i> Back</a>

<br />

<h3>Review</h3>

<p>
    <strong>Name:</strong> {{ view.review.getNickname() }}<br />
    <strong>Date:</strong> {{ view.review.getTimestamp()|date('M. j, Y') }}
</p>

{% if view.numManageableFacilities > 1 %}
    <p class="minor">
        {{ view.review.getFacility().getTitleWithCompanyCode() }}<br />
        {{ view.review.getFacility().getLocation().getAddress1() }}<br />
        {{ view.review.getFacility().getLocation().getCity() }},
        {{ view.review.getFacility().getLocation().getState() }}
        {{ view.review.getFacility().getLocation().getZip() }}
    </p>
{% endif %}

<div class="ui segment">
    <h3>{{ view.review.getTitle() }}</h3>
    <div class="rating-stars">
        {% for i in 0..4 %}
            {% if i < view.review.getRating()|floor %}
                <i class="fa fa-star"></i>
            {% elseif i < ((view.review.getRating() * 2)|floor / 2) %}
                <i class="fa fa-star-half-o"></i>
            {% else %}
                <i class="fa fa-star-o"></i>
            {% endif %}
        {% endfor %}
    </div>
    <p>{{ view.review.getMessage() }}</p><br />
</div>

{% if view.review.getRatingPrice() is not null %}
{% set revPrice = view.review.getRatingPrice() %}
{% set revLocation = view.review.getRatingLocation() %}
{% set revPaperwork = view.review.getRatingPaperwork() %}
{% set revSecurity = view.review.getRatingSecurity() %}
{% set revService = view.review.getRatingService() %}
{% set revClean = view.review.getRatingCleanliness() %}

{% if revPrice %}
    <strong>Price</strong> - {{ revPrice }}/5 ({{ view.review.getPriceValue(revPrice) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revPrice }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revPrice/5)*100 }}%">
      </div>
    </div>
{% endif %}
{% if revLocation %}
    <strong>Location</strong> - {{ revLocation }}/5 ({{ view.review.getLocationValue(revLocation) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revLocation }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revLocation/5)*100 }}%">
      </div>
    </div>
{% endif %}
{% if revPaperwork %}
    <strong>Time on paperwork</strong> - {{ revPaperwork }}/5 ({{ view.review.getPaperworkValue(revPaperwork) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revPaperwork }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revPaperwork/5)*100 }}%">
      </div>
    </div>
{% endif %}
{% if revSecurity %}
    <strong>Security</strong> - {{ revSecurity }}/5 ({{ view.review.getSecurityValue(revSecurity) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revSecurity }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revSecurity/5)*100 }}%">
      </div>
    </div>
{% endif %}
{% if revService %}
    <strong>Service</strong> - {{ revService }}/5 ({{ view.review.getServiceValue(revService) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revService }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revService/5)*100 }}%">
      </div>
    </div>
{% endif %}
{% if revClean %}
    <strong>Cleanliness</strong> - {{ revClean }}/5 ({{ view.review.getCleanlinessValue(revClean) }})
    <div class="progress">
      <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ revClean }}" aria-valuemin="0" aria-valuemax="5" style="width: {{ (revClean/5)*100 }}%">
      </div>
    </div>
{% endif %}
<p class="clear"><strong>Recommended:</strong> {{ view.review.getRatingRecommended() == 1 ? 'Yes' : 'No' }}</p>
{% endif %}
<br />
{% if view.review.getChildReview() %}
    <h4>Manager's Response</h4>
    <p>{{ view.review.getChildReview().getMessage() }}</p>
{% elseif view.review.hasPendingResponse() %}
    <h4>Manager's Response</h4>
    <p>Pending review</p>
{% else %}
    <form id="review-response-form" method="post" action="{{ url({'action': 'response'}, 'reviews') }}">
        <br />
        <p style="font-size:11px;color:#777;">Please use your discretion when posting your response to a review. This is your chance to publicly address any negative experiences associated with your facility. Remember, everyone who visits your facility page will be able to see this. Check out our quick list of <a target="_blank" href="http://blog.sparefoot.com/reviews/">review response best practices</a> before submitting yours. To stay within SpareFoot <a target="_blank" href="http://www.sparefoot.com/reviews.html">terms and conditions</a>, please do not include phone numbers, email addresses or websites in your response.</p>
        <input type="hidden" name="parent_id" value="{{ view.review.getId() }}" />
        <div class="form-group">
            <textarea name="response" class="form-control" rows="5"></textarea>
        </div>
        <div class="form-actions">
            <input type="submit" id="review-response-form-submit" value="Leave Response" data-loading-text="Saving" class="ui blue button" />
            &nbsp;&nbsp;<span id="review-response-form-loading" class="hide"><img src="/images/loaders/large.gif" /></span>
        </div>
    </form>
{% endif %}
{% endblock %}