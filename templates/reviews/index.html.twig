{% extends 'layout.html.twig' %}

{% block content %}
{# Extract facility ID for reuse throughout template #}
{% set facilityId = view.facility.getId() | default('') %}
{% if (view.review is not defined or view.reviews.current() == null) and not view.q %}
    <div class="ui grid">
        <div class="sixteen wide tablet ten wide computer column">
            <div class="ui icon warning message">
                <i class="star icon"></i>
                <div class="header">Looks like you don't have any reviews yet</div>
                <p>
                    Reviews help you stand out and earn a potential renter's trust.<br/>
                    Customers are 50% <em>more likely</em> to reserve at facilities with at least one review.
                </p>
            </div>

            <a href="{{ url('reviews_request', {fid: facilityId}) }}" 
               class="ui blue button right floated" 
               role="button" 
               aria-label="Request reviews for this facility">
                Request Reviews
            </a>
        </div>
    </div>

{% else %}
    <form action="{{ url('reviews_index', []) }}?fid={{ view.facility.getId() | default('') }}" class="ui form form-search">
        <div class="inline field">
            <a href="{{ url('reviews_index', {'action': 'request'}) }}?fid={{ view.facility.getId() | default('') }}" class="ui blue button">Request Reviews</a>
        </div>
        <div class="field">
            <div class="ui icon input">
                <input type="text" class="form-control search-query" name="q" value="{{ view.q }}" placeholder="Search Reviews">
                <input type="hidden" name="fid" value="{{ view.facility.getId() | default('') }}"/>
                <i class="search icon"></i>

                {% if view.q %}
                    <span class="clear-search">
                        <a href="{{ url('reviews_index', []) }}?fid={{ view.facility.getId() | default('') }}">
                        <img src="/images/search-clear.gif" />
                        </a>
                    </span>
                {% endif %}
            </div>
        </div>
    </form>

    <br/>

    {% if view.reviews.current() == null and view.q %}
    <div class="ui warning message">
        <p>No results found.</p>
    </div>
    {% endif %}

    <!-- TODO: Old Theme below -->
    <div class="list-group reviews-list">
        {% for review in view.reviews %}

            <a href="{{ url('reviews_index', {'action': 'detail', 'rid': review.getId()}) }}" class="list-group-item">
                <span class="pull-right text-right" style="color:#0088cc;">{{ review.getTimestamp()|date("m/j/y") }}</span>
                {{ (review.getChildReview() or review.hasPendingResponse()) ? '<i class="fa fa-reply" style="color:#999;"></i> '|raw : '' }}<strong>{{ review.getNickname() }}</strong>

                <div class="rating-stars">
                    {% for i in 0..4 %}
                        {% if i < review.getRating()|floor %}
                            <i class="fa fa-star"></i>
                        {% elseif i < ((review.getRating() * 2)|floor / 2) %}
                            <i class="fa fa-star-half-o"></i>
                        {% else %}
                            <i class="fa fa-star-o"></i>
                        {% endif %}
                    {% endfor %}
                </div>

                <span class="review-message">{{ review.getMessage() }}</span>

                {% if view.numManageableFacilities > 1 %}
                    <span class="minor">{{ review.getFacility().getTitleWithCompanyCode() }}</span>
                {% endif %}
            </a>

        {% endfor %}
    </div>
{% endif %}

{% endblock %}
