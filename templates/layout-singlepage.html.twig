{{ include('sitetop.html.twig') }}
{% set bodyClassNames = [] %}
{% if view.banner is defined and view.banner|length > 0 %}
    {% set bodyClassNames = bodyClassNames|merge(['banner-showing']) %}
{% endif %}

<body data-path="{{ app.request.requestUri }}" class="{{ bodyClassNames|join(' ') }}">
    {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'getLoggedUser') %}
        {{ include('new-ui/sidebar.html.twig', {
            'view': view
            }) }}
    {% endif %}

    <div id="wrapper" class="page-container pusher">
        {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'getLoggedUser') %}
            {{ include('new-ui/headerbar.html.twig', {'view': view}) }}
        {% endif %}

        <div id="page-content-wrapper">
            {% if view.banner.showCovidMsgBanner %}
            {{ include('new-ui/covid-banner.html.twig') }}
        {% endif %}
        {% if view.banner.showMoveInsBanner %}
            {{ include('new-ui/move-ins-banner.html.twig') }}
        {% endif %}
        {% if view.banner.showNotificationBanner %}
            {{ include('new-ui/notification-banner.html.twig') }}
        {% endif %}

            <div class="master-layout">
                {% if view.welcomeMessage is defined and view.welcomeMessage %}
                <div class="alert alert-block alert-success">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h4 class="alert-heading">Welcome to SpareFoot</h4>
                    This is MySpareFoot, your tool for managing your listings on the SpareFoot AdNetwork. If you have questions, click "Help" in the toolbar. We can't wait to send you some tenants!
                </div>
                {% endif %}

                {% if call_static('\\Sparefoot\\MyFootService\\Service\\User', 'getLoggedUser') and (view.loggedUser.isMyFootAdmin() or view.loggedUser.isMyFootGod()) %}
                    {% set account = view.loggedUser.getAccount() %}
                    {% set unassignedFacs = account.getUnassignedFacilities() %}
                    {% set accountId = view.accountId %}

                    {% if 'payment' in app.request.requestUri %}
                        {% set alertMsg = 'add a payment method below.' %}
                    {% else %}
                        {% set alertMsg = '<a href="' ~ path('payment') ~ '">add a payment method</a> to activate.' %}
                    {% endif %}

                    {% if unassignedFacs|length > 0 %}
                        <div class="ui error message">You have {{ unassignedFacs|length > 1 ? 'unactivated facilities' : 'an unactivated facility' }} - {{ alertMsg|raw }}</div>
                    {% endif %}

                    {% if accountId and accountId != view.loggedUser.getAccountId() %}
                        <div class="ui error message">Account ID {{ accountId }} does not exist.</div>
                    {% endif %}
                {% endif %}

                {% if view.errorMessages is defined and view.errorMessages is iterable and view.errorMessages|length > 0 %}
                <div class="ui error message">
                    <ul>
                        {% for message in view.errorMessages %}
                        <li>{{ message }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                {% if view.successMessages is defined and view.successMessages is iterable and view.successMessages|length > 0 %}
                <div class="ui success message">
                    <ul>
                        {% for message in view.successMessages %}
                        <li>{{ message }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <!-- YIELD View Content -->
                {% block content %}{% endblock %}
                <div id="featuresApp"></div>

                <!--footer id="page-footer">
                    <p>&copy; {{ "now"|date("Y") }} SpareFoot.  Got questions? Check out our <a href="https://support.sparefoot.com/hc/en-us" target="_blank">Help Center</a>.</p>
                </footer-->
            </div>
        </div><!--/#page-content-wrapper-->

        <div id="message-modal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer">
                        <a href="#" id="message-modal-ok" data-dismiss="modal" class="ui primary button">OK</a>
                    </div>
                </div>
            </div>
        </div>
    </div> <!--/.page-container-->

    {% if not call_static('\\Genesis_Config_Server', 'isProduction') %}
        {% set envString = call_static('\\Genesis_Config_Server', 'getEnvironmentAsString') %}
        {% if envString|lower == 'staging' %}
            {% set color = 'orange' %}
        {% elseif envString|lower == 'dev' %}
            {% set color = 'purple' %}
        {% elseif envString|lower == 'local' %}
            {% set color = 'teal' %}
        {% else %}
            {% set color = '' %}
        {% endif %}
        <div id="env-label" class="ui label {{ color }}">{{ envString }}</div>
    {% endif %}

    <script type="text/javascript">
        //Global variables used within the App
        App.authBearerToken = '{{ view.authBearerToken }}';
        App.servicesBaseUrl = '{{ view.servicesBaseUrl }}' + (location.port ? ':'+location.port : '');
    </script>

    <script src="{{ call_static('\\Genesis_Util_Versioner', 'version', '/dist/init.js') }}"></script>

    <script type="text/javascript">
        var CONFIG = { featureFlags: {} };
        // Feature Flags
        CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::UNIT_DELETE') }}'] = {{ call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::UNIT_DELETE')) ? 'true' : 'false' }};
        CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::VEHICLE_AMENITIES') }}'] = {{ call_static('\\Sparefoot\\MyFootService\\Service\\User', 'isFeatureActive', constant('\\Sparefoot\\MyFootService\\Models\\Features::VEHICLE_AMENITIES')) ? 'true' : 'false' }};
        CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::COVID_BANNER') }}'] = {{ view.covidBanner ? 'true' : 'false' }};
        CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::COVID_MODAL') }}'] = {{ view.covidModal ? 'true' : 'false' }};
        CONFIG.featureFlags['{{ constant('\\Sparefoot\\MyFootService\\Models\\Features::CUSTOM_CLOSURES') }}'] = {{ view.customClosures ? 'true' : 'false' }};

        SF.tools.setReadOnly(CONFIG, {
            appUrl: '//{{ app.request.host }}',
            cdnUrl: '//{{ app.request.host }}',
            env: location.hostname.match('sparefoot.com') ? 'live' : location.hostname.match('localhost') ? 'dev' : 'staging'
        });

        {% set userFields = ['email', 'firstName', 'id', 'lastName', 'phone', 'pictureExt', 'username'] %}
        {% set user = view.loggedUser.toArray(userFields) %}
        var USER = {};
        SF.tools.setReadOnly(USER, {{ user|json_encode|raw }});

        {% set accountFields = ['accountId', 'bidType', 'cpa', 'infoString', 'integrationsString', 'locationId', 'name', 'numFacilities', 'phone', 'sfAccountId', 'status'] %}
        {% set account = view.loggedUser.getAccount().toArray(accountFields) %}
        var ACCOUNT = {};
        SF.tools.setReadOnly(ACCOUNT, {{ account|json_encode|raw }});
        App.ACCOUNT = ACCOUNT;
        App.FACILITY = {{ view.facility ? view.facility|json_encode|raw : '{}' }};
    </script>

    <script type="text/javascript">
        $(document).ready(() => {
            $('#contact-us-button').click(() => {
                $('#contact-us-modal').modal('show')
            })
        })
    </script>

{{ include('sitebottom.html.twig', {
    'userId': view.loggedUser.getId(),
    'loggedUser': view.loggedUser,
    'excludeInitialPageView': true,
    'scripts': view.scripts
}) }}

