<div class="ui segment">
    <h3 class="ui header">
        <i class="sign in icon"></i>
        Customer Move-Ins
    </h3>
    
    {% if view.moveIns is defined and view.moveIns|length > 0 %}
        <div class="ui table container">
            <table class="ui celled striped table">
                <thead>
                    <tr>
                        <th>Customer Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Unit Number</th>
                        <th>Unit Size</th>
                        <th>Move-In Date</th>
                        <th>Monthly Rate</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for moveIn in view.moveIns %}
                        <tr>
                            <td>{{ moveIn.customerName }}</td>
                            <td>{{ moveIn.email }}</td>
                            <td>{{ moveIn.phone }}</td>
                            <td>{{ moveIn.unitNumber }}</td>
                            <td>{{ moveIn.unitSize }}</td>
                            <td>{{ moveIn.moveInDate|date('m/d/Y') }}</td>
                            <td>${{ moveIn.monthlyRate|number_format(2) }}</td>
                            <td>
                                <div class="ui label {{ moveIn.status == constant('\\Sparefoot\\MyFootService\\Service\\Booking::VERIFIED_STATE_MOVED_IN') ? 'green' : 'blue' }}">
                                    {{ moveIn.status|title }}
                                </div>
                            </td>
                            <td>
                                <div class="ui small buttons">
                                    <button class="ui button">View</button>
                                    <button class="ui button">Edit</button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="ui placeholder segment">
            <div class="ui icon header">
                <i class="sign in icon"></i>
                No move-ins found
            </div>
            <div class="inline">
                <div class="ui primary button">
                    <i class="plus icon"></i>
                    Process New Move-In
                </div>
            </div>
        </div>
    {% endif %}
</div>
