<?=$this->partial('customer/customers_subnav.phtml', array('selected' => 'reservations', 'loggedUser' => $this->loggedUser, 'facilityId' => $this->facility->getId()))?>

<div class="ui secondary menu">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => '/customers/reservations',
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'showExport'    => false,
            'facility'      => $this->facility
        )
    )?>
</div>

<div class="ui-layout-content">
    <table class="ui cell-headers striped sortable table">
        <thead>
            <tr>
                <th id="date_reserved" class="sorted descending">Date&nbsp;Reserved</th>
                <th>Status</th>
                <th>Customer</th>
                <th>Unit</th>
                <th>Dimensions</th>
                <th>Monthly&nbsp;Rent</th>
                <?php if($this->loggedUser->getAccount()->getBidType() != 'RESIDUAL'){ ?>
                    <th>Bid&nbsp;Amount</th>
                    <th>Transaction&nbsp;Fee</th>
                <?php } ?>
                <th>Move-In&nbsp;Date</th>
                <?php if($this->includeTenantConnectCalls) { ?>
                	<th>Tenant Connect Call</th>
                <?php } ?>
            </tr>
        </thead>
        <tbody>
        <?php if ($this->reservations && count($this->reservations) > 0) { ?>
            <?php foreach($this->reservations as $reservation) { ?>
            <tr id="reservation-row-<?=$reservation['confirmation_code']?>">
                <td data-sort-value="<?=strtotime($reservation['timestamp'])?>">
                    <?=date("m/d/Y", strtotime($reservation['timestamp']))?>
                </td>
                <td>
                    <?php
                        switch($reservation['booking_state']){

                            case Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED:
                                echo 'Moved In';
                                break;

                            case Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED:
                                echo 'Did Not Move In';
                                break;

                            case Genesis_Entity_Transaction::BOOKING_STATE_ERRORED:
                                echo 'Errored';
                                break;

                            case Genesis_Entity_Transaction::BOOKING_STATE_CANCELLED:
                                echo 'Cancelled';
                                break;

                            default:
                                echo '';
                                break;
                        }

                    ?>
                </td>
                <td data-sort-value="<?=$reservation['last_name'].$reservation['first_name']?>">
                    <?=trim(ucwords($reservation['last_name']))?>, <?=ucwords($reservation['first_name'])?><br />
                    <?=($reservation['email'] == '<EMAIL>' || empty($reservation['email'])) ? 'No e-mail address provided' : $reservation['email']?><br />
                    <?=preg_replace("/^[\+]?[1]?[- ]?[\(]?([2-9][0-8][0-9])[\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/", "(\\1) \\2-\\3", $reservation['phone'])?><br/>
                    <?php if($reservation['aaa_member_number'] !== NULL AND $reservation['aaa_member_number'] != '') { ?><span class="label label-success">AAA Reservation</span> <?php } ?></td>
                <td><?=$reservation['unit_number']?></td>
                <td>
                <?php if ($reservation['booked_unit_size'] != $reservation['actual_unit_size'] && $reservation['actual_unit_size'] != "") : ?>
                        <h6>Reserved</h6>
                        <?=$reservation['booked_unit_size']?>
                        <h6>Moved Into</h6>
                        <?=$reservation['actual_unit_size']?><br />
                <?php else: ?>
                    <?=$reservation['booked_unit_size']?>

                <?php endif; ?>
                </td>
                <td>
                <?php
                    $meta = $this->bookingMeta[$reservation['confirmation_code']];
                    if ($meta && $meta['billing_price']) { ?>
                    $<?=number_format($meta['billing_price'], 2)?>
                <?php } else { ?>
                    $<?=number_format($reservation['monthly_rent'], 2)?>
                <?php } ?>
                </td>
                <?php if($this->loggedUser->getAccount()->getBidType() != 'RESIDUAL'){ ?>
                    <td><?=$reservation['string_base_bid']?></td>
                    <?php
                    if ($reservation['is_free'] == 1) { ?>
                        <td>$<?=number_format(0, 2)?></td>
                    <?php } else { ?>
                        <td><?=$reservation['string_bid_amount']?></td>
                    <?php }
                 } ?>
                <td><?=date("m/d/Y", strtotime($reservation['move_in']))?></td>
                <?php if($this->includeTenantConnectCalls) { ?>
                <td>
                    <?php if($reservation['tcCall']){ ?>
                    <?=$reservation['tcCall']->stringStatus()?>
                    <?php if($reservation['tcCall']->booleanPlayCall()){?>
                    <p>
                        <a class="ui button" href="<?=$reservation['tcCall']->getRecordingUrl()?>" target="_blank"><i class="fa fa-play"></i> Play</a>
                    </p>
                    <?php } ?>
                    <?php } ?>
                </td>
               	<?php } ?>
            </tr>
            <?php } // foreach ?>
        <?php } else { ?>
            <tr>
                <td colspan="12">No results</td>
            </tr>
            <?php } ?>
        </tbody>
    </table>
</div>
