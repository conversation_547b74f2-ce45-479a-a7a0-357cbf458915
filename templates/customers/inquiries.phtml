<?=$this->partial('customer/customers_subnav.phtml', array('selected' => 'inquiries', 'loggedUser' => $this->loggedUser, 'facilityId' => $this->facility->getId()))?>
<h5 class="videos-link">These people have expressed interest or asked us for more information about your facility. We're following up with them, and we'll let you know if they make a reservation.
</h5>


<div class="ui secondary menu">
    <?=$this->partial('daterange.phtml',
        array(
            'action' => '/customers/inquiries',
            'trueDateRange' => $this->trueDateRange,
            'trueBeginDate' => $this->trueBeginDate,
            'trueEndDate'   => $this->trueEndDate,
            'showExport'    => false,
            'facility'      => $this->facility
        )
    )?>
</div>

<div class="ui-layout-content">
    <table class="ui cell-headers striped sortable table">
        <thead>
            <tr>
                <th id="date_created">Date Created</th>
                <th>Customer Name</th>
            </tr>
        </thead>
        <tbody>
    <?php if ($this->inquiries && count($this->inquiries) > 0) { ?>
        <?php foreach($this->inquiries as $inquiry) { ?>
            <tr>
                <td><?=date("m/d/Y H:i:s", strtotime($inquiry->getTimestamp()))?></td>
                <td><?=$inquiry->getFullName() ?></td>
            </tr>
        <?php } // foreach ?>
    <?php } else { ?>
            <tr>
                <td colspan="2">No results</td>
            </tr>
    <?php } ?>
        </tbody>
    </table>
</div>
