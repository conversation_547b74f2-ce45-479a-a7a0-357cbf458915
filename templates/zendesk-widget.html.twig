<script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=01e6fe16-5f4d-45e0-8f78-c981d935cf37"> </script>

<script type="text/javascript">
    // customize color settings of Zendesk Help Widget
    {% if view.darkTheme %}
    window.useDarkTheme = true;
    {% else %}
    window.useDarkTheme = false;
    {% endif %}

    window.yellowTheme = {
        theme:'#444444',
        launcher:'#FFCC00',
        launcherText:'#444444',
        button:'#444444',
        resultLists:'#2096F3',
        header:'#FFCC00',
        articleLinks:'#2096F3'
    };

    window.darkTheme = {
        theme:'#444444',
        launcher:'#444444',
        launcherText:'#FFFFFF',
        button:'#444444',
        resultLists:'#2096F3',
        header:'#444444',
        articleLinks:'#2096F3'
    };

    window.useTheme = (window.useDarkTheme) ? window.darkTheme : window.yellowTheme;

    window.zESettings = {
        webWidget: {
            color: useTheme
        }
    };
</script>

{% if view.loggedUser %}
    <script>
        // pull user info for Zendesk Support and prefill form only if user is logged in
        window.userInfo = {
            name: '{{ view.loggedUser.getFirstName() }} {{ view.loggedUser.getLastName() }}',
            email: '{{ view.loggedUser.getEmail() }}'
        }

        zE('webWidget', 'identify', userInfo);

        zE('webWidget', 'prefill', {
            name: {
                value: window.userInfo.name
            },
            email: {
                value: window.userInfo.email
            }
        });

    </script>
{% endif %}

<!--Chat settings-->
<script>
    zE(function() {
        $zopim(function() {
            $zopim.livechat.window.setTitle('Chat');
        });
    });
</script>
