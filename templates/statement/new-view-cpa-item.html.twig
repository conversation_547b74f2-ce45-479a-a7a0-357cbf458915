<tr class="cpa-item" data-id="{{ view.clientItem.getConfirmationCode() }}" data-facility-id="{{ view.clientItem.getFacility().getId() }}">
    {% if not view.clientItem.getFree() %}
    <td id="action-{{ view.clientItem.getConfirmationCode() }}" class="statement-actions">
        <div class="ui basic icon buttons" data-toggle="buttons">
            {% set isDisputed = view.bookingExtra.status in [
                constant('\\Genesis_Entity_Statement_Item_Cpa::STATUS_NEVER_MOVED_IN'),
                constant('\\Genesis_Entity_Statement_Item_Cpa::STATUS_NEVER_MOVED_IN_REVIEWED'),
                constant('\\Genesis_Entity_Statement_Item_Cpa::STATUS_UNDER_REVIEW')
            ] %}
            {% set yesClasses = '' %}
            {% set noClasses = '' %}
            
            {% if view.clientItem.getAutoState() == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
                {% set yesClasses = 'confirm-autoconfirmed-button ' %}
                {% set noClasses = 'dispute-autoconfirmed-button ' %}

                {% if not view.clientItem.getReviewStatus() %}
                    {% set yesClasses = yesClasses ~ (isDisputed ? '' : 'active') %}
                    {% set noClasses = noClasses ~ (isDisputed ? 'active' : '') %}
                {% elseif view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                    {% set yesClasses = yesClasses ~ (isDisputed ? '' : 'active') %}
                    {% set noClasses = noClasses ~ (isDisputed ? 'active' : '') %}
                {% elseif view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %}
                    {% if isDisputed %}
                        {% set noClasses = noClasses ~ ' active' %}
                    {% else %}
                        {% set yesClasses = yesClasses ~ ' active' %}
                        {% set noClasses = noClasses ~ ' hidden' %}
                    {% endif %}
                {% endif %}
            {% elseif view.isLate %}
                {% set yesClasses = 'change-move-in-date-button' %}
                {% set noClasses = 'dispute-button active' %}
            {% else %}
                {% set yesClasses = 'confirm-button ' ~ (isDisputed ? '' : 'active') %}
                {% set noClasses = 'dispute-button ' ~ (isDisputed ? 'active' : '') %}
            {% endif %}
            
            <button class="ui basic green compact button {{ yesClasses }}">
                <i class="checkmark icon"></i>
            </button>
            <button class="ui basic red compact button {{ noClasses }}">
                <i class="remove icon"></i>
            </button>
        </div>
        <input type="hidden" name="options_{{ view.clientItem.getConfirmationCode() }}"/>
    </td>
    <td id="status-{{ view.clientItem.getConfirmationCode() }}" class="status-cell">
        {% if view.clientItem.getAutoState() == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
            {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                <span class="under-review">Under Review by SpareFoot</span>
                <span class="error is-hidden">Did Not Move In</span>
                <span class="success is-hidden">Moved In</span>
            {% else %}
                <span class="under-review is-hidden">Under Review by SpareFoot</span>
                <span class="error{% if not isDisputed %} is-hidden{% endif %}">Did Not Move In {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %} (Reviewed){% endif %}</span>
                <span class="success{% if isDisputed %} is-hidden{% endif %}">Moved In {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %} (Reviewed){% endif %}</span>
            {% endif %}
        {% elseif view.isLate %}
            <span class="error">Did Not Move In</span><img id="activity-indicator-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        {% else %}
            <span class="error {% if not isDisputed %}is-hidden{% endif %}">Did Not Move In</span>
            <span class="success {% if isDisputed %}is-hidden{% endif %}">Moved In</span>
        {% endif %}

        {# Support notes #}
        {% if view.clientItem.stringStatementSupportNotes() %}
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="{{ view.clientItem.stringStatementSupportNotes() }}"><strong>NOTES</strong></a>
        {% endif %}
    </td>
    <td class="statement-verification">
        {% if 'consumer' in view.clientItem.getMoveInVerifiedBy()|keys %}
            <img src="/images/customer-verified.gif" width="19" height="20" alt="" />  Customer
        {% endif %}
        {% if view.clientItem.getMoveInVerifiedBy()|length > 1 %}
            <br /><br />
        {% endif %}
        {% if 'facility' in view.clientItem.getMoveInVerifiedBy()|keys %}
            <img src="/images/facility-verified.gif" width="19" height="20" alt="" />  Facility
        {% endif %}
    </td>
    {% endif %}

    {% if not view.facility %}
        <td id="facility-name-{{ view.clientItem.getConfirmationCode() }}"><a href="{{ url('statement_view') }}?id={{ view.statementId }}&facility={{ view.clientItem.getFacility().getId() }}">{{ view.clientItem.getFacility().getTitleWithCompanyCode()|e }}</a></td>
    {% endif %}

    <td id="customer-{{ view.clientItem.getConfirmationCode() }}">
        <span id="customer-info-{{ view.clientItem.getConfirmationCode() }}">
            {{ view.bookingExtra.customerInfo|nl2br|e }}
        </span>
        {% if view.clientItem.getUnitNumber() %}<br/>Unit {{ view.clientItem.getUnitNumber() }}{% endif %}
        {% if view.clientItem.getBookingType() == constant('\\Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE') %}
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        {% endif %}

        {% if view.bookingExtra.hasDuplicates %}
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        {% endif %}
        {% if view.clientItem.getAutoState() == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
            <div id="tenant-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden">
                {{ view.bookingExtra.tenantInfo|nl2br }}
                {% if view.clientItem.getUnitNumber() %}<br/>Unit {{ view.clientItem.getUnitNumber() }}{% endif %}
            </div>
        {% endif %}
    </td>
    <td id="date-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.getMoveIn()|date('m-d-Y') }}</td>
    <td id="reservation-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.getTimestamp()|date('m-d-Y') }}</td>
    <td id="baseBid-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringBaseBid() }}</td>
    <td id="amount-{{ view.clientItem.getConfirmationCode() }}">
        {% set billableInstance = call_static('\\Genesis_Service_BillableInstance', 'loadByConfirmationCodeStatementId', [view.clientItem.getConfirmationCode(), view.statementId]) %}
        {{ billableInstance ? ('$' ~ (billableInstance.getSparefootCharge()|number_format(2, '.', ','))) : '$0.00' }}
    </td>
    <td id="lifetime-{{ view.clientItem.getConfirmationCode() }}">
        <a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ view.clientItem.getLatestBillableInstance().getUnitPrice()|number_format(2, '.', ',') }}.">
            ${{ (view.clientItem.getLatestBillableInstance().getUnitPrice() * 12)|number_format(2, '.', ',') }}
        </a>
        <br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">
            {% if view.clientItem.getBidAmount() > 0 %}
                {% set lifetimeValueROI = ((view.clientItem.getLatestBillableInstance().getUnitPrice() * 12) / view.clientItem.getBidAmount()) * 100 %}
                {{ lifetimeValueROI|number_format(0, '.', ',') }}%
            {% endif %}
            <abbr data-content="Return on Investment">ROI</abbr>
        </a>
    </td>
</tr>
