{% extends 'layout.html.twig' %}
{% block content %}

{# improve performance #}
{#
    * @var view.clientStatement Genesis_Entity_LtvStatementInterface
#}
{% set numExistingLtvItems = view.clientStatement.getNumExistingLtvItems() %}
{% set newLateLtvItems = view.clientStatement.getNewLateLtvItems() %}

{#
 * lets make it easier to use this in the view, and always get it right
 #}
{% set RESIDUAL_AUTO_CONFIRM = constant('\\Genesis_Service_Feature::isActive')(constant('\\Genesis_Entity_Feature::MYFOOT_RESIDUAL_AUTOCONFIRMS'), {'account_id': view.clientStatement.getAccountId()}) ? true : false %}
{# can remove the script below after 6-10-2015, FAC-916 #}
<script>
var cdpEnabled = {{ view.clientStatement.getAccount().isPartiallyCdpEnabled() ? "true" : "false" }};
App.context = {facility_id:{{ view.facilityId ? view.facilityId : 'null' }},statement_id:{{ view.clientStatement.getStatementId() }}};
</script>

<div class="view-residual">
    {% set statementType = view.clientStatement.isCpaWithLtv() ? 'cpa' : 'residual' %}
    {{ include('statement/move-in-rate.html.twig', {
        'clientStatement': view.clientStatement,
        'showInterstitial': view.showInterstitial,
        'statementType': statementType
    }) }}

    {% if not view.clientStatement.isCpaWithLtv() %}
        <h1 class="ui header" id="statement-title">{{ view.clientStatement.getStatementTitle() }}</h1>

        {{ include('statement/facility-selector.html.twig', {
            'facilityCount': view.facilityCount,
            'facilityId': view.facilityId,
            'facilities': view.facilities,
            'statementId': view.clientStatement.getStatementId(),
            'hasItems': numExistingLtvItems or
                view.clientStatement.getNumNewNotFreeLtvItems() or
                view.clientStatement.getNewEarlyLtvItems() or
                newLateLtvItems
        }) }}

        <div id="js-message-box"></div>
    {% endif %}

    {% macro printColumnHeaders(clientStatement) %}
        <thead>
        <tr>
            <th data-sort="string" class="residual-status-header">Status</th>
            <th data-sort="string" class="verified-header">
                <a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said "Yes" is listed in this "Move-in Verified By" column.'>
                    Move-In&nbsp;Verified&nbsp;By <i class="fa fa-info-circle"></i>
                </a>
            </th>
            {% if not clientStatement.isFilteredByFacility() %}
                <th data-sort="string" class="facility-header">Facility</th>
            {% endif %}
            <th data-sort="string" class="customer-header">Customer</th>
            <th data-sort="string" class="scheduled-header">Scheduled Move-In</th>
            <th data-sort="string" class="reserved-header">Reserved</th>
            <th data-sort="string" class="sparefoot-fee-header">
                <a class="ui popup-text" data-content="{{ clientStatement.stringAcctResidualPercent() }} of the rent collected">
                    SpareFoot Fee <i class="fa fa-info-circle"></i>
                </a>
            </th>
        </tr>
        </thead>
    {% endmacro %}
    {% macro printClientItemRow(item, clientStatement, RESIDUAL_AUTO_CONFIRM) %}
        {% if item.isFreeBooking() %}
            {% return %}
        {% endif %}

        <tr id="{{ item.getConfirmationCode() }}">
            <td id="action-{{ item.getConfirmationCode() }}">
                <div class="statement-reservation-actions-residual">
                    {% set wasReviewed = (item.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED')) ? true : false %}
                    {% set underReview = (item.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW')) ? true : false %}
                    {% if (1 == 2) and RESIDUAL_AUTO_CONFIRM and item.isAutoConfirmed() %}
                        {% if wasReviewed %}
                            {% set checked = true %}
                        {% elseif underReview %}
                            {% set checked = false %}
                        {% else %}
                            {% set checked = true %}
                        {% endif %}

                    {% else %}
                        <input type="hidden" id="delinquent-{{ item.getConfirmationCode() }}" value="0" />
                        <p>This customer paid</p>
                        <h4><span class="unit-price">{% if item.isLate() %}
                                    $0
                                {% elseif item.getAmountCollected() > 0 %}
                                    {{ item.stringAmountCollected() }}
                                {% else %}
                                    $0
                                {% endif %}
                    </span></h4>
                        <p>for rent in {{ clientStatement.getStatementStartDate()|date('F Y') }}</p>
                    {% endif %}
                    <div id="status-{{ item.getConfirmationCode() }}">
                        <h6 class="under-review {{ underReview ? '' : 'is-hidden' }}">Under Review By SpareFoot</h6>
                        <h6 class="was-reviewed {{ wasReviewed ? '' : 'is-hidden' }}">Reviewed</h6>
                    </div>

                </div>
                <div class="statement-customer-paid-edit">
                    <a class="edit-rent-collected ui button secondary default">Edit</a>
                </div>
            </td>
            {% if RESIDUAL_AUTO_CONFIRM and item.isAutoConfirmed() %}
                <td id="status-{{ item.getConfirmationCode() }}">
                    {% if item.isAutoConfirmed() %}
                        {% if wasReviewed %}
                            {% set mode = 'success' %}
                        {% elseif underReview %}
                            {% set mode = 'review' %}
                        {% elseif item.isAutoDisputed() %}
                            {% set mode = 'error' %}
                        {% else %}
                            {% set mode = 'success' %}
                        {% endif %}
                    {% elseif item.isEarly() %}
                        {% set mode = 'early' %}
                    {% elseif item.isLate() %}
                        {% set mode = 'error' %}
                    {% else %}
                        {% set mode = 'error' %}
                    {% endif %}

                    <span class="early {{ mode == 'early' ? '' : 'is-hidden' }}">Move&nbsp;In&nbsp;Next&nbsp;Month</span>
                    <span class="under-review {{ mode == 'review' ? '' : 'is-hidden' }}">Under&nbsp;Review&nbsp;by&nbsp;SpareFoot</span>
                    <span class="error {{ mode == 'error' ? '' : 'is-hidden' }}">Did&nbsp;Not&nbsp;Move&nbsp;In {{ wasReviewed ? '(Reviewed)' : '' }}</span>
                    <span class="success {{ mode == 'success' ? '' : 'is-hidden' }}">Moved&nbsp;In {{ wasReviewed ? '(Reviewed)' : '' }}</span>
                    <img id="activity-indicator-{{ item.getConfirmationCode() }}" class="is-hidden" src="/images/loading.gif" />
                    {% if item.getSupportNotes() %}
                        <br /><a class="notes" data-original-title="Support Notes" data-html="{{ item.getSupportNotes() }}" data-position="right center"><strong>NOTES</strong></a>
                    {% endif %}
                </td>
            {% endif %}
            <td class="statement-verification">
                {% set verifiedBy = item.getMoveInVerifiedBy() %}
                {% if 'consumer' in verifiedBy|keys %}
                    <img src="/images/customer-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Customer
                {% endif %}
                {% if verifiedBy|length > 1 %}
                    <br /><br />
                {% endif %}
                {% if 'facility' in verifiedBy|keys %}
                    <img src="/images/facility-verified.gif" width="19" height="20" alt="" />&nbsp;&nbsp;Facility
                {% endif %}
            </td>
            {% if not clientStatement.isFilteredByFacility() %}
                <td id="facility-name-{{ item.getConfirmationCode() }}">
                    {% if not item.isLate() %}
                        <a class="edit-facility"><i class="pull-right fa fa-pencil"></i></a>
                    {% endif %}
                    <span class="facility-name">{{ item.getFacilityName() }}</span><br/>
                    <span class="unit-number-span">
                    {% if not item.isLate() %}
                        {% if item.getBookingUnitNumber() %}
                            Unit #: <span class="unit-number">{{ item.getBookingUnitNumber() }} </span>
                        {% else %}
                            <a class="edit-facility">Add unit number</a>
                        {% endif %}
                    {% endif %}
                    </span>
                </td>
            {% endif %}
            <td>
                <span id="customer-{{ item.getConfirmationCode() }}">
                {% if not item.isLate() %}
                    <a class="edit-customer-name"><i class="pull-right fa fa-pencil"></i></a>
                {% endif %}

                    {{ item.stringCustomerInfo()|nl2br }}
                </span>
                {% if item.isAutoConfirmed() %}
                    <div id="tenant-{{ item.getConfirmationCode() }}" class="is-hidden">
                        {{ item.stringTenantInfo()|nl2br }}
                        {% if item.getUnitNumber() %}
                            <br/>Unit {{ item.getUnitNumber() }}
                        {% endif %}
                    </div>
                {% endif %}

                {% if item.hasDuplicate() %}
                    <br/><a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                {% endif %}
                {% if item.isOffline() %}
                    <br/><a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
                {% endif %}
                {% if item.getSupportNotes() %}
                    <br/><a class="notes" data-original-title="Support Notes" data-html="{{ item.getSupportNotes() }}" data-position="right center"><strong>NOTES</strong></a>
                {% endif %}
            </td>
            <td id="date-{{ item.getConfirmationCode() }}">
                {% if not item.isLate() %}
                    <a class="edit-move-in-date"><i class="pull-right fa fa-pencil"></i></a>
                {% endif %}
                    {{ item.stringMoveInDate() }}

            </td>
            <td>{{ item.stringReservationDate() }}</td>
            <td id="sparefootfee-{{ item.getConfirmationCode() }}">{{ item.stringSparefootFee() }}</td>
        </tr>
    {% endmacro %}

    {% if newLateLtvItems %}
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">
                These customers had move-in dates during the last 10 days of 
                {{ (view.clientStatement.getStatementStartDate()|date_modify('-1 month'))|date('F') }}.
                If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking.
                If none of last month's customers moved in late, you don't need to do anything.
                We'll only bill you for customers you move to this month.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late_reservations">
                {{ _self.printColumnHeaders(view.clientStatement) }}
                <tbody id="late">
                    {% set sortedLateItems = newLateLtvItems|sort((a, b) => a.getMoveInDate() <=> b.getMoveInDate()) %}
                    {% for item in sortedLateItems %}
                        {{ _self.printClientItemRow(item, view.clientStatement, RESIDUAL_AUTO_CONFIRM) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% set reservationItems = RESIDUAL_AUTO_CONFIRM
        ? view.clientStatement.getNonAutoConfirmedLtvItems()
        : view.clientStatement.getNewLtvItems()
    %}
    {% if reservationItems %}
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">
                For the following reservations, please edit the customer details if needed, and let us know how much rent you collected in {{ view.clientStatement.getStatementStartDate()|date('F Y') }}. If the reservation moved in under a different name or at another one of your facilities, update it to be consistent with your records.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="new_reservations">
                {{ _self.printColumnHeaders(view.clientStatement) }}
                <tbody id="pending">
                    {% set sortedReservationItems = reservationItems|sort((a, b) => a.getMoveInDate() <=> b.getMoveInDate()) %}
                    {% for item in sortedReservationItems %}
                        {{ _self.printClientItemRow(item, view.clientStatement, RESIDUAL_AUTO_CONFIRM) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if RESIDUAL_AUTO_CONFIRM %}
        {% set autoConfirmedItems = view.clientStatement.getAutoConfirmedLtvItems() %}
        {% if autoConfirmedItems %}
            <h3 class="ui header">
                Auto-Matched
                <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_confirmed_reservations">
                    {{ _self.printColumnHeaders(view.clientStatement) }}
                    <tbody id="confirmed">
                        {% for item in autoConfirmedItems %}
                            {{ _self.printClientItemRow(item, view.clientStatement, RESIDUAL_AUTO_CONFIRM) }}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% endif %}

        {% set autoDisputedItems = view.clientStatement.getAutoDisputedLtvItems() %}
        {% if autoDisputedItems %}
            <h3 class="ui header">
                Unmatched
                <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
            </h3>
            <div class="table-responsive">
                <table class="ui table striped cell-headers sortable" id="auto_disputed_reservations">
                    {{ _self.printColumnHeaders(view.clientStatement) }}
                    <tbody id="disputed">
                        {% for item in autoDisputedItems %}
                            {{ _self.printClientItemRow(item, view.clientStatement, RESIDUAL_AUTO_CONFIRM) }}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% endif %}
    {% endif %}

    {% if numExistingLtvItems > 0 %}
        <h3 class="ui header">
            Tenants
            <div class="sub header">You've previously received rent from each of these AdNetwork tenants. Let us know how much you collected from them during {{ view.clientStatement.getStatementStartDate()|date('F Y') }}.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="existing_tenants">
                <thead>
                    <tr>
                        <th data-sort="string" class="residual-status-header">Status</th>
                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th data-sort="string" class="facility-header">Facility</th>
                        {% endif %}
                        <th data-sort="string" class="customer-header">Tenant</th>
                        <th data-sort="string" class="sparefoot-fee-header"><a class="ui popup-text" data-content="{{ view.clientStatement.stringAcctResidualPercent() }} of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
                        <th data-sort="string" class="total-rent-header">Total Rent Collected</th>
                    </tr>
                </thead>
                <tbody id="tenants">
                {% set filtered = view.clientStatement.isFilteredByFacility() ? true : false %}

                {% for item in view.clientStatement.getExistingLtvItems() %}
                    {% set wasDelinquent = (item.wasDelinquentLastStatement is defined and item.wasDelinquentLastStatement()) ? 1 : 0 %}

                    <tr id="{{ item.getConfirmationCode() }}">
                        <td id="action-{{ item.getConfirmationCode() }}">
                            <div class="statement-reservation-actions-residual">
                                <p>This customer paid</p>
                                <h4><span class="unit-price">
                                    {{ (item.getAmountCollected() > 0) ? item.stringAmountCollected() : '$0' }}
                                </span></h4>
                                <p>for rent in {{ view.clientStatement.getStatementStartDate()|date('F Y') }}</p>
                                <div id="status-{{ item.getConfirmationCode() }}">
                                    {{ wasDelinquent ? '<h6>Previously Delinquent</h6>' : '' }}
                                </div>
                            </div>
                            <div class="statement-customer-paid-edit">
                                <a class="edit-tenant-rent-collected ui button secondary default">Edit</a>
                            </div>

                        </td>
                        {% if not filtered %}
                            <td id="facility-name-{{ item.getConfirmationCode() }}">{{ item.getFacilityName() }}<br/>
                            <span class="unit-number-span">
                            {% if item.getBookingUnitNumber() != '' and item.getBookingUnitNumber() is not null %}
                                Unit #: <span class="unit-number">{{ item.getBookingUnitNumber() }} </span><a style="padding-left:15px" class="edit-unit-number"><i class="fa fa-pencil"></i></a>
                            {% else %}
                                <a class="edit-unit-number">Add unit number</a>
                            {% endif %}
                            </span>
                            </td>
                        {% endif %}
                        <td>
                            <span id="customer-info-{{ item.getConfirmationCode() }}">{{ item.stringCustomerInfo()|nl2br }}</span>
                            <span id="tenant-{{ item.getConfirmationCode() }}" class="is-hidden">{{ item.stringTenantInfo()|nl2br }}</span>
                            {{ item.getSupportNotes() ? '<br/><a class="notes" data-original-title="Support Notes" data-html="' ~ item.getSupportNotes() ~ '" data-position="right center"><strong>NOTES</strong></a>' : '' }}
                        </td>

                        <td id="sparefootfee-{{ item.getConfirmationCode() }}">{{ item.stringSparefootFee() }}</td>
                        <td id="lifetime-{{ item.getConfirmationCode() }}}">${{ constant('\\Genesis_Service_BillableInstance::getAmountCollectedSumByConfCode')(item.getConfirmationCode()) }}</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {{ include('statement/done-form.html.twig', {
        loggedUser: view.loggedUser,
        confirmedTime: view.confirmedTime,
        confirmations: view.confirmations,
        allowChanges: view.allowChanges
    }) }}

    <input type="hidden" id="residual_percent" name="residual_percent" value="{{ view.clientStatement.getAcctResidualPercent() }}" />

    <form id="edit-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the customer move in?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="customer-move-in-deny" >No</a>
                            <a class="ui button primary pull-right" id="customer-move-in-confirm">Yes</a>

                        </div>
                    </div>
                    <div id="edit-rent-step-2-A" style="display:none">
                        <input type="hidden" id="early_late" name="early_late" value="0" />
                        <input type="hidden" id="into_date" name="into_date" value="{{ view.clientStatement.getStatementStartDate() }}" />
                        <p>How much rent did you collect?</p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id" value="{{ view.clientStatement.getStatementId() }}" />
                    <input type="hidden" name="residual_percent" value="{{ view.clientStatement.getAcctResidualPercent() }}" />

                    <a class="ui button default pull-left"  id="edit-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-rent-submit" style="display:none">Submit</a>

                </div>

            </div>
        </div>
    </form>

    <form id="edit-facility-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-facility-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Facility</h4>
                </div>
                <div class="modal-body">
                    <p>Facility</p>
                    <select id="sister-facility-select" name="facility_id" class="form-control">
                        {% for facility in view.clientStatement.getSisterFacilityList() %}
                            {% if facility.getApproved() and facility.getPublished() and facility.getBillableEntityId() %}
                                <option value="{{ facility.getId() }}">{{ facility.getTitleWithCompanyCode() }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                    <p style="margin-top:30px">Unit Number (optional)</p>
                    <input type="text" id="unit-number" name="unit_number" value="" class="form-control" />

                </div>
                <div class="modal-footer">
                    <a id="edit-facility-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="sister-facility-select-submit"/>
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="{{ view.clientStatement.getStatementId() }}" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-unit-number-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-unit-number-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Unit Number</h4>
                </div>
                <div class="modal-body">
                    <p>Unit Number</p>
                    <input type="text" name="unit_number" value="" class="form-control unit-number" id="edit-unit-number-value" />

                </div>
                <div class="modal-footer">
                    <a id="edit-unit-number-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" id="edit-unit-number-submit" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="{{ view.clientStatement.getStatementId() }}" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-customer-name-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-customer-name-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Customer Name</h4>
                </div>
                <div class="modal-body">
                    <p>Please provide us with accurate contact information to appear on your bill.</p>
                    <p><input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" class="form-control" /></p>
                    <p><input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" class="form-control" /></p>

                </div>
                <div class="modal-footer">
                    <a id="edit-customer-name-modal-cancel" class="ui button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="{{ view.clientStatement.getStatementId() }}" />
                </div>
            </div>
        </div>
    </form>

    <form id="edit-tenant-rent-collected-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title" id="edit-tenant-rent-collected-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height:300px;">
                    <div id="edit-tenant-rent-step-1" style="text-align:center;padding-top:10%;">
                        <h4>Did the Customer pay you rent in {{ view.clientStatement.getStatementStartDate()|date('F \\o\\f Y') }}?</h4>
                        <div style="width:30%;margin:0 auto;padding-top:20px;">
                            <a class="ui button default pull-left" id="tenant-rent-collected-deny" >No</a>
                            <a class="ui button primary pull-right" id="tenant-rent-collected-confirm">Yes</a>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-A" style="display:none">
                        <p>Please enter how much rent the customer paid you during {{ view.clientStatement.getStatementStartDate()|date('F Y') }}</p>
                        <div class="indent-left">
                            Enter new amount:
                            <div class="input-group">
                                <span class="input-group-addon">$</span><input type="text" id="rent-tenant-other" name="rent_other" value="" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div id="edit-tenant-rent-step-2-B" style="display:none">
                        This customer
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_PREPAID') }}" id="tenant-rent-zero" /> customer has previously pre-paid</label>
                        </div>
                        <div class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_DELIQUENCY') }}" id="tenant-rent-deliquent" /> is delinquent on their rent payments</label>
                        </div>
                        <div class="radio controls only-existing-tenants">
                            <label><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVED_OUT') }}" id="tenant-rent-moved-out" /> moved out (this means the customer last paid you rent in {{ (view.clientStatement.getStatementStartDate()|date_modify('-1 month'))|date('F Y') }})</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVED_OUT') }}" id="tenant-rent-lien"/> lien sale of delinquent unit</label>
                        </div>
                        <div id="lien-sale-option" class="radio controls only-existing-tenants show-after-delinquency">
                            <label><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_OTHER') }}" id="tenant-rent-other"/> other</label>
                        </div>

                    </div>

                </div>

                <div class="modal-footer">
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="id"  value="{{ view.clientStatement.getStatementId() }}" />
                    <input type="hidden" name="residual_percent" value="{{ view.clientStatement.getAcctResidualPercent() }}" />
                    <input type="hidden" name="is_tenant" value="1"/>
                    <a class="ui button default pull left"  id="edit-tenant-rent-cancel" style="display:none" data-dismiss="modal" >Cancel</a>
                    <a class="ui button primary pull-right" id="edit-tenant-rent-submit" style="display:none">Submit</a>

                </div>
            </div>
        </div>
    </form>

    <form id="edit-move-in-date-modal" class="modal fade" action="/billing/updateresidualstatement" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="edit-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                    <h4 class="modal-title">Edit Move-In Date</h4>
                </div>
                <div class="modal-body">
                    <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                    <input type="text" id="edit-move-in-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="edit-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="submit" class="ui primary button" value="Save" />
                    <input type="hidden" name="confirmation_code" />
                    <input type="hidden" name="statement_id" value="{{ view.clientStatement.getStatementId() }}" />
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}