{% if view.loggedUser.isMyfootAdmin() and view.confirmations|length %}
    <h4>Statement Confirmations</h4>
    <div class="table-responsive">
        <table class="ui table striped cell-headers sortable" id="confirmations-table">
            <thead>
                <tr>
                <th data-sort="string">User</th>
                <th data-sort="string">Email</th>
                <th data-sort="string">Role</th>
                <th data-sort="string">Confirmation Date</th>
                </tr>
            </thead>
            <tbody>
                {% for confirmation in view.confirmations %}
                <tr>
                <td>{{ confirmation.getUser().getFullName() }}</td>
                <td><a href="mailto:{{ confirmation.getUser().getEmail() }}">{{ confirmation.getUser().getEmail() }}</a></td>
                <td>{{ confirmation.getUser().getMyfootRole()|capitalize }}</td>
                <td>{{ confirmation.getConfirmationTime()|date('H:ia \\o\\n F j, Y ') }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endif %}

<div id="statement-confirmed-message" class="ui message info" style="{{ view.confirmedTime ? null : 'display: none' }}">
 Statement confirmed by <b>{{ view.loggedUser.getFullName() }}</b> <em>(<a href="mailto:{{ view.loggedUser.getEmail() }}">{{ view.loggedUser.getEmail() }})</a></em>  at <span id="confirmation-time" style="font-weight: bold">{{ view.confirmedTime|date('H:ia \\o\\n F j, Y ') }}</span>.<br/>
    {% if view.allowChanges %}
    You can still edit and resubmit this statement until {{ view.allowChanges|date('F j, Y ') }}.<br/><br/>
    <a id="resubmit-statement-button" class="ui button primary" href="#" >Resubmit</a>
    {% endif %}
</div>
{% if view.allowChanges %}
    <a id="confirm-statement-button" class="ui button primary" href="#" style="{{ view.confirmedTime ? 'display: none' : null }}">Confirm</a>
{% endif %}
