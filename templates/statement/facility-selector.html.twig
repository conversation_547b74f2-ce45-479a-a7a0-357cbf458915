{% if view.facilityCount > 1 %}
    <select class="form-control" js-change-facility id="statement-select-facility">
    <option value="{{ url('statement_view') }}?id={{ view.statementId }}">All Facilities</option>
        {% for facility in view.facilities %}
        <option value="{{ url('statement_view') }}?id={{ view.statementId }}&facility={{ facility.getId() }}"{{ (facility.getId() == view.facilityId) ? " selected='selected'" : null }}>{{ facility.getTitleWithCompanyCode() }}</option>
        {% endfor %}
    </select>

    {% if not view.hasItems %}
    <p>There are no customers for this facility.</p>
    {% endif %}
{% endif %}
