<tr id="{{ view.consumerContact.getId() }}" class="cpa-contact">
    <td id="action-{{ view.consumerContact.getId() }}">
        <div class="statement-reservation-actions">
            <div class="ui basic icon buttons" data-toggle="buttons">
                <button class="ui basic green compact button confirm-consumercontact-button">
                    <i class="checkmark icon"></i>
                </button>
                <button class="ui basic red compact button dispute-consumercontact-button active">
                    <i class="remove icon"></i>
                </button>
            </div>
        </div>
    </td>
    {% if not view.clientStatement.isFilteredByFacility() %}
    <td id="facility-name-{{ view.consumerContact.getId() }}">
        <a href="{{ url('statement_view') }}?id={{ view.clientStatement.getStatementId() }}&facility={{ view.consumerContact.getListingAvailId() }}">{{ view.consumerContact.getFacility().getTitle() }}</a>
    </td>
    {% endif %}
    <td id="customer-{{ view.consumerContact.getId() }}">
        <span id="customer-info-{{ view.consumerContact.getId() }}">
            {{ view.consumerContact.stringCustomerInfo()|e|nl2br }}
        </span>
    </td>
    <td>{{ view.consumerContact.getTimestamp()|date('Y-m-d') }}</td>
</tr>
