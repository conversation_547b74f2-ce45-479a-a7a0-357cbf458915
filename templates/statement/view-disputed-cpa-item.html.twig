{% set reviewRuling = view.clientItem.getReviewRuling() %}
{% if reviewRuling %}
    {% set wasDenied = reviewRuling.getValue() == constant('\\Genesis_Entity_BookingMeta::REVIEW_RULING_DENY') %}
{% else %}
    {% set wasDenied = false %}
{% endif %}
{% set rulingReason = view.clientItem.getRulingReason() %}

<tr class="cpa-item" data-id="{{ view.clientItem.getConfirmationCode() }}">
    {% if not view.clientItem.isFreeBooking() %}
    <td id="dispute-action-{{ view.clientItem.getConfirmationCode() }}" class="statement-actions dispute-icon">
        {% if wasDenied %}
            <i class="remove icon"></i>
        {% else %}
            <i class="checkmark icon"></i>
        {% endif %}
    </td>

    <td id="dispute-status-{{ view.clientItem.getConfirmationCode() }}" class="status-cell">
        {% if wasDenied %}
            <span class="dispute-status">Dispute Denied</span>
        {% else %}
            <span class="dispute-status">Dispute Approved</span>
        {% endif %}

        {% if view.clientItem.isAutoConfirmed() %}
            <span class="error{{ wasDenied ? '' : ' is-hidden' }}">Did Not Move In {{ view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
            <span class="success{{ wasDenied ? ' is-hidden' : '' }}">Moved In {{ view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
        {% elseif view.clientItem.isEarly() %}
            <span>Move In Next Month</span>
            <span class="success is-hidden">Moved In</span>
        {% elseif view.clientItem.isLate() %}
            <span class="error">Did Not Move In</span><img id="activity-indicator-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        {% else %}
            <span class="error {{ view.clientItem.isDisputed() ? '' : 'is-hidden' }}">Did Not Move In</span>
            <span class="success {{ view.clientItem.isDisputed() ? 'is-hidden' : '' }}">Moved In</span>
        {% endif %}

        {% if view.clientItem.getSupportNotes() %}
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="{{ view.clientItem.getSupportNotes() }}"><strong>NOTES</strong></a>
        {% endif %}
    </td>

    {% if rulingReason %}
        <td id="ruling-reason-{{ view.clientItem.getConfirmationCode() }}">
            {{ rulingReason.getValue() }}
        </td>
    {% else %}
            <td id="ruling-reason-{{ view.clientItem.getConfirmationCode() }}">
                <span>N/A</span>
            </td>
    {% endif %}
    {% endif %}

    {% if not view.clientStatement.isFilteredByFacility() %}
        <td id="facility-name-{{ view.clientItem.getConfirmationCode() }}"><a href="{{ url('statement_view') }}?id={{ view.clientStatement.getStatementId() }}&facility={{ view.clientItem.getFacility().getId() }}">{{ view.clientItem.stringFacilityName()|e }}</a></td>
    {% endif %}

    <td id="customer-{{ view.clientItem.getConfirmationCode() }}">
        <span id="customer-info-{{ view.clientItem.getConfirmationCode() }}">
            {{ view.clientItem.stringCustomerInfo()|e|nl2br }}
        </span>
        {{ view.clientItem.getBookingUnitNumber() ? '<br/>Unit ' ~ view.clientItem.getBookingUnitNumber() : '' }}
        {% if view.clientItem.isOffline() %}
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        {% endif %}

        {% if view.clientItem.hasDuplicate() %}
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        {% endif %}
        {% if view.clientItem.isAutoConfirmed() %}
            <div id="tenant-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden">
                {{ view.clientItem.stringTenantInfo()|nl2br }}
                {{ view.clientItem.getUnitNumber() ? '<br/>Unit ' ~ view.clientItem.getUnitNumber() : '' }}
            </div>
        {% endif %}
    </td>
    <td id="date-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringMoveInDate() }}</td>
    <td id="reservation-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringReservationDate() }}</td>
    <td id="baseBid-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringBaseBid() }}</td>
    <td id="amount-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringSparefootFee() }}</td>
    <td id="lifetime-{{ view.clientItem.getConfirmationCode() }}"><a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of {{ view.clientItem.stringUnitPrice() }}.">{{ view.clientItem.stringLifetimeValue() }}</a><br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">{{ view.clientItem.stringLifetimeValueROI() }} <abbr data-content="Return on Investment">ROI</abbr></a></td>
</tr>
