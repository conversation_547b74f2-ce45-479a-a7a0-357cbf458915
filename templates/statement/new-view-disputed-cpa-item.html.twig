{% set billableInstance = view.clientItem.getLatestBillableInstance() %}
{% if not billableInstance %}
    <h1>{{ view.clientItem.getConfirmationCode() }}</h1>
{% endif %}
{% set reviewRuling = view.clientItem.getReviewRuling() %}
{% if reviewRuling %}
    {% set wasDenied = reviewRuling.getValue() == constant('\\Genesis_Entity_BookingMeta::REVIEW_RULING_DENY') %}
{% else %}
    {% set wasDenied = false %}
{% endif %}
{% set rulingReason = view.clientItem.getReviewRulingReason() %}

<tr class="cpa-item" data-id="{{ view.clientItem.getConfirmationCode() }}">
    {% if view.clientItem.getFree() != 1 %}
    <td id="dispute-action-{{ view.clientItem.getConfirmationCode() }}" class="statement-actions dispute-icon">
        {% if wasDenied %}
            <i class="remove icon"></i>
        {% else %}
            <i class="checkmark icon"></i>
        {% endif %}
    </td>

    <td id="dispute-status-{{ view.clientItem.getConfirmationCode() }}" class="status-cell">
        {% if wasDenied %}
            <span class="dispute-status">Dispute Denied</span>
        {% else %}
            <span class="dispute-status">Dispute Approved</span>
        {% endif %}

        {% if view.clientItem.getAutoState() == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
            <span class="error{% if not wasDenied %} is-hidden{% endif %}">Did Not Move In {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %} (Reviewed){% endif %}</span>
            <span class="success{% if wasDenied %} is-hidden{% endif %}">Moved In {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %} (Reviewed){% endif %}</span>
        {% elseif view.clientItem.isEarly() %}
            <span>Move In Next Month</span>
            <span class="success is-hidden">Moved In</span>
        {% elseif view.clientItem.isLate() %}
            <span class="error">Did Not Move In</span><img id="activity-indicator-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        {% else %}
            <span class="error {% if not view.clientItem.isDisputed() %}is-hidden{% endif %}">Did Not Move In</span>
            <span class="success {% if view.clientItem.isDisputed() %}is-hidden{% endif %}">Moved In</span>
        {% endif %}

        {# Support notes #}
        {% if view.clientItem.getSupportNotes() %}
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="{{ view.clientItem.getSupportNotes() }}"><strong>NOTES</strong></a>
        {% endif %}
    </td>

    {% if rulingReason %}
        <td id="ruling-reason-{{ view.clientItem.getConfirmationCode() }}">
            {{ rulingReason.getValue() }}
        </td>
    {% else %}
        <td id="ruling-reason-{{ view.clientItem.getConfirmationCode() }}">
            <span>N/A</span>
        </td>
    {% endif %}
    {% endif %}

    {% if not view.isFilteredByFacility %}
        <td id="facility-name-{{ view.clientItem.getConfirmationCode() }}"><a href="{{ url('statement_view') }}?id={{ view.statementId }}&facility={{ view.clientItem.getFacility().getId() }}">{{ view.clientItem.getFacility().getTitleWithCompanyCode()|e }}</a></td>
    {% endif %}

    <td id="customer-{{ view.clientItem.getConfirmationCode() }}">
        <span id="customer-info-{{ view.clientItem.getConfirmationCode() }}">
            {{ view.bookingExtra.customerInfo|nl2br|e }}
        </span>
        {% if view.clientItem.getUnitNumber() %}<br/>Unit {{ view.clientItem.getUnitNumber() }}{% endif %}
        {% if view.clientItem.getBookingType() == constant('\\Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE') %}
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        {% endif %}

        {% if view.bookingExtra.hasDuplicates %}
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        {% endif %}
        {% if view.clientItem.getAutoState() == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
            <div id="tenant-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden">
                {{ view.bookingExtra.tenantInfo|nl2br }}
                {% if view.clientItem.getUnitNumber() %}<br/>Unit {{ view.clientItem.getUnitNumber() }}{% endif %}
            </div>
        {% endif %}
    </td>
    <td id="date-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.getMoveIn()|date('m-d-Y') }}</td>
    <td id="reservation-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.getTimestamp()|date('m-d-Y') }}</td>
    <td id="baseBid-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringBaseBid() }}</td>
    <td id="amount-{{ view.clientItem.getConfirmationCode() }}">{{ billableInstance ? ('$' ~ (billableInstance.getSparefootCharge()|number_format(2, '.', ','))) : '$0.00' }}</td>
    <td id="lifetime-{{ view.clientItem.getConfirmationCode() }}">
        <a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ billableInstance.getUnitPrice() }}.">
            ${{ (billableInstance.getUnitPrice() * 12)|number_format(2, '.', ',') }}
        </a>
        <br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">
            {% if view.clientItem.getBidAmount() > 0 %}
                {% set lifetimeValueROI = ((billableInstance.getUnitPrice() * 12) / view.clientItem.getBidAmount()) * 100 %}
                {{ lifetimeValueROI|number_format(0, '.', ',') }}%
            {% endif %}
            <abbr data-content="Return on Investment">ROI</abbr>
        </a>
    </td>
</tr>
