{% extends 'layout.html.twig' %}

{% block content %}
{% if app.request.server.get('HTTP_USER_AGENT') and 'MSIE' in app.request.server.get('HTTP_USER_AGENT') %}
    <div class="ui negative message">
        <i class="close icon"></i>
        <div class="header">
            Make sure your popup blocker is turned off.
        </div>
    </div>
{% endif %}

{% for clientStatement in view.openStatements %}
    <div class="ui segment">
        <div class="ui top left attached label">Open Statement</div>
        <h2 class="ui header">
            {{ clientStatement.getStatementStartDate()|date('F j') }}-{{ clientStatement.getStatementEndDate()|date('j') }}
            <div class="sub header">Rent Collected</div>
        </h2>

        <div class="table-responsive">
            <table class="ui table very basic cell-headers">
                <tr>
                    <td>Reconciliation Deadline</td>
                    <td>{{ clientStatement.getReconciliationEndDate()|date('F j') }}</td>
                </tr>
                <tr>
                    <td>Current Tenants</td>
                    <td>{{ clientStatement.getNumLtvItemsGettingBill() }} out of {{ clientStatement.getNumBookingItems() }}</td>
                </tr>
                <tr>
                    <td>Current SpareFoot Fees</td>
                    <td>{{ clientStatement.stringTotalBookingCharge() }}</td>
                </tr>
            </table>
        </div>

        <div>
            {% if not call_static('\\Genesis_Service_Feature', 'isActive', ['myfoot.disable_reconcile_statement_button']) %}
                <a id="open_statement" class="ui button blue" href="{{ url('statement_' ~ view.reconcileStatementAction) }}?id={{ clientStatement.getStatementId() }}">Reconcile Statement</a>
            {% endif %}
            <div class="ui dropdown button right">
                <div class="text">Download</div>
                <i class="dropdown icon"></i>
                <div class="menu">
                    <a class="item" href="{{ url('statement_viewpdf') }}?id={{ clientStatement.getStatementId() }}">
                        <i class="file pdf outline icon"></i> PDF</a>
                    <a class="item" href="{{ url('statement_viewcsv') }}?id={{ clientStatement.getStatementId() }}">
                        <i class="file excel outline icon"></i> Excel</a>
                </div>
            </div>
        </div>

        <div class="ui divided list">
            {% if 'SLA' in view.loggedUser.getAccount().getInfoString() %}
                <a class="item" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">
                    <div class="ui blue horizontal label">How To</div>
                    Use SiteLink to reconcile my statement
                </a>
            {% endif %}
        </div>
    </div>
{% endfor %}

{{ include('statement/billing-history.html.twig') }}

<table id="statements" data-type="residual" class="table ui striped cell-headers sortable">
    <thead>
        <tr>
            <th></th>
            <th>Tenant Fees</th>
            <th>Tenants</th>
            <th class="no-sort"></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="4">&nbsp;</td>
        </tr>
        <tr id="payload-error">
            <td colspan="4">Error</td>
        </tr>
        <tr id="no-content">
            <td colspan="4">No receipts found</td>
        </tr>
    </tbody>
</table>

{% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD')) %}
    <h2 id="billing-history">Billing History</h2>
    <div class="ui segment basic">
        <a class="ui button primary" href="{{ url('statement_receipts') }}">View Billing History</a>
    </div>
{% endif %}
{% endblock %}