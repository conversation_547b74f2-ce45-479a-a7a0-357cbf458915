<form id="dispute-autoconfirmed-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal" id="dispute-autoconfirmed-modal-close">×</button>
                <h4 class="modal-title">What's wrong with this auto-matched move-in?</h4>

            </div>

            <div class="modal-body">
                <div style="padding-bottom:20px">We matched this SpareFoot customer to a new tenant in your software:</div>
                <div class="table-responsve">
                    <table class="table">
                        <tr>
                            <th>SpareFoot Reservation</th>
                            <th>Your Matched Tenant</th>
                        </tr>
                        <tr>
                            <td id="dispute-autoconfirmed-customer-info"></td>
                            <td id="dispute-autoconfirmed-tenant-info"></td>
                        </tr>
                    </table>
                </div>

                If you still believe SpareFoot should not charge you for this customer, please provide as much detail as you can about why this match was made in error. Our billing team will review the information and follow up with you.
                <textarea id="autoconfirmed-dispute-text" name="customer_dispute-reason-other" class="form-control"></textarea>
            </div>

            <div class="modal-footer">
                <a class="ui button" data-dismiss="modal" id="dispute-autoconfirmed-modal-cancel">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui button primary" value="Submit" id="dispute-autoconfirmed-submit" />
            </div>
        </div>
    </div>
</form>
