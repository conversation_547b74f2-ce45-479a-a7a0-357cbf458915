{% extends 'layout.html.twig' %}
{% block content %}

{#
    Cpa Statement:<br/>
    Name: {{ view.clientStatement.getStatementTitle() }}<br/>
    Num Bookings: {{ view.clientStatement.getNumBookingItems() }}<br/>
    Moved-In Bookings: {{ view.clientStatement.getNumMovedInCpaItems() }}<br/>
    Move-In Rate: {{ view.clientStatement.stringMoveInRate() }}<br/>
    Bookings Fee: {{ view.clientStatement.stringTotalBookingCharge() }}<br/>
    Products Fee: {{ view.clientStatement.stringTotalProductCharge() }}<br/>
    Total Fee: {{ view.clientStatement.stringTotalCharge() }}<br/>
    <br/>
#}

<script>
    App.context = {
        facility_id: {{ view.facilityId ? view.facilityId : 'null' }},
        statement_id: {{ view.clientStatement.getStatementId() }}
    };
</script>

<div class="view-cpa">
    {{ include('statement/move-in-rate.html.twig', {
        'clientStatement': view.clientStatement,
        'showInterstitial': view.showInterstitial,
        'statementType': 'cpa'
    }) }}
    {% set isFree = false %}
    {% set autoConfirmedBookings = [] %}
    {% for item in view.clientStatement.getAutoConfirmedBookingItems() %}
        {% if item.isBidTypePercent() %}
            {% set autoConfirmedBookings = autoConfirmedBookings|merge([item]) %}
        {% endif %}
    {% endfor %}
    {% set autoDisputedBookings = [] %}
    {% for item in view.clientStatement.getAutoDisputedBookingItems() %}
        {% if item.isBidTypePercent() %}
            {% set autoDisputedBookings = autoDisputedBookings|merge([item]) %}
        {% endif %}
    {% endfor %}
    {% set regularBookings = view.clientStatement.getNoAutoStateBookingCpaItems() %}
    {% set reviewedBookings = view.clientStatement.getReviewedBookingCpaItems() %}
    {% set lateBookings = view.clientStatement.getLateCpaItems() %}
    {% set freeBookings = view.clientStatement.getFreeCpaItems() %}

    {# if all facilities are manual, there should only be one section #}
    {% set allManual = true %}
    {% for facility in view.facilities %}
        {% if facility.getCorporation().getSourceId() != constant('\\Genesis_Entity_Source::ID_MANUAL') %}
            {% set allManual = false %}
            {% break %}
        {% endif %}
    {% endfor %}
    {% if allManual %}
        {% set regularBookings = regularBookings|merge(autoDisputedBookings) %}
        {% set autoDisputedBookings = null %}
    {% endif %}

    {# modal guide #}
    {% if view.arr_softwares|length > 0 and ([21, 23]|intersect(view.arr_softwares)) %}
        <div id="guides-modal" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">Do you HATE reconciling your statement?</h4>
                    </div>
                    <div class="modal-body">
                        <div class="modal-guides">
                            <p>Yeah, we know...</p>
                            <p>So we figured out the best way to use your management software for a more efficient reconciliation. Simply view the guide below and follow the steps. You'll enjoy more move-ins, which means higher rankings in our search results, which means EVEN MORE move-ins. Pretty cool cycle, huh?<br /><br /></p>

                            {% if view.arr_softwares[21] is defined %}
                                <p><a class="ui button primary" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">View Guide</a> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Reconciling with SiteLink Stand Alone</a></p>
                            {% endif %}
                            {% if view.arr_softwares[23] is defined %}
                                <p><a class="ui button primary" href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">View Guide</a> <a href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">Reconciling with WebSelfStorage</a></p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="#" data-dismiss="modal" class="ui button">Close</a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <h1 class="ui header" id="statement-title">
        {% if not view.clientStatement.isFilteredByFacility() %}
            {{ view.clientStatement.getStatementStartDate()|date('F Y') }}
        {% else %}
            {{ view.clientStatement.getFilteredFacility().getTitle() }} - {{ view.clientStatement.getStatementStartDate()|date('F Y') }}
        {% endif %}
    </h1>

    {% if _server['HTTP_USER_AGENT'] is defined and ('MSIE' in _server['HTTP_USER_AGENT']) %}
        <div class="ui negative message">
            <i class="close icon"></i>
            <div class="header">Make sure your popup blocker is turned off.</div>
        </div>
    {% endif %}

    {% if constant('\\Genesis_Service_Feature::isActive')(constant('\\Genesis_Entity_Feature::MYFOOT_SHOW_WSS_UPLOADER'), {'account_id': view.clientStatement.getAccountId()}) %}
        {% set hasManualCpaFacility = false %}
        {% for facility in view.facilities %}
            {% if facility.getPublished() 
                and facility.getCorporation().getSourceId() == constant('\\Genesis_Entity_Source::ID_MANUAL')
                and facility.getCorporation().getAccount().getCpa() %}
                {% set hasManualCpaFacility = true %}
                {% break %}
            {% endif %}
        {% endfor %}
        {% if hasManualCpaFacility %}
            <div class="ui warning message">
                <i class="close icon"></i>
                <div class="header">Save time! We'll reconcile your statement for you.</div>
                <p>
                    <strong>Do you use WebSelfStorage? </strong>
                    <a href="{{ url('statement_upload_mil') }}?id={{ view.clientStatement.getStatementId() }}">Upload your move-in list here</a> for automatic reconciliation.
                </p>
            </div>
        {% endif %}
    {% endif %}

    {% set hasItems = regularBookings or
        autoConfirmedBookings or
        lateBookings or
        freeBookings %}

    {% if view.clientStatement.isCpaWithLtv() %}
        {% set hasItems = hasItems or
            view.clientStatement.getNumExistingLtvItems() or
            view.clientStatement.getNumNewNotFreeLtvItems() or
            view.clientStatement.getNewEarlyLtvItems() or
            view.clientStatement.getNewLateLtvItems() %}
    {% endif %}
    {{ include('statement/facility-selector.html.twig', {
        'facilityCount': view.facilityCount,
        'facilityId': view.facilityId,
        'facilities': view.facilities,
        'statementId': view.clientStatement.getStatementId(),
        'hasItems': hasItems
    }) }}

    <div id="js-message-box"></div>

    {% if autoConfirmedBookings %}
        <div class="ui message">
            <i class="close icon"></i>
            <div class="header"><a href="http://blog.sparefoot.com/sparefoot-reconciliation/" target="_blank">Read more</a> about your new, automated SpareFoot statement.</div>
        </div>
    {% endif %}

    {% if view.isMIRFElegible and regularBookings %}

        <h3 class="ui header">
            Estimated Minimum Move-In Rate Fee
            {% if view.mirfPercentage == (constant('\\Genesis_Util_NewMirfCalculation::MIR_FLOOR') * 100) %}
                <div class="sub header">Non-integrated facilities will be charged an additional fee if their move-in rate is less than 50%. The estimated Minimum Move-In Rate Fee is shown below. <a href="https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR" target="_blank">Learn more</a></div>
            {% endif %}
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="mirf-table">
                <thead>
                    <tr>
                        <th>Facility</th>
                        <th>Move-Ins</th>
                        <th><a class="ui popup-text" data-html='SpareFoot only counts one reservation per<br/>customer when calculating move-in rates<br/>for a given statement period, even if that<br/>customer has made multiple reservations.'>Unique Reservations <i class="fa fa-info-circle"></i></a></th>
                        <th>Move-In Rate</th>
                        <th>Estimated Move-In Rate Floor Charge</th>
                    </tr>
                </thead>
                <tbody>
                    {% for facilityData in view.MIRFData.facilities %}
                        {% if (view.clientStatement.isFilteredByFacility() and view.clientStatement.getFilteredFacility().getId() == facilityData.facility_id) or not view.clientStatement.isFilteredByFacility() %}
                            <tr data-id="{{ facilityData.facility_id }}">
                                <td>
                                    <a href="{{ url('statement_view') }}?id={{ view.clientStatement.getStatementId() }}&facility={{ facilityData.facility_id }}">
                                        {{ facilityData.facility_name|e }}
                                    </a>
                                </td>
                                <td>{{ facilityData.total_pending }}</td>
                                <td>{{ facilityData.total_bookings }}</td>
                                <td>{{ facilityData.reservation_rate ~ " %" }}</td>
                                <td data-sort-value="{{ facilityData.mirf }}">
                                    <span>
                                        {{ "$" ~ facilityData.mirf|number_format(2) }}
                                    </span>
                                    <span id="loading-spinner" style="display:none">
                                        <img id="loading-spinner" src="/images/loading.gif" width="15" height="15" />
                                        Loading Charge...
                                    </span>
                                </td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if regularBookings %}
        {% do view.clientStatement.sortItems(regularBookings) %}
        <h3 class="ui header">
            Needs Your Review
            <div class="sub header">Please select the "X" next to each customer who did not move in.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="all-statements" data-type="pending">
                <thead>
                    <tr>
                        {% if not isFree %}
                            <th class="no-sort"><!-- √ or X --></th>
                            <th class="no-sort status-col">Status</th>
                            <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        {% endif %}

                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in regularBookings %}
                        {{ include('statement/view-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if autoConfirmedBookings %}
        {% do view.clientStatement.sortItems(autoConfirmedBookings) %}

        <h3 class="ui header">
            Auto-Matched
            <div class="sub header">These customers were automatically matched to tenants in your management software.</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-statements" data-type="confirmed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>

                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in autoConfirmedBookings %}
                        {{ include('statement/view-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if autoDisputedBookings %}
        {% do view.clientStatement.sortItems(autoDisputedBookings) %}
        <h3 class="ui header">
            Unmatched
            <div class="sub header">We weren't able to match these customers to tenants in your management software. To save you time, we marked these customers as "did not move in".</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="auto-disputed" data-type="disputed">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>

                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in autoDisputedBookings %}
                        {{ include('statement/view-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if lateBookings %}
        {% do view.clientStatement.sortItems(lateBookings) %}
        <h3 class="ui header">
            Late Move-Ins
            <div class="sub header">
                These customers had move-in dates during the last 10 days of
                {{ (view.clientStatement.getStatementStartDate()|date_modify('-1 month'))|date('F') }}.
                If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking.
                If none of last month's customers moved in late, you don’t need to do anything. We'll only bill you for customers you move to this month.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="late-move-ins" data-type="late">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th>
                            <a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>
                                Move-In Verified By <i class="fa fa-info-circle"></i>
                            </a>
                        </th>
                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th>
                            <a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">
                                Lifetime Value <i class="fa fa-info-circle"></i>
                            </a>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in lateBookings %}
                        {{ include('statement/view-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if reviewedBookings %}
        {% do view.clientStatement.sortItems(reviewedBookings) %}
        <h3 class="ui header">
            Previously Disputed Move-ins
            <div class="sub header">
                Our support team has reviewed your reservations disputed from the previous month.
                The disputed reservations that have been over ruled will show up on this months billing cycle.
            </div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="reviewed-statements" data-type="reviewed">
                <thead>
                    <tr>
                        {% if not isFree %}
                            <th class="no-sort"><!-- √ or X --></th>
                            <th class="no-sort status-col">Status</th>
                        {% endif %}

                        <th>Reason for ruling</th>

                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}

                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in reviewedBookings %}
                        {{ include('statement/view-disputed-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if freeBookings %}
        {% do view.clientStatement.sortItems(freeBookings) %}
        <h3 class="ui header">
            No-Fee Reservations
            <div class="sub header">We sent you these reservations from SpareFoot products without transaction fees (GeoPages, SiteBuilder, and Booking Widgets).</div>
        </h3>
        <div class="table-responsive">
            <table class="ui table striped cell-headers sortable" id="no-fee-reservations" data-type="free">
                <thead>
                    <tr>
                        <th class="no-sort"><!-- √ or X --></th>
                        <th class="no-sort status-col">Status</th>
                        <th><a class="ui popup-text" data-content='We emailed this customer and your facility staff to ask if they moved in. Whoever said “Yes” is listed in this “Move-in Verified By” column.'>Move-In Verified By <i class="fa fa-info-circle"></i></a></th>
                        {% if not view.clientStatement.isFilteredByFacility() %}
                            <th>Facility</th>
                        {% endif %}
                        <th>Customer</th>
                        <th>Move-In Date</th>
                        <th>Reservation Date</th>
                        <th>Bid Amount</th>
                        <th>Move-In Fee</th>
                        <th><a class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                    </tr>
                </thead>
                <tbody>
                    {% for clientItem in freeBookings %}
                        {{ include('statement/view-cpa-item.html.twig', {
                            'clientItem': clientItem,
                            'clientStatement': view.clientStatement
                        }) }}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if constant('\\Genesis_Service_Feature::isActive')(constant('\\Sparefoot\\MyFootService\\Models\\Features::CPA_STATEMENT_CONSUMER_CONTACTS'), {'account_id': view.clientStatement.getAccountId()}) %}
        {% set consumerContacts = view.clientStatement.getConsumerContacts() %}
        {% if consumerContacts and consumerContacts|length > 0 %}
            <h3 class="ui header">
                We told these customers about your facility. Did they move in?
                <div class="sub header">If they did, it's a good idea to let us know. This will improve your move-in rate, so you'll rank higher in SpareFoot search results and get even more new customers. You won't be charged unless you mark a customer as moved in. Then we'll charge your standard unit size move-in fee.</div>
            </h3>
            <div class="table-responsive">
                <table class="ui cell-headers sortable striped table" id="no-fee-reservations">
                    <thead>
                        <tr>
                            <th class="no-sort"><!-- √ or X --></th>
                            {% if not view.clientStatement.isFilteredByFacility() %}
                                <th>Facility</th>
                            {% endif %}
                            <th>Customer</th>
                            <th>Contact Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for consumerContact in consumerContacts %}
                            {% if constant('\\Genesis_Service_Feature::isActive')(constant('\\Sparefoot\\MyFootService\\Models\\Features::CPA_STATEMENT_CONSUMER_CONTACTS'), {'account_id': view.clientStatement.getAccountId(), 'consumer_contact': consumerContact}) %}
                                {{ include('statement/view-cpa-contact.html.twig', {
                                    'clientStatement': view.clientStatement,
                                    'consumerContact': consumerContact
                                }) }}
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% endif %}
    {% endif %}

    {% if not view.clientStatement.isCpaWithLtv() %}
        {{ include('done-form.html.twig', {
            'loggedUser': view.loggedUser,
            'confirmedTime': view.confirmedTime,
            'confirmations': view.confirmations,
            'allowChanges': view.allowChanges
        }) }}
    {% endif %}

    <div id="interstitial-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title">Introducing the SpareFoot Bidding Tool!</h4>
                </div>
                <div class="modal-body">
                    <img src="/images/bidding-interstitial.jpg" width="400" height="370" style="float:right; margin-left:12px;" alt="interstitial" />
                    <h3>Make your AdNetwork listings perform better.</h3>
                    <p>Our bidding tool is the perfect way to generate more bookings for facilities that need a little extra help,
                        while ensuring that you don't pay more than necessary. See how your facilities are currently ranking, and
                        adjust your bid up or down to get the level of exposure you want for a price you are comfortable with.</p>
                    <br />
                    <h3>See where your facilities are ranking now: </h3>
                    <ul>
                        {% for facility in view.interstitialFacilities %}
                            <li>
                                <a href="{{ url('features', {'action': 'bid'}) }}?fid={{ facility.getId() }}">{{ facility.getTitle() }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                    <a href="{{ url('features', {'action': 'list'}) }}" style="margin-left:2.5em;">See all facilities</a>
                    <span class="clear"></span>
                </div>
            </div>
        </div>
    </div>

    <form id="change-move-in-date-modal" class="modal fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">×</button>
                    <h4 class="modal-title">Change move-in date</h4>
                </div>
                <div class="modal-body">
                    <p id="change-move-in-date-customer-info"></p>
                    <label for="into-date">Please enter the customer's (approximate) new move-in date:</label>
                    <input type="text" id="into-date" name="into_date" value="" placeholder="" readonly="readonly" class="form-control datepicker-field" />
                </div>
                <div class="modal-footer">
                    <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                    <input type="hidden" name="confirmation_code" />
                    <input type="submit" class="ui primary button" value="Save" id="change-move-in-date-submit" />
                </div>
            </div>
        </div>
    </form>

    {{ include('statement/dispute-modal.html.twig', { 'clientStatement': view.clientStatement }) }}
    {{ include('statement/auto-dispute-modal.html.twig') }}
</div>
{% endblock %}