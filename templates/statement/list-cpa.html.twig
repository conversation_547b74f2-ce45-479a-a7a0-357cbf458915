{% extends 'layout.html.twig' %}
{% block content %}

{% if app.request.server.get('HTTP_USER_AGENT') and 'MSIE' in app.request.server.get('HTTP_USER_AGENT') %}
    <div class="ui negative message">
        <i class="close icon"></i>
        <div class="header">
            Make sure your popup blocker is turned off.
        </div>
    </div>
{% endif %}

{% for clientStatement in view.openStatements %}

    {% if clientStatement.isCpa() or clientStatement.isCpaWithLtv() %}
        <div class="ui segment">
            <div class="ui top left attached label">Open Statement</div>
            <h2 class="ui header">
                {{ clientStatement.getStatementStartDate()|date('F j') }}-{{ clientStatement.getStatementEndDate()|date('j') }}
                <div class="sub header">Move-Ins</div>
            </h2>

            <div class="table-responsive">
                <table class="ui table very basic">
                    <tr>
                        <td>Reconciliation Deadline</td>
                        <td>{{ clientStatement.getReconciliationEndDate()|date('F j') }}</td>
                    </tr>
                    <tr>
                        <td>Current Move-Ins</td>
                        <td>{{ clientStatement.getNumMovedInCpaItems() }} out of {{ clientStatement.getNumBookingItems() }}</td>
                    </tr>
                    <tr>
                        <td>Current Move-In Fees</td>
                        <td>{{ clientStatement.stringTotalBookingCharge() }}</td>
                    </tr>
                    {% if view.isMIRFElegible %}
                        <tr>
                            <td id="mirf_estimated">
                                {% if view.mirfPercentage == (constant('\\Genesis_Util_NewMirfCalculation::MIR_FLOOR') * 100) %}
                                    <a id="mirf_popup" href="#" data-html="Non-integrated facilities will be charged an additional fee if their move-in rate<br/>is less than 50%. The charge shown here is an estimate for the open<br/>statement period. It will be adjusted based on your reconciliation and move-in<br/>rate when the statement closes.<br/><br/><a href='https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR' target='_blank'>Learn more</a>">
                                        Current Estimated Minimum Move-In Rate Fees for Non-Integrated Facilities
                                        <i class="fa fa-info-circle"></i>
                                    </a>
                                {% else %}
                                    Current Estimated Minimum Move-In Rate Fees
                                {% endif %}
                            </td>
                            <td>{{ view.totalMIRF }}</td>
                        </tr>
                    {% endif %}
                </table>
            </div>
            <div>
                {% if not call_static('\\Genesis_Service_Feature', 'isActive', ['myfoot.disable_reconcile_statement_button']) %}
                    <a class="ui button blue" id="open_statement" href="{{ url('statement_' ~ view.reconcileStatementAction) }}?id={{ clientStatement.getStatementId() }}">Reconcile Statement</a>
                {% endif %}
                <div class="ui dropdown button right">
                    <div class="text">Download</div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        <a class="item" href="{{ url('statement_viewpdf') }}?id={{ clientStatement.getStatementId() }}&user_id={{ view.userId }}">
                            <i class="file pdf outline icon"></i> PDF</a>
                        <a class="item" href="{{ url('statement_viewcsv') }}?id={{ clientStatement.getStatementId() }}&user_id={{ view.userId }}">
                            <i class="file excel outline icon"></i> Excel</a>
                    </div>
                </div>
            </div>

            <div class="ui divided list">
                {% if 'SLA' in view.loggedUser.getAccount().getInfoString() %}
                    <a class="item" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">
                        <div class="ui blue horizontal label">How To</div>
                        Use SiteLink to reconcile my statement
                    </a>
                {% endif %}
            </div>
        </div>
    {% endif %}
{% endfor %}
{% if view.isMIRFElegible and view.mirfPercentage == (constant('\\Genesis_Util_NewMirfCalculation::MIR_FLOOR') * 100) %}
    <div class="ui segment">
        <div id="mirf_grid" class="ui grid">
            <div class="sixteen wide column">
                <h2 class="ui header">Minimum Move-In Rate Fees</h2>
            </div>
            <div class="twelve wide computer eight wide tablet column ">
                <p><b>Non-Integrated Facilities will be charged an additional fee if their move-in rate is less than 50%.</b> If you are using a Facility Management Software that we integrate with, you can avoid this fee by integrating your facilities. </p>
            </div>
            <div class="four wide computer eight wide tablet column">
                <a class="ui button fluid blue" target="_blank" href="https://info.storable.com/sparefoot-integration">
                    Integrate Facilities
                </a>
            </div>
            <div class="sixteen wide column">
                <a class="learn_about_integrations" href="https://support.sparefoot.com/hc/en-us/articles/115015444207#MinimumMIR" target="_blank">
                    Learn how the Minimum Move-In Rate Fee Is Calculated →
                </a>
            </div>
        </div>
    </div>
{% endif %}

{{ include('statement/billing-history.html.twig') }}

<table id="statements" data-type="cpa" class="ui table striped cell-headers sortable">
    <thead>
        <tr>
            <th></th>
            {% if view.isMIRFElegible %}
                <th id="total_fees" data-html="This column may display a combination of<br/>Move-In Fee Totals and Minimum Move-In<br/> Rate Fees{% if view.mirfPercentage == (constant('\\Genesis_Util_NewMirfCalculation::MIR_FLOOR') * 100) %} if you have facilities on your<br/>account that are not integrated. {% endif %}">
                    Total Fees
                    <i class="fa fa-info-circle"></i>
                </th>
            {% else %}
                <th>Move-In Fee Total</th>
            {% endif %}
            <th>Moved In</th>
            <th>Did Not Move In</th>
            <th>Move In Rate</th>
            <th class="no-sort"></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td colspan="4">&nbsp;</td>
        </tr>
    </tbody>
</table>

{% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD')) %}
    <h2 id="billing-history">Billing History</h2>
    <div class="ui segment basic">
        <a class="ui button primary" href="{{ url('statement_receipts') }}">View Billing History</a>
    </div>
{% endif %}
{% endblock %}