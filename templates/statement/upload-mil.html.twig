{% extends 'layout.html.twig' %}
{% block content %}

<div class="page-header">
    <h1>Reconcile with WebSelfStorage</h1>
    <p>
        Simply export and upload the following reports out of WebSelfStorage. We’ll match them against your SpareFoot reservations. Then you can review and finalize your statement.
    </p>
</div>

{% if view.error %}
    <div class="alert alert-danger">{{ view.error }}</div>
{% endif %}

{% if view.matchRun %}

    <div class="alert alert-success">

    {% if view.matchRun.getBookingsAutoConfirmed() > 0 %}
        <h4>Upload successful!</h4>
        <p>We found {{ view.matchRun.getBookingsAutoConfirmed() }} customer matches between your WebSelfStorage lists and your SpareFoot statement. Please review these matches and finish reconciling your statement as you normally would. </p>
    {% else %}
        <h4>Upload complete.</h4>
        <p>We found 0 customer matches between your WebSelfStorage lists and your SpareFoot statement. Please finish reconciling your statement as you normally would.</p>
    {% endif %}

    </div>
    {% if view.cdpErrors %}
        <div class="alert alert-warning">
            <h4>Some errors occurred:</h4>
            <ul>
            {% for error in view.cdpErrors %}
                <li>{{ error }}</li>
            {% endfor %}
            </ul>
        </div>
    {% endif %}
    <br />
    <p>
        <a href="{{ url('statement_view') }}?id={{ view.statementId }}" class="ui button">Go back to your statement</a>
    </p>

{% else %}
    {% if view.facilities %}
    <style>
        #wss-file1-info, #wss-file2-info {
            background: white;
            border:1px solid #000000;
            border-radius: .2em;
            color:black;
            font-size: 1em;
        }
    </style>
    <form id="webselfstorage-movein-upload-form" method="post" enctype="multipart/form-data" >
    <fieldset id="webselfstorage-movein-upload-fieldset">
        {% set step = 1 %}
        {% if view.facilities.toArray()|length > 1 %}
            <div id="facility-choose" class="form-group">
                <div>
                    <label for="facility_id">
                        <strong>Step {{ step }}:</strong><br/>
                        Select facility:
                    </label>
                </div>
                <div style="position: relative">
                    <select name="facility_id" class="form-control">
                        {% for facility in view.facilities %}
                            <option value="{{ facility.getId() }}">{{ facility.getTitle() }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% set step = step + 1 %}

        {% elseif view.facilities.current() %}
            <input type="hidden" name="facility_id" value="{{ view.facilities.current().getId() }}" />
        {% endif %}

        <div id="move-in-list" class="form-group">

            <div>
                <label for="wss_file1">
                    <strong>Step {{ step }}:</strong><br/>
                    In WebSelfStorage, select Reports, <strong>"New Move In/Move Out,"</strong> set date range to cover the entire prior month, and hit Continue. Then Download To: Text File (.TXT), save and select that file here:
                </label>
            </div>
            <div style="position: relative">
                <a class="button primary" href="javascript:;">
                    Select Move In/Move Out List <input type="file" id="wss_file1" name="wss_file1" style='position:absolute;z-index:2;top:0;left:0;height:100%;filter: alpha(opacity=0);-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";opacity:0;background-color:transparent;color:transparent;' size="40"  onchange='$("#wss-file1-info").html($(this).val());'/>
                </a>
                &nbsp;
                <span class='label label-info' id="wss-file1-info"></span>
            </div>
        </div>
        {% set step = step + 1 %}

        <div id="email-list" class="form-group">
            <div>
                <label for="wss_file2">
                    <strong>Step {{ step }}:</strong><br/>
                    In WebSelfStorage, select Reports, <strong>"E-Mail Customer List,"</strong> and hit Continue. Then Download To: Text File (.TXT), save and select that file here:
                </label>
            </div>
            <div style="position: relative">
                <a class="button primary" href="javascript:;">
                    Select E-Mail Customer List <input type="file" id="wss_file2" name="wss_file2"style='position:absolute;z-index:2;top:0;left:0;height:100%;filter: alpha(opacity=0);-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";opacity:0;background-color:transparent;color:transparent;' size="40"  onchange='$("#wss-file2-info").html($(this).val());'/>
                </a>
                &nbsp;
                <span class='label label-info' id="wss-file2-info"></span>
            </div>
        </div>

        <input type="hidden" name="statement_id" value="{{ view.statementId }}" />
        <input type="hidden" name="source_id" value="{{ constant('\\Genesis_Entity_Source::ID_WEB_SELF_STORAGE') }}" />

        <div class="form-actions">
            <input type="submit" id="webselfstorage-movein-upload-submit" value="Upload" data-loading-text="Uploading..." class="button primary" />
            &nbsp;<img src="/images/loaders/large.gif" class="is-hidden" />
            <div class="is-hidden">
                <br />
                <h4>We're processing your reports right now!</h4>
                <p>In just a moment, you'll see our matches. Please review the results, then finish reconciling your statement as you normally would.</p>
            </div>
        </div>
        </fieldset>
    </form>
    {% endif %}
{% endif %}
{% endblock %}
