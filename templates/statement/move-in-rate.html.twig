{% if view.clientStatement %}

{# 
 * the dates look complex, but we are passing them to the JS date constructor
 * as an int, which Date accepts directly as one param.
 * These also work for statements batch windows that are not exactly a month. Which is every test case...
 #}
<script type="text/javascript">
    var statementId = {{ view.clientStatement.getStatementId() }};
    {% set startDate = view.clientStatement.getStatementStartDate()|date('U') %}
    var startDate = new Date({{ 1000 * startDate }});               /*{{ startDate|date('r') }}*/
    {% set endDate = view.clientStatement.getStatementEndDate()|date('U') %}
    var endDate = new Date({{ 1000 * endDate }});                 /*{{ endDate|date('r') }}*/
    {% set nextStatementStartDate = (view.clientStatement.getStatementEndDate() ~ ' +1 days')|date('U') %}
    var nextStatementStartDate = new Date({{ 1000 * nextStatementStartDate }});  /*{{ nextStatementStartDate|date('r') }}*/
    {% set nextStatementEndDate = (view.clientStatement.getStatementEndDate() ~ ' +61 days')|date('U') %}
    var nextStatementEndDate = new Date({{ 1000 * nextStatementEndDate }});    /*{{ nextStatementEndDate|date('r') }}*/
    var defaultDate = '{{ view.clientStatement.getStatementStartDate()|date('m-d-Y') }}';
    var showInterstitial = {{ view.showInterstitial ? 'true' : 'false' }};
    var statementType = '{{ view.statementType }}';
</script>
{% endif %}