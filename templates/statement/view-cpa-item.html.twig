<tr class="cpa-item" data-id="{{ view.clientItem.getConfirmationCode() }}" data-facility-id="{{ view.clientItem.getFacility().getId() }}">
    {% if not view.clientItem.isFreeBooking() %}
    <td id="action-{{ view.clientItem.getConfirmationCode() }}" class="statement-actions">
        <div class="ui basic icon buttons" data-toggle="buttons">
            {% set yesClasses = '' %}
            {% set noClasses = '' %}
            {% if view.clientItem.isAutoConfirmed() %}
                {% set yesClasses = 'confirm-autoconfirmed-button ' %}
                {% set noClasses = 'dispute-autoconfirmed-button ' %}
                {% if not view.clientItem.getReviewStatus() %}
                    {% set yesClasses = yesClasses ~ (view.clientItem.isDisputed() ? '' : 'active') %}
                    {% set noClasses = noClasses ~ (view.clientItem.isDisputed() ? 'active' : '') %}
                {% elseif view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                    {% set yesClasses = yesClasses ~ (view.clientItem.isDisputed() ? '' : 'active') %}
                    {% set noClasses = noClasses ~ (view.clientItem.isDisputed() ? 'active' : '') %}
                {% elseif view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %}
                    {% if view.clientItem.isDisputed() %}
                        {% set noClasses = noClasses ~ ' active' %}
                    {% else %}
                        {% set yesClasses = yesClasses ~ ' active' %}
                        {% set noClasses = noClasses ~ ' hidden' %}
                    {% endif %}
                {% endif %}
            {% elseif view.clientItem.isEarly() %}
                {% set yesClasses = 'change-move-in-date-button' %}
                {% set noClasses = 'disabled' %}
            {% elseif view.clientItem.isLate() %}
                {% set yesClasses = 'change-move-in-date-button' %}
                {% set noClasses = 'dispute-button active' %}
            {% else %}
                {% set yesClasses = 'confirm-button ' ~ (view.clientItem.isDisputed() ? '' : 'active') %}
                {% set noClasses = 'dispute-button ' ~ (view.clientItem.isDisputed() ? 'active' : '') %}
            {% endif %}
            <button class="ui basic green compact button {{ yesClasses }}">
                <i class="checkmark icon"></i>
            </button>
            <button class="ui basic red compact button {{ noClasses }}">
                <i class="remove icon"></i>
            </button>
        </div>
        <input type="hidden" name="options_{{ view.clientItem.getConfirmationCode() }}"/>
    </td>
    <td id="status-{{ view.clientItem.getConfirmationCode() }}" class="status-cell">
        {% if view.clientItem.isAutoConfirmed() %}
            {% if view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                <span class="under-review">Under Review by SpareFoot</span>
                <span class="error is-hidden">Did Not Move In</span>
                <span class="success is-hidden">Moved In</span>
            {% else %}
                <span class="under-review is-hidden">Under Review by SpareFoot</span>
                <span class="error{{ view.clientItem.isDisputed() ? '' : ' is-hidden' }}">Did Not Move In {{ view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
                <span class="success{{ view.clientItem.isDisputed() ? ' is-hidden' : '' }}">Moved In {{ view.clientItem.getReviewStatus() == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
            {% endif %}
        {% elseif view.clientItem.isEarly() %}
            <span>Move In Next Month</span>
            <span class="success is-hidden">Moved In</span>
        {% elseif view.clientItem.isLate() %}
            <span class="error">Did Not Move In</span><img id="activity-indicator-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden" src="/images/loading.gif" alt="loading"/>
            <span class="success is-hidden">Moved In</span>
        {% else %}
            <span class="error {{ view.clientItem.isDisputed() ? '' : 'is-hidden' }}">Did Not Move In</span>
            <span class="success {{ view.clientItem.isDisputed() ? 'is-hidden' : '' }}">Moved In</span>
        {% endif %}

        {% if view.clientItem.getSupportNotes() %}
            <br /><a class="ui popup-text" data-title="Support Notes" data-content="{{ view.clientItem.getSupportNotes() }}"><strong>NOTES</strong></a>
        {% endif %}
    </td>
    <td class="statement-verification">
        {% if 'consumer' in view.clientItem.getMoveInVerifiedBy()|keys %}
            <img src="/images/customer-verified.gif" width="19" height="20" alt="" />  Customer
        {% endif %}
        {% if view.clientItem.getMoveInVerifiedBy()|length > 1 %}
            <br /><br />
        {% endif %}
        {% if 'facility' in view.clientItem.getMoveInVerifiedBy()|keys %}
            <img src="/images/facility-verified.gif" width="19" height="20" alt="" />  Facility
        {% endif %}
    </td>
    {% endif %}

    {% if not view.clientStatement.isFilteredByFacility() %}
        <td id="facility-name-{{ view.clientItem.getConfirmationCode() }}"><a href="{{ url('statement_view') }}?id={{ view.clientStatement.getStatementId() }}&facility={{ view.clientItem.getFacility().getId() }}">{{ view.clientItem.stringFacilityName()|e }}</a></td>
    {% endif %}

    <td id="customer-{{ view.clientItem.getConfirmationCode() }}">
        <span id="customer-info-{{ view.clientItem.getConfirmationCode() }}">
            {{ view.clientItem.stringCustomerInfo()|e|nl2br }}
        </span>
        {{ view.clientItem.getBookingUnitNumber() ? '<br/>Unit ' ~ view.clientItem.getBookingUnitNumber() : '' }}
        {% if view.clientItem.isOffline() %}
            <a class="ui popup-text" data-content="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
        {% endif %}

        {% if view.clientItem.hasDuplicate() %}
            <a class="ui popup-text" data-content="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant.">Multiple Reservations</a>
        {% endif %}
        {% if view.clientItem.isAutoConfirmed() %}
            <div id="tenant-{{ view.clientItem.getConfirmationCode() }}" class="is-hidden">
                {{ view.clientItem.stringTenantInfo()|nl2br }}
                {{ view.clientItem.getUnitNumber() ? '<br/>Unit ' ~ view.clientItem.getUnitNumber() : '' }}
            </div>
        {% endif %}
    </td>
    <td id="date-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringMoveInDate() }}</td>
    <td id="reservation-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringReservationDate() }}</td>
    <td id="baseBid-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringBaseBid() }}</td>
    <td id="amount-{{ view.clientItem.getConfirmationCode() }}">{{ view.clientItem.stringSparefootFee() }}</td>
    <td id="lifetime-{{ view.clientItem.getConfirmationCode() }}"><a href="#" class="ui popup-text" data-content="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of {{ view.clientItem.stringUnitPrice() }}.">{{ view.clientItem.stringLifetimeValue() }}</a><br />
        <a href="#" class="ui popup-text" data-content="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant.">{{ view.clientItem.stringLifetimeValueROI() }} <abbr data-content="Return on Investment">ROI</abbr></a></td>
</tr>
