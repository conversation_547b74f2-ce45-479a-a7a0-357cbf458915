{{ view.data|raw }}

<h2>{{ view.facilityTitle }}</h2>
<p>{{ view.facilityAddress }}</p>
<p>{{ view.reportMonth }} {{ view.reportYear }} &#183; Trade Area Radius: {{ view.searchRadius }} miles &#183; Facilities Sampled: {{ view.numSampledFacilities }}</p>

<div class="insight_reportdata">
    <table class="table1 insights_data">
        <tr>
            <th class="borderright_ccc">Unit Size</th>
            <th class="borderright_ccc">Average Climate Price</th>
            <th class="borderright_ccc">Average Non-Climate Price</th>
            <th class="borderright_ccc">Your Average Climate Price</th>
            <th>Your Average Non-Climate Price</th>
        </tr>
        
        {% set unitTypes = {
            "5' x 5'": 'cc25',
            "5' x 10'": 'cc50',
            "5' x 15'": 'cc75',
            "10' x 10'": 'cc100',
            "10' x 15'": 'cc150',
            "10' x 20'": 'cc200'
        } %}
        
        {% for size, type in unitTypes %}
            {% set nontype = 'non' ~ type %}
            {% set your_type = 'your_' ~ type %}
            {% set your_nontype = 'your_' ~ nontype %}
            {% set typeValue = attribute(view, type) %}
            {% set nontypeValue = attribute(view, nontype) %}
            {% set yourTypeValue = attribute(view, your_type) %}
            {% set yourNontypeValue = attribute(view, your_nontype) %}
            
            <tr>
                <th class="borderright_ccc">{{ size }}</th>
                <td class="borderright_ccc bg_white">
                    {{ typeValue is numeric ? '$' ~ typeValue : typeValue }}
                </td>
                <td class="borderright_ccc bg_white">
                    {{ nontypeValue is numeric ? '$' ~ nontypeValue : nontypeValue }}
                </td>
                <td class="borderright_ccc bg_white {% if typeValue != "N/A" and yourTypeValue != "N/A" %}{% if typeValue < yourTypeValue %}price_bad{% elseif typeValue > yourTypeValue %}price_good{% endif %}{% endif %}">
                    {{ yourTypeValue is numeric ? '$' ~ yourTypeValue : yourTypeValue }} 
                    <a href="{{ view.your_edit_link }}" class="small_link"> <i class="fa fa-pencil"></i></a>
                </td>
                <td class="bg_white {% if nontypeValue != "N/A" and yourNontypeValue != "N/A" %}{% if nontypeValue < yourNontypeValue %}price_bad{% elseif nontypeValue > yourNontypeValue %}price_good{% endif %}{% endif %}">
                    {{ yourNontypeValue is numeric ? '$' ~ yourNontypeValue : yourNontypeValue }} 
                    <a href="{{ view.your_edit_link }}"> <i class="fa fa-pencil"></i></a>
                </td>
            </tr>
        {% endfor %}
    </table>
</div>

<div class="clear">&nbsp;</div>
<span class="hidden" id="insightsLandingPageTest"></span>