{% extends 'layout.html.twig' %}
{% block content %}

{{ include('insights/header.html.twig', {'active': 'other', 'reportCount': view.allReports|length}) }}

{% set categories = view.allReports|keys %}
{% for category in categories|sort %}
    <h4>{{ category }}</h4>
    <ul>
    {% for reportClassName, reportName in view.allReports[category] %}
        <li><a href="/insights/custom/report/{{ reportClassName }}">{{ reportName }}</a></li>
    {% endfor %}
    </ul>
{% endfor %}
    
<span class="hidden" id="insightsOtherReportsPageTest"></span>

{% endblock %}