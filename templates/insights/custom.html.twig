{% extends 'layout.html.twig' %}
{% block content %}

<div class="page-header">
    <h6>Report</h6>
    <h2>{{ view.reportName }}</h2>
</div>

<form id="insights_form" method="get" action="/insights/custom/report">
    <div id="inputs">
    {% for input in view.inputs %}
        {{ input|raw }}
    {% endfor %}
    </div>
    <div class="form-actions">
        <input type="button" id="viewInsightsReportBtn" class="btn btn-primary" onclick="return viewAjaxReport();" value="View Report" />
        <input type="submit" name="csv" class="ui button" value="Download" />
        <input type="hidden" name="report" value="{{ view.reportClassName }}"/>
    </div>
</form>

<div id="data-table-report" class="clearfix">
</div>

{% endblock %}