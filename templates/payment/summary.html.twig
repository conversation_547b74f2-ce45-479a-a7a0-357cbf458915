{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}

{{ include('settings/header.html.twig', {'loggedUser': view.loggedUser, 'active': 'payment'}) }}

<script type="text/javascript">
//write billing data to json
var pms = {{ view.payment_methods|json_encode|raw }};
</script>

<h3>Your Payment Methods</h3>
{% if view.payment_methods|length > 0 %}
<p>These are the payment methods set up for your account</p>
{% else %}
<a class="ui primary button" href="/payment/add"><i class="plus icon"></i>Add Payment Method</a>
{% endif %}

<form action="/payment/edit" method="post">
    <table class="table table-striped">
		<tr>
			<th>Actions</th>
			<th>Nickname</th>
			<th>Invoice / Receipt Contacts</th>
		</tr>
        {% for be in view.payment_methods %}
            <tr>
                <td>
                    <a href="#" class="ui secondary button" onclick="editFacPayment({{ be.id }});">
                        <i class="pencil icon"></i> Edit / Replace
                    </a>
                </td>
                <td>
                    {{ be.displayStr }}
                </td>
				<td>
					{{ be.emails }}
				</td>
            </tr>
        {% endfor %}
    </table>
</form>

<div class="spacer"></div>

<h3>Payment Method by Facility</h3>
<p>Use the drop-down menus to assign a payment method to each facility or to create a more advanced account setup, contact <EMAIL>.</p>
<form action="/payment/edit" method="post" id="edit_pymt_form"></form>

<table class="ui sortable striped cell-headers table">
    <thead>
        <tr>
            <th>Facility</th>
            <th>Payment Method</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
{% for fac in view.facilities %}
    <tr id="{{ fac.getId() }}" {% if not fac.getBillableEntityId() %}class="red"{% endif %}>
        <td>{{ fac.getTitleWithCompanyCode() }}</td>
        <td>
                <select id="fac_{{ fac.getId() }}" name="beId_{{ fac.getId() }}" class="form-control fac_beId" onChange="changeFacBe({{ fac.getId() }}, this.value);">
                {% if not fac.getBillableEntityId() %}<option value="choose" selected="selected">Select a payment method</option>{% endif %}
                {% for be in view.payment_methods %}
                    <option value="{{ be.id }}" {% if fac.getBillableEntityId() == be.id %}selected="selected"{% endif %}>{{ be.displayStr }}</option>
                {% endfor %}
                </select>
        </td>
        <td id="{{ fac.getId() }}_notlive">
            {% if not fac.getBillableEntity() %}NOT LIVE{% endif %}
        </td>
    </tr>
{% endfor %}
    </tbody>

</table>

<span class="hidden" id="paymentOverviewPageTest"></span>

{% endblock %}
