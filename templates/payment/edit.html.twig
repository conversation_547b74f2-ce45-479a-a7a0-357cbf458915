{% extends 'layout.html.twig' %}

{% block content %}
<script type="text/javascript">
var accountId = {{ view.accountId }};
{% if view.billableEntityId %}
var billableEntityId = {{ view.billableEntityId }};
{% else %}
var billableEntityId = 0;
{% endif %}
{% if view.paymentType %}
var paymentType = '{{ view.paymentType }}';
var ccType = 'existing';
{% else %}
var paymentType = '';
var ccType = '';
{% endif %}
var returnScreen = '{{ view.returnscreen }}';
</script>

<form id="edit-payment-form" class="form-horizontal setup-content-container">
    <input type="hidden" id="csrf_token" name="csrf_token" value="{{ view.csrf_token }}">

    <div class="setup-content">

        <div class="content-row">
            <h2>Advanced Billing Setup <i class="icon lock"></i></h2>
            <p>Need to setup different payment methods for different facilities? Add additional payment methods below and connect them to your facilities on the main Payment Setup screen when you're done.</p>

            <div id="type_buttons" class="ui secondary menu">
                <div class="ui buttons">
                    <a href="#" id="btn_cc" class="ui button secondary active">
                        <i class="visa icon"></i>
                        Credit Card
                    </a>
                    <a href="#" id="btn_ach" class="ui button secondary">
                        <i class="dollar icon"></i>
                        ACH
                    </a>
                    {% if view.account.getAllowInvoiceBilling() %}
                    <a href="#" id="btn_invoice" class="ui button secondary">
                        <i class="file icon"></i>
                        Invoice
                    </a>
                    {% endif %}
                </div>
            </div>

        <div class="input-row" id="payment-type-nickname-div">
            <div class="form-group">
                <label for="payment-type-nickname" class="col-md-2 control-label">Payment Type Nickname</label>
                <div class="col-md-10">
                    <input id="payment-type-nickname" name="payment_type_nickname" type="text" class="form-control" value="{{ view.paymentTypeNickname }}"/>
                    <p class="help-block">This nickname will appear as your company name on all invoices and receipts. Our clients typically use the name of their facility/facilities and the payment method.<br>
                    Examples: "ABC Storage - Business Amex" or "AA Storage Properties - Company Visa"</p>
                </div>
            </div>
        </div>
    <div id="cc_section">
        <div class="content-row">
            <h2>Credit Card Information</h2>
        </div>
        <div class="input-row string">

            <div>

                <div class="form-group">
                    <label for="credit-card-number" class="col-md-2 control-label">Credit Card Number</label>
                    <div class="col-md-10">
                        <input type="hidden" id="cc-change-listener" name="cc_change_listener" value="0" />
                        <input id="credit-card-number" name="credit_card_number" type="text" class="form-control" value="{{ view.creditCardNumber }}" onChange="getCCType(this.value)"/>
                        <p class="help-block" id="credit-card-image">
                        {% if view.ccType == 'VISA' %}
                            <img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/visa.png" style=""/>
                        {% elseif view.ccType == 'Master Card' %}
                            <img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/mastercard.png" style=""/>
                        {% elseif view.ccType == 'American Express' %}
                            <img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/amex.png" style=""/>
                        {% elseif view.ccType == 'Discover' %}
                            <img src="/images/check.png" style="position:relative;left:40px;"/><img src="/images/discover.png" style=""/>
                        {% endif %}
                        </p>
                        <p class="help-block">We accept Visa, Mastercard, Discover, and American Express and automatically detect your credit card type.</p>
                    </div>
                </div>


                <div class="form-group">
                    <label for="credit-card-name" class="col-md-2 control-label">Name on Card</label>
                    <div class="col-md-10">
                        <input id="credit-card-name" name="credit_card_name" type="text" class="form-control" value="{{ view.creditCardName }}"/>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Expiration Date</label>
                    <p class="col-md-5">

                        {% set month = view.creditCardExpirationMonth ? view.creditCardExpirationMonth : "now"|date("m") %}

                        <select id="credit-card-expiration-month" name="credit_card_expiration_month" class="form-control">
                            {% set months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'] %}
                            {% for monthValue in months %}
                                <option value="{{ monthValue }}"{{ month == monthValue ? ' selected="selected"' : '' }}>{{ monthValue }}</option>
                            {% endfor %}
                        </select>
                    </p>
                    <p class="col-md-5">
                        <select id="credit-card-expiration-year" name="credit_card_expiration_year" class="form-control">

                            {% for i in "now"|date("Y").."now"|date("Y") + 10 %}
                                <option value="{{ i }}"{{ view.creditCardExpirationYear == i ? ' selected="selected"' : '' }}>{{ i }}</option>
                            {% endfor %}

                        </select>
                    </p>
                </div>
            </div>
        </div>
    </div>
	<input type="hidden" name="is_new" value="{{ view.isNew }}" />
    <div id="ach_section" style="display:none">
        {% if view.isNew %}
        <div class="content-row">
            <p>Download the <a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH form</a> and email it back to us.  Once we have verified your billing information, we will add it to your account.<p/>
            <p style="text-align: center;"><img src="/images/small_pdf_icon.gif"/><a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH Bank Draft Form</a></p>
            <p>Tel: (*************<br/>Email: <EMAIL></p>
        </div>
        {% else %}
        <div class="content-row">
            <p>To change your ACH information, download the <a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH form</a> and email it back to us.  Once we have verified your billing information, we will update your account.<p/>
            <p style="text-align: center;"><img src="/images/small_pdf_icon.gif"/><a href="/pdf/SpareFoot_ACH_Auth_no_fax.pdf" target="_blank">ACH Bank Draft Form</a></p>
            <p>Tel: (*************<br/>Email: <EMAIL></p>
        </div>
        <!--Cannot collect ACH online yet-->
        <!--<div class="content-row">
                <h2>ACH Information</h2>
        </div>
        <div class="input-row string">
            <label for="bank_name">Bank Name</label>
            <input id="bank_name" name="bank_name" type="text" class="form-control" value="{{ view.achName }}"/>
            <div style="clear:both"></div>
        </div>
        <div class="input-row string">
            <label for="bank_account_number">Bank Account Number</label>
            <input id="bank_account_number" name="bank_account_number" type="text" class="form-control" value="{{ view.achAccount }}"/>
            <div style="clear:both"></div>
        </div>
        <div class="input-row string">
            <label for="bank_routing_number">Bank Routing Number</label>
            <input id="bank_routing_number" name="bank_routing_number" type="text" class="form-control" value="{{ view.achRouting }}"/>
            <div style="clear:both"></div>
        </div>-->
        {% endif %}
    </div>

    <div id="address_section">
        <div class="content-row">
            <h2>Billing Address</h2>
        </div>

        <div class="input-row">

            <div>

                <div class="form-group">
                    <div class="controls">
                        <div class="ui checkbox bootstrap-aligned">
                            <input type="checkbox" name="billing_address_same" id="billing-address-same" />
                            <label>My billing address is the same as my company address.</label>

                        </div>
                        {% if not view.return %}
                        <p class="help-block"><a href="/settings/myaccount">Edit my company address</a></p>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group">
                    <label for="address" class="col-md-2 control-label">Address</label>
                    <div class="col-md-10">
                        <input id="address" name="address" type="text" class="form-control" value="{{ view.address }}"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="city" class="col-md-2 control-label">City</label>
                    <div class="col-md-10">
                        <input id="city" name="city" type="text" class="form-control" value="{{ view.city }}"/>
                    </div>
                </div>

                <div class="form-group">
                    <label for="state" class="col-md-2 control-label">State</label>
                    <div class="col-md-10">
                        <select id="state" name="state" class="form-control">
                            <option value=""></option>
                            {% set states = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'] %}
                            {% for state in states %}
                                <option value="{{ state }}"{{ view.state == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="zip" class="col-md-2 control-label">Zip Code</label>
                    <div class="col-md-10">
                        <input id="zip" name="zip" type="text" class="form-control" value="{{ view.zip }}"/>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <div id="invoice_section" style="display:none">
        <div class="content-row">
            <h2>Email Invoice Delivery</h2>
            <p>Our invoices are sent by email only.  Please include the email address of whomever should receive a copy of this invoice.</p>
        </div>
    </div>
        <div class="input-row string" id="emails-div">
            <div class="form-horizontal">
                <div class="form-group">
                    <label for="emails" class="col-md-2 control-label">Email Address(es)</label>
                    <div class="col-md-10">
                        <input id="emails" name="emails" type="text" class="form-control" value="{{ view.emails }}"/>
                        <p class="help-block" id="cc_section">(comma separate for multiple)<br />The email address/addresses entered here will receive an email copy of your receipt each time your payment method is charged</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-footer">
            <div class="pull-right">
                <img src="/images/loaders/large.gif" class="loading hide" />&nbsp;&nbsp;
                <input id="submit" class="ui primary large button" name="commit" type="submit" data-loading-text="Saving" value="{% if view.isNew %}Add{% else %}Update{% endif %}" />
            </div>
            <a class="ui basic large button" href="{{ view.returnscreen }}">Back</a>
        </div>

    </div>

</form>

{% endblock %}
