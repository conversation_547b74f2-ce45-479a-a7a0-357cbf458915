<style>
.modal-footer {	
	padding: 8px 20px 8px;
}
</style>
<form id="settings-integration-softwares" action="/settings/insert-account-software" method="post">
    <input type="hidden" name="software_account_id" value="{{ view.accountId }}">
    <input type="hidden" name="software_user_id" value="{{ view.loggedUser.getId() }}">
    
    <div class="modal" id="modal-integration-softwares">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Select the storage management software you use</h3>
                    <p>We'll use this to tailor your MySpareFoot experience. Select all that apply.</p>
                </div>
                <div class="modal-body">
            
            	   <div class="alert alert-danger hide"></div>
            	   <div class="span2">
            
                        <div class="control-group">
                            <div class="checkbox">
                                <label for="sl">
                                    <input type="checkbox" id="sl" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_SITELINK') }}" /><img src="/images/software-sitelink-web-edition-sm.jpg" alt="SiteLink Web Edition" />
                                </label>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <div class="checkbox">
                                <label for="cs4">
                                    <input type="checkbox" id="cs4" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_CENTERSHIFT4') }}" /><img src="/images/software-centershift-4-sm.jpg" alt="Centershift 4.0" />
                                </label>
                            </div>
                        </div>
                		  
                    </div>
                	<div class="span2">
                        
                        <div class="control-group">
                            <div class="checkbox">
                                <label for="qs">
                                    <input type="checkbox" id="qs" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_QUIKSTOR') }}" /><img src="/images/quikstor.png" alt="QuikStor" />
                                </label>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <div class="checkbox">
                                <label for="ssm">
                                    <input type="checkbox" id="ssm" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_SELFSTORAGEMANAGER') }}" /><img src="/images/selfstoragemanager.png" alt="Self Storage Manager" />
                                </label>
                            </div>
                        </div>
                	  
                    </div>
                    <div class="span2">
                
                	   <div class="control-group">
                            <div class="checkbox">
                                <label for="othersoft">
                                    <input type="checkbox" id="othersoft" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_OTHER') }}" />Other software
                                </label>
                            </div>
                        </div>
                
                        <div class="control-group">
                            <div class="checkbox">
                                <label for="nosoftware">
                                    <input type="checkbox" id="nosoftware" name="integration_type[]" value="{{ constant('\\Genesis_Entity_Source::ID_MANUAL') }}" />I don't use software
                                </label>
                            </div>
                        </div>
                	</div>			
                	    
                  </div>
                  <div class="modal-footer">
                    <input class="btn btn-primary pull-right" id="btn-integration-software-submit" type="submit" value="Save" />    
                  </div>
            </div>
        </div>
    </div>	
</form>
