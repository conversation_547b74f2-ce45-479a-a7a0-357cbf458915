{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
{{ include('settings/header.html.twig', {
    'loggedUser': view.loggedUser,
    'active': 'corporate'
}) }}

{% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) %}

<h3>Change Your Corporate Information</h3>

    <form method="post" action="/settings/corporate" class="form-horizontal">
        <br />

        {% if view.alert %}
            <p class="alert{{ view.alertClass ? ' ' ~ view.alertClass : '' }}">
                {{ view.alert }}
            </p>
        {% endif %}

        <div class="form-group{{ 'company_name' in view.erroredFields ? ' error' : '' }}">
            <label class="col-lg-2 control-label" for="company_name">Company Name</label>
            <div class="col-lg-10">
                <input id="company_name" name="company_name" type="text" class="form-control" readonly="readonly" value="{{ view.account.getName() }}" />
            </div>
        </div>

        <div class="form-group{{ 'address' in view.erroredFields ? ' error' : '' }}">
            <label class="col-lg-2 control-label" for="address">Address</label>
            <div class="col-lg-10">
                <input id="address" name="address" type="text" class="form-control" value="{{ view.account.getLocation() ? view.account.getLocation().getAddress1() : '' }}" />
            </div>
        </div>

        <div class="form-group{{ 'city' in view.erroredFields ? ' error' : '' }}">
            <label class="col-lg-2 control-label" for="city">City</label>
            <div class="col-lg-10">
                <input id="city" name="city" type="text" class="form-control" value="{{ view.account.getLocation() ? view.account.getLocation().getCity() : '' }}" />
            </div>
        </div>

        <div class="form-group{{ 'state' in view.erroredFields ? ' error' : '' }}">
            <label class="col-lg-2 control-label" for="state">State</label>
            <div class="col-lg-10">
                {% set states = ['AK', 'AL', 'AR', 'AZ', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL', 'GA', 'HI', 'IA', 'ID', 'IL', 'IN', 'KS', 'KY', 'LA', 'MA', 'MD', 'ME', 'MI', 'MN', 'MO', 'MS', 'MT', 'NC', 'ND', 'NE', 'NH', 'NJ', 'NM', 'NV', 'NY', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VA', 'VT', 'WA', 'WI', 'WV', 'WY'] %}
                {% set currentState = view.account.getLocation() ? view.account.getLocation().getState() : '' %}
                <select id="state" name="state" class="form-control">
                    <option value=""></option>
                    {% for state in states %}
                    <option value="{{ state }}"{{ currentState == state ? ' selected="selected"' : '' }}>{{ state }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group{{ 'zip' in view.erroredFields ? ' error' : '' }}">
            <label class="col-lg-2 control-label" for="zip">Zip Code</label>
            <div class="col-lg-10">
                <input id="zip" name="zip" type="text" class="form-control" value="{{ view.account.getLocation() ? view.account.getLocation().getZip() : '' }}" />
            </div>
        </div>
    <div class="form-group">
        <label class="col-lg-2 control-label"></label>
        <div class="col-lg-10">
            Account Payment Terms:
            {% if view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                {{ view.account.getResidualPercent() * 100 }}% of rent for a move-in.
            {% elseif view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_FLAT') %}
                One-time move in fee {{ view.account.getMinBid() is not null ? 'of ' ~ view.account.getMinBid() : '' }} for a move-in.
            {% elseif view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_TIERED') %}
                Middle-tier, one-time move in fee {{ view.account.getMinBid() is not null ? 'of ' ~ view.account.getMinBid() : '' }} for a move-in.
            {% endif %}
        </div>
    </div>
        <div class="form-group">

            {% if view.display_terms_download == true %}
                <label class="col-lg-2 control-label"></label>
                <div class="col-lg-10">
                    Download your Sparefoot Terms Agreement <a href="/accounts/download-terms" target="_blank"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your Terms Agreement" /> PDF</a>
                </div>
            {% endif %}
            <label class="col-lg-2 control-label"></label>
            <div class="col-lg-10">
                <a target="_blank" href="{{ view.terms_link }}">Your Terms of Service</a>
                {% if view.termsAddendumLink %}
                    <br /><a target="_blank" href="{{ view.termsAddendumLink }}">Your Terms of Service Addendum</a>
                {% endif %}
            </div>
        </div>


        <div class="form-actions">
            <div class="pull-right">
                <input id="submit_button" class="ui primary button" name="commit" type="submit" value="Save Changes" />
            </div>
        </div>

{% else %}
    <p>You don't have permission to edit this information.</p>
{% endif %}
{% endblock %}