<h1>Settings</h1>

{% set active = view.active is defined ? view.active : null %}
{% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) %}
<ul class="nav nav-tabs">
    <li{{ 'personal' == active ? ' class="active"' : '' }}><a href="{{ path('settings') }}" id="settings_menu_myaccount">Your Account</a></li>
    <li{{ 'corporate' == active ? ' class="active"' : '' }}><a href="{{ path('settings_corporate') }}" id="settings_menu_corporate">Corporate Account</a></li>
    {% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) %}
        <li{{ 'user' == active ? ' class="active"' : '' }}><a href="{{ path('user') }}" id="settings_menu_user">Users</a></li>
    {% endif %}
    {% if view.loggedUser and (view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_GOD') or view.loggedUser.getMyfootRole() == constant('\\Genesis_Entity_UserAccess::ROLE_ADMIN')) %}
        <li{{ 'payment' == active ? ' class="active"' : '' }}><a href="{{ path('payment') }}" id="settings_menu_payment">Payment</a></li>
    {% endif %}
    {% if view.loggedUser.getAccount() and view.loggedUser.getAccount().getHostedSite() %}
         <li{{ 'sites' == active ? ' class="active"' : '' }}><a href="{{ path('sites_setup') }}" id="settings_menu_geopages">GeoPages</a></li>
    {% endif %}
</ul>
{% endif %}
