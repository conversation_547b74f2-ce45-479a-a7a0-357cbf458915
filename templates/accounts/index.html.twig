{% extends 'layout.html.twig' %}
{% block content %}

{% if view.isMyfootGod %}
    <form action="{{ view.action|default(null) }}" id="accounts-form">
        <select name="account_id">
            {% for account in view.accounts %}
                <option{% if view.accountId == account.account_id %} selected="selected"{% endif %} value="{{ account.account_id }}">
                    {{ account.name }} &mdash; {{ account.integration_names|replace({',': ', '}) }} ({{ account.num_facilities }})
                    {% if account.status and account.status != constant('\\Genesis_Entity_Account::STATUS_LIVE') %} &mdash; {{ account.status|upper }}{% endif %}
                </option>
            {% endfor %}
        </select>
    </form>
{% endif %}

{% endblock %}