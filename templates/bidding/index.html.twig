{% extends 'layout.html.twig' %}
{% block content %}

	<h1>Unit-level bids</h1>
	<h2>Facility Average Bid: {{ view.facility.getBidFlat() }}</h2>
    
	<form method="post">
		<table border="1">
			<tr>
				<th>Unit ID</th>
				<th>Unit</th>
				<th>Bid Amount</th>
			</tr>
			{% for unit in view.units %}
				<tr>
					<td>{{ unit.getId() }}</td>
					<td>{{ unit.stringUnitDescription(false) }}</td>
					<td><input type="text" name="bid_amount_{{ unit.getId() }}" value="{{ unit.getUnitBidAmount() ? unit.getUnitBidAmount() : constant('\\Genesis_Entity_StorageSpace::MINIMUM_UNIT_BID_AMOUNT') }}"/></td>

				</tr>
			{% endfor %}
		</table>

		<input type="hidden" name="submit" value="1"/>
		<input type="submit"/>

	</form>

{% endblock %}
