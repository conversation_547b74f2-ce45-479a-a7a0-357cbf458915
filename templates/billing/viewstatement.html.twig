{% extends 'layout.html.twig' %}

{% block content %}
<script type="text/javascript">
var statementId = {{ view.statement.getId() }};
var nextStatementStartDate = new Date({{ view.batch.getStartDate()|date('Y') }}, {{ view.batch.getStartDate()|date('m') }}, 1);
var endDate = new Date({{ view.batch.getStartDate()|date('Y') }}, {{ (view.batch.getStartDate()|date('U') - 60*60*24)|date('m') }}, {{ view.batch.getStartDate()|date('t') }});
var startDate = new Date({{ ('-1 month' ~ view.batch.getStartDate())|date('Y, m, d') }});
var showInterstitial = {{ showInterstitial ? 'true' : 'false' }};
</script>

{% if arr_softwares|length > 0 and (21 in arr_softwares or 23 in arr_softwares) %}
	<div id="guides-modal" class="modal fade">
	    <div class="modal-header">
	        <button class="close" data-dismiss="modal">×</button>
	        <h3>Do you HATE reconciling your statement?</h3>
	    </div>
	    <div class="modal-body">
	        <div class="modal-guides">
		        <p>Yeah, we know...</p>
				<p>So we figured out the best way to use your management software for a more efficient reconciliation. Simply view the guide below and follow the steps. You'll enjoy more move-ins, which means higher rankings in our search results, which means EVEN MORE move-ins. Pretty cool cycle, huh?<br /><br /></p>

				{% if arr_softwares[21] %}
					<p><a class="ui primary button" href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">View Guide</a> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Reconciling with SiteLink Stand Alone</a></p>
				{% endif %}
				{% if arr_softwares[23] %}
					<p><a class="ui primary button" href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">View Guide</a> <a href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">Reconciling with WebSelfStorage</a></p>
				{% endif %}
	        </div>
	    </div>
	    <div class="modal-footer">
	        <a href="#" data-dismiss="modal" class="ui primary button">Close</a>
	    </div>
	</div>
{% endif %}

{% if app.request.headers.get('User-Agent') and 'MSIE' in app.request.headers.get('User-Agent') %}
	<div class="alert alert-danger">
	    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
		<span class="lead">Make sure your popup blocker is turned off.</span>
	</div>
{% endif %}

{% if not view.facilityId %}
    <h1>{{ view.batch.getStartDate()|date('F Y') }}</h1>
{% else %}
    <h1>{{ view.facility.getTitle() }} - {{ view.batch.getStartDate()|date('F') }} {{ view.batch.getStartDate()|date('Y') }}</h1>
{% endif %}

{% if view.facility.getAccount().getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
		<div class="well">
		    <h6>Help</h6>
			{% if arr_softwares|length > 0 and (21 in arr_softwares or 23 in arr_softwares) %}
				{% if arr_softwares[21] %}
					<p><i class="fa fa-file guide-doc"></i> Document - <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Using SiteLink Stand Alone To Reconcile Your Statement</a></p>
				{% endif %}
				{% if arr_softwares[23] %}
					<p><i class="fa fa-file guide-doc"></i> Document - <a href="/pdf/webselfstoragereconcilliation-v2.pdf" target="_blank">Using WebSelfStorage To Reconcile Your Statement</a></p>
				{% endif %}
			{% endif %}
		    <p><i class="fa fa-play-circle"></i> Video - <a href="https://www.youtube.com/v/gzR0y7IrjFo" class="video-link">How do I reconcile my statement?</a></p>
		</div>

{% else %}

{% endif %}

<h3>This Month's AdNetwork Reservations</h3>
<p>Please mark the status of each AdNetwork reservation you received this month.</p>

{% set validBookings = 0 %}
{% set validBookingAmount = 0 %}

{% if allTableList %}
<table class="table table-striped data-grid" id="all-statements">
    <thead>
        <tr>
            <th></th>
            <th>Status</th>
            {% if not view.facilityId %}
                <th>Facility</th>
            {% endif %}
            <th>Customer</th>
            <th>Move-In Date</th>
            <th>Reservation Date</th>
            <th>Bid Amount</th>
            <th>Transaction Fee</th>
            <th><a rel="tooltip" title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
        </tr>
    </thead>
    <tbody>
        {% for tableInfo in allTableList %}
            {% for confirmation_code, transaction in tableInfo.TRANSACTIONS %}
                {% if transaction.auto_state != constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') %}
                    {% if transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_PENDING') and transaction.free != 1 %}
                        {% set validBookings = validBookings + 1 %}
                        {% set validBookingAmount = validBookingAmount + transaction.bid_amount %}
                    {% endif %}

                    {% set supNotesDisplay = '' %}
                    {% if transaction.support_notes is not empty %}
                        {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ transaction.support_notes ~ '"><strong>NOTES</strong></a>' %}
                    {% endif %}

                    {% set txn = call_static('\\Genesis_Service_Transaction', 'loadById', [transaction.confirmation_code]) %}
                    {% set txnMeta = txn.getBookingMeta(true) %}

            <tr id="{{ transaction.confirmation_code }}">
                <td id="action-{{ transaction.confirmation_code }}">
                    <div  class="statement-reservation-actions">
                        <div class="btn-group" data-toggle="buttons-radio">
                            <a class="confirm-button btn{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? '' : ' active' }}"><i class="icon-ok fa fa-check{{ transaction.is_checked ? ' icon-green' : '' }}"></i></a>
                            <a class="dispute-button btn{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? ' active' : '' }}"><i class="icon-remove fa fa-times"></i></a>
                        </div>
                    </div>
                </td>
                <td id="status-{{ transaction.confirmation_code }}">
                    <span class="error{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? '' : ' hide' }}">Did&nbsp;Not&nbsp;Move&nbsp;In</span>
                    <span class="success{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? ' hide' : '' }}">Moved&nbsp;In</span>
                    <img id="activity-indicator-{{ transaction.confirmation_code }}" class="hide" src="/images/loading.gif" />
                    {{ supNotesDisplay is not empty ? supNotesDisplay|raw : '' }}
                </td>
                {% if not view.facilityId %}
                    <td id="facility-name-{{ transaction.confirmation_code }}"><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo.FACILITY.getId() }}">{{ tableInfo.FACILITY.getTitleWithCompanyCode() }}</a></td>
                {% endif %}
                <td id="customer-{{ transaction.confirmation_code }}">
                    <span id="customer-name-{{ transaction.confirmation_code }}">{{ transaction.last_name|trim|title }}, {{ transaction.first_name|title }}</span><br />
                    {% for names in txn.getAlternateNamesMeta(true) %}
                        {{ names.last|trim|title }}, {{ names.first|trim|title }}<br />
                    {% endfor %}
                    {{ (transaction.email == '<EMAIL>' or transaction.email is empty) ? 'No e-mail address provided' : transaction.email }}<br />
                    {% for email in txnMeta.alternate_email %}
                        {% if email.getValue() != transaction.email %}
                            {{ email.getValue() }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ transaction.phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                    {% for phone in txnMeta.alternate_phone %}
                        {% if phone.getValue() != transaction.phone %}
                            <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                        {% endif %}
                    {% endfor %}
                    {% if transaction.confirmation_code in possibleDups %}
                        <a rel="tooltip" title="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                    {% endif %}
                    {% if transaction.booking_type == constant('\\Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE') %}
                        <a rel="tooltip" title="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
                    {% endif %}

                </td>
                <td id="date-{{ transaction.confirmation_code }}">{{ transaction.move_in|date('m-d-Y') }}</td>
                <td>{{ transaction.timestamp|date('m-d-Y') }}</td>
                <td>${{ transaction.base_bid }}</td>
                <td id="amount-{{ transaction.confirmation_code }}">{{ transaction.free ? '$0.00' : '$' ~ transaction.bid_amount }}</td>
                <td><a href="#" rel="tooltip" data-original-title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ transaction.regular_price }}.">${{ transaction.tenant_value }}.00</a><br />
                    <a href="#" rel="tooltip" data-original-title="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant."><h6>{{ transaction.roi }}% <abbr title="Return on Investment">ROI</abbr></h6></a>
                </td>
            </tr>
                {% endif %}
            {% endfor %}
        {% endfor %}
    </tbody>
</table>
{% endif %}

{# if there are auto-confirmed transactions show them in the next table #}
{% if autoTableList %}
        <br/><br/><h3>Auto-Confirmed AdNetwork Move-Ins</h3>
        <p>The following AdNetwork move-ins were auto-confirmed by your management software. (<a rel="tooltip" title="Your management software provider notified us that the following tenants moved into their space.">what's this?</a>)</p>
        <table class="table table-striped data-grid" id="auto-statements">
            <thead>
                <tr>
                    <th></th>
                    <th>Status</th>
                   {% if not view.facilityId %}
                       <th>Facility</th>
                   {% endif %}
                   <th>Customer</th>
                   <th>Move-In Date</th>
                   <th>Reservation Date</th>
                   <th>Bid Amount</th>
                   <th>Transaction Fee</th>
                    <th><a rel="tooltip" title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                </tr>
            </thead>
            <tbody>
                {% for tableInfo in autoTableList %}
                    {% for confirmation_code, transaction in tableInfo.TRANSACTIONS %}
                        {% if not transaction.free %}
                            {% if transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_PENDING') %}
                                {% set validBookings = validBookings + 1 %}
                                {% set validBookingAmount = validBookingAmount + transaction.bid_amount %}
                            {% endif %}

                            {% set supNotesDisplay = '' %}
                            {% if transaction.support_notes is not empty %}
                                {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ transaction.support_notes ~ '"><strong>NOTES</strong></a>' %}
                            {% endif %}

                            {% set txn = call_static('\\Genesis_Service_Transaction', 'loadById', [transaction.confirmation_code]) %}
                            {% set txnMeta = txn.getBookingMeta(true) %}

                    <tr id="{{ transaction.confirmation_code }}">
                        <span id="auto-dispute-reason-{{ transaction.confirmation_code }}" class="hide">{{ transaction.dispute_reason }}</span>
                        <td id="action-{{ transaction.confirmation_code }}">
                            <div  class="statement-reservation-actions">
                                <div class="btn-group" data-toggle="buttons-radio">

                                    {% if not transaction.review_status %}
                                        <a class="confirm-autoconfirmed-button btn {{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? '' : 'active' }}"><i class="icon-ok fa fa-check"></i></a>
                                        <a class="dispute-autoconfirmed-button btn {{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? 'active' : '' }}"><i class="icon-remove fa fa-times"></i></a>
                                    {% elseif transaction.review_status == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                                        <a class="confirm-autoconfirmed-button btn {{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? '' : 'active' }}"><i class="icon-ok fa fa-check"></i></a>
                                        <a class="dispute-autoconfirmed-button btn {{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? 'active' : '' }}"><i class="icon-remove fa fa-times"></i></a>
                                    {% elseif transaction.review_status == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') %}
                                        {% if transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') %}
                                            <a class="confirm-autoconfirmed-button btn"><i class="icon-ok fa fa-check"></i></a>
                                            <a class="dispute-autoconfirmed-button btn active"><i class="icon-remove fa fa-times"></i></a>
                                        {% else %}
                                            <a class="confirm-autoconfirmed-button btn active"><i class="icon-ok fa fa-check"></i></a>
                                        {% endif %}
                                    {% endif %}

                                </div>
                            </div>
                        </td>
                        <td id="status-{{ transaction.confirmation_code }}">
                            {% if transaction.review_status == constant('\\Genesis_Entity_Transaction::STATUS_UNDER_REVIEW') %}
                                <span class="under-review">Under Review by SpareFoot</span>
                                <span class="error hide">Did&nbsp;Not&nbsp;Move&nbsp;In</span>
                                <span class="success hide">Moved&nbsp;In</span>
                            {% else %}
                                <span class="under-review hide">Under Review by SpareFoot</span>
                                <span class="error{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? '' : ' hide' }}">Did&nbsp;Not&nbsp;Move&nbsp;In {{ transaction.review_status == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
                                <span class="success{{ transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_DISPUTED') ? ' hide' : '' }}">Moved&nbsp;In {{ transaction.review_status == constant('\\Genesis_Entity_Transaction::STATUS_REVIEWED') ? ' (Reviewed)' : '' }}</span>
                            {% endif %}
                            <img id="activity-indicator-{{ transaction.confirmation_code }}" class="hide" src="/images/loading.gif" />
                            {{ supNotesDisplay is not empty ? supNotesDisplay|raw : '' }}
                        </td>
                        {% if not view.facilityId %}
                            <td><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo.FACILITY.getId() }}">{{ tableInfo.FACILITY.getTitleWithCompanyCode() }}</a></td>
                        {% endif %}


                        <td id="customer-{{ transaction.confirmation_code }}">

                            {% set matchReasons = transaction.match_reasons|split(', ') %}

                            <span id="customer-name-{{ transaction.confirmation_code }}">{{ transaction.last_name|trim|title }}, {{ transaction.first_name|title }}</span><br />
                            {% for names in txn.getAlternateNamesMeta(true) %}
                                {{ names.last|trim|title }}, {{ names.first|trim|title }}<br />
                            {% endfor %}
		                    {{ (transaction.email == '<EMAIL>' or transaction.email is empty) ? 'No e-mail address provided' : transaction.email }}<br />
		                    {% for email in txnMeta.alternate_email %}
                                {% if email.getValue() != transaction.email %}
                                    {{ email.getValue() }}<br />
                                {% endif %}
                            {% endfor %}
                            {{ transaction.phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                            {% for phone in txnMeta.alternate_phone %}
                                {% if phone.getValue() != transaction.phone %}
                                    <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                {% endif %}
                            {% endfor %}

                            {% if 'CustomerId' in matchReasons %}
                                <br/>
                                <span>
                                    Tenant ID {{ transaction.tenant_customer_id }}
                                </span>
                            {% endif %}

                            {% if 'ConfirmationCode' in matchReasons %}
                                <br/>
                                <span>
                                    Confirmation Code {{ transaction.confirmation_code }}
                                </span>
                            {% endif %}

                            {% if 'LeadID' in matchReasons %}
                                <br/>
                                <span>
                                    Lead ID {{ transaction.tenant_lead_id }}
                                </span>
                            {% endif %}

                            {% if transaction.confirmation_code in possibleDups %}
                                <a rel="tooltip" title="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                            {% endif %}
		                    {% if transaction.booking_type == constant('\\Genesis_Entity_Transaction::BOOKING_TYPE_OFFLINE') %}
		                        <a rel="tooltip" title="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
		                    {% endif %}

                            <div id="tenant-{{ transaction.confirmation_code }}" class="hide">
                                {{ transaction.tenant_last_name }}, {{ transaction.tenant_first_name }}<br />
                                {{ transaction.tenant_email }}<br />
                                {{ transaction.tenant_phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}

                                {% if 'HomePhone' in matchReasons %}
                                    <br/>
                                    <span>
                                        Home Phone {{ transaction.tenant_home_phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    </span>
                                {% endif %}

                                {% if 'WorkPhone' in matchReasons %}
                                    <br/>
                                    <span>
                                        Work Phone {{ transaction.tenant_work_phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    </span>
                                {% endif %}

                                {% if 'CellPhone' in matchReasons %}
                                    <br/>
                                    <span>
                                        Mobile Phone {{ transaction.tenant_cell_phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    </span>
                                {% endif %}

                                {% if 'CustomerId' in matchReasons %}
                                    <br/>
                                    <span>
                                        Tenant ID {{ transaction.tenant_customer_id }}
                                    </span>
                                {% endif %}

                                {% if 'ConfirmationCode' in matchReasons %}
                                    <br/>
                                    <span>
                                        Confirmation Code {{ transaction.confirmation_code }}
                                    </span>
                                {% endif %}

                                {% if 'LeadID' in matchReasons %}
                                    <br/>
                                    <span>
                                        Lead ID {{ transaction.tenant_lead_id }}
                                    </span>
                                {% endif %}
                            </div>
                            <div id="alt-tenant-{{ transaction.confirmation_code }}" class="hide">
                                {% if transaction.alt_tenant_last_name %}
                                    {{ transaction.alt_tenant_last_name }}, {{ transaction.alt_tenant_first_name }}<br/>
                                {% endif %}
                                {% if transaction.alt_tenant_email %}
                                    {{ transaction.alt_tenant_email }}<br/>
                                {% endif %}
                                {% if transaction.alt_tenant_phone %}
                                    {{ transaction.alt_tenant_phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}<br/>
                                {% endif %}
                            </div>

                        </td>
                        <td id="date-{{ transaction.confirmation_code }}">{{ transaction.move_in|date('m-d-Y') }}</td>
                        <td>{{ transaction.timestamp|date('m-d-Y') }}
                        </td>
                        <td>${{ transaction.base_bid }}</td>
                        <td id="amount-{{ transaction.confirmation_code }}">${{ transaction.bid_amount }}</td>
                        <td><a href="#" rel="tooltip" data-original-title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ transaction.regular_price }}.">${{ transaction.tenant_value }}.00</a><br />
                            <a href="#" rel="tooltip" data-original-title="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant."><h6>{{ transaction.roi }}% <abbr title="Return on Investment">ROI</abbr></h6></a>
                        </td>
                    </tr>
                        {% endif %}
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
{% endif %}

{# if there are early move ins show them in another table #}
{% if earlyTableList %}
        <br/><br/><h3>Early Move-Ins</h3>
        <p>Let us know if any of these customers moved in early. If they did, change their move-in date below to improve your move-in rate and search ranking. If none of them moved in early, you don't need to do anything. Each reservation will simply appear on your statement next month.</p>
        <table class="table table-striped data-grid" id="early-move-ins">
            <thead>
                <tr>
                   <th></th>
                   <th>Status</th>
                   {% if not view.facilityId %}
                       <th>Facility</th>
                   {% endif %}
                   <th>Customer</th>
                   <th>Move-In Date</th>
                   <th>Reservation Date</th>
                   <th>Bid Amount</th>
                   <th>Transaction Fee</th>
                    <th><a rel="tooltip" title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                </tr>
            </thead>
            <tbody>
                {% for tableInfo in earlyTableList %}
                    {% for confirmation_code, transaction in tableInfo.TRANSACTIONS %}
                        {% if transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_PENDING') %}
                            {% set validBookings = validBookings + 1 %}
                            {% set validBookingAmount = validBookingAmount + transaction.bid_amount %}
                        {% endif %}

                        {% set supNotesDisplay = '' %}
                        {% if transaction.support_notes is not empty %}
                            {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ transaction.support_notes ~ '"><strong>NOTES</strong></a>' %}
                        {% endif %}

                        {% set txn = call_static('\\Genesis_Service_Transaction', 'loadById', [transaction.confirmation_code]) %}
                        {% set txnMeta = txn.getBookingMeta(true) %}                        <tr id="{{ transaction.confirmation_code }}">
                            <td id="action-{{ transaction.confirmation_code }}">
                                <div class="statement-reservation-actions">
                                    <div class="btn-group" data-toggle="buttons-radio">
                                        <a class="change-move-in-date-button btn"><i class="icon-ok fa fa-check"></i></a>
                                        <a class="btn disabled"><i class="icon-remove fa fa-times"></i></a>
                                    </div>
                                </div>
                            </td>
                            <td id="status-{{ transaction.confirmation_code }}">
                                <span>Move&nbsp;In&nbsp;Next&nbsp;Month</span>
                                <span class="success hide">Moved&nbsp;In</span>
                                <img id="activity-indicator-{{ transaction.confirmation_code }}" class="hide" src="/images/loading.gif" />
                                {{ supNotesDisplay is not empty ? supNotesDisplay|raw : '' }}
                            </td>
                            {% if not view.facilityId %}
                                <td><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo.FACILITY.getId() }}">{{ tableInfo.FACILITY.getTitleWithCompanyCode() }}</a></td>
                            {% endif %}
                            <td id="customer-{{ transaction.confirmation_code }}">
                                {{ transaction.last_name|trim|title }}, {{ transaction.first_name|title }}<br />
                                {% for names in txn.getAlternateNamesMeta(true) %}
                                    {{ names.last|trim|title }}, {{ names.first|trim|title }}<br />
                                {% endfor %}
			                    {{ (transaction.email == '<EMAIL>' or transaction.email is empty) ? 'No e-mail address provided' : transaction.email }}<br />
			                    {% for email in txnMeta.alternate_email %}
                                    {% if email.getValue() != transaction.email %}
                                        {{ email.getValue() }}<br />
                                    {% endif %}
                                {% endfor %}
                                {{ transaction.phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                {% for phone in txnMeta.alternate_phone %}
                                    {% if phone.getValue() != transaction.phone %}
                                        <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    {% endif %}
                                {% endfor %}
                            </td>
                            <td id="date-{{ transaction.confirmation_code }}">{{ transaction.move_in|date('m-d-Y') }}</td>
                            <td>{{ transaction.timestamp|date('m-d-Y') }}</td>
                            <td>${{ transaction.base_bid }}</td>
                            <td id="amount-{{ transaction.confirmation_code }}">$0</td>
                            <td><a href="#" rel="tooltip" data-original-title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ transaction.regular_price }}.">${{ transaction.tenant_value }}.00</a><br />
                                <a href="#" rel="tooltip" data-original-title="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant."><h6>{{ transaction.roi }}% <abbr title="Return on Investment">ROI</abbr></h6></a>
                            </td>
                        </tr>
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
{% endif %}

{# if there are late move ins show them here #}
{% if lateTableList %}
        <br/><br/><h3>Late Move-Ins</h3>
        <p>These customers had move-in dates during the last 10 days of {{ ('-1 month' ~ view.batch.getStartDate())|date('F') }}. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don't need to do anything. We'll only bill you for customers you move to this month.</p>
        <table class="table table-striped data-grid" id="late-move-ins">
            <thead>
                <tr>
                   <th></th>
                   <th>Status</th>
                   {% if not view.facilityId %}
                       <th>Facility</th>
                   {% endif %}
                   <th>Customer</th>
                   <th>Move-In Date</th>
                   <th>Reservation Date</th>
                   <th>Bid Amount</th>
                   <th>Transaction Fee</th>
                    <th><a rel="tooltip" title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                </tr>
            </thead>
            <tbody>
                {% for tableInfo in lateTableList %}
                    {% for confirmation_code, transaction in tableInfo.TRANSACTIONS %}
                        {% if transaction.booking_state == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_PENDING') %}
                            {% set validBookings = validBookings + 1 %}
                            {% set validBookingAmount = validBookingAmount + transaction.bid_amount %}
                        {% endif %}

                        {% set supNotesDisplay = '' %}
                        {% if transaction.support_notes is not empty %}
                            {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ transaction.support_notes ~ '"><strong>NOTES</strong></a>' %}
                        {% endif %}

                        {% set txn = call_static('\\Genesis_Service_Transaction', 'loadById', [transaction.confirmation_code]) %}
                        {% set txnMeta = txn.getBookingMeta(true) %}                        <tr id="{{ transaction.confirmation_code }}">
                            <td id="action-{{ transaction.confirmation_code }}">
                                <div class="statement-reservation-actions">
                                    <div class="btn-group" data-toggle="buttons-radio">
                                        <a class="change-move-in-date-button btn"><i class="icon-ok fa fa-check"></i></a>
                                        <a class="dispute-button btn active"><i class="icon-remove fa fa-times"></i></a>
                                    </div>
                                </div>
                            </td>
                            <td id="status-{{ transaction.confirmation_code }}">
                                <span class="error">Did&nbsp;Not&nbsp;Move&nbsp;In</span><img id="activity-indicator-{{ transaction.confirmation_code }}" class="hide" src="/images/loading.gif" />
                                <span class="success hide">Moved&nbsp;In</span>
                                <img id="activity-indicator-{{ transaction.confirmation_code }}" class="hide" src="/images/loading.gif" />
                                {{ supNotesDisplay is not empty ? supNotesDisplay|raw : '' }}
                            </td>
                            {% if not view.facilityId %}
                                <td><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo.FACILITY.getId() }}">{{ tableInfo.FACILITY.getTitleWithCompanyCode() }}</a></td>
                            {% endif %}
                            <td id="customer-{{ transaction.confirmation_code }}">
                                {{ transaction.last_name|trim|title }}, {{ transaction.first_name|title }}<br />
                                {% for names in txn.getAlternateNamesMeta(true) %}
                                    {{ names.last|trim|title }}, {{ names.first|trim|title }}<br />
                                {% endfor %}
			                    {{ (transaction.email == '<EMAIL>' or transaction.email is empty) ? 'No e-mail address provided' : transaction.email }}<br />
			                    {% for email in txnMeta.alternate_email %}
                                    {% if email.getValue() != transaction.email %}
                                        {{ email.getValue() }}<br />
                                    {% endif %}
                                {% endfor %}
                                {{ transaction.phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                {% for phone in txnMeta.alternate_phone %}
                                    {% if phone.getValue() != transaction.phone %}
                                        <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    {% endif %}
                                {% endfor %}
                            </td>
                            <td id="date-{{ transaction.confirmation_code }}">{{ transaction.move_in|date('m-d-Y') }}</td>
                            <td>{{ transaction.timestamp|date('m-d-Y') }}</td>
                            <td>${{ transaction.base_bid }}</td>
                            <td id="amount-{{ transaction.confirmation_code }}">$0</td>
                            <td><a href="#" rel="tooltip" data-original-title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price of ${{ transaction.regular_price }}.">${{ transaction.tenant_value }}.00</a><br />
                                <a href="#" rel="tooltip" data-original-title="This is the marketing dollar Return on Investment of your transaction fee based on the estimated Lifetime Value of the tenant."><h6>{{ transaction.roi }}% <abbr title="Return on Investment">ROI</abbr></h6></a>
                            </td>
                        </tr>
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
{% endif %}

{# list free bookings in a different table #}
{% if freeTableList %}
        <br/><br/><h3>No-Fee Reservations</h3>
        <p>We sent you these reservations from SpareFoot products without transaction fees (GeoPages, SiteBuilder, and Booking Widgets).</p>
        <table class="table table-striped data-grid" id="no-fee-reservations">
            <thead>
                <tr>
                   {% if not view.facilityId %}
                       <th>Facility</th>
                   {% endif %}
                   <th>Customer</th>
                   <th>Move-In Date</th>
                   <th>Reservation Date</th>
                   <th>Unit Number</th>
                    <th><a rel="tooltip" title="On average, SpareFoot tenants stay for 12 months. This is an estimate of the revenue you will receive from this tenant based on the unit price they reserved.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>
                </tr>
            </thead>
            <tbody>
                {% for tableInfo in freeTableList %}
                    {% for confirmation_code, transaction in tableInfo.TRANSACTIONS %}
                        {% set txn = call_static('\\Genesis_Service_Transaction', 'loadById', [transaction.confirmation_code]) %}
                        {% set txnMeta = txn.getBookingMeta(true) %}
                            <tr id="{{ transaction.confirmation_code }}">
                                {% if not view.facilityId %}
                                    <td><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo.FACILITY.getId() }}">{{ tableInfo.FACILITY.getTitleWithCompanyCode() }}</a></td>
                                {% endif %}
                                <td>{{ transaction.last_name|trim|title }}, {{ transaction.first_name|title }}<br />
                                    {% for names in txn.getAlternateNamesMeta(true) %}
                                        {{ names.last|trim|title }}, {{ names.first|trim|title }}<br />
                                    {% endfor %}
				                    {{ (transaction.email == '<EMAIL>' or transaction.email is empty) ? 'No e-mail address provided' : transaction.email }}<br />
				                    {% for email in txnMeta.alternate_email %}
                                        {% if email.getValue() != transaction.email %}
                                            {{ email.getValue() }}<br />
                                        {% endif %}
                                    {% endfor %}
                                    {{ transaction.phone|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                    {% for phone in txnMeta.alternate_phone %}
                                        {% if phone.getValue() != transaction.phone %}
                                            <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td id="date-{{ transaction.confirmation_code }}">{{ transaction.move_in|date('m-d-Y') }}</td>
                                <td>{{ transaction.timestamp|date('m-d-Y') }}</td>
                                <td>{{ transaction.unit_number }}</td>
                                <td>${{ transaction.tenant_value }}.00</td>
                            </tr>
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
{% endif %}

<!--<div style="margin-top:2em; padding-top:1em; border-top:1px solid #eee;">
    <h3><span style="font-weight:normal;">Valid Move-Ins:</span> <span id="valid-bookings">{{ validBookings }}</span></h3>
    <h3><span style="font-weight:normal;">Move-In Fee Total:</span> $<span id="valid-booking-amount">{{ validBookingAmount }}</span></h3>
</div>-->

<div id="interstitial-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">×</button>
                <h4>Introducing the SpareFoot Bidding Tool!</h4>
            </div>
            <div class="modal-body">
                <img src="/images/bidding-interstitial.jpg" width="400" height="370" style="float:right; margin-left:12px;" />
                <h3>Make your AdNetwork listings perform better.</h3>
                <p>Our bidding tool is the perfect way to generate more bookings for facilities that need a little extra help, while ensuring that you don't pay more than necessary. See how your facilities are currently ranking, and adjust your bid up or down to get the level of exposure you want for a price you are comfortable with.</p><br />
                <h3>See where your facilities are ranking now: </h3>
                <ul>
                {% for facility in interstitialFacilities %}
                    <li><a href="/facility/bid/fid/{{ view.facility.getId() }}">{{ view.facility.getTitle() }}</a></li>
                {% endfor %}
                </ul>
                <a href="/facility" style="margin-left:2.5em;">See all facilities</a>
                <span class="clear"></span>
            </div>
        </div>
    </div>
</div>

<form id="change-move-in-date-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">×</button>
                <h4>Change move-in date</h4>
            </div>
            <div class="modal-body">
                <p id="change-move-in-date-customer-info"></p>
                <label for="into-date">Please enter the customer's (approximate) new move-in date:</label>
                <input type="text" id="into-date" name="into_date" value="" placeholder="" readonly="readonly" class="datepicker-field" />
            </div>
            <div class="modal-footer">
                <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui primary button" value="Save" />
            </div>
        </div>
    </div>
</form>

<form id="dispute-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal" id="dispute-modal-close">×</button>
                <h4>Why didn't the customer move in this month?</h4>
            </div>
            <div class="modal-body">
                <p id="dispute-customer-info"></p>
                <div class="controls">
                    <label class="radio"><input type="radio" name="dispute-reason" value="delay" /> Customer might move in at a future date</label>
                    <div class="hide customer-reason-specifics">
                        <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                        <input type="text" id="out-date" name="out_date" value="" placeholder="" readonly="readonly" class="datepicker-field" />
                    </div>
                </div>
                <div class="controls">
                    <label class="radio"><input type="radio" name="dispute-reason" value="name-change" /> Customer moved in under a different name</label>
                    <div class="hide customer-reason-specifics">
                        <p>This is still a valid move-in. Please provide us with accurate contact information to appear on your bill.</p>
                        <input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" />
                        <input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" />
                    </div>
                </div>
                {% if availableSisterFacilities|length > 1 %}
                    <div class="controls">
                        <label class="radio"><input type="radio" name="dispute-reason" value="facility-change" /> Customer moved into a sister facility</label>
                        <div id="facility-change-input" class="hide customer-reason-specifics">
                            <p>Which facility?</p>
                            <select id="new-facility-id" name="facility_id">
                                {% for facility in availableSisterFacilities %}
                                    {% if facility.getApproved() and facility.getPublished() and facility.getBillableEntityId() is not null %}
                                        <option value="{{ facility.getId() }}">{{ facility.getTitle() }} {{ facility.getCompanyCode() ? '(' ~ facility.getCompanyCode() ~ ')' : '' }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                {% endif %}
                <div class="controls">
                    <label class="radio"><input id="dispute-cancel" type="radio" name="dispute-reason" value="canceled" /> Customer will never move in</label>
                    <div class="hide customer-reason-specifics" style="margin-left:3em;">
                        <p>Please explain the reason for this cancellation (i.e., Customer did not show up):</p>
                        <textarea id="dispute-text" name="customer-dispute-reason"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a class="ui basic button" data-dismiss="modal" id="dispute-modal-cancel">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui primary button" value="Save" />
            </div>
        </div>
    </div>
</form>

<form id="dispute-autoconfirmed-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal" id="dispute-autoconfirmed-modal-close">×</button>
                <h4>Request auto-confirmed customer review</h4>
            </div>

            <div class="modal-body">
                <p>If you believe this customer was confirmed in error, submit this form and SpareFoot will review the record.</p><br />
                <table class="table">
                    <tr>
                        <th>SpareFoot Reservation</th>
                        <th>Your Matched Tenant</th>
                        <th>Alternate Tenant Info</th>
                    </tr>
                    <tr>
                        <td id="dispute-autoconfirmed-customer-info"></td>
                        <td id="dispute-autoconfirmed-tenant-info"></td>
                        <td id="dispute-autoconfirmed-alt-tenant-info"></td>
                    </tr>
                </table>
            </div>

            <div style="margin-left:1em;">
                <p>Please explain why this confirmation is in error.</p>
                <textarea id="autoconfirmed-dispute-text" name="customer_dispute_reason" class="input-xlarge"></textarea>
            </div>

            <div class="modal-footer">
                <a class="ui basic button" data-dismiss="modal" id="dispute-autoconfirmed-modal-cancel">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui primary button" value="Submit" />
            </div>
        </div>
    </div>
</form>
