{% extends 'layout.html.twig' %}

{% block content %}
{% if view.statementPairs|length < 1 %}
<p style="margin:1em;">You have no statements at this time.
</p>

{% else %}

{% for pair in view.statementPairs %}

    {% if pair['BATCH'].getStatus() == constant('\\Genesis_Entity_StatementBatch::STATUS_OPEN') %}
        <div class="well form-inline">
                    <h6>Open Statement</h6> <h3>{{ pair['BATCH'].getStartDate()|date('F j') }}-{{ pair['BATCH'].getEndDate()|date('j') }}
                <span style="color:#888; font-weight:normal;">{{ view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') ? 'Rent Collected' : 'Move-Ins' }}</span></h3>
            <br />
            <div class="row">
                <div class="col-md-5">
                    <table class="table">
                        <tr>
                            <th>Reconciliation Deadline</th>
                            <td>{{ pair['BATCH'].getReconciliationEndDate()|date('F j') }}</td>
                        </tr>
                        <tr>
                            <th>{{ view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') ? 'Current Tenants' : 'Current Move-Ins' }}</th>
                            <td>{{ view.userId ? 
                            (pair['STATEMENT'].getTotalPendingMinusDelayedByUserId(view.userId) + pair['STATEMENT'].getTotalConfirmedByUserId(view.userId)) :
                            (pair['STATEMENT'].getTotalPendingMinusDelayed() + pair['STATEMENT'].getTotalConfirmed())
                    }} out of {{ (view.userId ? 
                            pair['STATEMENT'].getTotalDisputedByUserId(view.userId) :
                            pair['STATEMENT'].getTotalDisputed()) + (view.userId ? 
                            (pair['STATEMENT'].getTotalPendingMinusDelayedByUserId(view.userId) + pair['STATEMENT'].getTotalConfirmedByUserId(view.userId)) :
                            (pair['STATEMENT'].getTotalPendingMinusDelayed() + pair['STATEMENT'].getTotalConfirmed())) }}</td>
                        </tr>
                        <tr>
                            <th>{{ view.account.getBidType() == constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') ? 'Current SpareFoot Fees (not including taxes)' : 'Current Move-In Fees (not including taxes)' }}</th>
                            <td>${{ view.userId ? 
                            (pair['STATEMENT'].getTotalAmountByUserId(view.userId) ? pair['STATEMENT'].getTotalAmountByUserId(view.userId) : '0.00') :
                            (pair['STATEMENT'].getDynamicTotalAmount() ? pair['STATEMENT'].getDynamicTotalAmount() : '0.00')
                            }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="btn-toolbar">
                <div class="btn-group">
                    <a href="{{ path('billing_viewstatement', {'id': pair['STATEMENT'].getId()}) }}" class="ui primary button" id="open_statement">Reconcile Statement</a>
                </div>
                <div class="btn-group">
                    <a class="ui button large dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="{{ path('billing_pdf', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" /> PDF</a></li>
                        <li><a href="{{ path('billing_csv', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" /> Excel</a></li>
                    </ul>
                </div>
            </div>

            {% if view.account.getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
                <br />
                {% if 'SLA' in view.loggedUser.getAccount().getInfoString() %}
                    <p><span class="label label-info">How To</span> <a href="/pdf/SiteLink-StandAlone-reconciliation.pdf" target="_blank">Use SiteLink to reconcile my statement</a></p>
                {% endif %}
                <p><span class="label label-info">How To</span> <a href="https://www.youtube.com/v/gzR0y7IrjFo" class="video-link">Reconcile my statement</a></p>

            {% else %}
            <p><i class="fa fa-play-circle"></i> <a href="https://www.youtube.com/v/zQbKQ2mk3hE" class="video-link">How do I reconcile my statement?</a></p>
            {% endif %}

        </div>
    {% endif %}

{% endfor %}

<h3>Statement Archive</h3>
{% if view.account.getBidType() != constant('\\Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
<table id="statements" class="data-grid">
    <thead>
        <tr>
           <th></th>
           <th>Move-In Fee Total</th>
           <th>Moved In</th>
           <th>Did Not Move In</th>
           <th>Move In Rate</th>
           <th></th>
        </tr>
    </thead>
    <tbody>
        {% for pair in view.statementPairs %}
            {% set didNotMoveIn = view.userId ? 
                        pair['STATEMENT'].getTotalDisputedByUserId(view.userId) :
                        pair['STATEMENT'].getTotalDisputed() %}

            {% set movedIn = view.userId ? 
                        (pair['STATEMENT'].getTotalPendingByUserId(view.userId) + pair['STATEMENT'].getTotalConfirmedByUserId(view.userId)) :
                        (pair['STATEMENT'].getTotalPending() + pair['STATEMENT'].getTotalConfirmed()) %}

            {% if pair['BATCH'].getStatus() != constant('\\Genesis_Entity_StatementBatch::STATUS_OPEN') %}
            <tr>
                <td>{{ pair['BATCH'].getStartDate()|date('F j') }}-{{ pair['BATCH'].getEndDate()|date('d, Y') }}</td>
                <td>${{ view.userId ? 
                        (pair['STATEMENT'].getTotalAmountByUserId(view.userId) ? pair['STATEMENT'].getTotalAmountByUserId(view.userId) : '0.00') :
                        (pair['STATEMENT'].getDynamicTotalAmount() ? pair['STATEMENT'].getDynamicTotalAmount() : '0.00')
                }}</td>
                <td>{{ movedIn }}</td>
                <td>{{ didNotMoveIn }}</td>
                <td>{{ (movedIn + didNotMoveIn) > 0 ? ((movedIn / (movedIn + didNotMoveIn)) * 100)|round(2) : 0 }}%</td>
                <td>
                    <div class="btn-group">
                        <a class="btn dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{ path('billing_pdf', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" />&nbsp;PDF</a></li>
                            <li><a href="{{ path('billing_csv', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" />&nbsp;Excel</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
            {% endif %}
        {% endfor %}
    </tbody>
</table>
{% else %}
<table id="statements" class="data-grid">
    <thead>
        <tr>
           <th></th>
           <th>Tenant Fees</th>
           <th>Tenants</th>
           <th></th>
        </tr>
    </thead>
    <tbody>
        {% for pair in view.statementPairs %}
            {% set confirmedTenants = view.userId ? 
                        pair['STATEMENT'].getTotalConfirmedByUserId(view.userId) :
                        pair['STATEMENT'].getTotalConfirmed() %}

            {% set existingTenants = pair['STATEMENT'].getTotalExistingTenants() %}

            {% set didNotMoveIn = view.userId ? 
                        pair['STATEMENT'].getTotalDisputedByUserId(view.userId) :
                        pair['STATEMENT'].getTotalDisputed() %}

            {% if pair['BATCH'].getStatus() != constant('\\Genesis_Entity_StatementBatch::STATUS_OPEN') %}
            <tr>
                <td>{{ pair['BATCH'].getStartDate()|date('F j') }}-{{ pair['BATCH'].getEndDate()|date('d, Y') }}</td>
                <td>${{ view.userId ? 
                        (pair['STATEMENT'].getTotalAmountByUserId(view.userId) ? pair['STATEMENT'].getTotalAmountByUserId(view.userId) : '0.00') :
                        (pair['STATEMENT'].getDynamicTotalAmount() ? pair['STATEMENT'].getDynamicTotalAmount() : '0.00')
                }}</td>
                <td>{{ confirmedTenants }}</td>
                <td>
                    <div class="btn-group">
                        <a class="btn dropdown-toggle" data-toggle="dropdown" href="#">Download <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a href="{{ path('billing_pdf', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/small_pdf_icon.gif" width="14" height="14" alt="Download a PDF of your statement" />&nbsp;PDF</a></li>
                            <li><a href="{{ path('billing_csv', {'statement_id': pair['STATEMENT'].getId()} | merge(view.userId ? {'user_id': view.userId} : {})) }}"><img src="/images/xls_icon.gif" width="14" height="14" alt="Download a CSV of your statement" />&nbsp;Excel</a></li>
                        </ul>
                    </div>
                </td>
            </tr>
            {% endif %}
        {% endfor %}
    </tbody>
</table>
{% endif %}


{% endif %}
{% endblock %}
