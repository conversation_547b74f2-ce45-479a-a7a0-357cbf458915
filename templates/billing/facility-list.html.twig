{% extends 'layout.html.twig' %}
{% block content %}

<h2>Billing</h2>
<ul>
    <li{% if view.selectedAction == 'statements' or view.selectedAction == 'viewstatement' %} class="selected"{% endif %}>
        <a href="/billing/statements">Statements</a>
    </li>
</ul>

<h2>Facilities</h2>
<div class="ui-layout-content" style="overflow-x: hidden;">
    <ul>
        <li{% if not view.facilityId %} class="selected"{% endif %}>
            <a href="/billing/viewstatement/id/{{ view.statementId }}" class="allfacilities">All Facilities</a>
        </li>
        {% for tableInfo in view.tableList %}
            <li{% if view.facilityId == tableInfo['FACILITY'].getId() %} class="selected"{% endif %}>
                <a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ tableInfo['FACILITY'].getId() }}" class="facility">
                    {{ tableInfo['FACILITY'].getTitle() }}
                </a>
            </li>
        {% endfor %}
    </ul>
</div>

{% endblock %}
