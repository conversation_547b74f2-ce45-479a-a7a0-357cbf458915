{% extends 'layout.html.twig' %}

{% block content %}
<script type="text/javascript">
var nextStatementStartDate = new Date({{ view.batch.getStartDate()|date('Y') }}, {{ view.batch.getStartDate()|date('m') }}, 1);
var endDate = new Date({{ view.batch.getStartDate()|date('Y') }}, {{ (view.batch.getStartDate()|date('U') - 60*60*24)|date('m') }}, {{ view.batch.getStartDate()|date('t') }});var startDate = '{{ ("-1 month " ~ view.batch.getStartDate())|date("m-d-Y") }}';
var startDate = new Date({{ ('-1 month' ~ view.batch.getStartDate())|date('Y, m, d') }});
var defaultDate = '{{ view.batch.getStartDate()|date("m-d-Y") }}';
</script>


{% if not view.facilityId %}
    <h1>{{ view.batch.getStartDate()|date('F Y') }}</h1>
{% else %}
    <h1>{{ view.facility.getTitle() }} - {{ view.batch.getStartDate()|date('F') }} {{ view.batch.getStartDate()|date('Y') }}</h1>
{% endif %}

{% if view.lateTableList|length > 0 %}
<h3>Late Move-Ins</h3>
<p>These customers had move-in dates during the last 10 days of {{ ('-1 month' ~ view.batch.getStartDate())|date('F') }}. If any of these customers moved in late, change their move-in date below to improve your move-in rate and search ranking. If none of last month's customers moved in late, you don't need to do anything. We'll only bill you for customers you move to this month.</p>

<table class="table table-striped data-grid" id="late_reservations">
    <thead>
        <tr>
            <th>Rent Collected</th>
            {% if not view.facilityId %}
                <th>Facility</th>
            {% endif %}
            <th>Customer</th>
            <th>Scheduled Move-In</th>
            <th>Reserved</th>
            <th><a rel="tooltip" title="{{ view.account.getResidualPercent() * 100 }}% of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
        </tr>
    </thead>
    <tbody>
        {% for trans in view.lateTableList %}
            {% set facility = loadById(trans.getFacilityId()) %}
            {% set supNotesDisplay = '' %}
            {% set supNotes = trans.stringStatementSupportNotes() %}
            {% if supNotes is not empty %}
                {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ supNotes ~ '"><strong>NOTES</strong></a>' %}
            {% endif %}
            {% set txnMeta = trans.getBookingMeta(true) %}

            <tr id="{{ trans.getConfirmationCode() }}">
                <td id="action-{{ trans.getConfirmationCode() }}">
                    <div class="statement-reservation-actions-residual">
                        <div class="btn-group">
                            <a class="btn active">$0</a>
                            <a class="change-move-in-date-button btn">${{ trans.getPrice() }}</a>
                            <a class="rent-other-button btn">Other</a>
                        </div>
                    </div>
                </td>
                {% if not view.facilityId %}
                    <td id="facility-name-{{ trans.getConfirmationCode() }}"><a href="/billing/residualstatement/id/{{ view.statementId }}/facility/{{ facility.getId() }}">{{ facility.getTitleWithCompanyCode() }}</a></td>
                {% endif %}
                <td id="customer-{{ trans.getConfirmationCode() }}">
                    <span id="customer-name-{{ trans.getConfirmationCode() }}">
                        {{ trans.getLastName()|trim|title }}, {{ trans.getFirstName()|trim|title }}
                    </span><br />
                    {% for names in trans.getAlternateNamesMeta(true) %}
                        {{ names['last']|trim|title }}, {{ names['first']|trim|title }}<br />
                    {% endfor %}
                    {{ (trans.getUser() and (trans.getUser().getEmail() != '<EMAIL>') ? trans.getUser().getEmail() : 'No e-mail address provided') }}<br />
                    {% for email in txnMeta['alternate_email'] %}
                        {% if not (trans.getUser() and email.getValue() == trans.getUser().getEmail()) %}
                            {{ email.getValue() }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ trans.getPhone()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                    {% for phone in txnMeta['alternate_phone'] %}
                        {% if phone.getValue() != trans.getPhone() %}
                            <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                        {% endif %}
                    {% endfor %}
                </td>
                <td id="date-{{ trans.getConfirmationCode() }}">{{ trans.getMoveIn()|date("m-d-Y") }} {{ supNotesDisplay is not empty ? supNotesDisplay : '' }}</td>
                <td>{{ trans.getTimestamp()|date("m-d-Y") }}</td>
                <td id="sparefootfee-{{ trans.getConfirmationCode() }}">$0.00</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}


{% if view.reservationTableList|length > 0 %}
<h3>Reservations</h3>
<p>For the following AdNetwork reservations, please edit the customer details if needed, and let us know how much rent you collected in {{ view.batch.getStartDate()|date('F Y') }}. If the reservation moved in under a different name or at another one of your facilities, update it to be consistent with your records.</p>

<table class="table table-striped data-grid" id="new_reservations">
    <thead>
        <tr>
            <th>Rent Collected</th>
            {% if not view.facilityId %}
                <th>Facility</th>
            {% endif %}
            <th>Customer</th>
            <th></th>
            <th>Scheduled Move-In</th>
            <th>Reserved</th>
            <th><a rel="tooltip" title="{{ view.account.getResidualPercent() * 100 }}% of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
        </tr>
    </thead>
    <tbody>
        {% for biId, trans in view.reservationTableList %}
            {% set billableInstance = loadById(biId) %}
            {% set facility = loadById(billableInstance.getFacilityId()) %}

            {% set supNotesDisplay = '' %}
            {% set supNotes = trans.stringStatementSupportNotes() %}
            {% if supNotes is not empty %}
                {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ supNotes ~ '"><strong>NOTES</strong></a>' %}
            {% endif %}
            {% set txnMeta = trans.getBookingMeta(true) %}

            <tr id="{{ trans.getConfirmationCode() }}">
                <td id="action-{{ trans.getConfirmationCode() }}">
                    <div class="statement-reservation-actions-residual">
                        <input type="hidden" id="delinquent-{{ trans.getConfirmationCode() }}" value="0" />
                        <div class="btn-group" data-toggle="buttons-radio">
                            <a class="rent-zero-button btn {{ billableInstance.getAmountCollected() > 0 ? '' : 'active' }}">$0</a>
                            {% if billableInstance.getAmountCollected() > 0 %}
                                <a class="rent-default-button btn active">${{ billableInstance.getAmountCollected() }}</a>
                            {% else %}
                                <a class="rent-default-button btn">${{ billableInstance.getUnitPrice() }}</a>
                            {% endif %}
                            <a class="rent-other-button btn">Other</a>
                        </div>
                    </div>
                </td>
                {% if not view.facilityId %}
                    <td id="facility-name-{{ trans.getConfirmationCode() }}"><a href="/billing/residualstatement/id/{{ view.statementId }}/facility/{{ facility.getId() }}">{{ facility.getTitleWithCompanyCode() }}</a></td>
                {% endif %}
                <td id="customer-{{ trans.getConfirmationCode() }}">
                    <span id="customer-name-{{ trans.getConfirmationCode() }}">
                        {{ trans.getLastName()|trim|title }}, {{ trans.getFirstName()|trim|title }}
                    </span><br />
                    {% for names in trans.getAlternateNamesMeta(true) %}
                        {{ names['last']|trim|title }}, {{ names['first']|trim|title }}<br />
                    {% endfor %}
                    {{ (trans.getUser() and (trans.getUser().getEmail() != '<EMAIL>') ? trans.getUser().getEmail() : 'No e-mail address provided') }}<br />
                    {% for email in txnMeta['alternate_email'] %}
                        {% if not (trans.getUser() and email.getValue() == trans.getUser().getEmail()) %}
                            {{ email.getValue() }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ trans.getPhone()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                    {% for phone in txnMeta['alternate_phone'] %}
                        {% if phone.getValue() != trans.getPhone() %}
                            <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                        {% endif %}
                    {% endfor %}
                    {% if trans.getConfirmationCode() in view.possibleDups %}
                        <a rel="tooltip" title="This tenant separately reserved more than one unit. Check how many units were rented, and select Moved In for the same number of reservations as units occupied by the tenant."><h6>Multiple Reservations</h6></a>
                    {% endif %}
                    {% if trans.getConsumerContact() %}
                        <a rel="tooltip" title="This tenant requested a coupon for your facility via text message or email and subsequently moved in"><h6>Offline Reservation</h6></a>
                    {% endif %}
                    {{ supNotesDisplay is not empty ? '<br />' ~ supNotesDisplay : '' }}
                </td>
                <td><a class="edit-customer-button btn">Edit</a></td>
                <td id="date-{{ trans.getConfirmationCode() }}">{{ trans.getMoveIn()|date("m-d-Y") }}</td>
                <td>{{ trans.getTimestamp()|date("m-d-Y") }}</td>
                <td id="sparefootfee-{{ trans.getConfirmationCode() }}">${{ billableInstance.getSparefootCharge() }}</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if view.earlyTableList|length > 0 %}
<h3>Early Move Ins</h3>
<p>Let us know if any of these customers moved in early. If they did, change their move-in date below to improve your move-in rate and search ranking. If none of them moved in early, you don't need to do anything. Each reservation will simply appear on your statement next month.</p>

<table class="table table-striped data-grid" id="early_reservations">
    <thead>
        <tr>
            <th>Rent Collected</th>
            {% if not view.facilityId %}
                <th>Facility</th>
            {% endif %}
            <th>Customer</th>
            <th>Scheduled Move-In</th>
            <th>Reserved</th>
            <th><a rel="tooltip" title="{{ view.account.getResidualPercent() * 100 }}% of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
        </tr>
    </thead>
    <tbody>
        {% for trans in view.earlyTableList %}
            {% set facility = loadById(trans.getFacilityId()) %}
            {% set supNotesDisplay = '' %}
            {% set supNotes = trans.stringStatementSupportNotes() %}
            {% if supNotes is not empty %}
                {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ supNotes ~ '"><strong>NOTES</strong></a>' %}
            {% endif %}
            {% set txnMeta = trans.getBookingMeta(true) %}

            <tr id="{{ trans.getConfirmationCode() }}">
                <td id="action-{{ trans.getConfirmationCode() }}">
                    <div class="statement-reservation-actions-residual">
                        <div class="btn-group">
                            <a class="btn active">$0</a>
                            <a class="change-move-in-date-button btn">${{ trans.getPrice() }}</a>
                            <a class="rent-other-button btn">Other</a>
                        </div>
                    </div>
                </td>
                {% if not view.facilityId %}
                    <td id="facility-name-{{ trans.getConfirmationCode() }}"><a href="/billing/residualstatement/id/{{ view.statementId }}/facility/{{ facility.getId() }}">{{ facility.getTitleWithCompanyCode() }}</a></td>
                {% endif %}
                <td id="customer-{{ trans.getConfirmationCode() }}">
                    <span id="customer-name-{{ trans.getConfirmationCode() }}">
                        {{ trans.getLastName()|trim|title }}, {{ trans.getFirstName()|trim|title }}
                    </span><br />
                    {% for names in trans.getAlternateNamesMeta(true) %}
                        {{ names['last']|trim|title }}, {{ names['first']|trim|title }}<br />
                    {% endfor %}
                    {{ (trans.getUser() and (trans.getUser().getEmail() != '<EMAIL>') ? trans.getUser().getEmail() : 'No e-mail address provided') }}<br />
                    {% for email in txnMeta['alternate_email'] %}
                        {% if not (trans.getUser() and email.getValue() == trans.getUser().getEmail()) %}
                            {{ email.getValue() }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ trans.getPhone()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                    {% for phone in txnMeta['alternate_phone'] %}
                        {% if phone.getValue() != trans.getPhone() %}
                            <br />{{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}
                        {% endif %}
                    {% endfor %}
                </td>
                <td id="date-{{ trans.getConfirmationCode() }}">{{ trans.getMoveIn()|date("m-d-Y") }} {{ supNotesDisplay is not empty ? supNotesDisplay : '' }}</td>
                <td>{{ trans.getTimestamp()|date("m-d-Y") }}</td>
                <td id="sparefootfee-{{ trans.getConfirmationCode() }}">$0.00</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if view.tenantTableList|length > 0 %}
<br/><h3>Tenants</h3>
<p>You've previously received rent from each of these AdNetwork tenants. Let us know how much you collected from them during {{ view.batch.getStartDate()|date('F Y') }}.</p>
<table class="table table-striped data-grid" id="existing_tenants">
    <thead>
        <tr>
           <th>Rent Collected</th>
            {% if not view.facilityId %}
                <th>Facility</th>
            {% endif %}
           <th>Tenant</th>
           <th><a rel="tooltip" title="{{ view.account.getResidualPercent() * 100 }}% of the rent collected">SpareFoot Fee <i class="fa fa-info-circle"></i></a></th>
           <!--<th><a rel="tooltip" title="This is how much rent you've collected from this tenant so far.">Lifetime Value <i class="fa fa-info-circle"></i></a></th>-->
        </tr>
    </thead>
    <tbody>
        {% for biId, trans in view.tenantTableList %}
            {% set billableInstance = loadById(biId) %}
            {% set facility = loadById(billableInstance.getFacilityId()) %}

            {% set lastBis = loadBillableInstance({
                'statementBatchId': billableInstance.getStatementBatchId() - 1,
                'confirmationCode': billableInstance.getConfirmationCode()
            }) %}

            {% set wasDelinquent = 0 %}
            {% if lastBis is not empty and lastBis.getReason() == constant('\\Genesis_Entity_BillableInstance::REASON_DELIQUENCY') %}
                {% set wasDelinquent = 1 %}
            {% endif %}

            {% set supNotesDisplay = '' %}
            {% set supNotes = trans.stringStatementSupportNotes() %}
            {% if supNotes is not empty %}
                {% set supNotesDisplay = '<a rel="popover" data-original-title="Support Notes" data-content="' ~ supNotes ~ '"><strong>NOTES</strong></a>' %}
            {% endif %}

            {% set previouslyDelinquent = 0 %}
            {% if billableInstance.getPreviousBillableInstance() and billableInstance.getPreviousBillableInstance().getReason() == constant('\\Genesis_Entity_BillableInstance::REASON_DELIQUENCY') %}
                {% set previouslyDelinquent = 1 %}
            {% endif %}

            {% set txnMeta = trans.getBookingMeta(true) %}

            <tr id="{{ trans.getConfirmationCode() }}">
                <td id="action-{{ trans.getConfirmationCode() }}">
                    <div class="statement-reservation-actions-residual">
                        <input type="hidden" id="delinquent-{{ trans.getConfirmationCode() }}" value="{{ wasDelinquent }}" />
                        <input type="hidden" id="previously-delinquent-{{ trans.getConfirmationCode() }}" value="{{ previouslyDelinquent }}" />
                        <div class="btn-group" data-toggle="buttons-radio">
                            <a class="rent-zero-button btn {{ previouslyDelinquent ? 'delinquent' : '' }} {{ billableInstance.getAmountCollected() > 0 ? '' : 'active' }}">$0</a>
                            {% if billableInstance.getAmountCollected() > 0 %}
                                <a class="rent-default-button btn active {{ previouslyDelinquent ? 'delinquent' : '' }}">${{ billableInstance.getAmountCollected() }}</a>
                            {% else %}
                                <a class="rent-default-button btn {{ previouslyDelinquent ? 'delinquent' : '' }}">${{ billableInstance.getUnitPrice() }}</a>
                            {% endif %}
                            <a class="rent-other-button btn {{ previouslyDelinquent ? 'delinquent' : '' }}">Other</a>
                            {{ previouslyDelinquent ? '<h6>Previously Delinquent</h6>' : '' }}
                        </div>
                    </div>
                </td>
                {% if not view.facilityId %}
                    <td id="facility-name-{{ trans.getConfirmationCode() }}"><a href="/billing/viewstatement/id/{{ view.statementId }}/facility/{{ facility.getId() }}">{{ facility.getTitleWithCompanyCode() }}</a></td>
                {% endif %}
                <td id="customer-{{ trans.getConfirmationCode() }}">
                    <span id="customer-name-{{ trans.getConfirmationCode() }}">{{ trans.getLastName()|trim|title }}, {{ trans.getFirstName()|trim|title }}</span><br />
                    {% for names in trans.getAlternateNamesMeta(true) %}
                        {{ names['last']|trim|title }}, {{ names['first']|trim|title }}<br />
                    {% endfor %}
                    {{ (trans.getUser() and (trans.getUser().getEmail() != '<EMAIL>') ? trans.getUser().getEmail() : 'No e-mail address provided') }}<br />
                    {% for email in txnMeta['alternate_email'] %}
                        {% if not (trans.getUser() and email.getValue() == trans.getUser().getEmail()) %}
                            {{ email.getValue() }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ trans.getPhone()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}<br/>
                    {% for phone in txnMeta['alternate_phone'] %}
                        {% if phone.getValue() != trans.getPhone() %}
                            {{ phone.getValue()|replace({'/^[\\+]?[1]?[- ]?[\\(]?([2-9][0-8][0-9])[\\)]?[- ]?([2-9][0-9]{2})[- ]?([0-9]{4})$/': '(\\1) \\2-\\3'}) }}<br />
                        {% endif %}
                    {% endfor %}
                    {{ supNotesDisplay is not empty ? supNotesDisplay : '' }}
                </td>
                <td id="sparefootfee-{{ trans.getConfirmationCode() }}">${{ billableInstance.getSparefootCharge() }}</td>
                <!--<td><a href="#" rel="tooltip" title="This is how much rent you've collected from this tenant so far.">{{ call_static('\\Genesis_Service_BillableInstance', 'getSumByConfCode', billableInstance.getConfirmationCode()) }}</a><br />

                </td>-->
            </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

<form id="rent-zero-modal" class="modal fade" action="/billing/updateresidualstatement">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">&#215;</button>
                <h4>Why didn't you collect rent in {{ view.batch.getStartDate()|date('F') }}?</h4>
            </div>
            <div class="modal-body">
                <p id="rent-zero-customer-info"></p>
                <p>This customer...</p>
                <div class="controls">
                    <label class="radio"><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_SPECIAL') }}" /> received a free month as a part of a promotion</label>
                </div>
                <div class="controls only-new-reservations">
                    <label class="radio"><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY') }}" /> might move in at a future date</label>
                    <div class="hide customer-reason-specifics">
                        <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                        <input type="text" id="zero-into-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="datepicker-field" />
                    </div>
                </div>
                <div class="controls only-new-reservations">
                    <label class="radio"><input id="rescancel" type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_CANCELED') }}" /> will never move in</label>
                    <div class="hide customer-reason-specifics" style="margin-left:3em;">
                        <label for="dispute-text">Please explain:</label>
                        <textarea id="dispute-text" name="customer-dispute-reason"></textarea>
                    </div>
                </div>
                <!-- the next two options are only shown if the tenant has been previously confirmed -->
                <div class="controls only-existing-tenants show-after-delinquency">
                    <label class="radio"><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_DELIQUENCY') }}" /> is delinquent on their rent payments</label>
                </div>
                <div class="controls only-existing-tenants">
                    <label class="radio"><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVED_OUT') }}" /> moved out</label>
                </div>
                <div id="lien-sale-option" class="controls only-existing-tenants show-after-delinquency">
                	<label class="radio"><input type="radio" name="rent-zero-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVED_OUT') }}" /> lien sale of delinquent unit</label>
                </div>
            </div>
            <div class="modal-footer">
                <a class="ui basic button" data-dismiss="modal" id="dispute-modal-cancel">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="hidden" name="id" id="statement_id" value="{{ view.statementId }}" />
                <input type="submit" class="ui primary button disabled" disabled="disabled" value="Save" />
            </div>
        </div>
    </div>
</form>

<form id="rent-other-modal" class="modal fade" action="/billing/updateresidualstatement">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">&#215;</button>
                <h4>Other rent amount</h4>
            </div>
            <div class="modal-body">
                <p id="rent-other-customer-info"></p>

                <span id="rent-other-modal-mid" style="display: none;">
                <input type="hidden" id="early_late" name="early_late" value="0"/>
                <label for="earlylate-into-date">Please enter the customer's (approximate) new move-in date:</label>
                <input type="text" id="earlylate-into-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="datepicker-field" />
                </span>

                <label for="rent-other">Please enter how much rent the customer paid you during {{ view.batch.getStartDate()|date('F Y') }}.</label>
                <div class="input-prepend">
                    <span class="add-on">$</span><input type="text" id="rent-other" name="rent_other" value="" class="input-small" />
                </div>
                <br />
                <p>Why did you collect a different rent amount than what was listed on the AdNetwork?</p>
                <div class="controls">
                    <label class="radio"><input id='rent-change-unit' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_UNIT_CHANGE') }}" /> change of unit price (different rate, different size/type)</label>
                </div>
                <div class="controls">
                    <label class="radio"><input id='rent-change-prorate' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_PRORATE') }}" /> prorate</label>
                </div>
                <div class="controls">
                    <label class="radio"><input id='rent-change-special' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_SPECIAL') }}" /> promotional/temporary rate</label>
                </div>
                <div class="controls">
                    <label class="radio"><input id='rent-change-prepaid' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_PREPAID') }}" /> prepayment</label>
                </div>
                <div id="partial-payment-option" class="controls only-existing-tenants show-after-delinquency">
                    <label class="radio"><input id='rent-change-delinquency' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_DELIQUENCY') }}" /> partial payment (is delinquent on their rent payments)</label>
                </div>
                <div id="cured-delinquency-option" class="controls only-existing-tenants show-after-delinquency">
                    <label class="radio"><input id='rent-change-cured' type="radio" name="rent-other-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_CURED_DELIQUENCY') }}" /> cured delinquency</label>
                </div>
            </div>
            <div class="modal-footer">
                <a id="rent-other-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="hidden" name="id" id="statement_id" value="{{ view.statementId }}" />
                <input type="hidden" name="residual_percent" id="residual_percent" value="{{ view.account.getResidualPercent() }}" />
                <input type="submit" class="ui primary button disabled" disabled="disabled" value="Save" id="rent-change-submit" />
            </div>
        </div>
    </div>
</form>

<form id="change-move-in-date-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                <h4>Change move-in date</h4>
            </div>
            <div class="modal-body">
                <p id="change-move-in-date-customer-info"></p>
                <label for="mid-into-date">Please enter the customer's (approximate) new move-in date:</label>
                <input type="text" id="mid-into-date" name="into_date" value="" placeholder="YYYY-MM-DD" readonly="readonly" class="datepicker-field" />
            </div>
            <div class="modal-footer">
                <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui primary button" value="Save" />
            </div>
        </div>
    </div>
</form>

<form id="edit-customer-modal" class="modal fade" action="/billing/updateresidualstatement">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button class="close" data-dismiss="modal">&#215;</button>
                <h4>Edit customer</h4>
            </div>
            <div class="modal-body">
                <p id="edit-customer-customer-info"></p>
                <br />
                <p>What needs to be changed about this customer record?</p>
                <div class="controls">
                    <label class="radio"><input type="radio" name="rent-edit-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_NAME_CHANGE') }}" /> moved in under a different name</label>
                    <div class="hide customer-reason-specifics">
                        <p>Please provide us with accurate contact information to appear on your bill.</p>
                        <input type="text" id="change-first-name" name="first_name" value="" placeholder="First Name" />
                        <input type="text" id="change-last-name" name="last_name" value="" placeholder="Last Name" />
                    </div>
                </div>
                <div class="controls">
                    <label class="radio"><input type="radio" name="rent-edit-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_FACILITY_CHANGE') }}" /> moved into a sister facility</label>
                    <div id="facility-change-input" class="hide customer-reason-specifics">
                        <p>Which facility?</p>
                        <select id="new-facility-id" name="facility_id">
                            <option value="" diabled="disabled"></option>
                            {% for facility in view.availableSisterFacilities %}
                                {% if facility.isLive() %}
                                    <option value="{{ facility.getId() }}">{{ facility.getTitle() }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="controls">
                    <label class="radio"><input type="radio" name="rent-edit-reason" value="{{ constant('\\Genesis_Entity_BillableInstance::REASON_MOVE_IN_DELAY') }}" /> might move in at a future date</label>
                    <div class="hide customer-reason-specifics">
                        <p>Please enter the customer's new move-in date (pick any date this month if you don't know it):</p>
                        <input type="text" id="edit-into-date" name="into_date" value="" placeholder="MM-DD-YYYY" readonly="readonly" class="datepicker-field" />
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a id="rent-other-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="hidden" name="id" id="statement_id" value="{{ view.statementId }}" />
                <input type="submit" class="ui primary button disabled" disabled="disabled" value="Save" />
            </div>
        </div>
    </div>
</form>

<form id="change-move-in-date-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button id="change-move-in-date-modal-close" class="close" data-dismiss="modal">&#215;</button>
                <h4>Change move-in date</h4>
            </div>
            <div class="modal-body">
                <p id="change-move-in-date-customer-info"></p>
                <label for="into-date">Please enter the customer's (approximate) new move-in date:</label>
                <input type="text" id="into-date" name="into_date" value="" placeholder="MM-DD-YYYY" readonly="readonly" class="datepicker-field" />
            </div>
            <div class="modal-footer">
                <a id="change-move-in-date-modal-cancel" class="ui basic button" data-dismiss="modal">Cancel</a>
                <input type="hidden" name="confirmation_code" />
                <input type="submit" class="ui primary button" value="Save" />
            </div>
            <input type="hidden" id="statement_id" value="{{ view.statementId }}" />
        </div>
    </div>
</form>
{% endblock %}